@page "/job-type-management"
@using CoreHub.Shared.Models.Database
@using CoreHub.Shared.Services
@inject IJobTypeService JobTypeService
@inject IUserManagementService UserManagementService
@inject ISnackbar Snackbar
@inject IDialogService DialogService

<PageTitle>工种管理</PageTitle>

<MudContainer MaxWidth="MaxWidth.ExtraLarge" Class="mt-4">
    <MudPaper Class="pa-4">
        <MudGrid>
            <MudItem xs="12">
                <MudText Typo="Typo.h4" Class="mb-4">
                    <MudIcon Icon="@Icons.Material.Filled.Work" Class="mr-2" />
                    工种管理
                </MudText>
            </MudItem>

            <!-- 工具栏 -->
            <MudItem xs="12">
                <MudStack Row Justify="Justify.SpaceBetween" AlignItems="AlignItems.Center" Class="mb-4">
                    <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                        <MudTextField @bind-Value="searchText" 
                                    Placeholder="搜索工种名称、编码或分类..." 
                                    Adornment="Adornment.Start" 
                                    AdornmentIcon="@Icons.Material.Filled.Search"
                                    Immediate="true"
                                    OnKeyUp="OnSearchKeyUp"
                                    Class="mr-4" />
                        <MudSelect T="string" @bind-Value="selectedCategory" 
                                 Label="分类筛选" 
                                 Clearable="true"
                                 OnClearButtonClick="ClearCategoryFilter">
                            @foreach (var category in categories)
                            {
                                <MudSelectItem T="string" Value="@category">@category</MudSelectItem>
                            }
                        </MudSelect>
                        <MudButton Variant="Variant.Outlined" 
                                 StartIcon="@Icons.Material.Filled.Refresh"
                                 OnClick="LoadJobTypes">
                            刷新
                        </MudButton>
                    </MudStack>
                    <MudButton Variant="Variant.Filled" 
                             Color="Color.Primary" 
                             StartIcon="@Icons.Material.Filled.Add"
                             OnClick="OpenCreateDialog">
                        新增工种
                    </MudButton>
                </MudStack>
            </MudItem>

            <!-- 统计卡片 -->
            <MudItem xs="12" md="3">
                <MudCard>
                    <MudCardContent>
                        <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                            <MudIcon Icon="@Icons.Material.Filled.Work" Color="Color.Primary" Size="Size.Large" />
                            <MudStack Spacing="0">
                                <MudText Typo="Typo.h6">@filteredJobTypes.Count</MudText>
                                <MudText Typo="Typo.body2" Color="Color.Secondary">总工种数</MudText>
                            </MudStack>
                        </MudStack>
                    </MudCardContent>
                </MudCard>
            </MudItem>
            <MudItem xs="12" md="3">
                <MudCard>
                    <MudCardContent>
                        <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                            <MudIcon Icon="@Icons.Material.Filled.CheckCircle" Color="Color.Success" Size="Size.Large" />
                            <MudStack Spacing="0">
                                <MudText Typo="Typo.h6">@filteredJobTypes.Count(jt => jt.IsEnabled)</MudText>
                                <MudText Typo="Typo.body2" Color="Color.Secondary">启用工种</MudText>
                            </MudStack>
                        </MudStack>
                    </MudCardContent>
                </MudCard>
            </MudItem>
            <MudItem xs="12" md="3">
                <MudCard>
                    <MudCardContent>
                        <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                            <MudIcon Icon="@Icons.Material.Filled.Category" Color="Color.Info" Size="Size.Large" />
                            <MudStack Spacing="0">
                                <MudText Typo="Typo.h6">@categories.Count</MudText>
                                <MudText Typo="Typo.body2" Color="Color.Secondary">工种分类</MudText>
                            </MudStack>
                        </MudStack>
                    </MudCardContent>
                </MudCard>
            </MudItem>
            <MudItem xs="12" md="3">
                <MudCard>
                    <MudCardContent>
                        <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                            <MudIcon Icon="@Icons.Material.Filled.People" Color="Color.Warning" Size="Size.Large" />
                            <MudStack Spacing="0">
                                <MudText Typo="Typo.h6">@totalUserCount</MudText>
                                <MudText Typo="Typo.body2" Color="Color.Secondary">关联用户</MudText>
                            </MudStack>
                        </MudStack>
                    </MudCardContent>
                </MudCard>
            </MudItem>

            <!-- 数据表格 -->
            <MudItem xs="12">
                <MudDataGrid T="JobType" 
                           Items="@filteredJobTypes" 
                           Loading="@loading"
                           Hover="true" 
                           Striped="true"
                           Dense="true"
                           FixedHeader="true"
                           Height="600px"
                           SortMode="SortMode.Single"
                           Groupable="true"
                           GroupExpanded="true">
                    <Columns>
                        <PropertyColumn Property="x => x.Category" Title="分类" Grouping />
                        <PropertyColumn Property="x => x.Code" Title="工种编码" />
                        <PropertyColumn Property="x => x.Name" Title="工种名称" />
                        <PropertyColumn Property="x => x.Description" Title="描述" />
                        <PropertyColumn Property="x => x.SortOrder" Title="排序" />
                        <TemplateColumn Title="状态" Sortable="false">
                            <CellTemplate>
                                <MudChip Color="@(context.Item.IsEnabled ? Color.Success : Color.Default)" 
                                       Size="Size.Small">
                                    @(context.Item.IsEnabled ? "启用" : "禁用")
                                </MudChip>
                            </CellTemplate>
                        </TemplateColumn>
                        <TemplateColumn Title="用户数量" Sortable="false">
                            <CellTemplate>
                                <MudChip Color="Color.Info" Size="Size.Small" 
                                       OnClick="() => ShowJobTypeUsers(context.Item)">
                                    @jobTypeUserCounts.GetValueOrDefault(context.Item.Id, 0) 人
                                </MudChip>
                            </CellTemplate>
                        </TemplateColumn>
                        <PropertyColumn Property="x => x.CreatedAt" Title="创建时间" Format="yyyy-MM-dd HH:mm" />
                        <TemplateColumn Title="操作" Sortable="false" Filterable="false">
                            <CellTemplate>
                                <MudStack Row Spacing="1">
                                    <MudIconButton Icon="@Icons.Material.Filled.Edit" 
                                                 Color="Color.Primary" 
                                                 Size="Size.Small"
                                                 OnClick="() => OpenEditDialog(context.Item)"
                                                 Title="编辑" />
                                    <MudIconButton Icon="@Icons.Material.Filled.People" 
                                                 Color="Color.Info" 
                                                 Size="Size.Small"
                                                 OnClick="() => ManageJobTypeUsers(context.Item)"
                                                 Title="用户分配" />
                                    <MudIconButton Icon="@(context.Item.IsEnabled ? Icons.Material.Filled.Block : Icons.Material.Filled.CheckCircle)" 
                                                 Color="@(context.Item.IsEnabled ? Color.Warning : Color.Success)" 
                                                 Size="Size.Small"
                                                 OnClick="() => ToggleStatus(context.Item)"
                                                 Title="@(context.Item.IsEnabled ? "禁用" : "启用")" />
                                    <MudIconButton Icon="@Icons.Material.Filled.Delete" 
                                                 Color="Color.Error" 
                                                 Size="Size.Small"
                                                 OnClick="() => DeleteJobType(context.Item)"
                                                 Title="删除" />
                                </MudStack>
                            </CellTemplate>
                        </TemplateColumn>
                    </Columns>
                    <NoRecordsContent>
                        <MudText Typo="Typo.body1" Align="Align.Center" Class="pa-4">
                            暂无数据
                        </MudText>
                    </NoRecordsContent>
                    <LoadingContent>
                        <MudProgressCircular Color="Color.Primary" Indeterminate="true" />
                    </LoadingContent>
                </MudDataGrid>
            </MudItem>
        </MudGrid>
    </MudPaper>
</MudContainer>

@code {
    private List<JobType> jobTypes = new();
    private List<JobType> filteredJobTypes = new();
    private List<string> categories = new();
    private Dictionary<int, int> jobTypeUserCounts = new();
    private bool loading = false;
    private string searchText = string.Empty;
    private string selectedCategory = string.Empty;
    private int totalUserCount = 0;

    protected override async Task OnInitializedAsync()
    {
        await LoadJobTypes();
    }

    private async Task LoadJobTypes()
    {
        loading = true;
        try
        {
            jobTypes = await JobTypeService.GetAllJobTypesAsync();
            categories = jobTypes.Select(jt => jt.Category).Distinct().OrderBy(c => c).ToList();

            // 加载用户数量统计
            await LoadUserCounts();

            FilterJobTypes();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载工种数据失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            loading = false;
        }
    }

    private async Task LoadUserCounts()
    {
        try
        {
            jobTypeUserCounts.Clear();
            totalUserCount = 0;

            foreach (var jobType in jobTypes)
            {
                var count = await JobTypeService.GetJobTypeUserCountAsync(jobType.Id);
                jobTypeUserCounts[jobType.Id] = count;
                totalUserCount += count;
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载用户统计失败: {ex.Message}", Severity.Error);
        }
    }

    private void FilterJobTypes()
    {
        var filtered = jobTypes.AsEnumerable();

        if (!string.IsNullOrWhiteSpace(searchText))
        {
            filtered = filtered.Where(jt =>
                jt.Name.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                jt.Code.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                jt.Category.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                (jt.Description?.Contains(searchText, StringComparison.OrdinalIgnoreCase) ?? false)
            );
        }

        if (!string.IsNullOrWhiteSpace(selectedCategory))
        {
            filtered = filtered.Where(jt => jt.Category == selectedCategory);
        }

        filteredJobTypes = filtered.OrderBy(jt => jt.Category).ThenBy(jt => jt.SortOrder).ThenBy(jt => jt.Name).ToList();
    }

    private void OnSearchKeyUp(KeyboardEventArgs e)
    {
        FilterJobTypes();
    }

    private void ClearCategoryFilter()
    {
        selectedCategory = string.Empty;
        FilterJobTypes();
    }

    private async Task OpenCreateDialog()
    {
        var parameters = new DialogParameters<JobTypeEditDialog>
        {
            { x => x.JobType, new JobType() },
            { x => x.IsEdit, false }
        };

        var dialog = await DialogService.ShowAsync<JobTypeEditDialog>("新增工种", parameters);
        var result = await dialog.Result;

        if (!result.Canceled)
        {
            await LoadJobTypes();
        }
    }

    private async Task OpenEditDialog(JobType jobType)
    {
        var parameters = new DialogParameters<JobTypeEditDialog>
        {
            { x => x.JobType, jobType },
            { x => x.IsEdit, true }
        };

        var dialog = await DialogService.ShowAsync<JobTypeEditDialog>("编辑工种", parameters);
        var result = await dialog.Result;

        if (!result.Canceled)
        {
            await LoadJobTypes();
        }
    }

    private async Task ToggleStatus(JobType jobType)
    {
        try
        {
            // 切换状态
            jobType.IsEnabled = !jobType.IsEnabled;
            jobType.UpdatedAt = DateTime.Now;

            var result = await JobTypeService.UpdateJobTypeAsync(jobType);
            if (result)
            {
                Snackbar.Add($"工种状态已{(jobType.IsEnabled ? "启用" : "禁用")}", Severity.Success);
                await LoadJobTypes();
            }
            else
            {
                // 恢复状态
                jobType.IsEnabled = !jobType.IsEnabled;
                Snackbar.Add("操作失败", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            // 恢复状态
            jobType.IsEnabled = !jobType.IsEnabled;
            Snackbar.Add($"操作失败: {ex.Message}", Severity.Error);
        }
    }

    private async Task DeleteJobType(JobType jobType)
    {
        // 检查是否有用户关联
        var userCount = jobTypeUserCounts.GetValueOrDefault(jobType.Id, 0);
        if (userCount > 0)
        {
            Snackbar.Add($"该工种下还有 {userCount} 个用户，无法删除", Severity.Warning);
            return;
        }

        var confirmed = await DialogService.ShowMessageBox(
            "确认删除",
            $"确定要删除工种 '{jobType.Name}' 吗？此操作不可撤销。",
            yesText: "删除",
            cancelText: "取消");

        if (confirmed == true)
        {
            try
            {
                var result = await JobTypeService.DeleteJobTypeAsync(jobType.Id);
                if (result)
                {
                    Snackbar.Add("工种删除成功", Severity.Success);
                    await LoadJobTypes();
                }
                else
                {
                    Snackbar.Add("删除失败", Severity.Error);
                }
            }
            catch (Exception ex)
            {
                Snackbar.Add($"删除失败: {ex.Message}", Severity.Error);
            }
        }
    }

    private async Task ShowJobTypeUsers(JobType jobType)
    {
        var parameters = new DialogParameters<JobTypeUsersDialog>
        {
            { x => x.JobType, jobType }
        };

        var options = new DialogOptions()
        {
            MaxWidth = MaxWidth.Large,
            FullWidth = true
        };

        await DialogService.ShowAsync<JobTypeUsersDialog>($"工种用户列表 - {jobType.Name}", parameters, options);
    }

    private async Task ManageJobTypeUsers(JobType jobType)
    {
        var parameters = new DialogParameters<UserJobTypeManagementDialog>
        {
            { x => x.JobType, jobType }
        };

        var options = new DialogOptions()
        {
            MaxWidth = MaxWidth.ExtraLarge,
            FullWidth = true
        };

        var dialog = await DialogService.ShowAsync<UserJobTypeManagementDialog>($"管理工种用户 - {jobType.Name}", parameters, options);
        var result = await dialog.Result;

        if (!result.Canceled)
        {
            // 刷新用户数量统计
            await LoadUserCounts();
        }
    }
}
