using FluentValidation;
using CustomerWebAPI.Models;

namespace CustomerWebAPI.Validators
{
    public class ChemicalBoxResultValidator : AbstractValidator<ChemicalBoxResult>
    {
    public ChemicalBoxResultValidator()
    {
        RuleFor(x => x.ChemicalCode)
            .NotEmpty().WithMessage("染料代码不能为空")
            .MaximumLength(50).WithMessage("染料代码长度不能超过50个字符");

        RuleFor(x => x.ChemicalName)
            .NotEmpty().WithMessage("染料名称不能为空")
            .MaximumLength(100).WithMessage("染料名称长度不能超过100个字符");

        RuleFor(x => x.ChemicalBoxNo)
            .NotEmpty().WithMessage("染料箱号不能为空")
                .MaximumLength(50).WithMessage("染料箱号长度不能超过50个字符");
        }
    }
} 
