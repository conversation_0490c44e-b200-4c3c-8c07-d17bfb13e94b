--根据染色工艺单自动出库染化料（改进版 - 支持先进先出多笔库存扣减）
CREATE PROCEDURE usp_msAutoDeliveryChemcialByBill ( @Bill_NO VARCHAR(50) )
AS
BEGIN

    --DECLARE @Bill_NO VARCHAR(50) = 'A250629051';

    BEGIN TRY 

        --DROP TABLE #PendingConsumeStockDtl;

        --工艺单染料使用量
        SELECT  a.Iden AS BillDtlIden ,
                a.Material_Code ,
                a.Chemical_Code ,
                a.Chemical_Name ,
                CONVERT(NUMERIC(18, 3), a.Order_Dosage / 1000) AS UsageKG ,
                a.Bill_NO ,
                CONVERT(INT, 0) AS StockInID
        INTO    #PendingConsumeStockDtl
        FROM    LABDB..uvw_lbBillOfStuff a
        WHERE   a.Chemical_Type = 'D'
                AND a.Bill_NO = @Bill_NO
                AND a.Is_Cancel = 0;

        BEGIN TRAN;

        ---------------------------------插入出货主表------------------------------------------
        --生成新出货单号
        DECLARE @NewNoteNO VARCHAR(50)= '';
        SELECT  @NewNoteNO = dbo.udf_msGenerateNewNoteNo('CS', 'OUT');

        IF ISNULL(@NewNoteNO, '') = ''
            BEGIN
                RAISERROR('生成出货单号失败！',16,1);
                RETURN;
            END;

        INSERT  INTO dbo.msMaterialOutHdr
                ( Out_Type ,
                  Note_NO ,
                  House_Code ,
                  Request_NO ,
                  Department ,
                  Destination ,
                  Operator ,
                  Operate_Time ,
                  Receiver ,
                  Remark
                )
        VALUES  ( '车间领用' , -- Out_Type - varchar(10)
                  @NewNoteNO , -- Note_NO - varchar(12)
                  'CS' , -- House_Code - varchar(12)
                  '' , -- Request_NO - varchar(12)
                  'RT' , -- Department - varchar(10)
                  '' , -- Destination - varchar(20)
                  '系统出货' , -- Operator - varchar(20)
                  GETDATE() , -- Operate_Time - smalldatetime
                  '染台' , -- Receiver - varchar(50)
                  '根据染纱工艺单出货'  -- Remark - varchar(200)
                );

        ------------------------------插入出货明细表（先进先出逻辑）--------------------------------------------
        DECLARE @BillDtlIden INT ,
            @Material_Code VARCHAR(50) ,
            @UsageKG NUMERIC(18, 3) ,
            @RemainingUsageKG NUMERIC(18, 3);
        
        DECLARE cur_BillDyestuff CURSOR FAST_FORWARD READ_ONLY
        FOR
        SELECT  BillDtlIden ,
                Material_Code ,
                UsageKG
        FROM    #PendingConsumeStockDtl;
        
        OPEN cur_BillDyestuff; 
        
        FETCH NEXT FROM cur_BillDyestuff INTO @BillDtlIden, @Material_Code, @UsageKG;
        
        WHILE @@FETCH_STATUS = 0
            BEGIN
                SET @RemainingUsageKG = @UsageKG;
                
                -- 检查该物料的总库存是否足够
                DECLARE @TotalStock NUMERIC(18, 3);
                SELECT  @TotalStock = ISNULL(SUM(Quantity), 0)
                FROM    WMSDB..uvw_msMaterialStock
                WHERE   Material_Code = @Material_Code
                        AND House_Code = 'CS'
                        AND Quantity > 0;
                
                IF @TotalStock < @UsageKG
                    BEGIN
                        DECLARE @ErrorMsg NVARCHAR(200) = N'物料 ' + @Material_Code + N' 库存不足！需要：' + 
                                                         CAST(@UsageKG AS NVARCHAR(20)) + N'KG，现有：' + 
                                                         CAST(@TotalStock AS NVARCHAR(20)) + N'KG';
                        RAISERROR(@ErrorMsg, 16, 1);
                        RETURN;
                    END;
                
                -- 创建临时表存储该物料的库存记录，按Operate_Time排序（先进先出）
                CREATE TABLE #TempStock (
                    RowNum INT IDENTITY(1,1),
                    In_ID INT,
                    Material_Code VARCHAR(50),
                    Quantity NUMERIC(18, 3),
                    Unit VARCHAR(10),
                    Spec VARCHAR(50),
                    Lot VARCHAR(50),
                    Usage VARCHAR(100),
                    Operate_Time SMALLDATETIME
                );
                
                -- 获取该物料的所有可用库存，按Operate_Time升序排列（先进先出）
                INSERT INTO #TempStock (In_ID, Material_Code, Quantity, Unit, Spec, Lot, Usage, Operate_Time)
                SELECT  In_ID,
                        Material_Code,
                        Quantity,
                        Unit,
                        Spec,
                        Lot,
                        Usage,
                        Operate_Time
                FROM    WMSDB..uvw_msMaterialStock
                WHERE   Material_Code = @Material_Code
                        AND House_Code = 'CS'
                        AND Quantity > 0
                ORDER BY Operate_Time ASC, In_ID ASC;
                
                -- 按先进先出顺序扣减库存
                DECLARE @CurrentInID INT,
                        @CurrentQuantity NUMERIC(18, 3),
                        @CurrentUnit VARCHAR(10),
                        @CurrentSpec VARCHAR(50),
                        @CurrentLot VARCHAR(50),
                        @CurrentUsage VARCHAR(100),
                        @OutQuantity NUMERIC(18, 3);
                
                DECLARE cur_Stock CURSOR FAST_FORWARD READ_ONLY
                FOR
                SELECT  In_ID, Quantity, Unit, Spec, Lot, Usage
                FROM    #TempStock
                ORDER BY RowNum;
                
                OPEN cur_Stock;
                
                FETCH NEXT FROM cur_Stock INTO @CurrentInID, @CurrentQuantity, @CurrentUnit, @CurrentSpec, @CurrentLot, @CurrentUsage;
                
                WHILE @@FETCH_STATUS = 0 AND @RemainingUsageKG > 0
                    BEGIN
                        -- 计算本次扣减数量
                        IF @CurrentQuantity >= @RemainingUsageKG
                            BEGIN
                                -- 当前库存足够剩余需求
                                SET @OutQuantity = @RemainingUsageKG;
                                SET @RemainingUsageKG = 0;
                            END
                        ELSE
                            BEGIN
                                -- 当前库存不足，全部扣减
                                SET @OutQuantity = @CurrentQuantity;
                                SET @RemainingUsageKG = @RemainingUsageKG - @CurrentQuantity;
                            END;
                        
                        -- 插入出货明细
                        INSERT  INTO dbo.msMaterialOutDtl
                                ( Note_NO ,
                                  In_ID ,
                                  Material_Code ,
                                  Quantity ,
                                  Unit ,
                                  Spec ,
                                  Lot ,
                                  Usage ,
                                  BillDetailId
                                )
                        VALUES  ( @NewNoteNO ,
                                  @CurrentInID ,
                                  @Material_Code ,
                                  @OutQuantity ,
                                  @CurrentUnit ,
                                  @CurrentSpec ,
                                  @CurrentLot ,
                                  @CurrentUsage ,
                                  @BillDtlIden
                                );
                        
                        -- 更新库存数量
                        UPDATE  dbo.msMaterialStock
                        SET     Quantity = Quantity - @OutQuantity
                        WHERE   In_ID = @CurrentInID;
                        
                        FETCH NEXT FROM cur_Stock INTO @CurrentInID, @CurrentQuantity, @CurrentUnit, @CurrentSpec, @CurrentLot, @CurrentUsage;
                    END;
                
                CLOSE cur_Stock;
                DEALLOCATE cur_Stock;
                
                -- 清理临时表
                DROP TABLE #TempStock;
                
                -- 检查是否还有未扣减完的需求
                IF @RemainingUsageKG > 0
                    BEGIN
                        DECLARE @ErrorMsg2 NVARCHAR(200) = N'物料 ' + @Material_Code + N' 扣减过程中库存不足！剩余需求：' + 
                                                          CAST(@RemainingUsageKG AS NVARCHAR(20)) + N'KG';
                        RAISERROR(@ErrorMsg2, 16, 1);
                        RETURN;
                    END;
                
                -- 标记该工艺单明细已处理
                UPDATE  #PendingConsumeStockDtl
                SET     StockInID = 1  -- 标记为已处理
                WHERE   BillDtlIden = @BillDtlIden;

                FETCH NEXT FROM cur_BillDyestuff INTO @BillDtlIden, @Material_Code, @UsageKG;
            END;
        
        CLOSE cur_BillDyestuff;
        DEALLOCATE cur_BillDyestuff;

        --标记工艺单明细对应的染料库存已经更新
        UPDATE  a
        SET     IsUpdatedStock = 1
        FROM    LABDB..lbBillOfStuffDtl a
                JOIN #PendingConsumeStockDtl b ON b.BillDtlIden = a.Iden
        WHERE   b.StockInID IS NOT NULL;

        -- 清理临时表
        DROP TABLE #PendingConsumeStockDtl;

        COMMIT TRANSACTION;
        PRINT N'出货单号：' + @NewNoteNO + N' 生成成功！';
                
    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
                
        -- 清理可能存在的临时表
        IF OBJECT_ID('tempdb..#TempStock') IS NOT NULL
            DROP TABLE #TempStock;
        IF OBJECT_ID('tempdb..#PendingConsumeStockDtl') IS NOT NULL
            DROP TABLE #PendingConsumeStockDtl;
                
        -- 记录错误信息
        DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE();
        DECLARE @ErrorSeverity INT = ERROR_SEVERITY();
        DECLARE @ErrorState INT = ERROR_STATE();
        DECLARE @ErrorProcedure NVARCHAR(50) = ISNULL(ERROR_PROCEDURE(), 'usp_msAutoDeliveryChemcialByBill');
        DECLARE @ErrorLine INT = ERROR_LINE();
                
        DECLARE @FullErrorMessage NVARCHAR(4000) = N'存储过程执行失败：'
            + @ErrorProcedure + N'，第 ' + CAST(@ErrorLine AS NVARCHAR(10))
            + N' 行。' + N'错误信息：' + @ErrorMessage;
                
        RAISERROR(@FullErrorMessage, @ErrorSeverity, @ErrorState);
    END CATCH;

END; 