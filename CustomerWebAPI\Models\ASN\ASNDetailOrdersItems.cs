using System;
using Dapper.Contrib.Extensions;

namespace CustomerWebAPI.Models
{
    /// <summary>
    /// 
    /// </summary>
    /// 
    [Table("ASNDetailOrdersItems")]
    public class ASNDetailOrdersItems
    {
        /// <summary>
        /// 
        /// </summary>
        /// 
        [ExplicitKey]
        public Guid? Id { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public Guid? ASNDetailOrdersId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string AssignedId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public int PoLineNumber { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string ItemNumber { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string ContractNumber { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string DyeLotSeries { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public int SubLineNumber { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public int PackQuantity { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string PackCode { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string WeightsQualifier { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string WeightsValue { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string WeightsUnit { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string DyeMatch { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string ColorDescription { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string FabricDescription { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public decimal? FreeOfChargeQuantity { get; set; }
    }
}