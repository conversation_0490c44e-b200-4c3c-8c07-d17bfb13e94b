using SqlSugar;
using System.ComponentModel.DataAnnotations;

namespace CoreHub.Shared.Models.Database
{
    /// <summary>
    /// 设备实体
    /// </summary>
    [SugarTable("Equipment")]
    public class Equipment
    {
        /// <summary>
        /// 设备ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        /// <summary>
        /// 设备编码（唯一）
        /// </summary>
        [SugarColumn(Length = 50, IsNullable = false)]
        [Required(ErrorMessage = "设备编码不能为空")]
        [StringLength(50, ErrorMessage = "设备编码长度不能超过50个字符")]
        public string Code { get; set; } = string.Empty;

        /// <summary>
        /// 设备名称
        /// </summary>
        [SugarColumn(Length = 100, IsNullable = false)]
        [Required(ErrorMessage = "设备名称不能为空")]
        [StringLength(100, ErrorMessage = "设备名称长度不能超过100个字符")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 所属部门ID
        /// </summary>
        [SugarColumn(IsNullable = false)]
        [Required(ErrorMessage = "所属部门不能为空")]
        public int DepartmentId { get; set; }

        /// <summary>
        /// 设备型号ID
        /// </summary>
        [SugarColumn(IsNullable = false)]
        [Required(ErrorMessage = "设备型号不能为空")]
        public int ModelId { get; set; }

        /// <summary>
        /// 所在位置ID
        /// </summary>
        [SugarColumn(IsNullable = false)]
        [Required(ErrorMessage = "所在位置不能为空")]
        public int LocationId { get; set; }

        /// <summary>
        /// 序列号
        /// </summary>
        [SugarColumn(Length = 100, IsNullable = true)]
        [StringLength(100, ErrorMessage = "序列号长度不能超过100个字符")]
        public string? SerialNumber { get; set; }

        /// <summary>
        /// 资产编号
        /// </summary>
        [SugarColumn(Length = 100, IsNullable = true)]
        [StringLength(100, ErrorMessage = "资产编号长度不能超过100个字符")]
        public string? AssetNumber { get; set; }

        /// <summary>
        /// 购买日期
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? PurchaseDate { get; set; }

        /// <summary>
        /// 保修到期日期
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? WarrantyExpiry { get; set; }

        /// <summary>
        /// 设备状态（1=正常,2=维修中,3=停用,4=报废）
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int Status { get; set; } = 1;

        /// <summary>
        /// 最后维护日期
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? LastMaintenanceDate { get; set; }

        /// <summary>
        /// 下次维护日期
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? NextMaintenanceDate { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        [SugarColumn(Length = 500, IsNullable = true)]
        [StringLength(500, ErrorMessage = "描述长度不能超过500个字符")]
        public string? Description { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 创建时间
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 创建人ID
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? CreatedBy { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// 更新人ID
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? UpdatedBy { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [SugarColumn(Length = 1000, IsNullable = true)]
        [StringLength(1000, ErrorMessage = "备注长度不能超过1000个字符")]
        public string? Remark { get; set; }

        // 导航属性
        /// <summary>
        /// 所属部门
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public Department? Department { get; set; }

        /// <summary>
        /// 设备型号
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public EquipmentModel? Model { get; set; }

        /// <summary>
        /// 所在位置
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public Location? Location { get; set; }

        /// <summary>
        /// 报修单列表
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public List<RepairOrder> RepairOrders { get; set; } = new List<RepairOrder>();

        /// <summary>
        /// 设备状态名称
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string StatusName => Status switch
        {
            1 => "正常",
            2 => "维修中",
            3 => "停用",
            4 => "报废",
            _ => "未知"
        };
    }
}
