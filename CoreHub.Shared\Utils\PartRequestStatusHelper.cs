using MudBlazor;

namespace CoreHub.Shared.Utils
{
    /// <summary>
    /// 零件申请状态帮助类
    /// 统一管理零件申请的状态定义、名称、颜色等
    /// </summary>
    public static class PartRequestStatusHelper
    {
        #region 状态常量定义

        /// <summary>
        /// 申请中 - 初始状态，可编辑删除
        /// </summary>
        public const int Pending = 1;

        /// <summary>
        /// 已领用 - 已从仓库领取，等待安装
        /// </summary>
        public const int Issued = 2;

        /// <summary>
        /// 已安装 - 安装完成，流程结束
        /// </summary>
        public const int Installed = 3;

        /// <summary>
        /// 已取消 - 申请取消，流程结束
        /// </summary>
        public const int Cancelled = 4;

        #endregion

        #region 状态名称映射

        /// <summary>
        /// 获取状态对应的中文名称
        /// </summary>
        /// <param name="status">状态值</param>
        /// <returns>状态名称</returns>
        public static string GetStatusName(int status)
        {
            return status switch
            {
                Pending => "申请中",
                Issued => "已领用",
                Installed => "已安装",
                Cancelled => "已取消",
                _ => "未知"
            };
        }

        #endregion

        #region 状态颜色映射

        /// <summary>
        /// 获取状态对应的颜色
        /// </summary>
        /// <param name="status">状态值</param>
        /// <returns>MudBlazor颜色</returns>
        public static Color GetStatusColor(int status)
        {
            return status switch
            {
                Pending => Color.Warning,    // 申请中 - 橙色
                Issued => Color.Primary,     // 已领用 - 蓝色
                Installed => Color.Success,  // 已安装 - 绿色
                Cancelled => Color.Error,    // 已取消 - 红色
                _ => Color.Default
            };
        }

        #endregion

        #region 状态验证

        /// <summary>
        /// 检查状态值是否有效
        /// </summary>
        /// <param name="status">状态值</param>
        /// <returns>是否有效</returns>
        public static bool IsValidStatus(int status)
        {
            return status is Pending or Issued or Installed or Cancelled;
        }

        /// <summary>
        /// 检查状态流转是否有效
        /// </summary>
        /// <param name="currentStatus">当前状态</param>
        /// <param name="newStatus">新状态</param>
        /// <returns>是否可以流转</returns>
        public static bool IsValidStatusTransition(int currentStatus, int newStatus)
        {
            var validTransitions = new Dictionary<int, List<int>>
            {
                { Pending, new List<int> { Issued, Cancelled } },    // 申请中 → 已领用或已取消
                { Issued, new List<int> { Installed, Cancelled } },  // 已领用 → 已安装或已取消
                { Installed, new List<int>() },                      // 已安装 → 无法变更
                { Cancelled, new List<int>() }                       // 已取消 → 无法变更
            };

            return validTransitions.ContainsKey(currentStatus) && 
                   validTransitions[currentStatus].Contains(newStatus);
        }

        #endregion

        #region 状态权限控制

        /// <summary>
        /// 检查是否可以编辑
        /// </summary>
        /// <param name="status">状态值</param>
        /// <returns>是否可以编辑</returns>
        public static bool CanEdit(int status)
        {
            return status == Pending; // 只有申请中状态可以编辑
        }

        /// <summary>
        /// 检查是否可以删除
        /// </summary>
        /// <param name="status">状态值</param>
        /// <returns>是否可以删除</returns>
        public static bool CanDelete(int status)
        {
            return status == Pending; // 只有申请中状态可以删除
        }

        /// <summary>
        /// 检查是否可以发放
        /// </summary>
        /// <param name="status">状态值</param>
        /// <returns>是否可以发放</returns>
        public static bool CanIssue(int status)
        {
            return status == Pending; // 只有申请中状态可以发放
        }

        /// <summary>
        /// 检查是否可以安装
        /// </summary>
        /// <param name="status">状态值</param>
        /// <returns>是否可以安装</returns>
        public static bool CanInstall(int status)
        {
            return status == Issued; // 只有已领用状态可以安装
        }

        /// <summary>
        /// 检查是否可以取消
        /// </summary>
        /// <param name="status">状态值</param>
        /// <returns>是否可以取消</returns>
        public static bool CanCancel(int status)
        {
            return status is Pending or Issued; // 申请中和已领用状态可以取消
        }

        #endregion

        #region 状态分组

        /// <summary>
        /// 检查是否为活跃状态（未完成的状态）
        /// </summary>
        /// <param name="status">状态值</param>
        /// <returns>是否为活跃状态</returns>
        public static bool IsActiveStatus(int status)
        {
            return status is Pending or Issued;
        }

        /// <summary>
        /// 检查是否为完成状态
        /// </summary>
        /// <param name="status">状态值</param>
        /// <returns>是否为完成状态</returns>
        public static bool IsCompletedStatus(int status)
        {
            return status == Installed;
        }

        /// <summary>
        /// 检查是否为终止状态（无法再变更的状态）
        /// </summary>
        /// <param name="status">状态值</param>
        /// <returns>是否为终止状态</returns>
        public static bool IsFinalStatus(int status)
        {
            return status is Installed or Cancelled;
        }

        #endregion

        #region 状态列表

        /// <summary>
        /// 获取所有有效状态列表
        /// </summary>
        /// <returns>状态列表</returns>
        public static List<(int Value, string Name)> GetAllStatuses()
        {
            return new List<(int, string)>
            {
                (Pending, GetStatusName(Pending)),
                (Issued, GetStatusName(Issued)),
                (Installed, GetStatusName(Installed)),
                (Cancelled, GetStatusName(Cancelled))
            };
        }

        /// <summary>
        /// 获取可以流转到的状态列表
        /// </summary>
        /// <param name="currentStatus">当前状态</param>
        /// <returns>可流转状态列表</returns>
        public static List<(int Value, string Name)> GetAvailableTransitions(int currentStatus)
        {
            var validTransitions = new Dictionary<int, List<int>>
            {
                { Pending, new List<int> { Issued, Cancelled } },
                { Issued, new List<int> { Installed, Cancelled } },
                { Installed, new List<int>() },
                { Cancelled, new List<int>() }
            };

            if (!validTransitions.ContainsKey(currentStatus))
                return new List<(int, string)>();

            return validTransitions[currentStatus]
                .Select(status => (status, GetStatusName(status)))
                .ToList();
        }

        #endregion

        #region 统计相关

        /// <summary>
        /// 计算完成率
        /// </summary>
        /// <param name="totalCount">总数量</param>
        /// <param name="installedCount">已安装数量</param>
        /// <returns>完成率百分比</returns>
        public static decimal CalculateCompletionRate(int totalCount, int installedCount)
        {
            if (totalCount == 0) return 100m;
            return Math.Round((decimal)installedCount / totalCount * 100, 2);
        }

        /// <summary>
        /// 获取状态统计描述
        /// </summary>
        /// <param name="pendingCount">申请中数量</param>
        /// <param name="issuedCount">已领用数量</param>
        /// <param name="installedCount">已安装数量</param>
        /// <param name="cancelledCount">已取消数量</param>
        /// <returns>统计描述</returns>
        public static string GetStatusSummary(int pendingCount, int issuedCount, int installedCount, int cancelledCount)
        {
            var total = pendingCount + issuedCount + installedCount + cancelledCount;
            if (total == 0) return "暂无零件申请";

            var parts = new List<string>();
            if (pendingCount > 0) parts.Add($"申请中{pendingCount}个");
            if (issuedCount > 0) parts.Add($"已领用{issuedCount}个");
            if (installedCount > 0) parts.Add($"已安装{installedCount}个");
            if (cancelledCount > 0) parts.Add($"已取消{cancelledCount}个");

            return $"共{total}个零件申请：{string.Join("，", parts)}";
        }

        #endregion
    }
}
