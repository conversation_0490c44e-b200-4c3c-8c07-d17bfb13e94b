using SqlSugar;

namespace CoreHub.Shared.Models.Database
{
    /// <summary>
    /// 角色部门分配关系表
    /// 简化的多对多关系，一个角色可以分配多个部门
    /// </summary>
    [SugarTable("RoleDepartmentAssignments")]
    public class RoleDepartmentAssignment
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        /// <summary>
        /// 角色ID
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int RoleId { get; set; }

        /// <summary>
        /// 部门ID
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int DepartmentId { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 创建时间
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 创建人ID
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? CreatedBy { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// 更新人ID
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? UpdatedBy { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [SugarColumn(IsNullable = true, Length = 500)]
        public string? Remark { get; set; }

        #region 导航属性

        /// <summary>
        /// 角色信息
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public Role? Role { get; set; }

        /// <summary>
        /// 部门信息
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public Department? Department { get; set; }

        #endregion
    }

    /// <summary>
    /// 角色部门分配视图模型
    /// </summary>
    public class RoleDepartmentAssignmentViewModel
    {
        public int RoleId { get; set; }
        public string RoleName { get; set; } = string.Empty;
        public string RoleCode { get; set; } = string.Empty;
        public List<DepartmentAssignmentItem> Departments { get; set; } = new();
        public int AssignedCount => Departments.Count(d => d.IsAssigned);
    }

    /// <summary>
    /// 部门分配项
    /// </summary>
    public class DepartmentAssignmentItem
    {
        public int DepartmentId { get; set; }
        public string DepartmentName { get; set; } = string.Empty;
        public string DepartmentCode { get; set; } = string.Empty;
        public bool IsAssigned { get; set; }
        public DateTime? AssignedAt { get; set; }
    }

    /// <summary>
    /// 分配统计信息
    /// </summary>
    public class AssignmentStatistics
    {
        public int TotalRoles { get; set; }
        public int TotalDepartments { get; set; }
        public int TotalAssignments { get; set; }
        public int RolesWithAssignments { get; set; }
        public double AssignmentRate => TotalRoles > 0 ? (double)RolesWithAssignments / TotalRoles * 100 : 0;
        public double CoverageRate => (TotalRoles * TotalDepartments) > 0 ? (double)TotalAssignments / (TotalRoles * TotalDepartments) * 100 : 0;
    }
}
