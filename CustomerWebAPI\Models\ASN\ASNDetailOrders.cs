using System;
using Dapper.Contrib.Extensions;

namespace CustomerWebAPI.Models
{
    /// <summary>
    /// 
    /// </summary>
    [Table("ASNDetailOrders")]
    public class ASNDetailOrders
    {
        /// <summary>
        /// 
        /// </summary>
        /// 
        [ExplicitKey]
        public Guid Id { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public Guid? ASNDetailId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string PurchaseOrderNumber { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string PurchaseOrderReleaseNumber { get; set; }
    }
}