using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http;
using System.Threading.Tasks;
using System.IO;
using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.AspNetCore.Hosting;
using OfficeOpenXml;
using System.Text.Json;

namespace CustomerWebAPI.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class ExcelController : ControllerBase
    {
        private readonly string _uploadPath;
        private readonly IWebHostEnvironment _environment;

        public ExcelController(IWebHostEnvironment environment)
        {
            _environment = environment;
            _uploadPath = Path.Combine(environment.ContentRootPath, "Uploads", "Excel");
            if (!Directory.Exists(_uploadPath))
            {
                Directory.CreateDirectory(_uploadPath);
            }
            // 设置 EPPlus 非商业许可
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
        }

        [HttpPost("upload")]
        public async Task<IActionResult> UploadExcel(IFormFile file)
        {
            if (file == null || file.Length == 0)
                return BadRequest("No file uploaded");

            var fileExtension = Path.GetExtension(file.FileName).ToLower();
            if (fileExtension != ".xlsx" && fileExtension != ".xls")
                return BadRequest("Invalid file format. Only Excel files are allowed.");

            try
            {
                var fileName = $"{DateTime.Now:yyyyMMddHHmmss}_{Path.GetFileNameWithoutExtension(file.FileName)}{fileExtension}";
                var filePath = Path.Combine(_uploadPath, fileName);

                using (var stream = new FileStream(filePath, FileMode.Create))
                {
                    await file.CopyToAsync(stream);
                }

                return Ok(new { fileName, originalName = file.FileName });
            }
            catch (Exception ex)
            {
                return BadRequest($"Error uploading file: {ex.Message}");
            }
        }

        [HttpGet("files")]
        public IActionResult GetExcelFiles()
        {
            try
            {
                var files = Directory.GetFiles(_uploadPath)
                    .Select(f => new
                    {
                        fileName = Path.GetFileName(f),
                        uploadTime = System.IO.File.GetCreationTime(f),
                        fileSize = new FileInfo(f).Length
                    })
                    .OrderByDescending(f => f.uploadTime);

                return Ok(files);
            }
            catch (Exception ex)
            {
                return BadRequest($"Error getting file list: {ex.Message}");
            }
        }

        [HttpGet("preview/{fileName}")]
        public IActionResult PreviewExcel(string fileName)
        {
            var filePath = Path.Combine(_uploadPath, fileName);
            if (!System.IO.File.Exists(filePath))
                return NotFound("File not found");

            try
            {
                using var package = new ExcelPackage(new FileInfo(filePath));
                var worksheet = package.Workbook.Worksheets[0]; // 获取第一个工作表
                var dimension = worksheet.Dimension;
                
                if (dimension == null)
                    return Ok(new { headers = new List<string>(), data = new List<Dictionary<string, string>>() });

                var headers = new List<string>();
                var data = new List<Dictionary<string, string>>();

                // 获取表头
                for (int col = 1; col <= dimension.End.Column; col++)
                {
                    var headerValue = worksheet.Cells[1, col].Text;
                    headers.Add(string.IsNullOrEmpty(headerValue) ? $"Column{col}" : headerValue);
                }

                // 获取数据
                for (int row = 2; row <= dimension.End.Row; row++)
                {
                    var rowData = new Dictionary<string, string>();
                    for (int col = 1; col <= dimension.End.Column; col++)
                    {
                        var cell = worksheet.Cells[row, col];
                        var value = cell.Text;
                        
                        // 处理日期格式
                        if (cell.Value != null && cell.Value.GetType() == typeof(DateTime))
                        {
                            value = ((DateTime)cell.Value).ToString("yyyy-MM-dd HH:mm:ss");
                        }
                        
                        rowData[headers[col - 1]] = value;
                    }
                    data.Add(rowData);
                }

                return Ok(new { headers, data });
            }
            catch (Exception ex)
            {
                return BadRequest($"Error processing Excel file: {ex.Message}");
            }
        }

        [HttpDelete("delete/{fileName}")]
        public IActionResult DeleteExcel(string fileName)
        {
            try
            {
                var filePath = Path.Combine(_uploadPath, fileName);
                if (!System.IO.File.Exists(filePath))
                    return NotFound("File not found");

                System.IO.File.Delete(filePath);
                return Ok(new { message = "File deleted successfully" });
            }
            catch (Exception ex)
            {
                return BadRequest($"Error deleting file: {ex.Message}");
            }
        }

        [HttpGet("latest")]
        public IActionResult GetLatestFile()
        {
            try
            {
                if (!Directory.Exists(_uploadPath))
                {
                    return Ok(new { fileName = "", uploadTime = DateTime.MinValue });
                }

                var latestFile = Directory.GetFiles(_uploadPath)
                    .Select(f => new FileInfo(f))
                    .OrderByDescending(f => f.CreationTime)
                    .FirstOrDefault();

                if (latestFile == null)
                {
                    return Ok(new { fileName = "", uploadTime = DateTime.MinValue });
                }

                return Ok(new
                {
                    fileName = latestFile.Name,
                    uploadTime = latestFile.CreationTime,
                    fileSize = latestFile.Length
                });
            }
            catch (Exception ex)
            {
                return BadRequest($"Error getting latest file: {ex.Message}");
            }
        }
    }
} 