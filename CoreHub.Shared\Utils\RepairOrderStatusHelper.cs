using MudBlazor;

namespace CoreHub.Shared.Utils
{
    /// <summary>
    /// 报修单状态帮助类 - 统一管理所有状态相关的定义和方法
    /// </summary>
    public static class RepairOrderStatusHelper
    {
        #region 状态常量定义
        
        public const int Pending = 1;          // 待处理
        public const int InProgress = 2;       // 处理中
        public const int Completed = 3;        // 已完成
        public const int Cancelled = 4;        // 已作废
        public const int Closed = 5;           // 已关闭
        public const int PendingConfirmation = 6; // 待确认（维修完成，等待报修人确认）

        #endregion

        #region 状态名称映射

        /// <summary>
        /// 获取状态名称
        /// </summary>
        /// <param name="status">状态值</param>
        /// <returns>状态名称</returns>
        public static string GetStatusName(int status)
        {
            return status switch
            {
                Pending => "待处理",
                InProgress => "处理中",
                Completed => "已完成",
                Cancelled => "已作废",
                Closed => "已关闭",
                PendingConfirmation => "待确认",
                _ => "未知"
            };
        }

        #endregion

        #region 状态颜色映射

        /// <summary>
        /// 获取状态对应的颜色
        /// </summary>
        /// <param name="status">状态值</param>
        /// <returns>MudBlazor颜色</returns>
        public static Color GetStatusColor(int status)
        {
            return status switch
            {
                Pending => Color.Warning,       // 待处理 - 橙色
                InProgress => Color.Info,       // 处理中 - 蓝色
                Completed => Color.Success,     // 已完成 - 绿色
                Cancelled => Color.Default,     // 已作废 - 灰色
                Closed => Color.Secondary,      // 已关闭 - 深灰色
                PendingConfirmation => Color.Tertiary, // 待确认 - 青色
                _ => Color.Default
            };
        }

        #endregion

        #region 状态验证方法

        /// <summary>
        /// 检查状态是否有效
        /// </summary>
        /// <param name="status">状态值</param>
        /// <returns>是否有效</returns>
        public static bool IsValidStatus(int status)
        {
            return status >= Pending && status <= PendingConfirmation && status != 7 && status != 8;
        }

        /// <summary>
        /// 检查状态是否为活跃状态（可以进行操作的状态）
        /// </summary>
        /// <param name="status">状态值</param>
        /// <returns>是否为活跃状态</returns>
        public static bool IsActiveStatus(int status)
        {
            return status == Pending || status == InProgress || status == PendingConfirmation;
        }

        /// <summary>
        /// 检查状态是否为终结状态（不可再变更的状态）
        /// </summary>
        /// <param name="status">状态值</param>
        /// <returns>是否为终结状态</returns>
        public static bool IsFinalStatus(int status)
        {
            return status == Completed || status == Cancelled || status == Closed;
        }

        /// <summary>
        /// 检查是否可以分配技术员
        /// </summary>
        /// <param name="status">状态值</param>
        /// <returns>是否可以分配</returns>
        public static bool CanAssignTechnician(int status)
        {
            return status == Pending;
        }

        /// <summary>
        /// 检查是否可以开始维修
        /// </summary>
        /// <param name="status">状态值</param>
        /// <returns>是否可以开始维修</returns>
        public static bool CanStartRepair(int status)
        {
            return status == Pending;
        }

        /// <summary>
        /// 检查是否可以完成维修（技术员完成维修工作）
        /// </summary>
        /// <param name="status">状态值</param>
        /// <returns>是否可以完成维修</returns>
        public static bool CanCompleteRepair(int status)
        {
            return status == InProgress;
        }

        /// <summary>
        /// 检查是否可以确认维修（报修人确认维修结果）
        /// </summary>
        /// <param name="status">状态值</param>
        /// <returns>是否可以确认维修</returns>
        public static bool CanConfirmRepair(int status)
        {
            return status == PendingConfirmation;
        }

        /// <summary>
        /// 检查是否可以暂停维修
        /// </summary>
        /// <param name="status">状态值</param>
        /// <returns>是否可以暂停维修</returns>
        public static bool CanPauseRepair(int status)
        {
            return status == InProgress;
        }

        /// <summary>
        /// 检查是否可以作废
        /// </summary>
        /// <param name="status">状态值</param>
        /// <returns>是否可以作废</returns>
        public static bool CanCancel(int status)
        {
            return status == Pending || status == InProgress;
        }

        /// <summary>
        /// 检查是否可以关闭
        /// </summary>
        /// <param name="status">状态值</param>
        /// <returns>是否可以关闭</returns>
        public static bool CanClose(int status)
        {
            return status == Completed;
        }

        #endregion

        #region 状态列表

        /// <summary>
        /// 获取所有状态列表
        /// </summary>
        /// <returns>状态列表</returns>
        public static List<(int Value, string Name)> GetAllStatuses()
        {
            return new List<(int Value, string Name)>
            {
                (Pending, GetStatusName(Pending)),
                (InProgress, GetStatusName(InProgress)),
                (Completed, GetStatusName(Completed)),
                (Cancelled, GetStatusName(Cancelled)),
                (Closed, GetStatusName(Closed)),
                (PendingConfirmation, GetStatusName(PendingConfirmation))
            };
        }

        /// <summary>
        /// 获取活跃状态列表
        /// </summary>
        /// <returns>活跃状态列表</returns>
        public static List<(int Value, string Name)> GetActiveStatuses()
        {
            return GetAllStatuses().Where(s => IsActiveStatus(s.Value)).ToList();
        }

        /// <summary>
        /// 获取终结状态列表
        /// </summary>
        /// <returns>终结状态列表</returns>
        public static List<(int Value, string Name)> GetFinalStatuses()
        {
            return GetAllStatuses().Where(s => IsFinalStatus(s.Value)).ToList();
        }

        #endregion
    }
}
