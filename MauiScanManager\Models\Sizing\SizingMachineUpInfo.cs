using System.Text.Json.Serialization;

namespace MauiScanManager.Models
{
    public class SizingMachineUpInfo
    {
        [JsonPropertyName("sizingMachineId")]
        public string SizingMachineId { get; set; } = string.Empty;

        [JsonPropertyName("sizingBatchSerialNo")]
        public int SizingBatchSerialNo { get; set; }

        [JsonPropertyName("wvCardNo")]
        public string WvCardNo { get; set; } = string.Empty;

        // ... 如果有其他字段也需要添加
    }
}