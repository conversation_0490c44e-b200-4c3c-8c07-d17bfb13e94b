@page "/menu-management"
@using CoreHub.Shared.Models.Database
@using CoreHub.Shared.Services
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.Extensions.Logging
@using CoreHub.Shared.Components
@using DatabaseMenuItem = CoreHub.Shared.Models.Database.MenuItem
@inject IMenuService MenuService
@inject ILogger<MenuManagement> Logger
@inject ISnackbar Snackbar
@inject IDialogService DialogService

<PageTitle>菜单管理</PageTitle>

<PermissionView RequiredPermission="MenuManagement.View">
    <ChildContent>
        <MudContainer MaxWidth="MaxWidth.ExtraExtraLarge">
            <MudPaper Elevation="2" Class="pa-4">
                <div style="display: flex; justify-content: between; align-items: center; margin-bottom: 20px;">
                    <MudText Typo="Typo.h4" Color="Color.Primary">菜单管理</MudText>
                    <MudSpacer />
                    <PermissionView RequiredPermission="MenuManagement.Create">
                        <MudButton Variant="Variant.Filled" 
                                   Color="Color.Primary" 
                                   StartIcon="@Icons.Material.Filled.Add"
                                   OnClick="ShowCreateDialog">
                            新增菜单
                        </MudButton>
                    </PermissionView>
                </div>

                @if (isLoading)
                {
                    <div style="display: flex; justify-content: center; padding: 40px;">
                        <MudProgressCircular Color="Color.Primary" Indeterminate="true" />
                    </div>
                }
                else if (menuItems?.Any() == true)
                {
                    <MudDataGrid Items="@GetFlatMenuList(menuItems)" 
                                 Hover="true" 
                                 Striped="true"
                                 Dense="true"
                                 Elevation="0">
                        <Columns>
                            <PropertyColumn Property="x => x.Code" Title="编码">
                                <CellTemplate>
                                    <div style="display: flex; align-items: center;">
                                        @if (context.Item.Level > 1)
                                        {
                                            <span style="margin-left: @((context.Item.Level - 1) * 20)px; color: #666;">└─</span>
                                        }
                                        <MudText>@context.Item.Code</MudText>
                                    </div>
                                </CellTemplate>
                            </PropertyColumn>
                            
                            <PropertyColumn Property="x => x.Name" Title="名称" />
                            
                            <PropertyColumn Property="x => x.RouteUrl" Title="路由" />
                            
                            <PropertyColumn Property="x => x.Icon" Title="图标">
                                <CellTemplate>
                                    @if (!string.IsNullOrEmpty(context.Item.Icon))
                                    {
                                        <MudIcon Icon="@GetMudIcon(context.Item.Icon)" Size="Size.Small" />
                                    }
                                </CellTemplate>
                            </PropertyColumn>
                            
                            <PropertyColumn Property="x => x.ParentId" Title="父菜单">
                                <CellTemplate>
                                    <MudText>@GetParentMenuName(context.Item.ParentId)</MudText>
                                </CellTemplate>
                            </PropertyColumn>
                            
                            <PropertyColumn Property="x => x.SortOrder" Title="排序" />
                            
                            <PropertyColumn Property="x => x.MenuType" Title="类型">
                                <CellTemplate>
                                    @if (context.Item.MenuType == 1)
                                    {
                                        <MudChip Color="Color.Primary" Size="Size.Small">菜单</MudChip>
                                    }
                                    else
                                    {
                                        <MudChip Color="Color.Secondary" Size="Size.Small">分组</MudChip>
                                    }
                                </CellTemplate>
                            </PropertyColumn>
                            
                            <PropertyColumn Property="x => x.PermissionCode" Title="权限代码" />
                            
                            <PropertyColumn Property="x => x.IsEnabled" Title="状态">
                                <CellTemplate>
                                    @if (context.Item.IsEnabled)
                                    {
                                        <MudChip Color="Color.Success" Size="Size.Small">启用</MudChip>
                                    }
                                    else
                                    {
                                        <MudChip Color="Color.Error" Size="Size.Small">禁用</MudChip>
                                    }
                                </CellTemplate>
                            </PropertyColumn>
                            
                            <TemplateColumn Title="操作" Sortable="false">
                                <CellTemplate>
                                    <div style="display: flex; gap: 8px;">
                                        <PermissionView RequiredPermission="MenuManagement.Edit">
                                            <MudIconButton Icon="@Icons.Material.Filled.Edit"
                                                          Size="Size.Small"
                                                          Color="Color.Primary"
                                                          OnClick="() => ShowEditDialog(context.Item)" />
                                        </PermissionView>
                                        
                                        @if (!context.Item.IsSystem)
                                        {
                                            <PermissionView RequiredPermission="MenuManagement.Delete">
                                                <MudIconButton Icon="@Icons.Material.Filled.Delete"
                                                              Size="Size.Small"
                                                              Color="Color.Error"
                                                              OnClick="() => DeleteMenuItem(context.Item)" />
                                            </PermissionView>
                                        }
                                    </div>
                                </CellTemplate>
                            </TemplateColumn>
                        </Columns>
                    </MudDataGrid>
                }
                else
                {
                    <div style="display: flex; flex-direction: column; align-items: center; padding: 60px 20px;">
                        <MudIcon Icon="@Icons.Material.Filled.FolderOpen" 
                                 Size="Size.Large" 
                                 Color="Color.Default" 
                                 Style="font-size: 4rem; margin-bottom: 16px;" />
                        <MudText Typo="Typo.h6" Color="Color.Default" Class="mb-4">暂无菜单数据</MudText>
                        <PermissionView RequiredPermission="MenuManagement.Create">
                            <MudButton Variant="Variant.Filled" 
                                       Color="Color.Primary" 
                                       StartIcon="@Icons.Material.Filled.AutoFixHigh"
                                       OnClick="InitializeDefaultMenus">
                                初始化默认菜单
                            </MudButton>
                        </PermissionView>
                    </div>
                }
            </MudPaper>
        </MudContainer>
    </ChildContent>
    
    <NotAuthorized>
        <MudContainer MaxWidth="MaxWidth.Medium">
            <MudPaper Elevation="2" Class="pa-8" Style="text-align: center;">
                <MudIcon Icon="@Icons.Material.Filled.Lock" 
                         Size="Size.Large" 
                         Color="Color.Error" 
                         Style="font-size: 4rem; margin-bottom: 16px;" />
                <MudText Typo="Typo.h4" Color="Color.Error" Class="mb-4">权限不足</MudText>
                <MudText Typo="Typo.body1" Color="Color.Default">您没有权限访问菜单管理页面</MudText>
            </MudPaper>
        </MudContainer>
    </NotAuthorized>
</PermissionView>

<!-- 菜单编辑对话框 -->
@if (showDialog)
{
    <MudOverlay @bind-Visible="showDialog" DarkBackground="true" Absolute="false">
        <MudPaper Class="pa-6" Style="width: 800px; max-width: 90vw; max-height: 90vh; overflow-y: auto; margin: 20px auto; position: relative;">
            <div style="display: flex; align-items: center; margin-bottom: 20px;">
                <MudIcon Icon="@(isEditing ? Icons.Material.Filled.Edit : Icons.Material.Filled.Add)" Class="mr-3" />
                <MudText Typo="Typo.h5">@(isEditing ? "编辑菜单" : "新增菜单")</MudText>
                <MudSpacer />
                <MudIconButton Icon="@Icons.Material.Filled.Close" OnClick="CloseDialog" />
            </div>

            <EditForm Model="currentMenuItem" OnValidSubmit="HandleValidSubmit">
                <DataAnnotationsValidator />
                
                <MudGrid>
                    <MudItem xs="12" sm="6">
                        <MudTextField @bind-Value="currentMenuItem.Code"
                                      Label="菜单编码"
                                      Required="true"
                                      RequiredError="请输入菜单编码"
                                      Variant="Variant.Outlined" />
                    </MudItem>
                    
                    <MudItem xs="12" sm="6">
                        <MudTextField @bind-Value="currentMenuItem.Name"
                                      Label="菜单名称"
                                      Required="true"
                                      RequiredError="请输入菜单名称"
                                      Variant="Variant.Outlined" />
                    </MudItem>
                    
                    <MudItem xs="12" sm="6">
                        <MudTextField @bind-Value="currentMenuItem.RouteUrl"
                                      Label="路由地址"
                                      Placeholder="请输入路由地址"
                                      Variant="Variant.Outlined" />
                    </MudItem>
                    
                    <MudItem xs="12" sm="6">
                        <IconSelector SelectedIcon="@currentMenuItem.Icon"
                                      SelectedIconChanged="@((string? value) => currentMenuItem.Icon = value)"
                                      Label="图标"
                                      Placeholder="搜索或选择图标"
                                      Variant="Variant.Outlined" />
                    </MudItem>
                    
                    <MudItem xs="12" sm="6">
                        <MudSelect T="int?" @bind-Value="currentMenuItem.ParentId"
                                   Label="父菜单"
                                   Variant="Variant.Outlined"
                                   Clearable="true">
                            <MudSelectItem T="int?" Value="@((int?)null)">-- 顶级菜单 --</MudSelectItem>
                            @foreach (var item in GetParentMenuOptions())
                            {
                                <MudSelectItem T="int?" Value="@(item.Id)">@item.Name</MudSelectItem>
                            }
                        </MudSelect>
                    </MudItem>
                    
                    <MudItem xs="12" sm="6">
                        <MudNumericField T="int" @bind-Value="currentMenuItem.SortOrder"
                                         Label="排序号"
                                         Variant="Variant.Outlined" />
                    </MudItem>
                    
                    <MudItem xs="12" sm="6">
                        <MudSelect T="int" @bind-Value="currentMenuItem.MenuType"
                                   Label="菜单类型"
                                   Variant="Variant.Outlined">
                            <MudSelectItem T="int" Value="1">菜单项</MudSelectItem>
                            <MudSelectItem T="int" Value="2">分组标题</MudSelectItem>
                        </MudSelect>
                    </MudItem>
                    
                    <MudItem xs="12" sm="6">
                        <MudTextField @bind-Value="currentMenuItem.PermissionCode"
                                      Label="权限代码"
                                      Placeholder="如: UserManagement.View"
                                      Variant="Variant.Outlined" />
                    </MudItem>
                    
                    <MudItem xs="12">
                        <MudTextField @bind-Value="currentMenuItem.Description"
                                      Label="描述"
                                      Lines="3"
                                      Placeholder="请输入菜单描述"
                                      Variant="Variant.Outlined" />
                    </MudItem>
                    
                    <MudItem xs="12" sm="4">
                        <MudCheckBox @bind-Value="currentMenuItem.IsEnabled"
                                     Label="启用菜单"
                                     Color="Color.Primary" />
                    </MudItem>
                    
                    <MudItem xs="12" sm="4">
                        <MudCheckBox @bind-Value="currentMenuItem.IsPublic"
                                     Label="公开菜单"
                                     Color="Color.Primary" />
                    </MudItem>
                    
                    <MudItem xs="12" sm="4">
                        <MudCheckBox @bind-Value="currentMenuItem.IsSystem"
                                     Label="系统菜单"
                                     Color="Color.Primary" />
                    </MudItem>
                </MudGrid>
                
                <ValidationSummary />
                
                <div style="display: flex; justify-content: flex-end; gap: 12px; margin-top: 24px;">
                    <MudButton OnClick="CloseDialog" Color="Color.Default">取消</MudButton>
                    <MudButton ButtonType="ButtonType.Submit" 
                               Color="Color.Primary" 
                               Variant="Variant.Filled"
                               Disabled="@isSaving">
                        @if (isSaving)
                        {
                            <MudProgressCircular Class="ms-n1" Size="Size.Small" Indeterminate="true" />
                            <MudText Class="ms-2">保存中...</MudText>
                        }
                        else
                        {
                            <MudText>保存</MudText>
                        }
                    </MudButton>
                </div>
            </EditForm>
        </MudPaper>
    </MudOverlay>
}

@code {
    private List<DatabaseMenuItem>? menuItems;
    private List<DatabaseMenuItem> allMenuItems = new();
    private bool isLoading = true;
    private bool showDialog = false;
    private bool isEditing = false;
    private bool isSaving = false;
    private DatabaseMenuItem currentMenuItem = new DatabaseMenuItem();

    protected override async Task OnInitializedAsync()
    {
        await LoadMenuItems();
    }

    private async Task LoadMenuItems()
    {
        try
        {
            isLoading = true;
            StateHasChanged();

            menuItems = await MenuService.GetAllMenusAsync();
            allMenuItems = GetFlatMenuList(menuItems);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "加载菜单数据失败");
            Snackbar.Add("加载菜单数据失败：" + ex.Message, Severity.Error);
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private List<DatabaseMenuItem> GetFlatMenuList(List<DatabaseMenuItem> menus)
    {
        var result = new List<DatabaseMenuItem>();
        foreach (var menu in menus.OrderBy(m => m.SortOrder))
        {
            result.Add(menu);
            if (menu.Children?.Any() == true)
            {
                result.AddRange(GetFlatMenuList(menu.Children));
            }
        }
        return result;
    }

    private string GetParentMenuName(int? parentId)
    {
        if (!parentId.HasValue) return "-";
        var parent = allMenuItems.FirstOrDefault(m => m.Id == parentId.Value);
        return parent?.Name ?? "-";
    }

    private List<DatabaseMenuItem> GetParentMenuOptions()
    {
        return allMenuItems.Where(m => m.MenuType == 2 || (m.MenuType == 1 && m.Level == 1)).ToList();
    }

    private string GetMudIcon(string? iconPath)
    {
        if (string.IsNullOrEmpty(iconPath))
            return Icons.Material.Filled.Circle;
            
        // 如果是MudBlazor图标名称常量，需要转换为实际值
        if (iconPath.StartsWith("Icons.Material."))
        {
            return ConvertIconConstantToValue(iconPath);
        }
        
        // 如果是SVG路径（包含M字符且长度较长），直接返回
        if (iconPath.Contains("M") && iconPath.Length > 10)
        {
            return iconPath;
        }
        
        // 兼容旧格式的图标映射
        return iconPath switch
        {
            "home" => Icons.Material.Filled.Home,
            "add" => Icons.Material.Filled.Add,
            "qr_code_scanner" => Icons.Material.Filled.QrCodeScanner,
            "camera_alt" => Icons.Material.Filled.Camera,
            "wb_sunny" => Icons.Material.Filled.WbSunny,
            "build" => Icons.Material.Filled.Build,
            "settings" => Icons.Material.Filled.Settings,
            "people" => Icons.Material.Filled.People,
            "security" => Icons.Material.Filled.Security,
            "key" => Icons.Material.Filled.Key,
            "menu" => Icons.Material.Filled.Menu,
            "person_add" => Icons.Material.Filled.PersonAdd,
            "storage" => Icons.Material.Filled.Storage,
            "shield" => Icons.Material.Filled.Shield,
            "bug_report" => Icons.Material.Filled.BugReport,
            "login" => Icons.Material.Filled.Login,
            "code" => Icons.Material.Filled.Code,
            "palette" => Icons.Material.Filled.Palette,
            "account_tree" => Icons.Material.Filled.AccountTree,
            "bi bi-house-door-fill-nav-menu" or "bi bi-house-door-fill" => Icons.Material.Filled.Home,
            "bi bi-plus-square-fill-nav-menu" or "bi bi-plus-square-fill" => Icons.Material.Filled.Add,
            "bi bi-list-nested-nav-menu" or "bi bi-list-nested" => Icons.Material.Filled.List,
            "bi bi-camera-fill-nav-menu" or "bi bi-camera-fill" => Icons.Material.Filled.QrCodeScanner,
            "bi bi-tools-nav-menu" or "bi bi-tools" => Icons.Material.Filled.Build,
            "bi bi-gear-nav-menu" or "bi bi-gear" => Icons.Material.Filled.Settings,
            "bi bi-people-nav-menu" or "bi bi-people" => Icons.Material.Filled.People,
            "bi bi-person-badge-nav-menu" or "bi bi-person-badge" => Icons.Material.Filled.Security,
            "bi bi-key-nav-menu" or "bi bi-key" => Icons.Material.Filled.Key,
            "bi bi-list-ul" => Icons.Material.Filled.Menu,
            "bi bi-person-gear-nav-menu" or "bi bi-person-gear" => Icons.Material.Filled.PersonAdd,
            "bi bi-code-slash" => Icons.Material.Filled.Code,
            "bi bi-palette" => Icons.Material.Filled.Palette,
            "bi bi-diagram-2" => Icons.Material.Filled.AccountTree,
            "bi bi-bug" => Icons.Material.Filled.BugReport,
            "bi bi-wrench" => Icons.Material.Filled.Build,
            "bi bi-box-arrow-in-right-nav-menu" => Icons.Material.Filled.Login,
            _ => Icons.Material.Filled.Circle
        };
    }

    private string ConvertIconConstantToValue(string iconConstant)
    {
        return iconConstant switch
        {
            "Icons.Material.Filled.Home" => Icons.Material.Filled.Home,
            "Icons.Material.Filled.Add" => Icons.Material.Filled.Add,
            "Icons.Material.Filled.QrCodeScanner" => Icons.Material.Filled.QrCodeScanner,
            "Icons.Material.Filled.Camera" => Icons.Material.Filled.Camera,
            "Icons.Material.Filled.WbSunny" => Icons.Material.Filled.WbSunny,
            "Icons.Material.Filled.Build" => Icons.Material.Filled.Build,
            "Icons.Material.Filled.Settings" => Icons.Material.Filled.Settings,
            "Icons.Material.Filled.People" => Icons.Material.Filled.People,
            "Icons.Material.Filled.Security" => Icons.Material.Filled.Security,
            "Icons.Material.Filled.Key" => Icons.Material.Filled.Key,
            "Icons.Material.Filled.Menu" => Icons.Material.Filled.Menu,
            "Icons.Material.Filled.PersonAdd" => Icons.Material.Filled.PersonAdd,
            "Icons.Material.Filled.Storage" => Icons.Material.Filled.Storage,
            "Icons.Material.Filled.Shield" => Icons.Material.Filled.Shield,
            "Icons.Material.Filled.BugReport" => Icons.Material.Filled.BugReport,
            "Icons.Material.Filled.Login" => Icons.Material.Filled.Login,
            "Icons.Material.Filled.Logout" => Icons.Material.Filled.Logout,
            "Icons.Material.Filled.Code" => Icons.Material.Filled.Code,
            "Icons.Material.Filled.Palette" => Icons.Material.Filled.Palette,
            "Icons.Material.Filled.AccountTree" => Icons.Material.Filled.AccountTree,
            "Icons.Material.Filled.Person" => Icons.Material.Filled.Person,
            "Icons.Material.Filled.Search" => Icons.Material.Filled.Search,
            "Icons.Material.Filled.Edit" => Icons.Material.Filled.Edit,
            "Icons.Material.Filled.Delete" => Icons.Material.Filled.Delete,
            "Icons.Material.Filled.Save" => Icons.Material.Filled.Save,
            "Icons.Material.Filled.Close" => Icons.Material.Filled.Close,
            "Icons.Material.Filled.Check" => Icons.Material.Filled.Check,
            "Icons.Material.Filled.Circle" => Icons.Material.Filled.Circle,
            "Icons.Material.Filled.ArrowBack" => Icons.Material.Filled.ArrowBack,
            "Icons.Material.Filled.ArrowForward" => Icons.Material.Filled.ArrowForward,
            "Icons.Material.Filled.KeyboardArrowUp" => Icons.Material.Filled.KeyboardArrowUp,
            "Icons.Material.Filled.KeyboardArrowDown" => Icons.Material.Filled.KeyboardArrowDown,
            "Icons.Material.Filled.ExpandMore" => Icons.Material.Filled.ExpandMore,
            "Icons.Material.Filled.ExpandLess" => Icons.Material.Filled.ExpandLess,
            "Icons.Material.Filled.Refresh" => Icons.Material.Filled.Refresh,
            "Icons.Material.Filled.AdminPanelSettings" => Icons.Material.Filled.AdminPanelSettings,
            "Icons.Material.Filled.VerifiedUser" => Icons.Material.Filled.VerifiedUser,
            "Icons.Material.Filled.Tune" => Icons.Material.Filled.Tune,
            "Icons.Material.Filled.ContentCopy" => Icons.Material.Filled.ContentCopy,
            "Icons.Material.Filled.ContentPaste" => Icons.Material.Filled.ContentPaste,
            "Icons.Material.Filled.ContentCut" => Icons.Material.Filled.ContentCut,
            "Icons.Material.Filled.Download" => Icons.Material.Filled.Download,
            "Icons.Material.Filled.Upload" => Icons.Material.Filled.Upload,
            "Icons.Material.Filled.Print" => Icons.Material.Filled.Print,
            "Icons.Material.Filled.Share" => Icons.Material.Filled.Share,
            "Icons.Material.Filled.Favorite" => Icons.Material.Filled.Favorite,
            "Icons.Material.Filled.Star" => Icons.Material.Filled.Star,
            "Icons.Material.Filled.ThumbUp" => Icons.Material.Filled.ThumbUp,
            "Icons.Material.Filled.Email" => Icons.Material.Filled.Email,
            "Icons.Material.Filled.Phone" => Icons.Material.Filled.Phone,
            "Icons.Material.Filled.Message" => Icons.Material.Filled.Message,
            "Icons.Material.Filled.Chat" => Icons.Material.Filled.Chat,
            "Icons.Material.Filled.Notifications" => Icons.Material.Filled.Notifications,
            "Icons.Material.Filled.Send" => Icons.Material.Filled.Send,
            "Icons.Material.Filled.Comment" => Icons.Material.Filled.Comment,
            "Icons.Material.Filled.Videocam" => Icons.Material.Filled.Videocam,
            "Icons.Material.Filled.Mouse" => Icons.Material.Filled.Mouse,
            "Icons.Material.Filled.Keyboard" => Icons.Material.Filled.Keyboard,
            "Icons.Material.Filled.Monitor" => Icons.Material.Filled.Monitor,
            "Icons.Material.Filled.PhoneAndroid" => Icons.Material.Filled.PhoneAndroid,
            "Icons.Material.Filled.Tablet" => Icons.Material.Filled.Tablet,
            "Icons.Material.Filled.Computer" => Icons.Material.Filled.Computer,
            "Icons.Material.Filled.Router" => Icons.Material.Filled.Router,
            "Icons.Material.Filled.Construction" => Icons.Material.Filled.Construction,
            "Icons.Material.Filled.InsertDriveFile" => Icons.Material.Filled.InsertDriveFile,
            "Icons.Material.Filled.Folder" => Icons.Material.Filled.Folder,
            "Icons.Material.Filled.Image" => Icons.Material.Filled.Image,
            "Icons.Material.Filled.VideoFile" => Icons.Material.Filled.VideoFile,
            "Icons.Material.Filled.AudioFile" => Icons.Material.Filled.AudioFile,
            "Icons.Material.Filled.CalendarToday" => Icons.Material.Filled.CalendarToday,
            "Icons.Material.Filled.AccessTime" => Icons.Material.Filled.AccessTime,
            "Icons.Material.Filled.LocationOn" => Icons.Material.Filled.LocationOn,
            "Icons.Material.Filled.Help" => Icons.Material.Filled.Help,
            "Icons.Material.Filled.Info" => Icons.Material.Filled.Info,
            "Icons.Material.Filled.Warning" => Icons.Material.Filled.Warning,
            "Icons.Material.Filled.Error" => Icons.Material.Filled.Error,
            "Icons.Material.Filled.CheckCircle" => Icons.Material.Filled.CheckCircle,
            "Icons.Material.Filled.Analytics" => Icons.Material.Filled.Analytics,
            "Icons.Material.Filled.Dashboard" => Icons.Material.Filled.Dashboard,
            "Icons.Material.Filled.Assessment" => Icons.Material.Filled.Assessment,
            _ => Icons.Material.Filled.Circle // 默认图标
        };
    }

    private void ShowCreateDialog()
    {
        isEditing = false;
        currentMenuItem = new DatabaseMenuItem
        {
            IsEnabled = true,
            MenuType = 1,
            SortOrder = (allMenuItems.Max(m => (int?)m.SortOrder) ?? 0) + 10,
            Level = 1
        };
        showDialog = true;
        StateHasChanged();
    }

    private void ShowEditDialog(DatabaseMenuItem item)
    {
        isEditing = true;
        currentMenuItem = new DatabaseMenuItem
        {
            Id = item.Id,
            Code = item.Code,
            Name = item.Name,
            Description = item.Description,
            RouteUrl = item.RouteUrl,
            Icon = item.Icon,
            ParentId = item.ParentId,
            Level = item.Level,
            SortOrder = item.SortOrder,
            PermissionCode = item.PermissionCode,
            IsEnabled = item.IsEnabled,
            IsPublic = item.IsPublic,
            IsSystem = item.IsSystem,
            MenuType = item.MenuType
        };
        showDialog = true;
        StateHasChanged();
    }

    private void CloseDialog()
    {
        showDialog = false;
        currentMenuItem = new DatabaseMenuItem();
        isEditing = false;
        isSaving = false;
        StateHasChanged();
    }

    private async Task HandleValidSubmit()
    {
        await SaveMenuItem();
    }

    private async Task SaveMenuItem()
    {
        try
        {
            isSaving = true;
            StateHasChanged();

            // 设置父菜单级别
            if (currentMenuItem.ParentId.HasValue)
            {
                var parent = allMenuItems.FirstOrDefault(m => m.Id == currentMenuItem.ParentId.Value);
                if (parent != null)
                {
                    currentMenuItem.Level = parent.Level + 1;
                }
            }
            else
            {
                currentMenuItem.Level = 1;
            }

            (bool isSuccess, string errorMessage) result;

            if (isEditing)
            {
                result = await MenuService.UpdateMenuAsync(currentMenuItem);
            }
            else
            {
                result = await MenuService.CreateMenuAsync(currentMenuItem);
            }

            if (result.isSuccess)
            {
                Snackbar.Add(isEditing ? "菜单更新成功！" : "菜单创建成功！", Severity.Success);
                CloseDialog();
                await LoadMenuItems();
            }
            else
            {
                Snackbar.Add("操作失败：" + result.errorMessage, Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "保存菜单失败");
            Snackbar.Add("保存菜单失败：" + ex.Message, Severity.Error);
        }
        finally
        {
            isSaving = false;
            StateHasChanged();
        }
    }

    private async Task DeleteMenuItem(DatabaseMenuItem item)
    {
        var result = await DialogService.ShowMessageBox(
            "确认删除",
            $"确定要删除菜单 '{item.Name}' 吗？",
            yesText: "删除", cancelText: "取消");

        if (result == true)
        {
            try
            {
                var deleteResult = await MenuService.DeleteMenuAsync(item.Id);
                if (deleteResult.IsSuccess)
                {
                    Snackbar.Add("菜单删除成功！", Severity.Success);
                    await LoadMenuItems();
                }
                else
                {
                    Snackbar.Add("删除失败：" + deleteResult.ErrorMessage, Severity.Error);
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "删除菜单失败");
                Snackbar.Add("删除菜单失败：" + ex.Message, Severity.Error);
            }
        }
    }

    private async Task InitializeDefaultMenus()
    {
        var result = await DialogService.ShowMessageBox(
            "确认初始化",
            "确定要初始化默认菜单数据吗？",
            yesText: "初始化", cancelText: "取消");

        if (result == true)
        {
            try
            {
                isLoading = true;
                StateHasChanged();

                var initResult = await MenuService.InitializeDefaultMenusAsync();
                if (initResult.IsSuccess)
                {
                    Snackbar.Add("默认菜单初始化成功！", Severity.Success);
                    await LoadMenuItems();
                }
                else
                {
                    Snackbar.Add("初始化失败：" + initResult.ErrorMessage, Severity.Error);
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "初始化默认菜单失败");
                Snackbar.Add("初始化失败：" + ex.Message, Severity.Error);
            }
            finally
            {
                isLoading = false;
                StateHasChanged();
            }
        }
    }
} 