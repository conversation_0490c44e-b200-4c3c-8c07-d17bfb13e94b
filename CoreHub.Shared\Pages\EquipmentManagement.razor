@page "/equipment-management"
@using CoreHub.Shared.Models.Database
@using CoreHub.Shared.Services
@inject IEquipmentService EquipmentService
@inject IDepartmentService DepartmentService
@inject IEquipmentModelService EquipmentModelService
@inject ILocationService LocationService
@inject ISnackbar Snackbar
@inject IDialogService DialogService

<PageTitle>设备管理</PageTitle>

<MudContainer MaxWidth="MaxWidth.ExtraLarge" Class="mt-4">
    <MudPaper Class="pa-4">
        <MudGrid>
            <MudItem xs="12">
                <MudText Typo="Typo.h4" Class="mb-4">
                    <MudIcon Icon="@Icons.Material.Filled.PrecisionManufacturing" Class="mr-2" />
                    设备管理
                </MudText>
            </MudItem>

            <!-- 统计卡片 -->
            <MudItem xs="12" Class="mb-4">
                <MudGrid>
                    <MudItem xs="12" sm="6" md="3">
                        <MudCard>
                            <MudCardContent>
                                <MudStack Row Justify="Justify.SpaceBetween" AlignItems="AlignItems.Center">
                                    <div>
                                        <MudText Typo="Typo.body2" Color="Color.Secondary">总设备数</MudText>
                                        <MudText Typo="Typo.h5">@statistics.TotalCount</MudText>
                                    </div>
                                    <MudIcon Icon="@Icons.Material.Filled.Devices" Color="Color.Primary" Size="Size.Large" />
                                </MudStack>
                            </MudCardContent>
                        </MudCard>
                    </MudItem>
                    <MudItem xs="12" sm="6" md="3">
                        <MudCard>
                            <MudCardContent>
                                <MudStack Row Justify="Justify.SpaceBetween" AlignItems="AlignItems.Center">
                                    <div>
                                        <MudText Typo="Typo.body2" Color="Color.Secondary">正常运行</MudText>
                                        <MudText Typo="Typo.h5" Color="Color.Success">@statistics.NormalCount</MudText>
                                    </div>
                                    <MudIcon Icon="@Icons.Material.Filled.CheckCircle" Color="Color.Success" Size="Size.Large" />
                                </MudStack>
                            </MudCardContent>
                        </MudCard>
                    </MudItem>
                    <MudItem xs="12" sm="6" md="3">
                        <MudCard>
                            <MudCardContent>
                                <MudStack Row Justify="Justify.SpaceBetween" AlignItems="AlignItems.Center">
                                    <div>
                                        <MudText Typo="Typo.body2" Color="Color.Secondary">维修中</MudText>
                                        <MudText Typo="Typo.h5" Color="Color.Warning">@statistics.MaintenanceCount</MudText>
                                    </div>
                                    <MudIcon Icon="@Icons.Material.Filled.Build" Color="Color.Warning" Size="Size.Large" />
                                </MudStack>
                            </MudCardContent>
                        </MudCard>
                    </MudItem>
                    <MudItem xs="12" sm="6" md="3">
                        <MudCard>
                            <MudCardContent>
                                <MudStack Row Justify="Justify.SpaceBetween" AlignItems="AlignItems.Center">
                                    <div>
                                        <MudText Typo="Typo.body2" Color="Color.Secondary">保修到期</MudText>
                                        <MudText Typo="Typo.h5" Color="Color.Error">@statistics.WarrantyExpiredCount</MudText>
                                    </div>
                                    <MudIcon Icon="@Icons.Material.Filled.Warning" Color="Color.Error" Size="Size.Large" />
                                </MudStack>
                            </MudCardContent>
                        </MudCard>
                    </MudItem>
                </MudGrid>
            </MudItem>

            <!-- 搜索和过滤 -->
            <MudItem xs="12">
                <MudExpansionPanels>
                    <MudExpansionPanel Text="高级搜索">
                        <MudGrid>
                            <MudItem xs="12" md="4">
                                <MudTextField @bind-Value="searchDto.SearchText" 
                                            Label="搜索关键词" 
                                            Placeholder="设备名称、编码、序列号..."
                                            Adornment="Adornment.Start" 
                                            AdornmentIcon="@Icons.Material.Filled.Search" />
                            </MudItem>
                            <MudItem xs="12" md="4">
                                <MudSelect T="int?" @bind-Value="searchDto.DepartmentId"
                                         Label="所属部门"
                                         Clearable="true">
                                    @foreach (var dept in departments)
                                    {
                                        <MudSelectItem T="int?" Value="@(dept.Id)">@dept.Name</MudSelectItem>
                                    }
                                </MudSelect>
                            </MudItem>
                            <MudItem xs="12" md="4">
                                <MudSelect T="int?" @bind-Value="searchDto.ModelId"
                                         Label="设备型号"
                                         Clearable="true">
                                    @foreach (var model in equipmentModels)
                                    {
                                        <MudSelectItem T="int?" Value="@(model.Id)">@model.Name</MudSelectItem>
                                    }
                                </MudSelect>
                            </MudItem>
                            <MudItem xs="12" md="4">
                                <MudSelect T="int?" @bind-Value="searchDto.LocationId"
                                         Label="所在位置"
                                         Clearable="true">
                                    @foreach (var location in locations)
                                    {
                                        <MudSelectItem T="int?" Value="@(location.Id)">@location.Name</MudSelectItem>
                                    }
                                </MudSelect>
                            </MudItem>
                            <MudItem xs="12" md="4">
                                <MudSelect T="int?" @bind-Value="searchDto.Status"
                                         Label="设备状态"
                                         Clearable="true">
                                    <MudSelectItem T="int?" Value="1">正常</MudSelectItem>
                                    <MudSelectItem T="int?" Value="2">维修中</MudSelectItem>
                                    <MudSelectItem T="int?" Value="3">停用</MudSelectItem>
                                    <MudSelectItem T="int?" Value="4">报废</MudSelectItem>
                                </MudSelect>
                            </MudItem>
                            <MudItem xs="12" md="4">
                                <MudStack Row Spacing="2">
                                    <MudButton Variant="Variant.Filled" 
                                             Color="Color.Primary" 
                                             StartIcon="@Icons.Material.Filled.Search"
                                             OnClick="SearchEquipment">
                                        搜索
                                    </MudButton>
                                    <MudButton Variant="Variant.Outlined" 
                                             StartIcon="@Icons.Material.Filled.Clear"
                                             OnClick="ClearSearch">
                                        清空
                                    </MudButton>
                                </MudStack>
                            </MudItem>
                        </MudGrid>
                    </MudExpansionPanel>
                </MudExpansionPanels>
            </MudItem>

            <!-- 工具栏 -->
            <MudItem xs="12">
                <MudStack Row Justify="Justify.SpaceBetween" AlignItems="AlignItems.Center" Class="mb-4">
                    <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                        <MudButton Variant="Variant.Outlined" 
                                 StartIcon="@Icons.Material.Filled.Refresh"
                                 OnClick="LoadEquipment">
                            刷新
                        </MudButton>
                        <MudButton Variant="Variant.Outlined" 
                                 StartIcon="@Icons.Material.Filled.FileDownload"
                                 OnClick="ExportEquipment">
                            导出
                        </MudButton>
                    </MudStack>
                    <MudButton Variant="Variant.Filled" 
                             Color="Color.Primary" 
                             StartIcon="@Icons.Material.Filled.Add"
                             OnClick="OpenCreateDialog">
                        新增设备
                    </MudButton>
                </MudStack>
            </MudItem>

            <!-- 数据表格 -->
            <MudItem xs="12">
                <MudDataGrid T="EquipmentDetailDto" 
                           Items="@filteredEquipment" 
                           Loading="@loading"
                           Hover="true" 
                           Striped="true"
                           Dense="true"
                           FixedHeader="true"
                           Height="600px">
                    <Columns>
                        <PropertyColumn Property="x => x.Code" Title="设备编码" />
                        <PropertyColumn Property="x => x.Name" Title="设备名称" />
                        <PropertyColumn Property="x => x.DepartmentName" Title="所属部门" />
                        <PropertyColumn Property="x => x.ModelName" Title="设备型号" />
                        <PropertyColumn Property="x => x.LocationName" Title="所在位置" />
                        <PropertyColumn Property="x => x.SerialNumber" Title="序列号" />
                        <TemplateColumn Title="设备状态" Sortable="false">
                            <CellTemplate>
                                <MudChip Color="@GetStatusColor(context.Item.Status)" 
                                       Size="Size.Small">
                                    @context.Item.StatusName
                                </MudChip>
                            </CellTemplate>
                        </TemplateColumn>
                        <PropertyColumn Property="x => x.RepairOrderCount" Title="报修次数" />
                        <TemplateColumn Title="启用状态" Sortable="false">
                            <CellTemplate>
                                <MudChip Color="@(context.Item.IsEnabled ? Color.Success : Color.Default)" 
                                       Size="Size.Small">
                                    @(context.Item.IsEnabled ? "启用" : "禁用")
                                </MudChip>
                            </CellTemplate>
                        </TemplateColumn>
                        <PropertyColumn Property="x => x.CreatedAt" Title="创建时间" Format="yyyy-MM-dd" />
                        <TemplateColumn Title="操作" Sortable="false" Filterable="false">
                            <CellTemplate>
                                <MudStack Row Spacing="1">
                                    <MudIconButton Icon="@Icons.Material.Filled.Visibility" 
                                                 Color="Color.Info" 
                                                 Size="Size.Small"
                                                 OnClick="() => ViewEquipmentDetail(context.Item)"
                                                 Title="查看详情" />
                                    <MudIconButton Icon="@Icons.Material.Filled.Edit" 
                                                 Color="Color.Primary" 
                                                 Size="Size.Small"
                                                 OnClick="() => OpenEditDialog(context.Item)"
                                                 Title="编辑" />
                                    <MudIconButton Icon="@(context.Item.IsEnabled ? Icons.Material.Filled.Block : Icons.Material.Filled.CheckCircle)" 
                                                 Color="@(context.Item.IsEnabled ? Color.Warning : Color.Success)" 
                                                 Size="Size.Small"
                                                 OnClick="() => ToggleStatus(context.Item)"
                                                 Title="@(context.Item.IsEnabled ? "禁用" : "启用")" />
                                    <MudIconButton Icon="@Icons.Material.Filled.Delete" 
                                                 Color="Color.Error" 
                                                 Size="Size.Small"
                                                 OnClick="() => DeleteEquipment(context.Item)"
                                                 Title="删除" />
                                </MudStack>
                            </CellTemplate>
                        </TemplateColumn>
                    </Columns>
                    <NoRecordsContent>
                        <MudText Typo="Typo.body1" Align="Align.Center" Class="pa-4">
                            暂无数据
                        </MudText>
                    </NoRecordsContent>
                    <LoadingContent>
                        <MudProgressCircular Color="Color.Primary" Indeterminate="true" />
                    </LoadingContent>
                </MudDataGrid>
            </MudItem>
        </MudGrid>
    </MudPaper>
</MudContainer>

@code {
    private List<EquipmentDetailDto> equipment = new();
    private List<EquipmentDetailDto> filteredEquipment = new();
    private List<Department> departments = new();
    private List<EquipmentModel> equipmentModels = new();
    private List<Location> locations = new();
    private EquipmentStatisticsDto statistics = new();
    private EquipmentSearchDto searchDto = new();
    private bool loading = false;

    protected override async Task OnInitializedAsync()
    {
        await LoadBasicData();
        await LoadEquipment();
        await LoadStatistics();
    }

    private async Task LoadBasicData()
    {
        try
        {
            var departmentTask = DepartmentService.GetEnabledDepartmentsAsync();
            var equipmentModelTask = EquipmentModelService.GetEnabledEquipmentModelsAsync();
            var locationTask = LocationService.GetEnabledLocationsAsync();

            await Task.WhenAll(departmentTask, equipmentModelTask, locationTask);

            departments = await departmentTask;
            equipmentModels = await equipmentModelTask;
            locations = await locationTask;
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载基础数据失败: {ex.Message}", Severity.Error);
        }
    }

    private async Task LoadEquipment()
    {
        loading = true;
        try
        {
            equipment = await EquipmentService.GetEquipmentDetailsAsync();
            filteredEquipment = equipment.ToList();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载设备数据失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            loading = false;
        }
    }

    private async Task LoadStatistics()
    {
        try
        {
            statistics = await EquipmentService.GetEquipmentStatisticsAsync();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载统计数据失败: {ex.Message}", Severity.Error);
        }
    }

    private async Task SearchEquipment()
    {
        loading = true;
        try
        {
            filteredEquipment = await EquipmentService.SearchEquipmentAsync(searchDto);
        }
        catch (Exception ex)
        {
            Snackbar.Add($"搜索设备失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            loading = false;
        }
    }

    private async Task ClearSearch()
    {
        searchDto = new EquipmentSearchDto();
        await LoadEquipment();
    }

    private Color GetStatusColor(int status)
    {
        return status switch
        {
            1 => Color.Success,  // 正常
            2 => Color.Warning,  // 维修中
            3 => Color.Default,  // 停用
            4 => Color.Error,    // 报废
            _ => Color.Default
        };
    }

    private async Task OpenCreateDialog()
    {
        var parameters = new DialogParameters<EquipmentEditDialog>
        {
            { x => x.Equipment, new Equipment() },
            { x => x.IsEdit, false }
        };

        var dialog = await DialogService.ShowAsync<EquipmentEditDialog>("新增设备", parameters);
        var result = await dialog.Result;

        if (!result.Canceled)
        {
            await LoadEquipment();
            await LoadStatistics();
        }
    }

    private async Task OpenEditDialog(EquipmentDetailDto equipmentDetail)
    {
        // 调试信息：表格中的数据
        Console.WriteLine($"[EquipmentManagement] 表格中的设备数据:");
        Console.WriteLine($"[EquipmentManagement] - 设备ID: {equipmentDetail.Id}");
        Console.WriteLine($"[EquipmentManagement] - 设备编码: {equipmentDetail.Code}");
        Console.WriteLine($"[EquipmentManagement] - 设备名称: {equipmentDetail.Name}");
        Console.WriteLine($"[EquipmentManagement] - 设备状态 (Status): {equipmentDetail.Status}");
        Console.WriteLine($"[EquipmentManagement] - 启用状态 (IsEnabled): {equipmentDetail.IsEnabled}");

        // 获取完整的设备对象
        var equipment = await EquipmentService.GetEquipmentByIdAsync(equipmentDetail.Id);
        if (equipment == null)
        {
            Snackbar.Add("设备不存在", Severity.Error);
            return;
        }

        // 调试信息：数据库中的数据
        Console.WriteLine($"[EquipmentManagement] 数据库中的设备数据:");
        Console.WriteLine($"[EquipmentManagement] - 设备ID: {equipment.Id}");
        Console.WriteLine($"[EquipmentManagement] - 设备编码: {equipment.Code}");
        Console.WriteLine($"[EquipmentManagement] - 设备名称: {equipment.Name}");
        Console.WriteLine($"[EquipmentManagement] - 设备状态 (Status): {equipment.Status}");
        Console.WriteLine($"[EquipmentManagement] - 启用状态 (IsEnabled): {equipment.IsEnabled}");

        var parameters = new DialogParameters<EquipmentEditDialog>
        {
            { x => x.Equipment, equipment },
            { x => x.IsEdit, true }
        };

        var dialog = await DialogService.ShowAsync<EquipmentEditDialog>("编辑设备", parameters);
        var result = await dialog.Result;

        if (!result.Canceled)
        {
            await LoadEquipment();
            await LoadStatistics();
        }
    }

    private async Task ViewEquipmentDetail(EquipmentDetailDto equipmentDetail)
    {
        var parameters = new DialogParameters<EquipmentDetailDialog>
        {
            { x => x.EquipmentDetail, equipmentDetail }
        };

        await DialogService.ShowAsync<EquipmentDetailDialog>("设备详情", parameters);
    }

    private async Task ToggleStatus(EquipmentDetailDto equipmentDetail)
    {
        try
        {
            var result = await EquipmentService.ToggleStatusAsync(equipmentDetail.Id);
            if (result.IsSuccess)
            {
                Snackbar.Add($"设备状态已{(equipmentDetail.IsEnabled ? "禁用" : "启用")}", Severity.Success);
                await LoadEquipment();
                await LoadStatistics();
            }
            else
            {
                Snackbar.Add($"操作失败: {result.ErrorMessage}", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"操作失败: {ex.Message}", Severity.Error);
        }
    }

    private async Task DeleteEquipment(EquipmentDetailDto equipmentDetail)
    {
        var confirmed = await DialogService.ShowMessageBox(
            "确认删除",
            $"确定要删除设备 '{equipmentDetail.Name}' 吗？此操作不可撤销。",
            yesText: "删除",
            cancelText: "取消");

        if (confirmed == true)
        {
            try
            {
                var result = await EquipmentService.DeleteEquipmentAsync(equipmentDetail.Id);
                if (result.IsSuccess)
                {
                    Snackbar.Add("设备删除成功", Severity.Success);
                    await LoadEquipment();
                    await LoadStatistics();
                }
                else
                {
                    Snackbar.Add($"删除失败: {result.ErrorMessage}", Severity.Error);
                }
            }
            catch (Exception ex)
            {
                Snackbar.Add($"删除失败: {ex.Message}", Severity.Error);
            }
        }
    }

    private async Task ExportEquipment()
    {
        // TODO: 实现导出功能
        Snackbar.Add("导出功能开发中...", Severity.Info);
    }
}
