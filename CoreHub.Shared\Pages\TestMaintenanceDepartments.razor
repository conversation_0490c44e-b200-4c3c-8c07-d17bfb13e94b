@page "/test-maintenance-departments"
@using CoreHub.Shared.Services
@using CoreHub.Shared.Models.Database
@using CoreHub.Shared.Data
@inject IDepartmentTypeService DepartmentTypeService
@inject IDepartmentService DepartmentService
@inject DatabaseContext DbContext
@inject ISnackbar Snackbar

<PageTitle>测试维修部门数据</PageTitle>

<MudContainer MaxWidth="MaxWidth.Large" Class="mt-4">
    <MudText Typo="Typo.h4" Class="mb-4">测试维修部门数据</MudText>
    
    <MudCard Class="mb-4">
        <MudCardContent>
            <MudButton Variant="Variant.Filled" Color="Color.Primary" OnClick="TestData">
                测试数据
            </MudButton>
        </MudCardContent>
    </MudCard>
    
    @if (!string.IsNullOrEmpty(testResult))
    {
        <MudCard>
            <MudCardContent>
                <MudText Typo="Typo.h6" Class="mb-2">测试结果</MudText>
                <pre style="white-space: pre-wrap;">@testResult</pre>
            </MudCardContent>
        </MudCard>
    }
    
    @if (allDepartments.Any())
    {
        <MudCard Class="mt-4">
            <MudCardHeader>
                <CardHeaderContent>
                    <MudText Typo="Typo.h6">所有部门数据</MudText>
                </CardHeaderContent>
            </MudCardHeader>
            <MudCardContent>
                <MudTable Items="allDepartments" Hover="true" Striped="true" Dense="true">
                    <HeaderContent>
                        <MudTh>ID</MudTh>
                        <MudTh>编码</MudTh>
                        <MudTh>名称</MudTh>
                        <MudTh>部门类型ID</MudTh>
                        <MudTh>部门类型</MudTh>
                        <MudTh>是否启用</MudTh>
                    </HeaderContent>
                    <RowTemplate>
                        <MudTd DataLabel="ID">@context.Id</MudTd>
                        <MudTd DataLabel="编码">@context.Code</MudTd>
                        <MudTd DataLabel="名称">@context.Name</MudTd>
                        <MudTd DataLabel="部门类型ID">@context.DepartmentTypeId</MudTd>
                        <MudTd DataLabel="部门类型">@context.DepartmentTypeName</MudTd>
                        <MudTd DataLabel="是否启用">
                            <MudChip T="string" Color="@(context.IsEnabled ? Color.Success : Color.Error)" Size="Size.Small">
                                @(context.IsEnabled ? "启用" : "禁用")
                            </MudChip>
                        </MudTd>
                    </RowTemplate>
                </MudTable>
            </MudCardContent>
        </MudCard>
    }
    
    @if (maintenanceDepartments.Any())
    {
        <MudCard Class="mt-4">
            <MudCardHeader>
                <CardHeaderContent>
                    <MudText Typo="Typo.h6">维修部门数据</MudText>
                </CardHeaderContent>
            </MudCardHeader>
            <MudCardContent>
                <MudTable Items="maintenanceDepartments" Hover="true" Striped="true" Dense="true">
                    <HeaderContent>
                        <MudTh>ID</MudTh>
                        <MudTh>编码</MudTh>
                        <MudTh>名称</MudTh>
                        <MudTh>描述</MudTh>
                    </HeaderContent>
                    <RowTemplate>
                        <MudTd DataLabel="ID">@context.Id</MudTd>
                        <MudTd DataLabel="编码">@context.Code</MudTd>
                        <MudTd DataLabel="名称">@context.Name</MudTd>
                        <MudTd DataLabel="描述">@context.Description</MudTd>
                    </RowTemplate>
                </MudTable>
            </MudCardContent>
        </MudCard>
    }
</MudContainer>

@code {
    private string testResult = "";
    private List<DepartmentWithType> allDepartments = new();
    private List<Department> maintenanceDepartments = new();
    
    private class DepartmentWithType
    {
        public int Id { get; set; }
        public string Code { get; set; } = "";
        public string Name { get; set; } = "";
        public int? DepartmentTypeId { get; set; }
        public string DepartmentTypeName { get; set; } = "";
        public bool IsEnabled { get; set; }
    }
    
    private async Task TestData()
    {
        try
        {
            testResult = "开始测试数据...\n\n";
            
            // 1. 检查部门类型数据
            var departmentTypes = await DbContext.Db.Queryable<DepartmentType>().ToListAsync();
            testResult += $"部门类型数量: {departmentTypes.Count}\n";
            foreach (var dt in departmentTypes)
            {
                testResult += $"- ID:{dt.Id}, Code:{dt.Code}, Name:{dt.Name}, Enabled:{dt.IsEnabled}\n";
            }
            testResult += "\n";
            
            // 2. 检查所有部门数据
            allDepartments = await DbContext.Db.Queryable<Department>()
                .LeftJoin<DepartmentType>((d, dt) => d.DepartmentTypeId == dt.Id)
                .Select((d, dt) => new DepartmentWithType
                {
                    Id = d.Id,
                    Code = d.Code,
                    Name = d.Name,
                    DepartmentTypeId = d.DepartmentTypeId,
                    DepartmentTypeName = dt.Name ?? "无类型",
                    IsEnabled = d.IsEnabled
                })
                .ToListAsync();
                
            testResult += $"所有部门数量: {allDepartments.Count}\n";
            foreach (var dept in allDepartments)
            {
                testResult += $"- ID:{dept.Id}, Code:{dept.Code}, Name:{dept.Name}, TypeId:{dept.DepartmentTypeId}, TypeName:{dept.DepartmentTypeName}, Enabled:{dept.IsEnabled}\n";
            }
            testResult += "\n";
            
            // 3. 检查维修部门
            var maintenanceTypeId = departmentTypes.FirstOrDefault(dt => dt.Code == "Maintenance")?.Id;
            testResult += $"维修部门类型ID: {maintenanceTypeId}\n";
            
            if (maintenanceTypeId.HasValue)
            {
                var maintenanceDepts = await DbContext.Db.Queryable<Department>()
                    .Where(d => d.DepartmentTypeId == maintenanceTypeId.Value && d.IsEnabled)
                    .ToListAsync();
                    
                testResult += $"维修部门数量: {maintenanceDepts.Count}\n";
                foreach (var dept in maintenanceDepts)
                {
                    testResult += $"- ID:{dept.Id}, Code:{dept.Code}, Name:{dept.Name}\n";
                }
            }
            testResult += "\n";
            
            // 4. 测试 GetMaintenanceDepartmentsAsync 方法
            maintenanceDepartments = await DepartmentTypeService.GetMaintenanceDepartmentsAsync();
            testResult += $"GetMaintenanceDepartmentsAsync 返回数量: {maintenanceDepartments.Count}\n";
            foreach (var dept in maintenanceDepartments)
            {
                testResult += $"- ID:{dept.Id}, Code:{dept.Code}, Name:{dept.Name}\n";
            }
            
            StateHasChanged();
        }
        catch (Exception ex)
        {
            testResult = $"测试出错: {ex.Message}\n{ex.StackTrace}";
            Snackbar.Add($"测试失败: {ex.Message}", Severity.Error);
        }
    }
}
