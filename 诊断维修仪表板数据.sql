-- 诊断维修仪表板数据问题
USE [EquipmentManagement]
GO

PRINT '=== 维修仪表板数据诊断 ==='

-- 1. 检查当前用户信息
PRINT '1. 检查用户ID 5的信息:'
SELECT 
    u.Id as '用户ID',
    u.Username as '用户名',
    u.Name as '姓名',
    u.DepartmentId as '部门ID',
    d.Name as '部门名称',
    u.IsEnabled as '是否启用'
FROM Users u
LEFT JOIN Departments d ON u.DepartmentId = d.Id
WHERE u.Id = 5

-- 2. 检查用户的角色分配
PRINT ''
PRINT '2. 检查用户ID 5的角色分配:'
SELECT 
    ur.UserId as '用户ID',
    ur.RoleId as '角色ID',
    r.Code as '角色代码',
    r.Name as '角色名称',
    ur.IsEnabled as '是否启用'
FROM UserRoles ur
INNER JOIN Roles r ON ur.RoleId = r.Id
WHERE ur.UserId = 5 AND ur.IsEnabled = 1

-- 3. 检查角色部门分配
PRINT ''
PRINT '3. 检查用户角色的部门分配:'
SELECT 
    r.Name as '角色名称',
    d.Name as '部门名称',
    rda.IsEnabled as '是否启用',
    rda.CreatedAt as '创建时间'
FROM UserRoles ur
INNER JOIN Roles r ON ur.RoleId = r.Id
INNER JOIN RoleDepartmentAssignments rda ON r.Id = rda.RoleId
INNER JOIN Departments d ON rda.DepartmentId = d.Id
WHERE ur.UserId = 5 AND ur.IsEnabled = 1 AND rda.IsEnabled = 1
ORDER BY r.Name, d.Name

-- 4. 检查报修单数据
PRINT ''
PRINT '4. 检查报修单总数:'
SELECT COUNT(*) as '报修单总数' FROM RepairOrders

PRINT ''
PRINT '5. 检查报修单详情:'
SELECT TOP 10
    ro.Id as '报修单ID',
    ro.OrderNumber as '报修单号',
    ro.EquipmentId as '设备ID',
    e.Name as '设备名称',
    e.DepartmentId as '设备部门ID',
    ed.Name as '设备部门名称',
    ro.MaintenanceDepartmentId as '维修部门ID',
    md.Name as '维修部门名称',
    ro.Status as '状态',
    ro.ReporterId as '报修人ID',
    u.Name as '报修人姓名',
    ro.ReportedAt as '报修时间'
FROM RepairOrders ro
LEFT JOIN Equipment e ON ro.EquipmentId = e.Id
LEFT JOIN Departments ed ON e.DepartmentId = ed.Id
LEFT JOIN Departments md ON ro.MaintenanceDepartmentId = md.Id
LEFT JOIN Users u ON ro.ReporterId = u.Id
ORDER BY ro.ReportedAt DESC

-- 6. 检查用户可访问的部门（基于新的权限系统）
PRINT ''
PRINT '6. 用户ID 5可访问的部门:'
SELECT DISTINCT
    d.Id as '部门ID',
    d.Name as '部门名称',
    d.DepartmentTypeId as '部门类型ID',
    dt.Name as '部门类型名称'
FROM UserRoles ur
INNER JOIN RoleDepartmentAssignments rda ON ur.RoleId = rda.RoleId
INNER JOIN Departments d ON rda.DepartmentId = d.Id
LEFT JOIN DepartmentTypes dt ON d.DepartmentTypeId = dt.Id
WHERE ur.UserId = 5 AND ur.IsEnabled = 1 AND rda.IsEnabled = 1 AND d.IsEnabled = 1
ORDER BY d.Name

-- 7. 检查用户应该能看到的报修单
PRINT ''
PRINT '7. 用户ID 5应该能看到的报修单:'
SELECT 
    ro.Id as '报修单ID',
    ro.OrderNumber as '报修单号',
    e.Name as '设备名称',
    ed.Name as '设备部门',
    md.Name as '维修部门',
    ro.Status as '状态',
    ro.ReportedAt as '报修时间',
    '设备部门匹配' as '匹配原因'
FROM RepairOrders ro
LEFT JOIN Equipment e ON ro.EquipmentId = e.Id
LEFT JOIN Departments ed ON e.DepartmentId = ed.Id
LEFT JOIN Departments md ON ro.MaintenanceDepartmentId = md.Id
WHERE e.DepartmentId IN (
    SELECT DISTINCT rda.DepartmentId
    FROM UserRoles ur
    INNER JOIN RoleDepartmentAssignments rda ON ur.RoleId = rda.RoleId
    WHERE ur.UserId = 5 AND ur.IsEnabled = 1 AND rda.IsEnabled = 1
)

UNION ALL

SELECT 
    ro.Id as '报修单ID',
    ro.OrderNumber as '报修单号',
    e.Name as '设备名称',
    ed.Name as '设备部门',
    md.Name as '维修部门',
    ro.Status as '状态',
    ro.ReportedAt as '报修时间',
    '维修部门匹配' as '匹配原因'
FROM RepairOrders ro
LEFT JOIN Equipment e ON ro.EquipmentId = e.Id
LEFT JOIN Departments ed ON e.DepartmentId = ed.Id
LEFT JOIN Departments md ON ro.MaintenanceDepartmentId = md.Id
WHERE ro.MaintenanceDepartmentId IN (
    SELECT DISTINCT rda.DepartmentId
    FROM UserRoles ur
    INNER JOIN RoleDepartmentAssignments rda ON ur.RoleId = rda.RoleId
    WHERE ur.UserId = 5 AND ur.IsEnabled = 1 AND rda.IsEnabled = 1
)
ORDER BY '报修时间' DESC

-- 8. 检查维修部门类型
PRINT ''
PRINT '8. 检查维修部门类型:'
SELECT 
    dt.Id as '部门类型ID',
    dt.Name as '部门类型名称',
    dt.IsMaintenance as '是否维修类型',
    COUNT(d.Id) as '部门数量'
FROM DepartmentTypes dt
LEFT JOIN Departments d ON dt.Id = d.DepartmentTypeId AND d.IsEnabled = 1
GROUP BY dt.Id, dt.Name, dt.IsMaintenance
ORDER BY dt.Name

PRINT ''
PRINT '=== 诊断完成 ==='
