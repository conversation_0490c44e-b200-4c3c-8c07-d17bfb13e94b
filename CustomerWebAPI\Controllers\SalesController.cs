﻿using System;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Net.Http.Json;
using System.Text;
using System.Threading.Tasks;
using CustomerWebAPI.Models;
using CustomerWebAPI.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;

namespace CustomerWebAPI.Controllers
{
    [Authorize, Route("api/[controller]"), ApiController]
    public class SalesController : ControllerBase
    {
        private readonly ISalesService _salesService;

        public SalesController(ISalesService salesService)
        {
            _salesService = salesService;
        }

        //[AllowAnonymous]
        [HttpPost("SubmitPO")]
        public IActionResult SubmitPo(dynamic poJson)
        {
            try
            {
                // Get Auth0 identifier for the user performing the request
                //string nameIdentifier = User.Claims.FirstOrDefault(c => c.Type == System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;
                // Retrieve this user from the database
                //User user = await _context.User.SingleOrDefaultAsync(u => u.Username == nameIdentifier);
                // Return a list of properties that belong to the user's agency
                //return Ok(_context.Properties
                //.Where(p.AgencyId == user.AgencyId));


                //string curUserName = User.Identity.Name;


                TALPO po = JsonConvert.DeserializeObject<TALPO>(poJson.ToString()); //反序列化

                if (po.Order == null)
                {
                    throw new Exception("Incorrect Format!");
                }

                string trackingId = DateTime.Now.ToString("yyyyMMddHHmmssfff");
                string PoJsonText = poJson.ToString();


                var tupleResult = _salesService.SaveTalPo(new SalesPO
                {
                    TrackingId = trackingId,
                    PoJsonText = PoJsonText,
                    IsSyncedToERP = false,
                    IsCreatedPO = true,
                    CreatedBy = User.Identity.Name,
                    CreatedDate = DateTime.Now
                });


                if (tupleResult.Item1 == false)
                {
                    throw new Exception(tupleResult.Item2);
                }

                return Ok(new
                {
                    trackingId
                });


                // string outputStr = "";}
                // foreach (var orderDetailItem in po.Order.orderDetail)
                // {
                //     foreach (var identificationItem in orderDetailItem.orderParties.seller.identification)
                //     {
                //         outputStr += identificationItem.value.ToString();
                //     }
                // }
                //
                // outputStr += Environment.NewLine + po.Order.orderDetail[0].orderParties.buyer.memberId;
                //
                //  return Ok(new {Version = outputStr});}
            }
            catch (Exception e)
            {
                return BadRequest(new
                {
                    status = "Fail",
                    errorMessage = e.Message
                });
            }
        }

        [HttpPost("DeletePO")]
        public IActionResult DeletePo(dynamic trackingId)
        {
            try
            {
                TrackingIdModel trackingID = JsonConvert.DeserializeObject<TrackingIdModel>(trackingId.ToString()); //反序列化

                if (trackingID == null)
                {
                    throw new Exception("Incorrect Format!");
                }


                Tuple<bool, string> delResult = _salesService.DeletePo(trackingID.TrackingId);

                if (delResult.Item1 == false)
                {
                    throw new Exception(delResult.Item2);
                }

                return Ok(new
                {
                    trackingId,
                    message = "Delete succeeded!"
                });
            }
            catch (Exception e)
            {
                return BadRequest(new
                {
                    message = e.Message
                });
            }
        }

        [HttpPost("SendASN_bak")]
        public async Task<IActionResult> SendASN_bak(dynamic jsonAsnRootId)
        {
            const string ASNURI = "https://testapi.talgroup.com/b2b/api/1.0/suppliers/asn";
            try
            {
                var asnRootID = JsonConvert.DeserializeObject<AsnRootIdModel>(jsonAsnRootId.ToString()); //反序列化

                if (asnRootID == null)
                {
                    return BadRequest(new
                    {
                        BadResult = "Json_asnRootId format not correct!"
                    });
                }

                var clientHandler = new HttpClientHandler
                {
                    AllowAutoRedirect = true,
                    ClientCertificateOptions = ClientCertificateOption.Automatic
                };
                using var client = new HttpClient(clientHandler);
                var authInfo = "saintyeartex" + ":" + "saintyeartex";
                authInfo = Convert.ToBase64String(Encoding.Default.GetBytes(authInfo));
                client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", authInfo);

                var seriJson = _salesService.GetAsnJsonFromDb(asnRootID.RootId);


                //var contentEncoded = new StringContent(seriJson, Encoding.UTF8, "application/json");
                var content = new StringContent(seriJson);
                content.Headers.ContentType = new MediaTypeHeaderValue("application/json");

                var response = await client.PostAsync(ASNURI, content);
                response.EnsureSuccessStatusCode();
                var trackingIdModel = await response.Content.ReadFromJsonAsync<TrackingIdModel>();
                return Ok(trackingIdModel);
            }
            catch (Exception e)
            {
                return BadRequest("SendASN Failed!" + Environment.NewLine + e.Message);
            }
        }


        //[HttpPost("SendASN")]
        //public async Task<IActionResult> SendAsn(dynamic jsonAsnRootId)
        //{
        //    Tuple<bool, string , TrackingIdModel> response = await _salesService.SendAsnToTalAsync(jsonAsnRootId);
           
        //    if (response.Item1)
        //    {
        //        return Ok(response.Item3);
        //    }

        //    return BadRequest(response.Item2);
        //}
    }
}