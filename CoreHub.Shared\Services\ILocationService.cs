using CoreHub.Shared.Models.Database;

namespace CoreHub.Shared.Services
{
    /// <summary>
    /// 位置服务接口
    /// </summary>
    public interface ILocationService
    {
        /// <summary>
        /// 获取所有位置
        /// </summary>
        Task<List<Location>> GetAllLocationsAsync();

        /// <summary>
        /// 根据ID获取位置
        /// </summary>
        Task<Location?> GetLocationByIdAsync(int id);

        /// <summary>
        /// 根据编码获取位置
        /// </summary>
        Task<Location?> GetLocationByCodeAsync(string code);

        /// <summary>
        /// 创建位置
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> CreateLocationAsync(Location location);

        /// <summary>
        /// 更新位置
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> UpdateLocationAsync(Location location);

        /// <summary>
        /// 删除位置
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> DeleteLocationAsync(int id);

        /// <summary>
        /// 切换位置状态
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> ToggleStatusAsync(int id);

        /// <summary>
        /// 获取启用的位置列表
        /// </summary>
        Task<List<Location>> GetEnabledLocationsAsync();

        /// <summary>
        /// 根据部门获取位置
        /// </summary>
        Task<List<Location>> GetLocationsByDepartmentAsync(int departmentId);

        /// <summary>
        /// 获取位置树形结构
        /// </summary>
        Task<List<Location>> GetLocationTreeAsync();

        /// <summary>
        /// 获取位置详细信息（包含关联数据）
        /// </summary>
        Task<List<LocationDetailDto>> GetLocationDetailsAsync();

        /// <summary>
        /// 检查位置编码是否存在
        /// </summary>
        Task<bool> IsCodeExistsAsync(string code, int? excludeId = null);
    }

    /// <summary>
    /// 位置详细信息DTO
    /// </summary>
    public class LocationDetailDto
    {
        public int Id { get; set; }
        public string Code { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public int DepartmentId { get; set; }
        public string DepartmentName { get; set; } = string.Empty;
        public int? ParentId { get; set; }
        public string? ParentName { get; set; }
        public int Level { get; set; }
        public string? Address { get; set; }
        public string? Description { get; set; }
        public bool IsEnabled { get; set; }
        public DateTime CreatedAt { get; set; }
        public string? Remark { get; set; }
        public int EquipmentCount { get; set; }
    }
}
