# 🔧 零件更换记录功能使用指南

## 📋 功能概述

本功能为CoreHub维修单系统添加了零件更换记录管理能力，支持在创建维修单时录入零件信息，并为外部系统集成预留了接口字段。

## 🏗️ 架构设计

### 数据存储方式
- **存储位置**: RepairOrders表的PartReplacementRecordsJson字段
- **存储格式**: JSON格式，支持动态结构
- **数据模型**: PartReplacementRequestDto / PartReplacementRequestCollectionDto

### 核心组件
1. **PartReplacementRecordInput.razor** - 零件录入组件（用于创建/编辑）
2. **PartReplacementRecordDisplay.razor** - 零件显示组件（用于查看）
3. **PartReplacementRequestDto** - 零件数据传输对象

## 🚀 快速开始

### 1. 数据库迁移
```sql
-- 执行迁移脚本
-- 文件：维修单零件更换记录字段迁移脚本.sql
```

### 2. 在创建维修单页面中使用
```razor
@using CoreHub.Shared.Components
@using CoreHub.Shared.Models.Dto

<!-- 零件更换记录录入 -->
<PartReplacementRecordInput PartRecords="partReplacementRecords" 
                            PartRecordsChanged="OnPartRecordsChanged"
                            ReadOnly="false" />

@code {
    private PartReplacementRequestCollectionDto partReplacementRecords = new();
    
    private void OnPartRecordsChanged(PartReplacementRequestCollectionDto updatedRecords)
    {
        partReplacementRecords = updatedRecords;
        StateHasChanged();
    }
    
    private async Task SaveRepairOrder()
    {
        // 保存零件记录到维修单
        repairOrder.PartReplacementRecords = partReplacementRecords;
        
        // 提交维修单
        var result = await RepairOrderService.CreateRepairOrderAsync(repairOrder);
    }
}
```

### 3. 在维修单详情页面中显示
```razor
@using CoreHub.Shared.Components

<!-- 显示零件更换记录 -->
<PartReplacementRecordDisplay PartRecords="repairOrder.PartReplacementRecords"
                              ShowSummary="true"
                              ShowProgress="true"
                              ShowAsTable="true"
                              ShowDetails="true"
                              ShowExternalFields="false" />
```

## 📊 数据结构

### PartReplacementRequestDto 字段说明

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| TempId | string | 是 | 临时ID（前端列表管理用） |
| PartName | string | 是 | 零件名称 |
| Specification | string | 否 | 规格型号 |
| RequestedQuantity | int | 是 | 申请数量 |
| Unit | string | 是 | 计量单位 |
| Reason | string | 否 | 更换原因 |
| Remark | string | 否 | 备注 |
| Status | int | 是 | 状态（1=申请中,2=已批准,3=已领用,4=已安装,5=已取消） |

### 外部系统集成预留字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| ExternalPartNumber | string | 外部系统零件编号 |
| ExternalRequisitionDetailId | string | 外部系统领用单明细ID |
| ActualQuantity | int? | 实际领用数量 |
| ActualPartName | string | 实际领用名称 |
| ActualSpecification | string | 实际领用规格 |

## 🎨 组件参数

### PartReplacementRecordInput 参数

| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| PartRecords | PartReplacementRequestCollectionDto | new() | 零件记录集合 |
| PartRecordsChanged | EventCallback | - | 记录变更回调 |
| ReadOnly | bool | false | 是否只读 |

### PartReplacementRecordDisplay 参数

| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| PartRecords | PartReplacementRequestCollectionDto | new() | 零件记录集合 |
| ShowSummary | bool | true | 显示统计信息 |
| ShowProgress | bool | true | 显示进度条 |
| ShowAsTable | bool | true | 表格显示（否则卡片显示） |
| ShowDetails | bool | true | 显示详细信息 |
| ShowExternalFields | bool | false | 显示外部系统字段 |
| Dense | bool | true | 紧凑模式 |

## 💡 使用示例

### 示例1：基础录入
```csharp
// 创建零件记录
var partRecord = new PartReplacementRequestDto
{
    PartName = "轴承",
    Specification = "6205-2RS",
    RequestedQuantity = 2,
    Unit = "个",
    Reason = "轴承磨损严重，需要更换"
};

// 添加到集合
partReplacementRecords.AddPart(partRecord);
```

### 示例2：批量操作
```csharp
// 验证所有记录
var (isValid, errors) = partReplacementRecords.ValidateAll();
if (!isValid)
{
    foreach (var error in errors)
    {
        Console.WriteLine($"记录 {error.Key} 有错误: {string.Join(", ", error.Value)}");
    }
}

// 获取统计信息
var totalCount = partReplacementRecords.TotalCount;
var pendingCount = partReplacementRecords.PendingCount;
var completedCount = partReplacementRecords.CompletedCount;
```

### 示例3：JSON序列化
```csharp
// 转换为JSON（保存到数据库）
string json = partReplacementRecords.ToJson();

// 从JSON恢复（从数据库读取）
var restored = PartReplacementRequestCollectionDto.FromJson(json);
```

## 🔧 外部系统集成

### 数据回写接口设计
```csharp
public class ExternalPartUpdateDto
{
    public string TempId { get; set; }                    // 临时ID（用于匹配）
    public string ExternalPartNumber { get; set; }       // 外部零件编号
    public string ExternalRequisitionDetailId { get; set; } // 外部领用单明细ID
    public int ActualQuantity { get; set; }              // 实际领用数量
    public string ActualPartName { get; set; }           // 实际领用名称
    public string ActualSpecification { get; set; }      // 实际领用规格
    public int Status { get; set; }                      // 更新后的状态
}
```

### 状态同步流程
1. 维修单创建时，零件记录状态为"申请中"(1)
2. 外部系统处理后，通过API回写实际信息和状态
3. 系统更新JSON数据并触发状态变更通知

## 📱 移动端适配

### 响应式设计
- 组件支持Breakpoint.Sm断点
- 表格在小屏幕上自动切换为卡片显示
- 对话框支持FullWidth模式

### 触摸优化
- 按钮大小适合触摸操作
- 表单字段间距合理
- 支持滑动操作

## 🔍 查询和统计

### SQL查询示例
```sql
-- 查询包含零件记录的维修单
SELECT * FROM V_RepairOrdersWithParts 
WHERE HasPartReplacementRecords = 1

-- 统计零件使用情况
SELECT 
    JSON_VALUE(parts.value, '$.partName') AS PartName,
    COUNT(*) AS UsageCount,
    SUM(CAST(JSON_VALUE(parts.value, '$.requestedQuantity') AS INT)) AS TotalQuantity
FROM RepairOrders ro
CROSS APPLY OPENJSON(ro.PartReplacementRecordsJson) AS parts
WHERE ro.PartReplacementRecordsJson IS NOT NULL
GROUP BY JSON_VALUE(parts.value, '$.partName')
ORDER BY UsageCount DESC
```

### 数据分析
- 零件使用频率统计
- 维修成本分析
- 供应商绩效评估
- 库存需求预测

## ⚠️ 注意事项

### 数据验证
- 前端使用FluentValidation进行实时验证
- 后端需要额外的业务逻辑验证
- JSON数据完整性检查

### 性能考虑
- JSON字段不支持传统索引
- 大量数据查询时考虑性能影响
- 建议定期清理历史数据

### 安全性
- 输入数据需要防XSS处理
- JSON序列化安全性检查
- 权限控制和操作审计

## 🔄 版本更新

### v1.0.0 (2025-01-07)
- 初始版本发布
- 基础零件录入和显示功能
- 外部系统集成预留字段
- 移动端响应式支持

### 后续计划
- 零件库存集成
- 条码扫描支持
- 批量导入功能
- 高级统计报表

---

**技术支持**: 如有问题请联系开发团队  
**文档版本**: 1.0.0  
**最后更新**: 2025-01-07
