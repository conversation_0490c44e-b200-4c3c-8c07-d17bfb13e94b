-- =============================================
-- 清理审批字段脚本
-- 创建日期: 2025-01-07
-- 描述: 专门清理审批相关的外键约束和字段
-- =============================================

USE [EquipmentManagement]
GO

PRINT '开始清理审批相关字段和约束...'
PRINT '========================================'

-- 检查表是否存在
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[RepairOrderPartRequests]') AND type in (N'U'))
BEGIN
    PRINT '○ RepairOrderPartRequests表不存在，无需清理'
    RETURN
END

-- 删除审批相关的外键约束
PRINT '1. 删除外键约束...'

IF EXISTS (SELECT * FROM sys.foreign_keys WHERE object_id = OBJECT_ID(N'[dbo].[FK_RepairOrderPartRequests_ApprovedBy]'))
BEGIN
    ALTER TABLE [dbo].[RepairOrderPartRequests] DROP CONSTRAINT [FK_RepairOrderPartRequests_ApprovedBy]
    PRINT '✓ 删除FK_RepairOrderPartRequests_ApprovedBy约束'
END
ELSE
BEGIN
    PRINT '○ FK_RepairOrderPartRequests_ApprovedBy约束不存在'
END

-- 删除审批字段
PRINT '2. 删除审批字段...'

IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[RepairOrderPartRequests]') AND name = 'ApprovedBy')
BEGIN
    ALTER TABLE [dbo].[RepairOrderPartRequests] DROP COLUMN [ApprovedBy]
    PRINT '✓ 删除ApprovedBy字段'
END
ELSE
BEGIN
    PRINT '○ ApprovedBy字段不存在'
END

IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[RepairOrderPartRequests]') AND name = 'ApprovedAt')
BEGIN
    ALTER TABLE [dbo].[RepairOrderPartRequests] DROP COLUMN [ApprovedAt]
    PRINT '✓ 删除ApprovedAt字段'
END
ELSE
BEGIN
    PRINT '○ ApprovedAt字段不存在'
END

-- 删除可能存在的旧视图
PRINT '3. 删除旧视图...'

IF EXISTS (SELECT * FROM sys.views WHERE object_id = OBJECT_ID(N'[dbo].[V_RepairOrderPartRequestDetails]'))
BEGIN
    DROP VIEW [dbo].[V_RepairOrderPartRequestDetails]
    PRINT '✓ 删除V_RepairOrderPartRequestDetails视图'
END

IF EXISTS (SELECT * FROM sys.views WHERE object_id = OBJECT_ID(N'[dbo].[V_RepairOrderPartSummary]'))
BEGIN
    DROP VIEW [dbo].[V_RepairOrderPartSummary]
    PRINT '✓ 删除V_RepairOrderPartSummary视图'
END

IF EXISTS (SELECT * FROM sys.views WHERE object_id = OBJECT_ID(N'[dbo].[V_RepairOrdersWithParts]'))
BEGIN
    DROP VIEW [dbo].[V_RepairOrdersWithParts]
    PRINT '✓ 删除V_RepairOrdersWithParts视图'
END

-- 删除可能重复的索引
PRINT '4. 删除重复索引...'

IF EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[RepairOrderPartRequests]') AND name = N'IX_RepairOrderPartRequests_ExternalPartNumber')
BEGIN
    DROP INDEX [IX_RepairOrderPartRequests_ExternalPartNumber] ON [dbo].[RepairOrderPartRequests]
    PRINT '✓ 删除重复的外部零件编号索引'
END

IF EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[RepairOrderPartRequests]') AND name = N'IX_RepairOrderPartRequests_ExternalRequisitionDetailId')
BEGIN
    DROP INDEX [IX_RepairOrderPartRequests_ExternalRequisitionDetailId] ON [dbo].[RepairOrderPartRequests]
    PRINT '✓ 删除重复的外部领用单明细ID索引'
END

-- 验证清理结果
PRINT '5. 验证清理结果...'

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[RepairOrderPartRequests]') AND name = 'ApprovedBy')
    PRINT '✓ ApprovedBy字段已删除'
ELSE
    PRINT '❌ ApprovedBy字段仍然存在'

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[RepairOrderPartRequests]') AND name = 'ApprovedAt')
    PRINT '✓ ApprovedAt字段已删除'
ELSE
    PRINT '❌ ApprovedAt字段仍然存在'

IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE object_id = OBJECT_ID(N'[dbo].[FK_RepairOrderPartRequests_ApprovedBy]'))
    PRINT '✓ ApprovedBy外键约束已删除'
ELSE
    PRINT '❌ ApprovedBy外键约束仍然存在'

PRINT ''
PRINT '========================================'
PRINT '审批字段清理完成！'
PRINT '现在可以执行主脚本了。'
PRINT '========================================'

-- 显示当前表结构
PRINT ''
PRINT '当前表字段：'
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'RepairOrderPartRequests'
ORDER BY ORDINAL_POSITION;
