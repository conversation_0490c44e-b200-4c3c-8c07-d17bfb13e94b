﻿using MauiScanManager.Constants;
using MauiScanManager.Models;
using Microsoft.Extensions.Logging;


namespace MauiScanManager.Services;

public class CPDeliveryService : ICPDeliveryService
{
    private readonly IApiService _apiService;
    private readonly IPlatformLoadingService _loadingService;
    private readonly ILogger<CPDeliveryService> _logger;
    private List<RTcpMaterialFlowDept> _cachedcurdepts;

    public CPDeliveryService(
        IApiService apiService,
        IPlatformLoadingService loadingService,
        ILogger<CPDeliveryService> logger)
    {
        _apiService = apiService;
        _loadingService = loadingService;
        _logger = logger;
    }
    public async Task<ServiceResult<List<RTcpMaterialFlowDept>>> GetMaterialFlowDeptAsync()
    {
        _loadingService.ShowNativeLoading("正在获取出货地点...");
        try
        {
            if (_cachedcurdepts != null)
            {
                return ServiceResult<List<RTcpMaterialFlowDept>>.Success(_cachedcurdepts);
            }

            var response = await _apiService.PostAsync<List<RTcpMaterialFlowDept>>(
                ApiEndpoints.RTcpDelivery.GetMaterialFlowDept,""
                );


            if (response.Data == null)
            {
                return ServiceResult<List<RTcpMaterialFlowDept>>.Failure("未获取到出货地点");
            }
            _cachedcurdepts = response.Data;
            return ServiceResult<List<RTcpMaterialFlowDept>>.Success(_cachedcurdepts);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取出货地点失败");
            return ServiceResult<List<RTcpMaterialFlowDept>>.Failure(ex.Message);
        }
        finally
        {
            _loadingService.HideNativeLoading();
        }



    }

    public async Task<ServiceResult<RTcpFiveLayers>> GetFiveLayersByBatchNOAsync(RTcpDeliveryCheck cpDeliveryCheck)
    {
        _loadingService.ShowNativeLoading("检查五层样信息...");
        try
        {
            var model = new RTcpDeliveryCheck
            {
                CkType= "ckFiveLayers",
                BatchNO = cpDeliveryCheck.BatchNO,
                CarNo ="五层样"
            };
            var response = await _apiService.PostAsync<RTcpFiveLayers>(
                ApiEndpoints.RTcpDelivery.GetFiveLayersByBatchNO, model
                );

            return response.Success
                ? ServiceResult<RTcpFiveLayers>.Success(response.Data)
                : ServiceResult<RTcpFiveLayers>.Failure(response.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查五层样信息失败");
            return ServiceResult<RTcpFiveLayers>.Failure(ex.Message);
        }
        finally
        {
            _loadingService.HideNativeLoading();
        }
    }

    public async Task<ServiceResult<RTcpCheckReturnMsg>> CheckCanDeliveryAsync(RTcpDeliveryCheck cpCheckCanDelivery)
    {
        _loadingService.ShowNativeLoading("检查出货缸号...");
        try
        {
            var model = new RTcpDeliveryCheck
            {
                CkType = "ckBatchNO",
                BatchNO = cpCheckCanDelivery.BatchNO,
                CarNo = cpCheckCanDelivery.CarNo
            };

            var response = await _apiService.PostAsync<RTcpCheckReturnMsg>(
                ApiEndpoints.RTcpDelivery.CheckCanDelivery, model
                );

            return response.Success
                ? ServiceResult<RTcpCheckReturnMsg>.Success(response.Data)
                : ServiceResult<RTcpCheckReturnMsg>.Failure(response.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查出货缸号失败");
            return ServiceResult<RTcpCheckReturnMsg>.Failure(ex.Message);
        }
        finally
        {
            _loadingService.HideNativeLoading();
        }
    }

    public async Task<ServiceResult<RTcpCheckReturnMsg>> CheckRtCarNoAsync(RTcpDeliveryCheck cpCheckRtCarNo)
    {
        _loadingService.ShowNativeLoading("检查车号信息...");
        try
        {
            var model = new RTcpDeliveryCheck
            {
                CkType = "ckCarNO",
                BatchNO = "车号",
                CarNo = cpCheckRtCarNo.CarNo
            };
            var response = await _apiService.PostAsync<RTcpCheckReturnMsg>(
                ApiEndpoints.RTcpDelivery.CheckRtCarNo, model
                );

            return response.Success
                ? ServiceResult<RTcpCheckReturnMsg>.Success(response.Data)
                : ServiceResult<RTcpCheckReturnMsg>.Failure(response.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查车号信息失败");
            return ServiceResult<RTcpCheckReturnMsg>.Failure(ex.Message);
        }
        finally
        {
            _loadingService.HideNativeLoading();
        }
    }

    public async Task<ServiceResult<RTcpWorkerName>> GetWorkerNameAsync(RTcpDeliveryCheck cpGetWorkerName)
    {
        _loadingService.ShowNativeLoading("获取员工姓名...");
        try
        {
            var model = new RTcpDeliveryCheck
            {
                CkType = "GetName",
                BatchNO = cpGetWorkerName.BatchNO, //是工号
                CarNo = "工号"
            };
            var response = await _apiService.PostAsync<RTcpWorkerName>(
                ApiEndpoints.RTcpDelivery.GetWorkerName, model
                );

            return response.Success
                ? ServiceResult<RTcpWorkerName>.Success(response.Data)
                : ServiceResult<RTcpWorkerName>.Failure(response.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取员工姓名失败");
            return ServiceResult<RTcpWorkerName>.Failure(ex.Message);
        }
        finally
        {
            _loadingService.HideNativeLoading();
        }
    }
    public async Task<ServiceResult<RTcpBatchNO>> GetFiveBatchNOAsync(RTcpDeliveryCheck cpGetBatchNO)
    {
        _loadingService.ShowNativeLoading("正在获取待取五层样缸号...");
        try
        {
            var model = new RTcpDeliveryCheck
            {
                CkType = "GetBatchNO",
                BatchNO = cpGetBatchNO.BatchNO, 
                CarNo = ""
            };

            var response = await _apiService.PostAsync<RTcpBatchNO>(
                ApiEndpoints.RTcpDelivery.GetFiveBatchNO, model
                );

            return response.Success
                ? ServiceResult<RTcpBatchNO>.Success(response.Data)
                : ServiceResult<RTcpBatchNO>.Failure(response.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取五层样缸号失败");
            return ServiceResult<RTcpBatchNO>.Failure(ex.Message);
        }
        finally
        {
            _loadingService.HideNativeLoading();
        }
    }

    public async Task<ServiceResult<string>> SaveCPAutoDeliveryByScanAsync(RTcpDeliveryByScan cpDeliveryByScan)
    {
        _loadingService.ShowNativeLoading("正在保存出货信息...");
        try
        {
            var model = new RTcpDeliveryByScan
            {
                BatchNoList = cpDeliveryByScan.BatchNoList,
                CurDept ="CP",
                Destination = cpDeliveryByScan.Destination,
                WorkerName = cpDeliveryByScan.WorkerName
            };

            var response = await _apiService.PostAsync<string>(ApiEndpoints.RTcpDelivery.SaveCPAutoDeliveryByScan, model);
            return response.Success
                ? ServiceResult<string>.Success("保存成功")
                : ServiceResult<string>.Failure(response.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存失败");
            return ServiceResult<string>.Failure(ex.Message);
        }
        finally
        {
            _loadingService.HideNativeLoading();
        }
    }

  

}
