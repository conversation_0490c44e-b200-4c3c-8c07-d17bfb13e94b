﻿using MauiScanManager.Services;
using MauiScanManager.Pages;
using MauiScanManager.Services;

namespace MauiScanManager
{
    public partial class App : Application
    {
        private readonly IAppUpdateService _updateService;
        private readonly INetworkService _networkService;
        private readonly IDialogService _dialogService;
        private readonly IPlatformLoadingService _loadingService;
        private readonly IOperationNavigationService _navigationService;

        public App(
            IAppUpdateService updateService,
            INetworkService networkService,
            IDialogService dialogService,
            IPlatformLoadingService loadingService,
            IOperationNavigationService navigationService)
        {
            InitializeComponent();
            _updateService = updateService;
            _networkService = networkService;
            _dialogService = dialogService;
            _loadingService = loadingService;
            _navigationService = navigationService;

            MainPage = new AppShell(_navigationService);

            Routing.RegisterRoute("update", typeof(UpdatePage));
        }

        protected override async void OnStart()
        {
            base.OnStart();
            await CheckForUpdates();
        }

        private async Task CheckForUpdates()
        {
            try
            {
                // 检查网络连接
                if (!await _networkService.CheckNetworkAsync())
                {
                    return;
                }

                // 检查更新
                _loadingService?.ShowNativeLoading("正在检查更新...");
                if (await _updateService.CheckForUpdate())
                {
                    if (_updateService.IsForceUpdate)
                    {
                        await Shell.Current.GoToAsync("update");
                    }
                    else
                    {
                        bool shouldUpdate = await _dialogService.ShowConfirmAsync(
                            "发现新版本",
                            "是否立即更新？",
                            "更新",
                            "稍后");

                        if (shouldUpdate)
                        {
                            await Shell.Current.GoToAsync("update");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                await _dialogService.ShowErrorAsync($"检查更新失败: {ex.Message}");
            }
            finally
            {
                _loadingService?.HideNativeLoading();
            }
        }

        protected override void OnSleep()
        {
            base.OnSleep();
        }

        protected override void OnResume()
        {
            base.OnResume();
        }
    }
}
