# 🎯 统一数据库配置系统

## 📋 概述

现在整个解决方案使用统一的数据库配置系统，**只需要在一个地方修改配置**，所有项目（MAUI、Web）都会自动应用。

## 🔧 配置位置

**主配置文件**: `CoreHub.Shared/Configuration/DatabaseConfig.cs`

```csharp
public static class DatabaseConfig
{
    // 修改这里的连接字符串，所有项目都会生效
    public static string DefaultConnectionString => 
        Environment.GetEnvironmentVariable("DB_CONNECTION_STRING") ??
        "Server=172.16.1.12;Database=PermissionSystem;User Id=sa;Password=****;...";

    // 修改这里的认证方式，所有项目都会生效
    public static bool UseStoredProcedure => 
        bool.TryParse(Environment.GetEnvironmentVariable("USE_STORED_PROCEDURE"), out var result) ? result : true;
}
```

## 🚀 使用方式

### 方式一：直接修改代码（简单）

1. 打开 `CoreHub.Shared/Configuration/DatabaseConfig.cs`
2. 修改 `DefaultConnectionString` 中的连接字符串
3. 修改 `UseStoredProcedure` 中的认证方式
4. 重新编译，所有项目自动生效

### 方式二：环境变量（推荐用于部署）

设置以下环境变量：

```bash
# 数据库连接字符串
DB_CONNECTION_STRING=Server=your-server;Database=your-db;User Id=your-user;Password=********;TrustServerCertificate=True;Connect Timeout=30;MultipleActiveResultSets=True

# 是否使用存储过程认证
USE_STORED_PROCEDURE=true
```

## 🎯 架构优势

### ✅ 统一配置
- **一处修改，处处生效**: 只需修改 `DatabaseConfig.cs`
- **避免配置不一致**: 不会出现不同项目配置不同的问题
- **减少维护成本**: 不需要同时维护多个配置文件

### ✅ 灵活部署
- **开发环境**: 直接修改代码中的默认值
- **测试环境**: 使用环境变量覆盖
- **生产环境**: 使用环境变量，无需修改代码

### ✅ 项目对应关系

| 项目 | 配置来源 | 说明 |
|------|----------|------|
| **CoreHub** | `DatabaseConfig` | 通过内存配置应用 |
| **CoreHub.Web** | `DatabaseConfig` | 覆盖 appsettings.json |
| **CoreHub.Shared** | - | 提供配置类和服务 |

## 🔍 配置验证

在程序启动时，可以添加配置日志来验证当前使用的配置：

```csharp
// 在 Program.cs 中添加
DatabaseConfig.LogCurrentConfig(message => Console.WriteLine(message));
```

输出示例：
```
数据库服务器: 172.16.1.12
数据库名称: PermissionSystem
使用存储过程: True
配置来源: 默认配置
```

## ⚙️ 常见场景

### 更换数据库服务器
```csharp
// 只需修改这一行
public static string DefaultConnectionString => 
    Environment.GetEnvironmentVariable("DB_CONNECTION_STRING") ??
    "Server=192.168.1.100;Database=PermissionSystem;...";  // 新服务器地址
```

### 切换认证方式
```csharp
// 只需修改这一行
public static bool UseStoredProcedure => 
    bool.TryParse(Environment.GetEnvironmentVariable("USE_STORED_PROCEDURE"), out var result) ? result : false;  // 改为 false
```

### 部署到不同环境
```bash
# 开发环境
DB_CONNECTION_STRING=Server=dev-server;...

# 生产环境  
DB_CONNECTION_STRING=Server=prod-server;...
USE_STORED_PROCEDURE=true
```

## 🎉 总结

现在您可以：
1. **一键切换**: 修改一个文件，所有项目生效
2. **环境隔离**: 使用环境变量适配不同部署环境
3. **配置追踪**: 可以查看当前使用的配置和来源
4. **维护简单**: 不再需要同步多个配置文件

这个统一配置系统让您的项目管理变得更加简单和可靠！ 