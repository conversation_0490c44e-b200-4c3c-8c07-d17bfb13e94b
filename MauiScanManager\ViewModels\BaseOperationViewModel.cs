using CommunityToolkit.Mvvm.ComponentModel;
using MauiScanManager.Models;
using MauiScanManager.Services;
namespace MauiScanManager.ViewModels
{
    public abstract class BaseOperationViewModel : ObservableObject
    {
        protected readonly IScanService _scanService;
        protected readonly IDialogService _dialogService;
        private Operation _operation;

        public Operation Operation
        {
            get => _operation;
            protected set => SetProperty(ref _operation, value);
        }

        protected BaseOperationViewModel(
            IScanService scanService,
            IDialogService dialogService)
        {
            _scanService = scanService;
            _dialogService = dialogService;
            _scanService.OnScanResult += OnScanResult;
            _scanService.Initialize();
        }

        public virtual void Initialize(Operation operation)
        {
            try
            {
                Operation = operation;
            }
            catch (Exception ex)
            {
                _dialogService.ShowErrorAsync($"初始化失败: {ex.Message}").ConfigureAwait(false);
            }
        }

        private void OnScanResult(object sender, ScanEventArgs e)
        {
            try
            {
                ProcessScanResult(e.Code, e.Type, e.CodeSource);
            }
            catch (Exception ex)
            {
                _dialogService.ShowErrorAsync($"处理扫描结果失败: {ex.Message}").ConfigureAwait(false);
            }
        }

        protected abstract void ProcessScanResult(string code, string type, byte[] codeSource);

        ~BaseOperationViewModel()
        {
            _scanService.OnScanResult -= OnScanResult;
            _scanService.Dispose();
        }
    }
}
