using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using CustomerWebAPI.Models;
using CustomerWebAPI.Services;
using CustomerWebAPI.Common;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;


namespace CustomerWebAPI.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class ColorCardController : ControllerBase
    {
        private readonly IColorCardService _colorCardService;
        private readonly ILogger<ColorCardController> _logger;

        public ColorCardController(IColorCardService colorCardService, ILogger<ColorCardController> logger)
        {
            _colorCardService = colorCardService ?? throw new ArgumentNullException(nameof(colorCardService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        [HttpPost("location")]
        [ProducesResponseType(typeof(ApiResponse<bool>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ApiResponse<bool>), StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<ApiResponse<bool>>> UpdateLocation([FromBody] ColorCardLocation model)
        {
            var result = await _colorCardService.UpdateColorCardLocationAsync(model);
            return result.Success ? Ok(result) : BadRequest(result);
        }
    }
} 