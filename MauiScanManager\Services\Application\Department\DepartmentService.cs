using MauiScanManager.Constants;
using MauiScanManager.Models;
using Microsoft.Extensions.Logging;

namespace MauiScanManager.Services
{
    public class DepartmentService : IDepartmentService
    {
        private readonly IApiService _apiService;
        private readonly IPlatformLoadingService _loadingService;
        private readonly ILogger<DepartmentService> _logger;
        private List<Department> _cachedDepartments;

        public DepartmentService(
            IApiService apiService,
            IPlatformLoadingService loadingService,
            ILogger<DepartmentService> logger)
        {
            _apiService = apiService;
            _loadingService = loadingService;
            _logger = logger;
        }

        public async Task<ServiceResult<List<Department>>> GetDepartmentsAsync()
        {
            _loadingService.ShowNativeLoading("正在获取部门信息...");
            try
            {
                if (_cachedDepartments != null)
                {
                    return ServiceResult<List<Department>>.Success(_cachedDepartments);
                }

                var response = await _apiService.GetAsync<List<Department>>(ApiEndpoints.Departments);
                if (!response.Success)
                {
                    return ServiceResult<List<Department>>.Failure(response.Message);
                }

                if (response.Data == null)
                {
                    return ServiceResult<List<Department>>.Failure("未获取到部门数据");
                }

                _cachedDepartments = response.Data;
                return ServiceResult<List<Department>>.Success(_cachedDepartments);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取部门数据失败");
                return ServiceResult<List<Department>>.Failure(ex.Message);
            }
            finally
            {
                _loadingService.HideNativeLoading();
            }
        }

        public async Task<ServiceResult<List<Operation>>> GetOperationsByDepartmentAsync(string departmentCode)
        {
            _loadingService.ShowNativeLoading("正在获取操作列表...");
            try
            {
                var departmentsResult = await GetDepartmentsAsync();
                if (!departmentsResult.IsSuccess)
                {
                    return ServiceResult<List<Operation>>.Failure(departmentsResult.ErrorMessage);
                }

                var department = departmentsResult.Data.FirstOrDefault(d => d.Code == departmentCode);
                if (department == null)
                {
                    return ServiceResult<List<Operation>>.Failure($"未找到部门: {departmentCode}");
                }

                return ServiceResult<List<Operation>>.Success(department.Operations ?? new List<Operation>());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取部门操作列表失败");
                return ServiceResult<List<Operation>>.Failure(ex.Message);
            }
            finally
            {
                _loadingService.HideNativeLoading();
            }
        }
    }
}
