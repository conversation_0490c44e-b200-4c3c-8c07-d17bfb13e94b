-- =============================================
-- 测试工作流历史功能
-- 创建日期: 2024-12-24
-- 描述: 测试工作流历史记录的创建、查询和显示功能
-- =============================================

USE [EquipmentManagement]
GO

PRINT '=== 开始测试工作流历史功能 ==='

-- 1. 检查工作流历史表是否存在
PRINT '1. 检查工作流历史表结构:'
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[RepairWorkflowHistory]') AND type in (N'U'))
BEGIN
    PRINT '✓ RepairWorkflowHistory 表已存在'
    
    -- 显示表结构
    SELECT 
        COLUMN_NAME as '列名',
        DATA_TYPE as '数据类型',
        IS_NULLABLE as '允许空值',
        COLUMN_DEFAULT as '默认值'
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'RepairWorkflowHistory'
    ORDER BY ORDINAL_POSITION
END
ELSE
BEGIN
    PRINT '✗ RepairWorkflowHistory 表不存在，请先执行创建脚本'
    RETURN
END

-- 2. 检查视图是否存在
PRINT ''
PRINT '2. 检查工作流历史视图:'
IF EXISTS (SELECT * FROM sys.views WHERE object_id = OBJECT_ID(N'[dbo].[V_RepairWorkflowHistoryDetails]'))
BEGIN
    PRINT '✓ V_RepairWorkflowHistoryDetails 视图已存在'
END
ELSE
BEGIN
    PRINT '✗ V_RepairWorkflowHistoryDetails 视图不存在'
END

-- 3. 检查存储过程是否存在
PRINT ''
PRINT '3. 检查存储过程:'
IF EXISTS (SELECT * FROM sys.procedures WHERE object_id = OBJECT_ID(N'[dbo].[sp_AddWorkflowHistory]'))
BEGIN
    PRINT '✓ sp_AddWorkflowHistory 存储过程已存在'
END
ELSE
BEGIN
    PRINT '✗ sp_AddWorkflowHistory 存储过程不存在'
END

IF EXISTS (SELECT * FROM sys.procedures WHERE object_id = OBJECT_ID(N'[dbo].[sp_GetRepairWorkflowHistory]'))
BEGIN
    PRINT '✓ sp_GetRepairWorkflowHistory 存储过程已存在'
END
ELSE
BEGIN
    PRINT '✗ sp_GetRepairWorkflowHistory 存储过程不存在'
END

-- 4. 获取测试数据
PRINT ''
PRINT '4. 准备测试数据:'

DECLARE @TestRepairOrderId INT;
DECLARE @AdminUserId INT;
DECLARE @ViewerUserId INT;

-- 获取第一个报修单ID
SELECT TOP 1 @TestRepairOrderId = Id FROM RepairOrders ORDER BY Id;

-- 获取用户ID
SELECT @AdminUserId = Id FROM Users WHERE Username = 'admin';
SELECT @ViewerUserId = Id FROM Users WHERE Username = 'viewer';

IF @TestRepairOrderId IS NULL
BEGIN
    PRINT '✗ 没有找到测试报修单'
    RETURN
END

IF @AdminUserId IS NULL OR @ViewerUserId IS NULL
BEGIN
    PRINT '✗ 没有找到测试用户'
    RETURN
END

PRINT '✓ 测试报修单ID: ' + CAST(@TestRepairOrderId AS NVARCHAR(10))
PRINT '✓ 管理员用户ID: ' + CAST(@AdminUserId AS NVARCHAR(10))
PRINT '✓ 查看者用户ID: ' + CAST(@ViewerUserId AS NVARCHAR(10))

-- 5. 清理旧的测试数据
PRINT ''
PRINT '5. 清理旧的测试数据:'
DELETE FROM RepairWorkflowHistory WHERE RepairOrderId = @TestRepairOrderId;
PRINT '✓ 已清理报修单 ' + CAST(@TestRepairOrderId AS NVARCHAR(10)) + ' 的历史记录'

-- 6. 插入测试历史记录
PRINT ''
PRINT '6. 插入测试历史记录:'

-- 创建报修单
EXEC sp_AddWorkflowHistory 
    @RepairOrderId = @TestRepairOrderId,
    @UserId = @ViewerUserId,
    @Action = '创建报修单',
    @Comment = '用户提交了新的报修申请',
    @FromStatus = NULL,
    @ToStatus = 1;

-- 分配维修人员
EXEC sp_AddWorkflowHistory 
    @RepairOrderId = @TestRepairOrderId,
    @UserId = @AdminUserId,
    @Action = '分配维修人员',
    @Comment = '将报修单分配给维修技术员',
    @FromStatus = 1,
    @ToStatus = 1;

-- 接受任务
EXEC sp_AddWorkflowHistory 
    @RepairOrderId = @TestRepairOrderId,
    @UserId = @AdminUserId,
    @Action = '接受维修任务',
    @Comment = '维修技术员接受了维修任务',
    @FromStatus = 1,
    @ToStatus = 2;

-- 开始维修
EXEC sp_AddWorkflowHistory 
    @RepairOrderId = @TestRepairOrderId,
    @UserId = @AdminUserId,
    @Action = '开始维修工作',
    @Comment = '开始对设备进行检查和维修',
    @FromStatus = 1,
    @ToStatus = 2;

-- 暂停维修
EXEC sp_AddWorkflowHistory 
    @RepairOrderId = @TestRepairOrderId,
    @UserId = @AdminUserId,
    @Action = '暂停维修工作',
    @Comment = '等待配件到货',
    @FromStatus = 2,
    @ToStatus = 6;

-- 恢复维修
EXEC sp_AddWorkflowHistory 
    @RepairOrderId = @TestRepairOrderId,
    @UserId = @AdminUserId,
    @Action = '恢复维修工作',
    @Comment = '配件已到货，继续维修',
    @FromStatus = 6,
    @ToStatus = 2;

-- 完成维修
DECLARE @CompleteData NVARCHAR(MAX) = '{"RepairDescription":"更换了主控制器和传感器","RepairCost":1500.00,"PartsUsed":"主控制器x1，温度传感器x2","TestResult":"设备运行正常，所有参数在正常范围内","RequiresApproval":true}';

EXEC sp_AddWorkflowHistory 
    @RepairOrderId = @TestRepairOrderId,
    @UserId = @AdminUserId,
    @Action = '完成维修工作',
    @Comment = '维修工作已完成，设备测试正常',
    @FromStatus = 2,
    @ToStatus = 3,
    @AdditionalData = @CompleteData;

PRINT '✓ 已插入 7 条测试历史记录'

-- 7. 查询测试结果
PRINT ''
PRINT '7. 查询测试结果:'

-- 使用存储过程查询
PRINT '使用存储过程查询历史记录:'
EXEC sp_GetRepairWorkflowHistory @RepairOrderId = @TestRepairOrderId;

-- 8. 验证视图查询
PRINT ''
PRINT '8. 验证视图查询:'
SELECT 
    Id as '历史ID',
    Action as '操作',
    UserName as '操作人',
    FromStatusName as '原状态',
    ToStatusName as '目标状态',
    Comment as '备注',
    CreatedAt as '创建时间'
FROM V_RepairWorkflowHistoryDetails
WHERE RepairOrderId = @TestRepairOrderId
ORDER BY CreatedAt DESC;

-- 9. 统计信息
PRINT ''
PRINT '9. 统计信息:'
SELECT 
    COUNT(*) as '总历史记录数',
    COUNT(DISTINCT RepairOrderId) as '涉及报修单数',
    COUNT(DISTINCT UserId) as '操作用户数'
FROM RepairWorkflowHistory;

PRINT ''
PRINT '=== 工作流历史功能测试完成 ==='
PRINT '请在应用程序中验证以下功能：'
PRINT '1. RepairWorkflowDialog 中的"操作历史"标签页'
PRINT '2. RepairOrderDetailDialog 中的"操作历史"部分'
PRINT '3. 执行工作流操作时自动记录历史'
PRINT '4. 历史记录的时间线显示效果'
