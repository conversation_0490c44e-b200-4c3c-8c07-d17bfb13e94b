using CustomerWebAPI.Models;
using CustomerWebAPI.Common;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Http;
using System;
using System.Threading.Tasks;
using System.Collections.Generic;


namespace CustomerWebAPI.Middleware
{
    public class ExceptionHandlingMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<ExceptionHandlingMiddleware> _logger;

        public ExceptionHandlingMiddleware(RequestDelegate next, ILogger<ExceptionHandlingMiddleware> logger)
        {
            _next = next;
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            try
            {
                await _next(context);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, 
                    "An error occurred processing {Path}. Error: {Error}", 
                    context.Request.Path, 
                    ex.Message);
                await HandleExceptionAsync(context, ex);
            }
        }

        private static async Task HandleExceptionAsync(HttpContext context, Exception ex)
        {
            context.Response.ContentType = "application/json";
            
            var response = ex switch
            {
                UnauthorizedAccessException => ApiResponse<object>.Fail(ex.Message, StatusCodes.Status401Unauthorized),
                ArgumentException => ApiResponse<object>.Fail(ex.Message, StatusCodes.Status400BadRequest),
                KeyNotFoundException => ApiResponse<object>.Fail(ex.Message, StatusCodes.Status404NotFound),
                _ => ApiResponse<object>.Fail(ex.Message, StatusCodes.Status500InternalServerError)
            };

            context.Response.StatusCode = response.Code;
            await context.Response.WriteAsJsonAsync(response);
        }
    }
} 