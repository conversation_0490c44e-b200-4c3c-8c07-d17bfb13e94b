using Microsoft.AspNetCore.Mvc;
using CoreHub.Web.Services;
using CoreHub.Shared.Services;
using System.Diagnostics;

namespace CoreHub.Web.Controllers
{
    /// <summary>
    /// 日志使用示例控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class LoggingExampleController : ControllerBase
    {
        private readonly IApplicationLogger _logger;

        public LoggingExampleController(IApplicationLogger logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// 测试不同级别的日志记录
        /// </summary>
        /// <returns></returns>
        [HttpGet("test-levels")]
        public IActionResult TestLogLevels()
        {
            _logger.LogDebug("这是一条调试信息");
            _logger.LogInformation("这是一条信息日志");
            _logger.LogWarning("这是一条警告日志");

            try
            {
                // 模拟一个错误
                throw new InvalidOperationException("这是一个测试异常");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "捕获到测试异常");
            }

            return Ok(new { message = "日志级别测试完成" });
        }

        /// <summary>
        /// 测试用户操作日志
        /// </summary>
        /// <returns></returns>
        [HttpPost("test-user-action")]
        public IActionResult TestUserAction([FromBody] UserActionRequest request)
        {
            var userId = User.Identity?.Name ?? "anonymous";
            
            _logger.LogUserAction(userId, request.Action, new
            {
                request.ResourceId,
                request.Details,
                Timestamp = DateTime.UtcNow,
                UserAgent = Request.Headers.UserAgent.ToString(),
                IpAddress = HttpContext.Connection.RemoteIpAddress?.ToString()
            });

            return Ok(new { message = "用户操作日志记录完成" });
        }

        /// <summary>
        /// 测试性能监控日志
        /// </summary>
        /// <returns></returns>
        [HttpGet("test-performance")]
        public async Task<IActionResult> TestPerformance()
        {
            var stopwatch = Stopwatch.StartNew();

            // 模拟一些耗时操作
            await Task.Delay(Random.Shared.Next(100, 2000));

            stopwatch.Stop();

            _logger.LogPerformance("TestPerformanceOperation", stopwatch.ElapsedMilliseconds, new
            {
                OperationType = "API调用",
                Endpoint = "/api/LoggingExample/test-performance",
                Parameters = new { delay = "随机延迟" }
            });

            return Ok(new 
            { 
                message = "性能监控测试完成", 
                duration = stopwatch.ElapsedMilliseconds 
            });
        }

        /// <summary>
        /// 测试安全事件日志
        /// </summary>
        /// <returns></returns>
        [HttpPost("test-security-event")]
        public IActionResult TestSecurityEvent([FromBody] SecurityEventRequest request)
        {
            var userId = User.Identity?.Name;

            _logger.LogSecurityEvent(request.EventType, userId, new
            {
                request.Description,
                IpAddress = HttpContext.Connection.RemoteIpAddress?.ToString(),
                UserAgent = Request.Headers.UserAgent.ToString(),
                Timestamp = DateTime.UtcNow,
                Severity = request.Severity
            });

            return Ok(new { message = "安全事件日志记录完成" });
        }

        /// <summary>
        /// 测试结构化日志
        /// </summary>
        /// <returns></returns>
        [HttpGet("test-structured")]
        public IActionResult TestStructuredLogging()
        {
            var orderData = new
            {
                OrderId = Guid.NewGuid(),
                CustomerId = "CUST001",
                Amount = 299.99m,
                Currency = "CNY",
                Items = new[]
                {
                    new { ProductId = "PROD001", Quantity = 2, Price = 149.99m },
                    new { ProductId = "PROD002", Quantity = 1, Price = 0.01m }
                }
            };

            _logger.LogInformation("订单创建成功: {@OrderData}", orderData);

            return Ok(new { message = "结构化日志测试完成", orderData });
        }
    }

    /// <summary>
    /// 用户操作请求模型
    /// </summary>
    public class UserActionRequest
    {
        public string Action { get; set; } = string.Empty;
        public string? ResourceId { get; set; }
        public string? Details { get; set; }
    }

    /// <summary>
    /// 安全事件请求模型
    /// </summary>
    public class SecurityEventRequest
    {
        public string EventType { get; set; } = string.Empty;
        public string? Description { get; set; }
        public string Severity { get; set; } = "Medium";
    }
}
