﻿<Project Sdk="Microsoft.NET.Sdk.Razor">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <SupportedPlatform Include="browser" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="MudBlazor" Version="7.8.0" />
    <PackageReference Include="Microsoft.AspNetCore.Components.Web" Version="8.0.15" />
    <PackageReference Include="Microsoft.AspNetCore.Components.Authorization" Version="8.0.15" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="8.0.3" />
    <PackageReference Include="SqlSugar" Version="5.1.4.196" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="System.Data.SqlClient" Version="4.8.6" />
    <PackageReference Include="FluentValidation" Version="11.9.0" />
  </ItemGroup>

</Project>
