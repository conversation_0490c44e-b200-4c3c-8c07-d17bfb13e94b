﻿using System.Text.Json.Serialization;

namespace MauiScanManager.Models
{
    public class RTcpFiveLayers
    {
        [JsonPropertyName("QCSuggestion")]
        public string QCSuggestion { get; set; } = string.Empty;

        [JsonPropertyName("TakeSamples")]
        public string TakeSamples { get; set; } = string.Empty;

        [JsonPropertyName("YarnTypeCD")]
        public string YarnTypeCD { get; set; } = string.Empty;
        [JsonPropertyName("YarnTypeR2R")]
        public string YarnTypeR2R { get; set; } = string.Empty;
        [JsonPropertyName("JW")]
        public string JW { get; set; } = string.Empty;
        [JsonPropertyName("IsSendSS")]
        public string sendSS { get; set; } = string.Empty;
        [JsonPropertyName("IsBatchNoOK")]
        public string IsBatchNoOK { get; set; } = string.Empty;
        


    }
}
