using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using MauiScanManager.Models;
using MauiScanManager.Services;
using System.Collections.ObjectModel;
using System.Threading.Tasks;

namespace MauiScanManager.ViewModels
{
    public enum YarnCarScanStep { LocationNo, CarNo, Done }

    public partial class YarnCarLocationViewModel : BaseOperationViewModel
    {
        private readonly IYarnCarLocationService _service;
        private readonly IPlatformLoadingService _loadingService;

        [ObservableProperty]
        [NotifyCanExecuteChangedFor(nameof(SaveCommand))]
        private string locationNo;

        [ObservableProperty]
        private ObservableCollection<string> carNos = new();

        [ObservableProperty]
        private string carNo;

        [ObservableProperty]
        private YarnCarScanStep scanStep = YarnCarScanStep.LocationNo;

        [ObservableProperty]
        private string scanPrompt = "请扫描架位号";

        [ObservableProperty]
        [NotifyCanExecuteChangedFor(nameof(SaveCommand))]
        private bool isBusy;

        [RelayCommand(CanExecute = nameof(CanSave))]
        private async Task SaveAsync()
        {
            if (string.IsNullOrWhiteSpace(LocationNo) || CarNos.Count == 0)
            {
                await _dialogService.ShowWarningAsync("请先扫描架位号和至少一个车号");
                return;
            }
            IsBusy = true;
            try
            {
                var carNosStr = string.Join(",", CarNos);
                var result = await _service.SaveBatchAsync(LocationNo, carNosStr);
                if (!result.IsSuccess)
                {
                    await _dialogService.ShowErrorAsync($"保存失败：{result.ErrorMessage}");
                    return;
                }
                await _dialogService.ShowSuccessAsync("全部保存成功！");
                ResetState();
            }
            finally
            {
                IsBusy = false;
            }
        }

        public bool CanSave() => !string.IsNullOrWhiteSpace(LocationNo) && CarNos.Count > 0 && !IsBusy;

        public YarnCarLocationViewModel(
            IScanService scanService,
            IDialogService dialogService,
            IYarnCarLocationService service,
            IPlatformLoadingService loadingService)
            : base(scanService, dialogService)
        {
            _service = service;
            _loadingService = loadingService;
            CarNos.CollectionChanged += (s, e) => SaveCommand.NotifyCanExecuteChanged();
        }

        public override void Initialize(Operation operation)
        {
            base.Initialize(operation);
            ResetState();
        }

        protected override void ProcessScanResult(string code, string type, byte[] codeSource)
        {
            if (string.IsNullOrEmpty(code)) return;
            MainThread.BeginInvokeOnMainThread(async () =>
            {
                await ProcessScanStateAsync(code);
            });
        }

        private async Task ProcessScanStateAsync(string code)
        {
            switch (ScanStep)
            {
                case YarnCarScanStep.LocationNo:
                    LocationNo = code;
                    CarNos.Clear();
                    ChangeToCarNoStep();
                    break;
                case YarnCarScanStep.CarNo:
                    if (!string.IsNullOrWhiteSpace(code) && !CarNos.Contains(code))
                    {
                        CarNos.Add(code);
                    }
                    CarNo = code;                   
                    ScanPrompt = "请扫描车号";
                    break;
            }
        }

        private void ResetState()
        {
            LocationNo = string.Empty;
            CarNo = string.Empty;
            CarNos.Clear();
            ChangeToLocationNoStep();
        }

        [RelayCommand]
        private async Task ClearStateAsync()
        {
            bool confirm = await _dialogService.ShowConfirmAsync(
                "确认清空",
                "确定要清空所有输入吗？",
                "确定",
                "取消");
            if (!confirm) return;
            ResetState();
        }

        [RelayCommand]
        private void AddCarNo()
        {
            if (string.IsNullOrWhiteSpace(LocationNo))
            {
                _ = _dialogService.ShowWarningAsync("请先输入或扫描架位号");
                return;
            }
            if (!string.IsNullOrWhiteSpace(CarNo) && !CarNos.Contains(CarNo))
            {
                CarNos.Add(CarNo);
                CarNo = string.Empty;
            }
        }

        private void ChangeToCarNoStep()
        {
            ScanStep = YarnCarScanStep.CarNo;
            ScanPrompt = "请扫描车号";
        }

        private void ChangeToLocationNoStep()
        {
            ScanStep = YarnCarScanStep.LocationNo;
            ScanPrompt = "请扫描架位号";
        }

        partial void OnLocationNoChanged(string value)
        {
            if (!string.IsNullOrWhiteSpace(value))
            {
                ChangeToCarNoStep();
            }
            else
            {
                ChangeToLocationNoStep();
            }
        }
    }
} 