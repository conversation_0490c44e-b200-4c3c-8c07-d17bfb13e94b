using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;

namespace CustomerWebAPI.Database
{
    public class RetryPolicy
    {
        public static async Task<T> ExecuteWithRetryAsync<T>(
            Func<Task<T>> action,
            ILogger logger,
            int maxRetries = 3,
            int retryDelayMs = 1000)
        {
            for (int i = 0; i <= maxRetries; i++)
            {
                try
                {
                    return await action();
                }
                catch (Exception ex) when (ShouldRetry(ex))
                {
                    logger.LogError(ex, $"数据库操作失败 (尝试 {i + 1}/{maxRetries + 1}): {ex.Message}");
                    
                    if (i == maxRetries) throw;
                    await Task.Delay(retryDelayMs * (i + 1));
                }
            }
            throw new Exception("Unexpected code path");
        }

        private static bool ShouldRetry(Exception ex)
        {
            if (ex is SqlException sqlEx)
            {
                var retryableErrors = new Dictionary<int, string>
                {
                    { -2, "超时" },
                    { -1, "连接失败" },
                    { 2, "连接断开" },
                    { 53, "网络或实例不可用" },
                    { 121, "传输层错误" },
                    { 233, "连接握手失败" },
                    { 10053, "软件导致连接中止" },
                    { 10054, "远程主机强迫关闭了连接" },
                    { 10060, "连接超时" },
                    { 40143, "连接被服务器终止" },
                    { 40197, "服务错误" },
                    { 40501, "服务正忙" },
                    { 40613, "数据库不可用" },
                    { 49918, "无法处理请求" },
                    { 49919, "无法处理创建或更新请求" },
                    { 49920, "服务太忙，无法处理请求" }
                };

                if (retryableErrors.ContainsKey(sqlEx.Number))
                {
                    var errorMessage = retryableErrors[sqlEx.Number];
                    return true;
                }
                return false;
            }
            
            return ex.Message.Contains("连接超时") || 
                   ex.Message.Contains("连接失败") || 
                   ex.Message.Contains("远程主机强迫关闭了一个现有的连接");
        }
    }
} 