using CoreHub.Shared.Data;
using CoreHub.Shared.Models.Database;
using Microsoft.Extensions.Logging;
using SqlSugar;

namespace CoreHub.Shared.Services
{
    /// <summary>
    /// 设备型号服务实现
    /// </summary>
    public class EquipmentModelService : IEquipmentModelService
    {
        private readonly DatabaseContext _dbContext;
        private readonly ILogger<EquipmentModelService> _logger;

        public EquipmentModelService(DatabaseContext dbContext, ILogger<EquipmentModelService> logger)
        {
            _dbContext = dbContext;
            _logger = logger;
        }

        public async Task<List<EquipmentModel>> GetAllEquipmentModelsAsync()
        {
            try
            {
                return await _dbContext.Db.Queryable<EquipmentModel>()
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取所有设备型号失败");
                throw;
            }
        }

        public async Task<EquipmentModel?> GetEquipmentModelByIdAsync(int id)
        {
            try
            {
                return await _dbContext.Db.Queryable<EquipmentModel>()
                    .Where(em => em.Id == id)
                    .FirstAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据ID获取设备型号失败: {id}", id);
                throw;
            }
        }

        public async Task<EquipmentModel?> GetEquipmentModelByCodeAsync(string code)
        {
            try
            {
                return await _dbContext.Db.Queryable<EquipmentModel>()
                    .Where(em => em.Code == code)
                    .FirstAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据编码获取设备型号失败: {code}", code);
                throw;
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage)> CreateEquipmentModelAsync(EquipmentModel equipmentModel)
        {
            try
            {
                // 检查编码是否存在
                if (await IsCodeExistsAsync(equipmentModel.Code))
                {
                    return (false, "设备型号编码已存在");
                }

                equipmentModel.CreatedAt = DateTime.Now;
                var result = await _dbContext.Db.Insertable(equipmentModel).ExecuteReturnIdentityAsync();
                
                _logger.LogInformation("创建设备型号成功: {name} ({code})", equipmentModel.Name, equipmentModel.Code);
                return (true, string.Empty);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建设备型号失败: {name}", equipmentModel.Name);
                return (false, $"创建设备型号失败: {ex.Message}");
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage)> UpdateEquipmentModelAsync(EquipmentModel equipmentModel)
        {
            try
            {
                // 检查编码是否存在（排除自己）
                if (await IsCodeExistsAsync(equipmentModel.Code, equipmentModel.Id))
                {
                    return (false, "设备型号编码已存在");
                }

                equipmentModel.UpdatedAt = DateTime.Now;
                var result = await _dbContext.Db.Updateable(equipmentModel).ExecuteCommandAsync();
                
                if (result > 0)
                {
                    _logger.LogInformation("更新设备型号成功: {name} ({code})", equipmentModel.Name, equipmentModel.Code);
                    return (true, string.Empty);
                }
                else
                {
                    return (false, "设备型号不存在或未发生变更");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新设备型号失败: {id}", equipmentModel.Id);
                return (false, $"更新设备型号失败: {ex.Message}");
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage)> DeleteEquipmentModelAsync(int id)
        {
            try
            {
                // 检查是否有关联的设备
                var hasEquipment = await _dbContext.Db.Queryable<Equipment>()
                    .Where(e => e.ModelId == id)
                    .AnyAsync();

                if (hasEquipment)
                {
                    return (false, "该设备型号下还有设备，无法删除");
                }

                var result = await _dbContext.Db.Deleteable<EquipmentModel>()
                    .Where(em => em.Id == id)
                    .ExecuteCommandAsync();

                if (result > 0)
                {
                    _logger.LogInformation("删除设备型号成功: {id}", id);
                    return (true, string.Empty);
                }
                else
                {
                    return (false, "设备型号不存在");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除设备型号失败: {id}", id);
                return (false, $"删除设备型号失败: {ex.Message}");
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage)> ToggleStatusAsync(int id)
        {
            try
            {
                var equipmentModel = await GetEquipmentModelByIdAsync(id);
                if (equipmentModel == null)
                {
                    return (false, "设备型号不存在");
                }

                equipmentModel.IsEnabled = !equipmentModel.IsEnabled;
                equipmentModel.UpdatedAt = DateTime.Now;

                var result = await _dbContext.Db.Updateable(equipmentModel)
                    .UpdateColumns(em => new { em.IsEnabled, em.UpdatedAt })
                    .ExecuteCommandAsync();

                if (result > 0)
                {
                    _logger.LogInformation("切换设备型号状态成功: {id} -> {status}", id, equipmentModel.IsEnabled);
                    return (true, string.Empty);
                }
                else
                {
                    return (false, "操作失败");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "切换设备型号状态失败: {id}", id);
                return (false, $"操作失败: {ex.Message}");
            }
        }

        public async Task<List<EquipmentModel>> GetEnabledEquipmentModelsAsync()
        {
            try
            {
                return await _dbContext.Db.Queryable<EquipmentModel>()
                    .Where(em => em.IsEnabled)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取启用的设备型号列表失败");
                throw;
            }
        }

        public async Task<List<EquipmentModel>> GetEquipmentModelsByCategoryAsync(string category)
        {
            try
            {
                return await _dbContext.Db.Queryable<EquipmentModel>()
                    .Where(em => em.Category == category && em.IsEnabled)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据类别获取设备型号失败: {category}", category);
                throw;
            }
        }

        public async Task<List<string>> GetAllCategoriesAsync()
        {
            try
            {
                return await _dbContext.Db.Queryable<EquipmentModel>()
                    .Where(em => em.IsEnabled)
                    .GroupBy(em => em.Category)
                    .Select(g => g.Category)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取所有设备类别失败");
                throw;
            }
        }

        public async Task<bool> IsCodeExistsAsync(string code, int? excludeId = null)
        {
            try
            {
                var query = _dbContext.Db.Queryable<EquipmentModel>()
                    .Where(em => em.Code == code);

                if (excludeId.HasValue)
                {
                    query = query.Where(em => em.Id != excludeId.Value);
                }

                return await query.AnyAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查设备型号编码是否存在失败: {code}", code);
                throw;
            }
        }
    }
}
