# SSL 证书到期更换完整指南

本指南详细说明如何在 CoreHub.Web 应用中安全地更换即将到期的 SSL 证书。

## 🔔 证书监控和提醒

### 自动监控
系统已配置自动证书监控：
- **检查频率**: 每24小时检查一次
- **提前警告**: 30天内到期发出警告
- **紧急警告**: 7天内到期发出紧急警告
- **过期警告**: 证书过期后立即警告

### 手动检查
```bash
# 通过 API 立即检查证书状态
curl -k -X POST https://172.16.9.111:8081/api/SslManagement/check-certificate-now
```

## 📋 证书更换前准备

### 1. 获取新证书
确保新证书满足以下要求：
- **格式**: PKCS#12 (.pfx/.p12)
- **包含**: 私钥 + 公钥证书 + 证书链
- **域名**: 与当前证书相同或兼容
- **有效期**: 足够长的有效期

### 2. 验证新证书
```bash
# 使用 API 验证新证书
curl -k -X POST https://172.16.9.111:8081/api/SslManagement/validate-new-certificate \
  -H "Content-Type: application/json" \
  -d '{
    "certificatePath": "path/to/new/certificate.pfx",
    "password": "new_certificate_password"
  }'
```

### 3. 准备维护窗口
- 选择业务影响最小的时间
- 通知相关用户系统维护
- 准备回滚方案

## 🔄 证书更换步骤

### 方法一：通过 API 更换（推荐）

#### 1. 上传新证书文件
```bash
# 将新证书文件复制到服务器
scp new_certificate.pfx user@server:/path/to/CoreHub.Web/Data/
```

#### 2. 验证新证书
```bash
curl -k -X POST https://172.16.9.111:8081/api/SslManagement/validate-new-certificate \
  -H "Content-Type: application/json" \
  -d '{
    "certificatePath": "Data/new_certificate.pfx",
    "password": "new_password"
  }'
```

#### 3. 执行证书更换
```bash
curl -k -X POST https://172.16.9.111:8081/api/SslManagement/replace-certificate \
  -H "Content-Type: application/json" \
  -d '{
    "newCertificatePath": "Data/new_certificate.pfx",
    "newPassword": "new_password",
    "createBackup": true
  }'
```

#### 4. 更新配置文件
```json
{
  "Kestrel": {
    "EndPoints": {
      "Https": {
        "Certificate": {
          "Path": "Data/new_certificate.pfx",
          "Password": "new_password"
        }
      }
    }
  }
}
```

#### 5. 重启应用程序
```bash
# 停止应用程序
sudo systemctl stop corehub-web

# 启动应用程序
sudo systemctl start corehub-web

# 检查状态
sudo systemctl status corehub-web
```

### 方法二：手动更换

#### 1. 停止应用程序
```bash
sudo systemctl stop corehub-web
```

#### 2. 备份当前证书
```bash
cp Data/api_saintyeartex_com.pfx Data/CertBackups/api_saintyeartex_com_backup_$(date +%Y%m%d_%H%M%S).pfx
```

#### 3. 替换证书文件
```bash
cp new_certificate.pfx Data/api_saintyeartex_com.pfx
```

#### 4. 更新配置文件
编辑 `appsettings.json`：
```json
{
  "Kestrel": {
    "EndPoints": {
      "Https": {
        "Certificate": {
          "Password": "new_password"
        }
      }
    }
  }
}
```

#### 5. 启动应用程序
```bash
sudo systemctl start corehub-web
```

## ✅ 验证更换结果

### 1. 检查应用程序状态
```bash
# 检查服务状态
sudo systemctl status corehub-web

# 检查日志
sudo journalctl -u corehub-web -f
```

### 2. 验证证书信息
```bash
# 通过 API 获取证书信息
curl -k https://172.16.9.111:8081/api/SslManagement/certificate-info

# 使用 OpenSSL 验证
openssl s_client -connect 172.16.9.111:8081 -servername api.saintyeartex.com
```

### 3. 测试应用程序功能
```bash
# 测试 HTTPS 访问
curl -k https://172.16.9.111:8081/

# 测试 API 功能
curl -k https://172.16.9.111:8081/api/LoggingExample/test-levels
```

## 🔙 回滚方案

如果新证书有问题，可以快速回滚：

### 通过 API 回滚
```bash
curl -k -X POST https://172.16.9.111:8081/api/SslManagement/rollback-certificate \
  -H "Content-Type: application/json" \
  -d '{
    "backupPath": "Data/CertBackups/api_saintyeartex_com_backup_20250712_223000.pfx"
  }'
```

### 手动回滚
```bash
# 停止应用程序
sudo systemctl stop corehub-web

# 恢复备份证书
cp Certificates/Backups/api_saintyeartex_com_backup_20250712_223000.pfx Certificates/api_saintyeartex_com.pfx

# 恢复配置文件
# 编辑 appsettings.json，恢复原密码

# 启动应用程序
sudo systemctl start corehub-web
```

## 📊 监控和维护

### 查看更换历史
```bash
curl -k https://172.16.9.111:8081/api/SslManagement/replacement-history
```

### 设置监控告警
```bash
# 添加到 crontab，每日检查
0 9 * * * curl -s https://172.16.9.111:8081/api/SslManagement/expiry-check | \
  jq '.data.isExpiring' | grep -q true && \
  echo "SSL证书即将过期" | mail -s "SSL证书告警" <EMAIL>
```

## 🚨 故障排查

### 常见问题

#### 1. 证书密码错误
```
错误: 指定的网络密码不正确
解决: 检查新证书密码是否正确
```

#### 2. 证书文件损坏
```
错误: 无法加载证书文件
解决: 重新下载证书文件，检查文件完整性
```

#### 3. 证书域名不匹配
```
错误: 证书主题名称与服务器名称不匹配
解决: 确保证书包含正确的域名
```

#### 4. 应用程序无法启动
```
解决步骤:
1. 检查证书文件路径和权限
2. 验证配置文件语法
3. 查看应用程序日志
4. 如有必要，回滚到备份证书
```

## 📅 最佳实践

### 1. 提前规划
- 在证书到期前30天开始准备
- 在测试环境先验证新证书
- 准备详细的更换计划

### 2. 自动化
- 使用 Let's Encrypt 等服务自动续期
- 编写自动化脚本处理证书更换
- 设置监控和告警

### 3. 安全措施
- 始终创建备份
- 使用强密码保护证书
- 限制证书文件访问权限
- 定期审查证书状态

### 4. 文档记录
- 记录每次证书更换
- 保存配置变更历史
- 维护应急联系信息

通过遵循这个指南，你可以安全、高效地完成 SSL 证书的更换，确保 CoreHub.Web 应用的持续安全运行。
