﻿using System.ComponentModel.DataAnnotations;


namespace CustomerWebAPI.Models.Twist
{
    public class TwistCreateBox
    {
        [Required(ErrorMessage = "缸号必填")]
        public string TaskNO { get; set; } = string.Empty;

        [Required(ErrorMessage = "单包个数大于0")]
        public int ConeNum { get; set; } 

        [Required(ErrorMessage = "单包重量大于0")]
        public float BoxWeight { get; set; }

        [Required(ErrorMessage = "包数大于0")]
        public int BoxNum { get; set; } 
    }
}
