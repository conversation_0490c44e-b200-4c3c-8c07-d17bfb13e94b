using MauiScanManager.Constants;
using MauiScanManager.Models;
using Microsoft.Extensions.Logging;

namespace MauiScanManager.Services
{
    public class ScanOrderService : IScanOrderService
    {
        private readonly IApiService _apiService;
        private readonly IPlatformLoadingService _loadingService;
        private readonly ILogger<ScanOrderService> _logger;

        public ScanOrderService(
            IApiService apiService,
            IPlatformLoadingService loadingService,
            ILogger<ScanOrderService> logger)
        {
            _apiService = apiService;
            _loadingService = loadingService;
            _logger = logger;
        }

        public async Task<ServiceResult<bool>> SaveAsync(ScanOrder model)
        {
            _loadingService.ShowNativeLoading("正在保存扫描订单...");
            try
            {
                var response = await _apiService.PostAsync<bool>(
                    ApiEndpoints.Order.Save, 
                    model);

                if (response.Success)
                {
                    _logger.LogInformation("扫描订单保存成功");
                }
                else
                {
                    _logger.LogWarning("扫描订单保存失败: {Message}", response.Message);
                }

                return response.Success 
                    ? ServiceResult<bool>.Success(response.Data)
                    : ServiceResult<bool>.Failure(response.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存扫描订单失败");
                return ServiceResult<bool>.Failure(ex.Message);
            }
            finally
            {
                _loadingService.HideNativeLoading();
            }
        }
    }
} 