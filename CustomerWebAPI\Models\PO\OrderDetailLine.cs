﻿using System;
using Dapper.Contrib.Extensions;

namespace CustomerWebAPI.Models
{
    [Table("OrderDetailLine")]
    public class OrderDetailLine
    {
        /// <summary>
        /// 
        /// </summary>
        [ExplicitKey]
        public Guid Iden { get; set; }

        public Guid OrderDetailId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string ItemKey { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string ItemSequenceNumber { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string BuyerNumber { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string ShortDescription { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string LongDescription { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string SkuNumber { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string CountryOfOriginCode { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string Construction { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string YarnCount { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string FabricWidth { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string PackagingTerms { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public decimal? Quantity { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string UnitOfMeasureCode { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string PackMethodCode { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string DestinationKey { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string EarliestDate { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string LatestDate { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string IsInspectionRequired { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string ShipmentMethodCode { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public int? UpperVariance { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public int? LowerVariance { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string VarianceTypeCode { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public decimal? PricePerUnit { get; set; }
    }
}