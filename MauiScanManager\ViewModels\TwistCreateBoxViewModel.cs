﻿using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using MauiScanManager.Models;
using MauiScanManager.Services;
using System.Collections.ObjectModel;


namespace MauiScanManager.ViewModels
{

    public partial class TwistCreateBoxViewModel : BaseOperationViewModel
    {
        private readonly IAudioService _audioService;
        private readonly ITwistService _twistService;
        private readonly IPlatformLoadingService _loadingService;

        [ObservableProperty]
        private string taskNO = string.Empty;

        [ObservableProperty]
        private int coneNum;

        [ObservableProperty]
        private float boxWeight;

        [ObservableProperty]
        private int boxNum;

        public TwistCreateBoxViewModel(
            IScanService scanService,
            IDialogService dialogService,
            IAudioService audioService,
            ITwistService twistService,
            IPlatformLoadingService loadingService)
            : base(scanService, dialogService)
        {
            _audioService = audioService;
            _twistService = twistService;
            _loadingService = loadingService;
         //   SizingMachines = new ObservableCollection<string> { "A01", "A02", "A03", "A04", "A05", "A06" };
        }


        public override async void Initialize(Operation operation)
        {
            base.Initialize(operation);
            ResetScanState();
        }

        protected override void ProcessScanResult(string code, string type, byte[] codeSource)
        {
            if (string.IsNullOrEmpty(code)) return;

            MainThread.BeginInvokeOnMainThread(async () =>
            {
                try
                {
                    await ProcessScanStateAsync(code);
                }
                catch (Exception ex)
                {
                    await _dialogService.ShowErrorAsync($"处理扫描结果失败：{ex.Message}");
                }
            });
        }

        private async Task ProcessScanStateAsync(string code)
        {
            if (string.IsNullOrWhiteSpace(code))
            {
                await _dialogService.ShowWarningAsync("无效的缸号");
                return;
            }

            TaskNO = code;
        }

        private string GetFriendlyErrorMessage(Exception ex)
        {
            if (ex.Message.Contains("HttpClient.Timeout") ||
                ex.Message.Contains("The request was canceled"))
            {
                return "网络请求超时，请检查网络连接后重试";
            }
            return ex.Message;
        }


        [RelayCommand]
        private async Task TwCreateBox()
        {
            if (string.IsNullOrEmpty(TaskNO) || BoxNum==0 || BoxWeight ==0 || ConeNum ==0)
            {
                await _dialogService.ShowWarningAsync("请输入正确信息！");
                return;
            }

            try
            {
                _loadingService?.ShowNativeLoading("正在保存...");
                var model = new TwistCreateBox
                {
                    TaskNO = TaskNO,
                    BoxNum = BoxNum,
                    BoxWeight = BoxWeight,
                    ConeNum=ConeNum
                };

                var result = await _twistService.CreateBoxByScanAsync(model);
                if (!result.IsSuccess)
                {
                    await _dialogService.ShowErrorAsync(result.ErrorMessage);
                    return;
                }
                await _dialogService.ShowSuccessAsync("装箱成功！");
                ResetScanState();
            }
            catch (Exception ex)
            {
                await _dialogService.ShowErrorAsync(GetFriendlyErrorMessage(ex));
            }
            finally
            {
                _loadingService?.HideNativeLoading();
            }
        }

        [RelayCommand]
      
        private void ResetScanState()
        {
            TaskNO = string.Empty;
            BoxNum = 1;
            ConeNum = 0;
            BoxWeight =0;

        }

    }
}
