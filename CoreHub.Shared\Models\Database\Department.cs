using SqlSugar;
using System.ComponentModel.DataAnnotations;

namespace CoreHub.Shared.Models.Database
{
    /// <summary>
    /// 部门实体
    /// </summary>
    [SugarTable("Departments")]
    public class Department
    {
        /// <summary>
        /// 部门ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        /// <summary>
        /// 部门编码（唯一）
        /// </summary>
        [SugarColumn(Length = 50, IsNullable = false)]
        [Required(ErrorMessage = "部门编码不能为空")]
        [StringLength(50, ErrorMessage = "部门编码长度不能超过50个字符")]
        public string Code { get; set; } = string.Empty;

        /// <summary>
        /// 部门名称（唯一）
        /// </summary>
        [SugarColumn(Length = 100, IsNullable = false)]
        [Required(ErrorMessage = "部门名称不能为空")]
        [StringLength(100, ErrorMessage = "部门名称长度不能超过100个字符")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 部门描述
        /// </summary>
        [SugarColumn(Length = 500, IsNullable = true)]
        [StringLength(500, ErrorMessage = "部门描述长度不能超过500个字符")]
        public string? Description { get; set; }

        /// <summary>
        /// 部门类型ID
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? DepartmentTypeId { get; set; }

        /// <summary>
        /// 父级部门ID
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? ParentId { get; set; }

        /// <summary>
        /// 部门级别
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int Level { get; set; } = 1;

        /// <summary>
        /// 排序号
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int SortOrder { get; set; } = 0;

        /// <summary>
        /// 是否启用
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 创建时间
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 创建人ID
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? CreatedBy { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// 更新人ID
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? UpdatedBy { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [SugarColumn(Length = 1000, IsNullable = true)]
        [StringLength(1000, ErrorMessage = "备注长度不能超过1000个字符")]
        public string? Remark { get; set; }

        // 导航属性
        /// <summary>
        /// 部门类型
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public DepartmentType? DepartmentType { get; set; }

        /// <summary>
        /// 子部门
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public List<Department> Children { get; set; } = new List<Department>();

        /// <summary>
        /// 父部门
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public Department? Parent { get; set; }

        /// <summary>
        /// 部门下的设备
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public List<Equipment> Equipment { get; set; } = new List<Equipment>();

        /// <summary>
        /// 部门下的位置
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public List<Location> Locations { get; set; } = new List<Location>();
    }
}
