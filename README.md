# CoreHub

CoreHub 是一个基于 .NET 8 和 Blazor 的跨平台设备管理系统，支持设备报修、维修工作流、权限管理等功能。

## 项目架构

### 技术栈
- **.NET 8.0** - 目标框架
- **Blazor** - UI框架
- **MudBlazor 7.8.0** - UI组件库
- **SqlSugar 5.1.4.196** - ORM框架
- **.NET MAUI** - 跨平台移动和桌面应用
- **ASP.NET Core** - Web应用框架

### 项目结构

```
CoreHub/
├── CoreHub.Maui/          # .NET MAUI 跨平台应用
│   ├── Platforms/         # 平台特定代码
│   │   ├── Android/       # Android 平台
│   │   ├── iOS/           # iOS 平台
│   │   ├── MacCatalyst/   # macOS 平台
│   │   └── Windows/       # Windows 平台
│   └── CoreHub.csproj     # MAUI 项目文件
├── CoreHub.Shared/        # 共享 Blazor 组件库
│   ├── Components/        # 可重用组件
│   ├── Pages/            # 页面组件
│   ├── Services/         # 业务服务
│   ├── Models/           # 数据模型
│   └── Utils/            # 工具类
├── CoreHub.Web/          # ASP.NET Core Blazor Server Web 应用
├── docs/                 # 项目文档
└── CoreHub.sln          # 解决方案文件
```

## 支持平台

### MAUI 应用
- **移动端**: Android, iOS
- **桌面端**: Windows, macOS (MacCatalyst)

### Web 应用
- 支持所有现代浏览器
- 响应式设计，适配移动和桌面设备

## 主要功能

- **设备管理**: 设备信息管理、设备型号管理
- **报修系统**: 设备报修申请、维修工单管理
- **工作流管理**: 维修流程控制、状态跟踪
- **权限管理**: 基于角色的权限控制
- **部门管理**: 部门类型、工种类型管理
- **用户管理**: 用户账户、角色分配
- **通知系统**: 跨平台消息通知
- **二维码扫描**: 设备快速识别

## 快速开始

### 环境要求

- .NET 8.0 SDK
- Visual Studio 2022 或 Visual Studio Code
- SQL Server (用于数据库)

### 运行 Web 应用

```bash
cd CoreHub.Web
dotnet run
```

### 运行 MAUI 应用

```bash
cd CoreHub.Maui
dotnet build -f net8.0-windows10.0.19041.0  # Windows
dotnet build -f net8.0-android              # Android
dotnet build -f net8.0-ios                  # iOS
dotnet build -f net8.0-maccatalyst          # macOS
```

## 数据库配置

项目使用 SQL Server 数据库，配置信息在 `appsettings.json` 中设置。

## 贡献

欢迎提交 Issue 和 Pull Request 来改进项目。

## 许可证

本项目采用 MIT 许可证。
