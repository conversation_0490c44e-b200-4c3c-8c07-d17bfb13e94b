<?xml version="1.0" encoding="utf-8" ?>
<views:BaseOperationPage
    x:Class="MauiScanManager.Views.TwistCreateBoxPage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:toolkit="http://schemas.microsoft.com/dotnet/2022/maui/toolkit"
    xmlns:viewmodels="clr-namespace:MauiScanManager.ViewModels"
    xmlns:views="clr-namespace:MauiScanManager.Views"
    Title="{Binding Operation.Description}"
    x:DataType="viewmodels:TwistCreateBoxViewModel">



    <Grid>
        <ScrollView>
            <VerticalStackLayout Padding="10" Spacing="5">



                <!--  缸号  -->
                <Label
                    FontAttributes="Bold"
                    FontSize="20"
                    Text="缸号" />
                <Entry
                    FontSize="20"
                    HeightRequest="45"
                    Text="{Binding TaskNO}" />

                <!--  包数  -->
                <Label
                    FontAttributes="Bold"
                    FontSize="20"
                    Text="包数" />
                <Entry
                    FontSize="20"
                    HeightRequest="45"
                    Keyboard="Numeric"
                    Text="{Binding BoxNum}" />

                <!--  单包个数  -->
                <Label
                    FontAttributes="Bold"
                    FontSize="20"
                    Text="单包个数" />
                <Entry
                    FontSize="20"
                    HeightRequest="45"
                    Keyboard="Numeric"
                    Text="{Binding ConeNum}" />

                <!--  单包重量  -->
                <Label
                    FontAttributes="Bold"
                    FontSize="20"
                    Text="单包重量" />
                <Entry
                    FontSize="20"
                    HeightRequest="45"
                    Keyboard="Numeric"
                    Text="{Binding BoxWeight}" />

                <!--  保存按钮  -->
                <Button
                    Margin="0,10,0,0"
                    BackgroundColor="{StaticResource Primary}"
                    Command="{Binding TwCreateBoxCommand}"
                    FontSize="20"
                    HeightRequest="48"
                    Text="保存" />


            </VerticalStackLayout>
        </ScrollView>
        <ActivityIndicator
            HorizontalOptions="Center"
            VerticalOptions="Center"
            Color="{StaticResource Primary}" />
    </Grid>
</views:BaseOperationPage> 