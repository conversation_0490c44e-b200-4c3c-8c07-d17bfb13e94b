using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using MauiScanManager.Models;
using MauiScanManager.Services;
using System.Collections.ObjectModel;

namespace MauiScanManager.ViewModels
{
    public partial class DepartmentSelectionViewModel : ObservableObject
    {
        private readonly IDepartmentService _departmentService;
        private readonly IOperationNavigationService _navigationService;
        private readonly IDialogService _dialogService;
        private readonly string _deviceId;
        private bool _isReturningFromOperation;
        private bool _isFirstLoad = true;
        private readonly IAppUpdateService _updateService;
        private readonly IPlatformLoadingService _loadingService;
        private readonly INetworkService _networkService;

        public ObservableCollection<Department> Departments { get; } = new();
        public ObservableCollection<Operation> Operations { get; } = new();

        [ObservableProperty] private bool isLoading;

        [ObservableProperty]
        [NotifyPropertyChangedFor(nameof(HasSelectedDepartment))]
        [NotifyPropertyChangedFor(nameof(CanConfirm))]
        private Department selectedDepartment;

        [ObservableProperty] [NotifyPropertyChangedFor(nameof(CanConfirm))]
        private Operation selectedOperation;

        [ObservableProperty] private bool isRefreshing;

        public bool HasSelectedDepartment => SelectedDepartment != null;
        public bool CanConfirm => SelectedDepartment != null && SelectedOperation != null;

        public string AppVersion =>
            $"v{AppInfo.Current.Version.Major}.{AppInfo.Current.Version.Minor}.{AppInfo.Current.Version.Build}";

        /// <summary>
        /// 是否为DEBUG模式（用于控制打印测试按钮显示）
        /// </summary>
        public bool IsDebugMode =>
#if DEBUG
            true;
#else
            false;
#endif

        public DepartmentSelectionViewModel(
            IDepartmentService departmentService,
            IOperationNavigationService navigationService,
            IDialogService dialogService,
            IAppUpdateService updateService,
            IPlatformLoadingService loadingService,
            INetworkService networkService)
        {
            _departmentService = departmentService;
            _navigationService = navigationService;
            _dialogService = dialogService;
            _updateService = updateService;
            _loadingService = loadingService;
            _networkService = networkService;

            _deviceId = GetDeviceId();
        }

        private string GetDeviceId()
        {
            try
            {
#if ANDROID
                        return Android.Provider.Settings.Secure.GetString(
                            Android.App.Application.Context.ContentResolver,
                            Android.Provider.Settings.Secure.AndroidId);
#else
                return string.Empty;
#endif
            }
            catch (Exception)
            {
                return string.Empty;
            }
        }

        [RelayCommand]
        private async Task InitializeAsync()
        {
            try
            {
                if (!await _networkService.CheckNetworkAsync())
                {
                    return;
                }

                // 如果更新检查还没完成，等待更新检查
                if (!_updateService.HasChecked)
                {
                    await Task.Delay(1000); // 等待一秒，让更新检查完成
                    if (!_updateService.HasChecked)
                    {
                        return; // 如果还是没完成，那么可能有问题，返回
                    }
                }

                // 如果正在更新，不加载数据
                if (_updateService.IsUpdating)
                {
                    return;
                }

                // 只有在首次加载或没有数据时才重新加载
                if (_isFirstLoad || !Departments.Any())
                {
                    await LoadDataAsync();
                    LoadStoredDefaults();
                    _isFirstLoad = false;
                }
            }
            catch (Exception ex)
            {
                await _dialogService.ShowErrorAsync($"初始化失败: {ex.Message}");
            }
        }

        [RelayCommand]
        private async Task LoadDataAsync()
        {
            if (IsLoading) return;

            try
            {
                IsLoading = true;
                _loadingService?.ShowNativeLoading("正在加载部门信息...");
                var result = await _departmentService.GetDepartmentsAsync();

                if (!result.IsSuccess)
                {
                    await _dialogService.ShowErrorAsync(result.ErrorMessage);
                    return;
                }

                Departments.Clear();
                foreach (var dept in result.Data)
                {
                    Departments.Add(dept);
                }

                await _dialogService.ShowToastAsync("加载成功");
            }
            catch (Exception ex)
            {
                string errorMessage = ex.Message;
                if (ex.Message.Contains("HttpClient.Timeout") ||
                    ex.Message.Contains("The request was canceled"))
                {
                    errorMessage = "网络请求超时，请检查网络连接后重试";
                }

                await _dialogService.ShowErrorAsync($"加载部门数据失败: {errorMessage}");
            }
            finally
            {
                IsLoading = false;
                _loadingService?.HideNativeLoading();
            }
        }

        [RelayCommand]
        private async Task LoadOperationsForDepartmentAsync(string departmentCode)
        {
            if (string.IsNullOrEmpty(departmentCode))
            {
                Operations.Clear();
                return;
            }

            if (IsLoading) return;

            try
            {
                IsLoading = true;
                _loadingService?.ShowNativeLoading("正在加载操作列表...");
                var result = await _departmentService.GetOperationsByDepartmentAsync(departmentCode);

                if (!result.IsSuccess)
                {
                    await _dialogService.ShowErrorAsync(result.ErrorMessage);
                    return;
                }

                Operations.Clear();
                foreach (var op in result.Data)
                {
                    Operations.Add(op);
                }
            }
            catch (Exception ex)
            {
                string errorMessage = ex.Message;
                if (ex.Message.Contains("HttpClient.Timeout") ||
                    ex.Message.Contains("The request was canceled"))
                {
                    errorMessage = "网络请求超时，请检查网络连接后重试";
                }

                await _dialogService.ShowErrorAsync($"加载操作数据失败: {errorMessage}");
            }
            finally
            {
                IsLoading = false;
                _loadingService?.HideNativeLoading();
            }
        }

        [RelayCommand(CanExecute = nameof(CanConfirm))]
        private async Task ConfirmAsync()
        {
            if (!CanConfirm) return;

            try
            {
                _loadingService?.ShowNativeLoading("正在验证权限...");

                if (SelectedOperation.AllowedDeviceIds.Any() &&
                    !SelectedOperation.AllowedDeviceIds.Contains(_deviceId))
                {
                    await _dialogService.ShowWarningAsync(
                        $"当前设备({_deviceId})未被授权使用此操作",
                        "访问受限");
                    return;
                }

                _isReturningFromOperation = true;

                Preferences.Set("DefaultDepartmentCode", SelectedDepartment.Code);
                Preferences.Set("DefaultOperationCode", SelectedOperation.Code);

                _loadingService?.ShowNativeLoading("正在跳转...");
                await _navigationService.NavigateToOperation(SelectedOperation);
            }
            catch (Exception ex)
            {
                await _dialogService.ShowErrorAsync($"导航失败: {ex.Message}");
            }
            finally
            {
                _loadingService?.HideNativeLoading();
            }
        }

        private async void LoadStoredDefaults()
        {
            try
            {
                var storedDepartmentCode = Preferences.Get("DefaultDepartmentCode", string.Empty);
                var storedOperationCode = Preferences.Get("DefaultOperationCode", string.Empty);

                if (!string.IsNullOrEmpty(storedDepartmentCode))
                {
                    SelectedDepartment = Departments.FirstOrDefault(d => d.Code == storedDepartmentCode);
                }

                if (!string.IsNullOrEmpty(storedOperationCode))
                {
                    SelectedOperation = Operations.FirstOrDefault(o => o.Code == storedOperationCode);

                    if (!_isReturningFromOperation && SelectedOperation?.IsAutoNavigate == true)
                    {
                        await ConfirmAsync();
                    }
                }
            }
            catch (Exception ex)
            {
                await _dialogService.ShowErrorAsync($"加载默认设置失败: {ex.Message}");
            }
        }

        partial void OnSelectedDepartmentChanged(Department value)
        {
            LoadOperationsForDepartmentAsync(value?.Code).ConfigureAwait(false);
        }

        // 添加一个公共方法用于外部触发重新加载
        public async Task RefreshDataAsync()
        {
            try
            {
                _loadingService?.ShowNativeLoading("正在刷新数据...");
                await LoadDataAsync();
                LoadStoredDefaults();
            }
            finally
            {
                _loadingService?.HideNativeLoading();
            }
        }

        [RelayCommand]
        private async Task RefreshAsync()
        {
            if (IsLoading) return;

            try
            {
                IsRefreshing = true;

                if (!await _networkService.CheckNetworkAsync())
                {
                    await _dialogService.ShowWarningAsync("请检查网络连接");
                    return;
                }

                await LoadDataAsync();
                LoadStoredDefaults();
            }
            catch (Exception ex)
            {
                await _dialogService.ShowErrorAsync($"刷新失败: {ex.Message}");
            }
            finally
            {
                IsRefreshing = false;
            }
        }

        [RelayCommand]
        private async Task NavigateToPrintTest()
        {
#if DEBUG
            try
            {
                await Shell.Current.GoToAsync("PrintTestPage");
            }
            catch (Exception ex)
            {
                await _dialogService.ShowErrorAsync($"导航到打印测试页面失败: {ex.Message}");
            }
#endif
        }
    }
}