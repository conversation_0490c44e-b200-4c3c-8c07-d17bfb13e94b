using CoreHub.Shared.Models.Database;

namespace CoreHub.Shared.Services
{
    /// <summary>
    /// 菜单服务接口
    /// </summary>
    public interface IMenuService
    {
        /// <summary>
        /// 获取用户可访问的菜单列表
        /// </summary>
        /// <param name="userId">用户ID，null表示未登录用户</param>
        /// <returns>菜单列表</returns>
        Task<List<MenuItem>> GetUserMenusAsync(int? userId = null);

        /// <summary>
        /// 获取所有菜单（管理用）
        /// </summary>
        /// <returns>所有菜单列表</returns>
        Task<List<MenuItem>> GetAllMenusAsync();

        /// <summary>
        /// 创建菜单
        /// </summary>
        /// <param name="menuItem">菜单信息</param>
        /// <returns>操作结果</returns>
        Task<(bool IsSuccess, string ErrorMessage)> CreateMenuAsync(MenuItem menuItem);

        /// <summary>
        /// 更新菜单
        /// </summary>
        /// <param name="menuItem">菜单信息</param>
        /// <returns>操作结果</returns>
        Task<(bool IsSuccess, string ErrorMessage)> UpdateMenuAsync(MenuItem menuItem);

        /// <summary>
        /// 删除菜单
        /// </summary>
        /// <param name="menuId">菜单ID</param>
        /// <returns>操作结果</returns>
        Task<(bool IsSuccess, string ErrorMessage)> DeleteMenuAsync(int menuId);

        /// <summary>
        /// 初始化默认菜单数据
        /// </summary>
        /// <returns>操作结果</returns>
        Task<(bool IsSuccess, string ErrorMessage)> InitializeDefaultMenusAsync();
    }
} 