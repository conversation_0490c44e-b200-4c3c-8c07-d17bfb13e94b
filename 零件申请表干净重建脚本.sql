-- =============================================
-- 零件申请表干净重建脚本
-- 创建日期: 2025-01-07
-- 版本: 4.0.0
-- 描述: 完全重建RepairOrderPartRequests表（无审批流程）
-- =============================================

USE [EquipmentManagement]
GO

PRINT '开始重建零件申请表...'
PRINT '========================================'

-- =============================================
-- 第一步：检查基础表是否存在
-- =============================================

IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[RepairOrders]') AND type in (N'U'))
BEGIN
    PRINT '❌ RepairOrders表不存在，请先创建基础表结构'
    RETURN
END

IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[Users]') AND type in (N'U'))
BEGIN
    PRINT '❌ Users表不存在，请先创建基础表结构'
    RETURN
END

PRINT '✓ 基础表存在，继续执行'

-- =============================================
-- 第二步：完全删除旧的对象
-- =============================================

PRINT '1. 删除所有相关对象...'

-- 删除视图
IF EXISTS (SELECT * FROM sys.views WHERE object_id = OBJECT_ID(N'[dbo].[V_RepairOrderPartRequestDetails]'))
BEGIN
    DROP VIEW [dbo].[V_RepairOrderPartRequestDetails]
    PRINT '✓ 删除V_RepairOrderPartRequestDetails视图'
END

IF EXISTS (SELECT * FROM sys.views WHERE object_id = OBJECT_ID(N'[dbo].[V_RepairOrderPartSummary]'))
BEGIN
    DROP VIEW [dbo].[V_RepairOrderPartSummary]
    PRINT '✓ 删除V_RepairOrderPartSummary视图'
END

IF EXISTS (SELECT * FROM sys.views WHERE object_id = OBJECT_ID(N'[dbo].[V_RepairOrdersWithParts]'))
BEGIN
    DROP VIEW [dbo].[V_RepairOrdersWithParts]
    PRINT '✓ 删除V_RepairOrdersWithParts视图'
END

-- 删除表（如果存在）
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[RepairOrderPartRequests]') AND type in (N'U'))
BEGIN
    DROP TABLE [dbo].[RepairOrderPartRequests]
    PRINT '✓ 删除RepairOrderPartRequests表'
END

PRINT '✓ 清理完成'

-- =============================================
-- 第三步：创建全新的零件申请表
-- =============================================

PRINT '2. 创建全新的RepairOrderPartRequests表...'

CREATE TABLE [dbo].[RepairOrderPartRequests](
    [Id] [int] IDENTITY(1,1) NOT NULL,
    [RepairOrderId] [int] NOT NULL,                 -- 关联的维修单ID
    [PartName] [nvarchar](100) NOT NULL,            -- 零件名称
    [Specification] [nvarchar](200) NULL,           -- 规格型号
    [RequestedQuantity] [int] NOT NULL DEFAULT(1),  -- 申请数量
    [Unit] [nvarchar](20) NOT NULL DEFAULT('个'),   -- 计量单位
    [Reason] [nvarchar](500) NULL,                  -- 更换原因
    [Remark] [nvarchar](1000) NULL,                 -- 备注
    [Status] [int] NOT NULL DEFAULT(1),             -- 状态：1=申请中,2=已领用,3=已安装,4=已取消
    [RequestedBy] [int] NOT NULL,                   -- 申请人ID
    [RequestedAt] [datetime2] NOT NULL DEFAULT(GETDATE()), -- 申请时间
    
    -- 外部系统集成预留字段
    [ExternalPartNumber] [nvarchar](50) NULL,       -- 外部系统零件编号
    [ExternalRequisitionDetailId] [nvarchar](50) NULL, -- 外部系统领用单明细ID
    [ActualQuantity] [int] NULL,                    -- 实际领用数量
    [ActualPartName] [nvarchar](100) NULL,          -- 实际领用名称
    [ActualSpecification] [nvarchar](200) NULL,     -- 实际领用规格
    
    -- 处理信息（无审批环节）
    [IssuedBy] [int] NULL,                          -- 发放人ID
    [IssuedAt] [datetime2] NULL,                    -- 发放时间
    [InstalledBy] [int] NULL,                       -- 安装人ID
    [InstalledAt] [datetime2] NULL,                 -- 安装时间
    
    -- 成本信息
    [UnitPrice] [decimal](18,2) NULL,               -- 单价
    [TotalCost] [decimal](18,2) NULL,               -- 总成本
    [WarehouseOrderNumber] [nvarchar](50) NULL,     -- 仓库出库单号
    
    -- 系统字段
    [CreatedAt] [datetime2] NOT NULL DEFAULT(GETDATE()),
    [UpdatedAt] [datetime2] NULL,
    [CreatedBy] [int] NULL,
    [UpdatedBy] [int] NULL,
    
    CONSTRAINT [PK_RepairOrderPartRequests] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [FK_RepairOrderPartRequests_RepairOrders] FOREIGN KEY([RepairOrderId]) REFERENCES [dbo].[RepairOrders] ([Id]) ON DELETE CASCADE,
    CONSTRAINT [FK_RepairOrderPartRequests_RequestedBy] FOREIGN KEY([RequestedBy]) REFERENCES [dbo].[Users] ([Id]),
    CONSTRAINT [FK_RepairOrderPartRequests_IssuedBy] FOREIGN KEY([IssuedBy]) REFERENCES [dbo].[Users] ([Id]),
    CONSTRAINT [FK_RepairOrderPartRequests_InstalledBy] FOREIGN KEY([InstalledBy]) REFERENCES [dbo].[Users] ([Id]),
    CONSTRAINT [FK_RepairOrderPartRequests_CreatedBy] FOREIGN KEY([CreatedBy]) REFERENCES [dbo].[Users] ([Id]),
    CONSTRAINT [FK_RepairOrderPartRequests_UpdatedBy] FOREIGN KEY([UpdatedBy]) REFERENCES [dbo].[Users] ([Id])
)

PRINT '✓ RepairOrderPartRequests表创建成功'

-- =============================================
-- 第四步：创建索引
-- =============================================

PRINT '3. 创建索引...'

CREATE NONCLUSTERED INDEX [IX_RepairOrderPartRequests_RepairOrderId] ON [dbo].[RepairOrderPartRequests] ([RepairOrderId])
CREATE NONCLUSTERED INDEX [IX_RepairOrderPartRequests_Status] ON [dbo].[RepairOrderPartRequests] ([Status])
CREATE NONCLUSTERED INDEX [IX_RepairOrderPartRequests_RequestedAt] ON [dbo].[RepairOrderPartRequests] ([RequestedAt])
CREATE NONCLUSTERED INDEX [IX_RepairOrderPartRequests_ExternalPartNumber] ON [dbo].[RepairOrderPartRequests] ([ExternalPartNumber]) WHERE [ExternalPartNumber] IS NOT NULL
CREATE NONCLUSTERED INDEX [IX_RepairOrderPartRequests_ExternalRequisitionDetailId] ON [dbo].[RepairOrderPartRequests] ([ExternalRequisitionDetailId]) WHERE [ExternalRequisitionDetailId] IS NOT NULL

PRINT '✓ 所有索引创建成功'

-- =============================================
-- 第五步：创建视图
-- =============================================

PRINT '4. 创建查询视图...'
GO

-- 零件申请详细视图
CREATE VIEW [dbo].[V_RepairOrderPartRequestDetails]
AS
SELECT 
    pr.Id,
    pr.RepairOrderId,
    ro.OrderNumber as RepairOrderNumber,
    ro.FaultDescription as RepairOrderDescription,
    pr.PartName,
    pr.Specification,
    pr.RequestedQuantity,
    pr.Unit,
    pr.Reason,
    pr.Remark,
    pr.Status,
    CASE pr.Status
        WHEN 1 THEN N'申请中'
        WHEN 2 THEN N'已领用'
        WHEN 3 THEN N'已安装'
        WHEN 4 THEN N'已取消'
        ELSE N'未知'
    END AS StatusName,
    pr.RequestedBy,
    ru.DisplayName as RequestedByName,
    pr.RequestedAt,
    pr.IssuedBy,
    iu.DisplayName as IssuedByName,
    pr.IssuedAt,
    pr.InstalledBy,
    inu.DisplayName as InstalledByName,
    pr.InstalledAt,
    pr.ExternalPartNumber,
    pr.ExternalRequisitionDetailId,
    pr.ActualQuantity,
    pr.ActualPartName,
    pr.ActualSpecification,
    pr.UnitPrice,
    pr.TotalCost,
    pr.WarehouseOrderNumber,
    pr.CreatedAt,
    pr.UpdatedAt
FROM [dbo].[RepairOrderPartRequests] pr
    INNER JOIN [dbo].[RepairOrders] ro ON pr.RepairOrderId = ro.Id
    LEFT JOIN [dbo].[Users] ru ON pr.RequestedBy = ru.Id
    LEFT JOIN [dbo].[Users] iu ON pr.IssuedBy = iu.Id
    LEFT JOIN [dbo].[Users] inu ON pr.InstalledBy = inu.Id
GO

PRINT '✓ 零件申请详细视图创建成功'
GO

-- 维修单零件统计视图
CREATE VIEW [dbo].[V_RepairOrderPartSummary]
AS
SELECT 
    ro.Id as RepairOrderId,
    ro.OrderNumber,
    ro.FaultDescription as Description,
    COUNT(pr.Id) as TotalPartRequests,
    SUM(CASE WHEN pr.Status = 1 THEN 1 ELSE 0 END) as PendingRequests,
    SUM(CASE WHEN pr.Status = 2 THEN 1 ELSE 0 END) as IssuedRequests,
    SUM(CASE WHEN pr.Status = 3 THEN 1 ELSE 0 END) as InstalledRequests,
    SUM(CASE WHEN pr.Status = 4 THEN 1 ELSE 0 END) as CancelledRequests,
    SUM(pr.RequestedQuantity) as TotalRequestedQuantity,
    SUM(CASE WHEN pr.ActualQuantity IS NOT NULL THEN pr.ActualQuantity ELSE 0 END) as TotalActualQuantity,
    SUM(CASE WHEN pr.TotalCost IS NOT NULL THEN pr.TotalCost ELSE 0 END) as TotalCost,
    CASE 
        WHEN COUNT(pr.Id) = 0 THEN 100
        ELSE CAST(SUM(CASE WHEN pr.Status = 3 THEN 1 ELSE 0 END) * 100.0 / COUNT(pr.Id) AS DECIMAL(5,2))
    END as CompletionPercentage
FROM [dbo].[RepairOrders] ro
    LEFT JOIN [dbo].[RepairOrderPartRequests] pr ON ro.Id = pr.RepairOrderId
GROUP BY ro.Id, ro.OrderNumber, ro.FaultDescription
GO

PRINT '✓ 维修单零件统计视图创建成功'

-- =============================================
-- 第六步：验证创建结果
-- =============================================

PRINT '5. 验证创建结果...'

-- 检查表
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[RepairOrderPartRequests]') AND type in (N'U'))
    PRINT '✓ RepairOrderPartRequests表存在'

-- 检查视图
IF EXISTS (SELECT * FROM sys.views WHERE object_id = OBJECT_ID(N'[dbo].[V_RepairOrderPartRequestDetails]'))
    PRINT '✓ V_RepairOrderPartRequestDetails视图存在'

IF EXISTS (SELECT * FROM sys.views WHERE object_id = OBJECT_ID(N'[dbo].[V_RepairOrderPartSummary]'))
    PRINT '✓ V_RepairOrderPartSummary视图存在'

-- 确认无审批字段
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[RepairOrderPartRequests]') AND name = 'ApprovedBy')
    PRINT '✓ 确认无ApprovedBy字段'

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[RepairOrderPartRequests]') AND name = 'ApprovedAt')
    PRINT '✓ 确认无ApprovedAt字段'

-- 测试视图查询
BEGIN TRY
    SELECT TOP 1 * FROM V_RepairOrderPartRequestDetails
    PRINT '✓ 详细视图查询正常'
END TRY
BEGIN CATCH
    PRINT '○ 详细视图查询测试（无数据）'
END CATCH

PRINT ''
PRINT '========================================'
PRINT '零件申请表重建完成！'
PRINT ''
PRINT '特性：'
PRINT '- 全新的干净表结构'
PRINT '- 无审批流程'
PRINT '- 简化的4状态流转'
PRINT '- 外部系统集成支持'
PRINT '- 完整的索引和视图'
PRINT ''
PRINT '状态：1=申请中, 2=已领用, 3=已安装, 4=已取消'
PRINT '流程：申请中 → 已领用 → 已安装'
PRINT '========================================'

-- 显示表结构
PRINT ''
PRINT '表结构：'
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, 
       CASE WHEN CHARACTER_MAXIMUM_LENGTH IS NOT NULL THEN CAST(CHARACTER_MAXIMUM_LENGTH AS VARCHAR(10))
            WHEN NUMERIC_PRECISION IS NOT NULL THEN CAST(NUMERIC_PRECISION AS VARCHAR(10)) + ',' + CAST(NUMERIC_SCALE AS VARCHAR(10))
            ELSE 'N/A' END AS LENGTH_PRECISION
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'RepairOrderPartRequests'
ORDER BY ORDINAL_POSITION;
