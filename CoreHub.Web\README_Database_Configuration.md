# CoreHub.Web 数据库配置指南

本文档说明 CoreHub.Web 项目中数据库连接配置的管理方式。

## 📋 配置概述

CoreHub.Web 现在使用标准的 ASP.NET Core 配置系统，数据库连接配置直接存储在 `appsettings.json` 和 `appsettings.Development.json` 文件中，方便修改和管理。

### 🏗️ 项目配置架构

| 项目 | 配置来源 | 说明 |
|------|----------|------|
| **CoreHub.Web** | `appsettings.json` | 使用标准 ASP.NET Core 配置文件 |
| **CoreHub.Maui** | `DatabaseConfig.cs` | 使用共享配置类（向后兼容） |
| **CoreHub.Shared** | - | 提供配置类和服务 |

**注意**：两个项目使用不同的配置方式，但可以通过环境变量统一覆盖。

## ⚙️ 配置文件结构

### 1. 生产环境配置 (`appsettings.json`)

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=***********;Database=CoreHub;User Id=sa;Password=****;TrustServerCertificate=True;Connect Timeout=30;MultipleActiveResultSets=True"
  },
  
  "AuthenticationSettings": {
    "UseStoredProcedure": true,
    "MaxLoginFailureCount": 5,
    "AccountLockoutDuration": 30,
    "PasswordComplexity": {
      "MinLength": 6,
      "RequireDigit": false,
      "RequireLowercase": false,
      "RequireUppercase": false,
      "RequireNonAlphanumeric": false
    }
  }
}
```

### 2. 开发环境配置 (`appsettings.Development.json`)

开发环境可以使用不同的数据库连接字符串，配置结构相同。

## 🔧 修改数据库配置

### 方式一：直接修改配置文件（推荐）

1. **修改数据库连接**：
   - 编辑 `appsettings.json` 中的 `ConnectionStrings:DefaultConnection`
   - 或编辑 `appsettings.Development.json` 用于开发环境

2. **修改认证设置**：
   - 编辑 `AuthenticationSettings` 部分的相关配置

### 方式二：环境变量覆盖

可以通过环境变量覆盖配置文件中的设置：

```bash
# 数据库连接字符串
ConnectionStrings__DefaultConnection="Server=new-server;Database=new-db;..."

# 认证设置
AuthenticationSettings__UseStoredProcedure=false
```

## 📝 配置说明

### 数据库连接字符串参数

| 参数 | 说明 | 示例值 |
|------|------|--------|
| `Server` | 数据库服务器地址 | `***********` |
| `Database` | 数据库名称 | `CoreHub` |
| `User Id` | 数据库用户名 | `sa` |
| `Password` | 数据库密码 | `****` |
| `TrustServerCertificate` | 信任服务器证书 | `True` |
| `Connect Timeout` | 连接超时时间（秒） | `30` |
| `MultipleActiveResultSets` | 允许多个活动结果集 | `True` |

### 认证设置参数

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `UseStoredProcedure` | 是否使用存储过程认证 | `true` |
| `MaxLoginFailureCount` | 最大登录失败次数 | `5` |
| `AccountLockoutDuration` | 账户锁定时长（分钟） | `30` |
| `PasswordComplexity` | 密码复杂度要求 | 见配置文件 |

## 🔄 配置优先级

1. **环境变量**（最高优先级）
2. **appsettings.{Environment}.json**（如 `appsettings.Development.json`）
3. **appsettings.json**（基础配置）

## 🚀 常见配置场景

### 1. 更换数据库服务器

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=*************;Database=CoreHub;User Id=sa;Password=****;TrustServerCertificate=True;Connect Timeout=30;MultipleActiveResultSets=True"
  }
}
```

### 2. 使用不同的数据库

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=***********;Database=NewSystemDB;User Id=sa;Password=****;TrustServerCertificate=True;Connect Timeout=30;MultipleActiveResultSets=True"
  }
}
```

### 3. 切换认证方式

```json
{
  "AuthenticationSettings": {
    "UseStoredProcedure": false
  }
}
```

## 🔍 验证配置

启动应用程序后，检查日志输出确认配置是否正确：

```
[23:41:15 INF] 正在启动 CoreHub Web 应用程序
[23:41:15 INF] SSL证书验证成功，有效期至: 01/26/2026 07:59:59
[23:41:15 INF] CoreHub Web 应用程序启动完成
[23:41:15 INF] Now listening on: https://0.0.0.0:8081
```

## 📚 相关文档

- [ASP.NET Core 配置](https://docs.microsoft.com/aspnet/core/fundamentals/configuration/)
- [连接字符串语法](https://docs.microsoft.com/dotnet/framework/data/adonet/connection-string-syntax)
- [环境变量配置](https://docs.microsoft.com/aspnet/core/fundamentals/configuration/#environment-variables)

## ⚠️ 注意事项

1. **安全性**：生产环境中建议使用环境变量或 Azure Key Vault 等安全方式存储敏感信息
2. **备份**：修改配置前请备份原配置文件
3. **重启**：修改配置文件后需要重启应用程序才能生效
4. **权限**：确保应用程序有权限访问指定的数据库
