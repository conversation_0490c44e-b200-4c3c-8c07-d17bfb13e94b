using Android.Content;
using Android.OS;
using FileProvider = AndroidX.Core.Content.FileProvider;
using JavaFile = Java.IO.File;
using Microsoft.Maui.Storage;
using MauiScanManager.Services;
using Debug = System.Diagnostics.Debug;
using System.IO;
using System.Threading;
using System;

namespace MauiScanManager.Platforms.Android.Services
{
    public class AndroidInstaller : IPlatformInstaller
    {
        public bool InstallUpdate(string filePath)
        {
            try
            {
                LogService.Log($"准备安装APK: {filePath}");
                
                if (!File.Exists(filePath))
                {
                    LogService.Log("APK文件不存在");
                    return false;
                }

                var fileInfo = new FileInfo(filePath);
                LogService.Log($"APK文件大小: {fileInfo.Length} bytes");

                var context = global::Android.App.Application.Context;
                var intent = new Intent(Intent.ActionView);
                LogService.Log("创建安装意图");

                var fileUri = FileProvider.GetUriForFile(
                    context,
                    $"{AppInfo.Current.PackageName}.fileprovider",
                    new JavaFile(filePath));
                
                LogService.Log($"文件URI: {fileUri}");

                intent.SetDataAndType(fileUri, "application/vnd.android.package-archive");
                intent.AddFlags(ActivityFlags.GrantReadUriPermission | ActivityFlags.NewTask);
                intent.AddFlags(ActivityFlags.ClearTop);  // 添加此标志

                LogService.Log("启动安装活动");
                try
                {
                    context.StartActivity(intent);
                    LogService.Log("安装活动已启动");

                    // 等待一段时间确保安装对话框显示
                    Thread.Sleep(1000);

                    // 验证安装意图是否成功启动
                    var packageManager = context.PackageManager;
                    var activities = packageManager.QueryIntentActivities(intent, 0);
                    if (activities.Count == 0)
                    {
                        LogService.Log("没有找到可以处理安装意图的应用");
                        return false;
                    }

                    LogService.Log($"找到 {activities.Count} 个可以处理安装的应用");
                    return true;
                }
                catch (Exception activityEx)
                {
                    LogService.Log($"启动安装活动失败: {activityEx.Message}");
                    LogService.Log($"错误类型: {activityEx.GetType().FullName}");
                    LogService.Log($"错误堆栈: {activityEx.StackTrace}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                LogService.Log($"安装更新失败: {ex.Message}");
                LogService.Log($"错误类型: {ex.GetType().FullName}");
                LogService.Log($"错误堆栈: {ex.StackTrace}");
                return false;
            }
        }
    }
}
