<?xml version="1.0" encoding="utf-8" ?>
<views:BaseOperationPage 
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:views="clr-namespace:MauiScanManager.Views"
    xmlns:viewmodels="clr-namespace:MauiScanManager.ViewModels"
    x:Class="MauiScanManager.Views.ChemicalBoxCheckPage"
    Title="{Binding Operation.Description}"
    BackgroundColor="{Binding BackgroundColor}">

    <ScrollView>
        <VerticalStackLayout 
            Spacing="10" 
            Padding="20,10" 
            VerticalOptions="Start">

            <!-- 扫描提示 -->
            <Label 
                Text="{Binding ScanPrompt}"
                HorizontalOptions="Center"
                FontSize="28"
                FontAttributes="Bold"
                TextColor="{StaticResource Primary}"
                Margin="0,0,0,5"/>

            <!-- 测试按钮区域 -->
            <Grid ColumnDefinitions="*,*" ColumnSpacing="10" IsVisible="{Binding IsDebugMode}">
                <Button 
                    Grid.Column="0"
                    Text="模拟扫描缸号"
                    Command="{Binding SimulateBatchScanCommand}"
                    BackgroundColor="{StaticResource Primary}"
                    TextColor="White"
                    FontSize="14"
                    Margin="0,0,0,5"/>
                
                <Button 
                    Grid.Column="1"
                    Text="模拟扫描箱号"
                    Command="{Binding SimulateBoxScanCommand}"
                    BackgroundColor="{StaticResource Primary}"
                    TextColor="White"
                    FontSize="14"
                    Margin="0,0,0,5"/>
            </Grid>

            <!-- 清空按钮 -->
            <Button 
                Text="清空"
                Command="{Binding ResetCommand}"
                BackgroundColor="{StaticResource Gray500}"
                TextColor="White"
                FontSize="14"
                Margin="0,0,0,5"/>

            <!-- 箱数信息 -->
            <Grid ColumnDefinitions="*,*,*" ColumnSpacing="10" Margin="0,0,0,5">
                <!-- 总箱数 -->
                <VerticalStackLayout Grid.Column="0" Spacing="2">
                    <Label 
                        Text="总箱数"
                        FontSize="14"
                        TextColor="{AppThemeBinding Light={StaticResource Gray600}, Dark={StaticResource Gray400}}"/>
                    <Frame 
                        Padding="8,6" 
                        BorderColor="{AppThemeBinding Light={StaticResource Gray300}, Dark={StaticResource Gray600}}"
                        BackgroundColor="{AppThemeBinding Light={StaticResource Gray100}, Dark={StaticResource Gray800}}"
                        CornerRadius="4"
                        HasShadow="False">
                        <Label 
                            Text="{Binding TotalBoxes}"
                            FontSize="18"
                            TextColor="{AppThemeBinding Light={StaticResource Black}, Dark={StaticResource White}}"/>
                    </Frame>
                </VerticalStackLayout>

                <!-- 已扫描箱数 -->
                <VerticalStackLayout Grid.Column="1" Spacing="2">
                    <Label 
                        Text="已扫描箱数"
                        FontSize="14"
                        TextColor="{AppThemeBinding Light={StaticResource Gray600}, Dark={StaticResource Gray400}}"/>
                    <Frame 
                        Padding="8,6" 
                        BorderColor="{AppThemeBinding Light={StaticResource Gray300}, Dark={StaticResource Gray600}}"
                        BackgroundColor="{AppThemeBinding Light={StaticResource Gray100}, Dark={StaticResource Gray800}}"
                        CornerRadius="4"
                        HasShadow="False">
                        <Label 
                            Text="{Binding ScannedBoxes}"
                            FontSize="18"
                            TextColor="{AppThemeBinding Light={StaticResource Black}, Dark={StaticResource White}}"/>
                    </Frame>
                </VerticalStackLayout>

                <!-- 剩余箱数 -->
                <VerticalStackLayout Grid.Column="2" Spacing="2">
                    <Label 
                        Text="剩余箱数"
                        FontSize="14"
                        TextColor="{AppThemeBinding Light={StaticResource Gray600}, Dark={StaticResource Gray400}}"/>
                    <Frame 
                        Padding="8,6" 
                        BorderColor="{AppThemeBinding Light={StaticResource Gray300}, Dark={StaticResource Gray600}}"
                        BackgroundColor="{AppThemeBinding Light={StaticResource Gray100}, Dark={StaticResource Gray800}}"
                        CornerRadius="4"
                        HasShadow="False">
                        <Label 
                            Text="{Binding RemainingBoxes}"
                            FontSize="18"
                            TextColor="{AppThemeBinding Light={StaticResource Black}, Dark={StaticResource White}}"/>
                    </Frame>
                </VerticalStackLayout>
            </Grid>

            <!-- 缸号 -->
            <VerticalStackLayout Spacing="4" Margin="0,5,0,5">
                <Label 
                    Text="缸号"
                    FontSize="16"
                    TextColor="{AppThemeBinding Light={StaticResource Gray600}, Dark={StaticResource Gray400}}"/>
                <Frame 
                    Padding="12,8" 
                    BorderColor="{AppThemeBinding Light={StaticResource Gray300}, Dark={StaticResource Gray600}}"
                    BackgroundColor="{AppThemeBinding Light={StaticResource Gray100}, Dark={StaticResource Gray800}}"
                    CornerRadius="6"
                    HasShadow="False">
                    <Label 
                        Text="{Binding BatchNo}"
                        FontSize="24"
                        TextColor="{AppThemeBinding Light={StaticResource Black}, Dark={StaticResource White}}"/>
                </Frame>
            </VerticalStackLayout>

            <!-- 染料箱号 -->
            <VerticalStackLayout Spacing="4" Margin="0,0,0,5">
                <Label 
                    Text="染料箱号"
                    FontSize="16"
                    TextColor="{AppThemeBinding Light={StaticResource Gray600}, Dark={StaticResource Gray400}}"/>
                <Frame 
                    Padding="12,8" 
                    BorderColor="{AppThemeBinding Light={StaticResource Gray300}, Dark={StaticResource Gray600}}"
                    BackgroundColor="{AppThemeBinding Light={StaticResource Gray100}, Dark={StaticResource Gray800}}"
                    CornerRadius="6"
                    HasShadow="False">
                    <Label 
                        Text="{Binding BoxNo}"
                        FontSize="24"
                        TextColor="{AppThemeBinding Light={StaticResource Black}, Dark={StaticResource White}}"/>
                </Frame>
            </VerticalStackLayout>

            <!-- 校验结果 -->
            <VerticalStackLayout Spacing="2">
                <Label 
                    Text="校验结果"
                    FontSize="14"
                    TextColor="{AppThemeBinding Light={StaticResource Gray600}, Dark={StaticResource Gray400}}"/>
                <Frame 
                    Padding="8,6" 
                    BorderColor="{AppThemeBinding Light={StaticResource Gray300}, Dark={StaticResource Gray600}}"
                    BackgroundColor="{AppThemeBinding Light={StaticResource Gray100}, Dark={StaticResource Gray800}}"
                    CornerRadius="4"
                    HasShadow="False">
                    <Label 
                        Text="{Binding ResultMessage}"
                        FontSize="18"
                        TextColor="{AppThemeBinding Light={StaticResource Black}, Dark={StaticResource White}}"/>
                </Frame>
            </VerticalStackLayout>

        </VerticalStackLayout>
    </ScrollView>

</views:BaseOperationPage> 