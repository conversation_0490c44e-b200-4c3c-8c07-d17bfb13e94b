using CommunityToolkit.Maui.Alerts;

namespace MauiScanManager.Services
{
    public class DefaultDialogService : IPlatformDialogService
    {
        private readonly IServiceProvider _services;

        public DefaultDialogService(IServiceProvider services)
        {
            _services = services;
        }

        public async Task ShowToastAsync(string message)
        {
            if (!MainThread.IsMainThread)
            {
                await MainThread.InvokeOnMainThreadAsync(() => ShowToastAsync(message));
                return;
            }

            var toast = Toast.Make(message);
            await toast.Show();
        }

        public async Task ShowSnackbarAsync(string message, string actionText = null, Action action = null)
        {
            if (!MainThread.IsMainThread)
            {
                await MainThread.InvokeOnMainThreadAsync(() => 
                    ShowSnackbarAsync(message, actionText, action));
                return;
            }

            var snackbar = Snackbar.Make(
                message,
                action,
                actionText,
                TimeSpan.FromSeconds(3));
            await snackbar.Show();
        }

        void IPlatformDialogService.ShowConfirmationDialog(string title, string message, int iconResourceId, Action onConfirm, Action onCancel)
        {
            throw new NotImplementedException();
        }
    }
} 