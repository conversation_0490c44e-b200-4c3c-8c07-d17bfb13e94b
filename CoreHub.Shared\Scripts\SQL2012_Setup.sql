-- =============================================
-- SQL Server 2012 数据库设置脚本
-- =============================================
-- 此脚本专门为 SQL Server 2012 环境设计
-- 包含版本检查、兼容性设置和初始数据

-- 检查 SQL Server 版本
DECLARE @Version NVARCHAR(128) = @@VERSION;
DECLARE @MajorVersion INT = CAST(SERVERPROPERTY('ProductMajorVersion') AS INT);

PRINT 'SQL Server 版本信息: ' + @Version;
PRINT '主版本号: ' + CAST(@MajorVersion AS NVARCHAR(10));

-- 确保版本兼容性（SQL Server 2012 = 版本 11）
IF @MajorVersion < 11
BEGIN
    RAISERROR('此脚本需要 SQL Server 2012 或更高版本', 16, 1);
    RETURN;
END

PRINT '版本检查通过，继续执行...';
GO

-- =============================================
-- 设置数据库兼容级别（SQL Server 2012 = 110）
-- =============================================
DECLARE @DbName NVARCHAR(128) = DB_NAME();
DECLARE @SQL NVARCHAR(500);

-- 设置兼容级别为 SQL Server 2012
SET @SQL = 'ALTER DATABASE [' + @DbName + '] SET COMPATIBILITY_LEVEL = 110';
EXEC sp_executesql @SQL;

PRINT '数据库兼容级别已设置为 SQL Server 2012 (110)';
GO

-- =============================================
-- 创建密码验证辅助函数（SQL Server 2012 兼容）
-- =============================================
-- 由于 SQL Server 2012 不支持 CLR 集成的 BCrypt
-- 我们创建一个简化的密码验证机制

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[fn_ValidatePassword]') AND type in (N'FN', N'IF', N'TF', N'FS', N'FT'))
    DROP FUNCTION [dbo].[fn_ValidatePassword];
GO

CREATE FUNCTION [dbo].[fn_ValidatePassword]
(
    @PlainPassword NVARCHAR(255),
    @HashedPassword NVARCHAR(255)
)
RETURNS BIT
AS
BEGIN
    DECLARE @IsValid BIT = 0;
    
    -- 简化的密码验证逻辑
    -- 在实际应用中，这里应该调用外部程序或使用 CLR 函数进行 BCrypt 验证
    -- 目前使用简单的哈希比较作为演示
    
    -- 检查是否为 BCrypt 哈希格式（以 $2a$, $2b$, $2y$ 开头）
    IF LEFT(@HashedPassword, 4) IN ('$2a$', '$2b$', '$2y$')
    BEGIN
        -- BCrypt 格式，需要外部验证
        -- 在生产环境中，这里应该调用应用程序层的 BCrypt 验证
        -- 暂时返回 0，强制在应用层进行验证
        SET @IsValid = 0;
    END
    ELSE
    BEGIN
        -- 非 BCrypt 格式，使用简单比较（仅用于测试）
        IF @PlainPassword = @HashedPassword
            SET @IsValid = 1;
    END
    
    RETURN @IsValid;
END;
GO

-- =============================================
-- 创建增强的用户验证存储过程（支持外部密码验证）
-- =============================================
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_ValidateUserEnhanced]') AND type in (N'P', N'PC'))
    DROP PROCEDURE [dbo].[sp_ValidateUserEnhanced];
GO

CREATE PROCEDURE [dbo].[sp_ValidateUserEnhanced]
    @Username NVARCHAR(50),
    @Password NVARCHAR(255),
    @IsPasswordPreHashed BIT = 0,  -- 指示密码是否已经通过应用层验证
    @ClientIP NVARCHAR(50) = NULL,
    @IsSuccess BIT OUTPUT,
    @ErrorMessage NVARCHAR(500) OUTPUT,
    @UserId INT OUTPUT,
    @DisplayName NVARCHAR(100) OUTPUT,
    @Email NVARCHAR(100) OUTPUT,
    @Role NVARCHAR(100) OUTPUT
AS
BEGIN
    SET NOCOUNT ON;
    
    -- 初始化输出参数
    SET @IsSuccess = 0;
    SET @ErrorMessage = '';
    SET @UserId = NULL;
    SET @DisplayName = '';
    SET @Email = '';
    SET @Role = '';
    
    BEGIN TRY
        BEGIN TRANSACTION;
        
        -- 输入参数验证
        IF @Username IS NULL OR @Username = '' OR @Password IS NULL OR @Password = ''
        BEGIN
            SET @ErrorMessage = '用户名和密码不能为空';
            ROLLBACK TRANSACTION;
            RETURN;
        END
        
        -- 查询用户信息
        DECLARE @StoredUserId INT, @StoredPasswordHash NVARCHAR(255), @StoredDisplayName NVARCHAR(100);
        DECLARE @StoredEmail NVARCHAR(100), @StoredIsEnabled BIT, @StoredIsLocked BIT;
        DECLARE @StoredLockReason NVARCHAR(500), @StoredLoginFailureCount INT;
        
        SELECT @StoredUserId = Id, @StoredPasswordHash = PasswordHash, @StoredDisplayName = DisplayName,
               @StoredEmail = Email, @StoredIsEnabled = IsEnabled, @StoredIsLocked = IsLocked,
               @StoredLockReason = LockReason, @StoredLoginFailureCount = LoginFailureCount
        FROM Users
        WHERE Username = @Username;
        
        -- 检查用户是否存在
        IF @StoredUserId IS NULL
        BEGIN
            SET @ErrorMessage = '用户名或密码错误';
            
            -- 记录登录失败日志
            INSERT INTO LoginLogs (Username, ClientIP, IsSuccess, FailureReason, LoginTime)
            VALUES (@Username, @ClientIP, 0, '用户不存在', GETDATE());
            
            ROLLBACK TRANSACTION;
            RETURN;
        END
        
        -- 检查账户状态
        IF @StoredIsEnabled = 0
        BEGIN
            SET @ErrorMessage = '账户已被禁用，请联系管理员';
            
            INSERT INTO LoginLogs (Username, ClientIP, IsSuccess, FailureReason, LoginTime)
            VALUES (@Username, @ClientIP, 0, '账户已禁用', GETDATE());
            
            ROLLBACK TRANSACTION;
            RETURN;
        END
        
        IF @StoredIsLocked = 1
        BEGIN
            SET @ErrorMessage = '账户已被锁定：' + ISNULL(@StoredLockReason, '');
            
            INSERT INTO LoginLogs (Username, ClientIP, IsSuccess, FailureReason, LoginTime)
            VALUES (@Username, @ClientIP, 0, '账户已锁定', GETDATE());
            
            ROLLBACK TRANSACTION;
            RETURN;
        END
        
        -- 验证密码（明文比较，暂不加密）
        DECLARE @IsPasswordValid BIT = 0;
        
        IF @IsPasswordPreHashed = 1
        BEGIN
            -- 密码已经在应用层验证，直接认为有效
            SET @IsPasswordValid = 1;
        END
        ELSE
        BEGIN
            -- 直接进行明文密码比较
            IF @StoredPasswordHash = @Password
                SET @IsPasswordValid = 1;
            ELSE
                SET @IsPasswordValid = 0;
        END
        
        IF @IsPasswordValid = 0
        BEGIN
            -- 增加登录失败次数
            UPDATE Users 
            SET LoginFailureCount = LoginFailureCount + 1,
                UpdatedAt = GETDATE()
            WHERE Id = @StoredUserId;
            
            -- 检查是否需要锁定账户
            IF @StoredLoginFailureCount + 1 >= 5
            BEGIN
                UPDATE Users
                SET IsLocked = 1,
                    LockedAt = GETDATE(),
                    LockReason = '连续登录失败' + CAST(@StoredLoginFailureCount + 1 AS NVARCHAR(10)) + '次，账户已自动锁定'
                WHERE Id = @StoredUserId;
            END
            
            SET @ErrorMessage = '用户名或密码错误';
            
            INSERT INTO LoginLogs (Username, ClientIP, IsSuccess, FailureReason, LoginTime)
            VALUES (@Username, @ClientIP, 0, '密码错误', GETDATE());
            
            ROLLBACK TRANSACTION;
            RETURN;
        END
        
        -- 登录成功，更新用户信息
        UPDATE Users
        SET LoginFailureCount = 0,
            LastLoginTime = GETDATE(),
            LastLoginIp = @ClientIP,
            UpdatedAt = GETDATE()
        WHERE Id = @StoredUserId;
        
        -- 获取用户角色
        DECLARE @UserRole NVARCHAR(100);
        SELECT TOP 1 @UserRole = r.Name
        FROM UserRoles ur
        INNER JOIN Roles r ON ur.RoleId = r.Id
        WHERE ur.UserId = @StoredUserId 
          AND ur.IsEnabled = 1 
          AND r.IsEnabled = 1
          AND (ur.ExpiresAt IS NULL OR ur.ExpiresAt > GETDATE())
        ORDER BY r.SortOrder;
        
        -- 设置输出参数
        SET @IsSuccess = 1;
        SET @UserId = @StoredUserId;
        SET @DisplayName = @StoredDisplayName;
        SET @Email = @StoredEmail;
        SET @Role = ISNULL(@UserRole, '未分配角色');
        
        -- 记录登录成功日志
        INSERT INTO LoginLogs (Username, ClientIP, IsSuccess, FailureReason, LoginTime)
        VALUES (@Username, @ClientIP, 1, NULL, GETDATE());
        
        COMMIT TRANSACTION;
        
    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION;
        
        SET @ErrorMessage = '验证过程中发生错误：' + ERROR_MESSAGE();
        
        -- 记录错误日志
        INSERT INTO LoginLogs (Username, ClientIP, IsSuccess, FailureReason, LoginTime)
        VALUES (@Username, @ClientIP, 0, @ErrorMessage, GETDATE());
    END CATCH
END;
GO

-- =============================================
-- 创建性能监控存储过程
-- =============================================
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_PermissionSystemStats]') AND type in (N'P', N'PC'))
    DROP PROCEDURE [dbo].[sp_PermissionSystemStats];
GO

CREATE PROCEDURE [dbo].[sp_PermissionSystemStats]
AS
BEGIN
    SET NOCOUNT ON;
    
    -- 系统统计信息
    SELECT 
        '用户统计' AS Category,
        COUNT(*) AS TotalCount,
        SUM(CASE WHEN IsEnabled = 1 THEN 1 ELSE 0 END) AS EnabledCount,
        SUM(CASE WHEN IsLocked = 1 THEN 1 ELSE 0 END) AS LockedCount
    FROM Users
    
    UNION ALL
    
    SELECT 
        '角色统计' AS Category,
        COUNT(*) AS TotalCount,
        SUM(CASE WHEN IsEnabled = 1 THEN 1 ELSE 0 END) AS EnabledCount,
        SUM(CASE WHEN IsSystem = 1 THEN 1 ELSE 0 END) AS SystemCount
    FROM Roles
    
    UNION ALL
    
    SELECT 
        '权限统计' AS Category,
        COUNT(*) AS TotalCount,
        SUM(CASE WHEN IsEnabled = 1 THEN 1 ELSE 0 END) AS EnabledCount,
        COUNT(DISTINCT Module) AS ModuleCount
    FROM Permissions;
    
    -- 最近登录统计（最近7天）
    SELECT 
        '登录统计(最近7天)' AS Category,
        COUNT(*) AS TotalAttempts,
        SUM(CASE WHEN IsSuccess = 1 THEN 1 ELSE 0 END) AS SuccessCount,
        SUM(CASE WHEN IsSuccess = 0 THEN 1 ELSE 0 END) AS FailureCount
    FROM LoginLogs
    WHERE LoginTime >= DATEADD(day, -7, GETDATE());
END;
GO

-- =============================================
-- 创建数据库维护存储过程
-- =============================================
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_MaintenancePermissionSystem]') AND type in (N'P', N'PC'))
    DROP PROCEDURE [dbo].[sp_MaintenancePermissionSystem];
GO

CREATE PROCEDURE [dbo].[sp_MaintenancePermissionSystem]
    @OperationType NVARCHAR(50) = 'ALL' -- ALL, STATS, REINDEX, CLEANUP
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @StartTime DATETIME = GETDATE();
    PRINT '开始数据库维护: ' + CONVERT(NVARCHAR(19), @StartTime, 120);
    
    -- 更新统计信息
    IF @OperationType IN ('ALL', 'STATS')
    BEGIN
        PRINT '更新统计信息...';
        UPDATE STATISTICS Users;
        UPDATE STATISTICS Roles;
        UPDATE STATISTICS Permissions;
        UPDATE STATISTICS UserRoles;
        UPDATE STATISTICS RolePermissions;
        UPDATE STATISTICS UserPermissions;
        UPDATE STATISTICS LoginLogs;
        PRINT '统计信息更新完成';
    END
    
    -- 重建索引
    IF @OperationType IN ('ALL', 'REINDEX')
    BEGIN
        PRINT '重建索引...';
        ALTER INDEX ALL ON Users REBUILD WITH (ONLINE = OFF);
        ALTER INDEX ALL ON Roles REBUILD WITH (ONLINE = OFF);
        ALTER INDEX ALL ON Permissions REBUILD WITH (ONLINE = OFF);
        ALTER INDEX ALL ON UserRoles REBUILD WITH (ONLINE = OFF);
        ALTER INDEX ALL ON RolePermissions REBUILD WITH (ONLINE = OFF);
        ALTER INDEX ALL ON UserPermissions REBUILD WITH (ONLINE = OFF);
        ALTER INDEX ALL ON LoginLogs REBUILD WITH (ONLINE = OFF);
        PRINT '索引重建完成';
    END
    
    -- 清理过期数据
    IF @OperationType IN ('ALL', 'CLEANUP')
    BEGIN
        PRINT '清理过期数据...';
        
        -- 清理90天前的登录日志
        DELETE FROM LoginLogs WHERE LoginTime < DATEADD(day, -90, GETDATE());
        PRINT '已清理90天前的登录日志';
        
        -- 清理过期的用户角色关联
        UPDATE UserRoles SET IsEnabled = 0 
        WHERE ExpiresAt IS NOT NULL AND ExpiresAt < GETDATE() AND IsEnabled = 1;
        PRINT '已禁用过期的用户角色关联';
        
        -- 清理过期的用户权限
        UPDATE UserPermissions SET IsEnabled = 0 
        WHERE ExpiresAt IS NOT NULL AND ExpiresAt < GETDATE() AND IsEnabled = 1;
        PRINT '已禁用过期的用户权限';
    END
    
    DECLARE @EndTime DATETIME = GETDATE();
    PRINT '维护完成: ' + CONVERT(NVARCHAR(19), @EndTime, 120);
    PRINT '耗时: ' + CAST(DATEDIFF(second, @StartTime, @EndTime) AS NVARCHAR(10)) + ' 秒';
END;
GO

-- =============================================
-- 初始化完成信息
-- =============================================
PRINT '';
PRINT '========================================';
PRINT 'SQL Server 2012 权限管理系统设置完成！';
PRINT '========================================';
PRINT '创建的组件:';
PRINT '- fn_ValidatePassword: 密码验证函数';
PRINT '- sp_ValidateUserEnhanced: 增强用户验证存储过程';
PRINT '- sp_PermissionSystemStats: 系统统计存储过程';
PRINT '- sp_MaintenancePermissionSystem: 维护存储过程';
PRINT '';
PRINT '使用建议:';
PRINT '1. 推荐在应用层使用 BCrypt 进行密码验证';
PRINT '2. 定期执行 sp_MaintenancePermissionSystem 进行维护';
PRINT '3. 使用 sp_PermissionSystemStats 监控系统状态';
PRINT '4. 确保定期备份数据库';
PRINT '========================================'; 