# 零件申请存储过程合并说明

## 📋 合并概述

已成功将 `零件申请存储过程.sql` 合并到 `数据库脚本_完整版.sql` 中，实现了零件申请管理功能的完整集成。

## 🔧 合并内容

### 新增存储过程

1. **sp_GetPendingPartRequests**
   - **功能**：查询所有状态为"申请中"(Status=1)的零件申请记录
   - **特性**：支持分页查询、多条件过滤、完整的关联信息
   - **参数**：
     - `@DepartmentId` - 按部门过滤（可选）
     - `@UrgencyLevel` - 按紧急程度过滤（可选）
     - `@StartDate` / `@EndDate` - 时间范围过滤（可选）
     - `@PageIndex` / `@PageSize` - 分页参数

2. **sp_UpdatePartIssueInfo**
   - **功能**：供外部仓库系统调用，更新零件的实际发放信息
   - **特性**：完整的参数验证、事务处理、错误处理
   - **参数**：
     - `@PartRequestId` - 零件申请ID（必填）
     - `@ActualQuantity` - 实际发放数量（必填）
     - `@WarehouseOrderNumber` - 仓库单号（必填）
     - `@UnitPrice` / `@TotalCost` - 价格信息（可选）
     - `@ExternalSystemId` - 外部系统ID（可选）
     - `@ActualPartName` / `@ActualSpecification` - 实际发放信息（可选）
     - `@ExternalPartNumber` - 外部系统零件编号（可选）

## 📂 文件结构更新

### 在完整版脚本中的位置
- **插入位置**：第10节 - 创建零件申请管理存储过程
- **位置**：工作流历史存储过程之后，示例数据插入之前
- **行号范围**：2434-2739行

### 脚本结构
```
-- =============================================
-- 10. 创建零件申请管理存储过程
-- =============================================

-- 存储过程1：获取待领用零件信息
sp_GetPendingPartRequests

-- 存储过程2：外部系统写入实际领用信息
sp_UpdatePartIssueInfo

-- =============================================
-- 11. 插入示例工作流历史数据
-- =============================================
```

## 📊 统计信息更新

### 存储过程统计
- **原来**：3个存储过程（报修单号生成、工作流历史管理）
- **现在**：5个存储过程（报修单号生成、工作流历史管理、零件申请管理）

### 新增功能说明
```
零件申请管理系统：
  零件申请存储过程: sp_GetPendingPartRequests, sp_UpdatePartIssueInfo
  功能: 外部仓库系统集成、零件发放管理、分页查询
  特性: 完整参数验证、事务处理、错误处理
```

## 🎯 功能特性

### 1. 外部系统集成支持
- 提供标准化的API接口
- 支持外部仓库系统数据写回
- 包含外部系统ID和零件编号字段

### 2. 完整的数据查询
- 关联查询报修单、设备、用户、部门等信息
- 支持多维度过滤和分页
- 按紧急程度和申请时间排序

### 3. 健壮的错误处理
- 完整的参数验证
- 事务处理确保数据一致性
- 详细的错误信息返回

### 4. 灵活的数据更新
- 支持部分字段更新
- 自动计算总成本
- 状态流转控制

## 🚀 使用示例

### 查询待领用零件
```sql
-- 查询所有待领用零件
EXEC sp_GetPendingPartRequests;

-- 按紧急程度查询
EXEC sp_GetPendingPartRequests @UrgencyLevel = 1;

-- 分页查询
EXEC sp_GetPendingPartRequests @PageIndex = 1, @PageSize = 20;
```

### 更新零件发放信息
```sql
EXEC sp_UpdatePartIssueInfo
    @PartRequestId = 1,
    @ActualQuantity = 2,
    @WarehouseOrderNumber = 'WH20250110001',
    @UnitPrice = 150.00,
    @TotalCost = 300.00,
    @ExternalSystemId = 'EXT001',
    @ActualPartName = '实际发放的零件名称',
    @ExternalPartNumber = 'P001';
```

## ✅ 验证要点

1. **脚本完整性**：确保合并后的脚本可以完整执行
2. **存储过程功能**：验证两个存储过程的功能正常
3. **数据一致性**：确保与现有零件申请表结构兼容
4. **错误处理**：测试各种异常情况的处理

## 📝 注意事项

1. **依赖关系**：存储过程依赖于零件申请表（RepairOrderPartRequests）
2. **权限要求**：执行存储过程需要相应的数据库权限
3. **外部集成**：外部系统调用时需要正确的参数格式
4. **状态管理**：只有"申请中"状态的记录才能进行发放操作

## 🔄 后续工作

1. 测试存储过程功能
2. 验证外部系统集成
3. 更新相关文档
4. 培训相关人员使用新功能
