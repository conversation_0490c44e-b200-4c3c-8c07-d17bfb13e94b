@namespace CoreHub.Shared.Components
@using MudBlazor

<PageTitle>页面未找到</PageTitle>

<MudContainer MaxWidth="MaxWidth.Medium" Class="mt-8">
    <MudPaper Class="pa-8" Elevation="4">
        <MudStack AlignItems="AlignItems.Center" Spacing="4">
            <MudIcon Icon="@Icons.Material.Filled.ErrorOutline"
                    Size="Size.Large"
                    Color="Color.Warning"
                    Style="font-size: 4rem;" />
            <MudText Typo="Typo.h4" Align="Align.Center">页面未找到</MudText>
            <MudText Typo="Typo.body1" Align="Align.Center">
                抱歉，您访问的页面不存在或已被移除。
            </MudText>
            @if (!string.IsNullOrEmpty(RequestedPath))
            {
                <MudText Typo="Typo.body2" Color="Color.Secondary" Align="Align.Center">
                    请求的路径：<code>@RequestedPath</code>
                </MudText>
            }
            <MudText Typo="Typo.body2" Color="Color.Secondary" Align="Align.Center">
                请检查URL是否正确，或者从下面的选项中选择：
            </MudText>

            <MudStack Row Justify="Justify.Center" Spacing="3">
                <MudButton Variant="Variant.Filled"
                          Color="Color.Primary"
                          Href="/"
                          StartIcon="@Icons.Material.Filled.Home">
                    返回首页
                </MudButton>
                <MudButton Variant="Variant.Outlined"
                          Color="Color.Primary"
                          OnClick="GoBack"
                          StartIcon="@Icons.Material.Filled.ArrowBack">
                    返回上页
                </MudButton>
            </MudStack>

        </MudStack>
    </MudPaper>
</MudContainer>

@code {
    [Inject] private NavigationManager Navigation { get; set; } = default!;
    [Inject] private IJSRuntime JSRuntime { get; set; } = default!;
    
    /// <summary>
    /// 请求的路径，用于显示用户尝试访问的URL
    /// </summary>
    [Parameter] public string? RequestedPath { get; set; }

    private async Task GoBack()
    {
        try
        {
            await JSRuntime.InvokeVoidAsync("history.back");
        }
        catch
        {
            // 如果JavaScript调用失败，回到首页
            Navigation.NavigateTo("/");
        }
    }
}
