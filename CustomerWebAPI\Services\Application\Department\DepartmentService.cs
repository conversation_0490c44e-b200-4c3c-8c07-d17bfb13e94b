using System;
using System.Threading.Tasks;
using System.Linq;
using System.Collections.Generic;
using Microsoft.Extensions.Logging;
using CustomerWebAPI.Models;
using CustomerWebAPI.Common;
using CustomerWebAPI.Database;

namespace CustomerWebAPI.Services
{
    public class DepartmentService : IDepartmentService
    {
        private readonly DbContext _dbContext;
        private readonly ILogger<DepartmentService> _logger;

        public DepartmentService(
            DbContext dbContext,
            ILogger<DepartmentService> logger)
        {
            _dbContext = dbContext;
            _logger = logger;
        }

        public async Task<ApiResponse<List<Department>>> GetDepartmentsAsync()
        {
            try
            {
                var result = await _dbContext.ExecuteAsync(async db =>
                {
                    return await db.Ado.UseStoredProcedure()
                        .SqlQueryAsync<DepartmentOperationView>(
                            "PUBDB.dbo.usp_pbGetAndroidDepartmentOperations");
                });

                var departments = result
                    .GroupBy(x => x.Department)
                    .Where(g => !string.IsNullOrEmpty(g.Key))
                    .Select(g => new Department
                    {
                        Code = g.Key,
                        Description = g.First().Department_Description,
                        Operations = g.Where(x => !string.IsNullOrEmpty(x.OpType))
                            .Select(x => new Operation
                            {
                                Code = x.OpType,
                                Description = x.OpDescription,
                                DepartmentCode = g.Key,
                                AllowedDeviceIds = (x.AllowedDeviceIds ?? "")
                                    .Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries)
                                    .ToList(),
                                IsAutoNavigate = x.IsAutoNavigate
                            }).ToList()
                    })
                    .ToList();

                return ApiResponse<List<Department>>.Ok(departments, "获取成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取部门信息失败");
                return ApiResponse<List<Department>>.Fail(
                    $"获取失败: {ex.Message}",
                    (int)ApiErrorCodes.DatabaseError);
            }
        }
    }
}
