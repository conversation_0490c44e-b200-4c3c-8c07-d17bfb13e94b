using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using CustomerWebAPI.Models;
using CustomerWebAPI.Common;
using System.Linq;

namespace CustomerWebAPI.Filters
{
    public class ValidationFilter : IActionFilter
    {
        public void OnActionExecuting(ActionExecutingContext context)
        {
            if (!context.ModelState.IsValid)
            {
                // 获取所有验证错误信息
                var errors = string.Join("; ", 
                    context.ModelState.Values
                        .SelectMany(v => v.Errors)
                        .Select(e => e.ErrorMessage)
                );

                var response = ApiResponse<string>.Fail(errors, (int)ApiErrorCodes.ValidationError);
                context.Result = new BadRequestObjectResult(response);
            }
        }

        public void OnActionExecuted(ActionExecutedContext context)
        {
        }
    }
} 