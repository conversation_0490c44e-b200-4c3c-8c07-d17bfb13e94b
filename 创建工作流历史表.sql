-- =============================================
-- 创建工作流历史表
-- 创建日期: 2024-12-24
-- 描述: 用于存储报修单的所有操作历史记录
-- =============================================

USE [EquipmentManagement]
GO

-- =============================================
-- 1. 创建工作流历史表 (RepairWorkflowHistory)
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[RepairWorkflowHistory]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[RepairWorkflowHistory](
        [Id] [int] IDENTITY(1,1) NOT NULL,
        [RepairOrderId] [int] NOT NULL,
        [UserId] [int] NOT NULL,
        [Action] [nvarchar](100) NOT NULL,
        [Comment] [nvarchar](1000) NULL,
        [FromStatus] [int] NULL,
        [ToStatus] [int] NULL,
        [CreatedAt] [datetime] NOT NULL DEFAULT(GETDATE()),
        [AdditionalData] [nvarchar](max) NULL, -- JSON格式存储额外数据
        CONSTRAINT [PK_RepairWorkflowHistory] PRIMARY KEY CLUSTERED ([Id] ASC),
        CONSTRAINT [FK_RepairWorkflowHistory_RepairOrders] FOREIGN KEY([RepairOrderId]) REFERENCES [dbo].[RepairOrders] ([Id]),
        CONSTRAINT [FK_RepairWorkflowHistory_Users] FOREIGN KEY([UserId]) REFERENCES [dbo].[Users] ([Id])
    )
END
GO

-- 创建索引以提高查询性能
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[RepairWorkflowHistory]') AND name = N'IX_RepairWorkflowHistory_RepairOrderId')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_RepairWorkflowHistory_RepairOrderId] ON [dbo].[RepairWorkflowHistory]
    (
        [RepairOrderId] ASC
    )
END
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[RepairWorkflowHistory]') AND name = N'IX_RepairWorkflowHistory_CreatedAt')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_RepairWorkflowHistory_CreatedAt] ON [dbo].[RepairWorkflowHistory]
    (
        [CreatedAt] DESC
    )
END
GO

-- =============================================
-- 2. 创建工作流历史视图 (V_RepairWorkflowHistoryDetails)
-- =============================================
IF EXISTS (SELECT * FROM sys.views WHERE object_id = OBJECT_ID(N'[dbo].[V_RepairWorkflowHistoryDetails]'))
    DROP VIEW [dbo].[V_RepairWorkflowHistoryDetails]
GO

CREATE VIEW [dbo].[V_RepairWorkflowHistoryDetails]
AS
SELECT
    rwh.Id,
    rwh.RepairOrderId,
    ro.OrderNumber,
    rwh.UserId,
    u.Name AS UserName,
    u.Username,
    rwh.Action,
    rwh.Comment,
    rwh.FromStatus,
    rwh.ToStatus,
    CASE rwh.FromStatus
        WHEN 1 THEN N'待处理'
        WHEN 2 THEN N'处理中'
        WHEN 3 THEN N'已完成'
        WHEN 4 THEN N'已作废'
        WHEN 5 THEN N'已关闭'
        WHEN 6 THEN N'已暂停'
        WHEN 7 THEN N'待审批'
        ELSE N'未知'
    END AS FromStatusName,
    CASE rwh.ToStatus
        WHEN 1 THEN N'待处理'
        WHEN 2 THEN N'处理中'
        WHEN 3 THEN N'已完成'
        WHEN 4 THEN N'已作废'
        WHEN 5 THEN N'已关闭'
        WHEN 6 THEN N'已暂停'
        WHEN 7 THEN N'待审批'
        ELSE N'未知'
    END AS ToStatusName,
    rwh.CreatedAt,
    rwh.AdditionalData,
    e.Name AS EquipmentName,
    e.Code AS EquipmentCode
FROM [dbo].[RepairWorkflowHistory] rwh
    INNER JOIN [dbo].[RepairOrders] ro ON rwh.RepairOrderId = ro.Id
    INNER JOIN [dbo].[Users] u ON rwh.UserId = u.Id
    INNER JOIN [dbo].[Equipment] e ON ro.EquipmentId = e.Id
GO

-- =============================================
-- 3. 创建存储过程：添加工作流历史记录
-- =============================================
IF EXISTS (SELECT * FROM sys.procedures WHERE object_id = OBJECT_ID(N'[dbo].[sp_AddWorkflowHistory]'))
    DROP PROCEDURE [dbo].[sp_AddWorkflowHistory]
GO

CREATE PROCEDURE [dbo].[sp_AddWorkflowHistory]
    @RepairOrderId INT,
    @UserId INT,
    @Action NVARCHAR(100),
    @Comment NVARCHAR(1000) = NULL,
    @FromStatus INT = NULL,
    @ToStatus INT = NULL,
    @AdditionalData NVARCHAR(MAX) = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        INSERT INTO [dbo].[RepairWorkflowHistory]
        (
            RepairOrderId,
            UserId,
            Action,
            Comment,
            FromStatus,
            ToStatus,
            AdditionalData
        )
        VALUES
        (
            @RepairOrderId,
            @UserId,
            @Action,
            @Comment,
            @FromStatus,
            @ToStatus,
            @AdditionalData
        );
        
        SELECT SCOPE_IDENTITY() AS NewId;
    END TRY
    BEGIN CATCH
        THROW;
    END CATCH
END
GO

-- =============================================
-- 4. 创建存储过程：获取报修单工作流历史
-- =============================================
IF EXISTS (SELECT * FROM sys.procedures WHERE object_id = OBJECT_ID(N'[dbo].[sp_GetRepairWorkflowHistory]'))
    DROP PROCEDURE [dbo].[sp_GetRepairWorkflowHistory]
GO

CREATE PROCEDURE [dbo].[sp_GetRepairWorkflowHistory]
    @RepairOrderId INT
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT
        Id,
        RepairOrderId,
        OrderNumber,
        UserId,
        UserName,
        Username,
        Action,
        Comment,
        FromStatus,
        ToStatus,
        FromStatusName,
        ToStatusName,
        CreatedAt,
        AdditionalData,
        EquipmentName,
        EquipmentCode
    FROM [dbo].[V_RepairWorkflowHistoryDetails]
    WHERE RepairOrderId = @RepairOrderId
    ORDER BY CreatedAt DESC;
END
GO

-- =============================================
-- 5. 插入一些示例历史记录（用于测试）
-- =============================================
-- 注意：这里只是示例，实际使用时应该通过应用程序插入
/*
-- 获取第一个报修单ID用于测试
DECLARE @TestRepairOrderId INT;
SELECT TOP 1 @TestRepairOrderId = Id FROM RepairOrders;

-- 获取管理员用户ID
DECLARE @AdminUserId INT;
SELECT @AdminUserId = Id FROM Users WHERE Username = 'admin';

IF @TestRepairOrderId IS NOT NULL AND @AdminUserId IS NOT NULL
BEGIN
    -- 插入创建报修单的历史记录
    INSERT INTO RepairWorkflowHistory (RepairOrderId, UserId, Action, Comment, FromStatus, ToStatus)
    VALUES (@TestRepairOrderId, @AdminUserId, '创建报修单', '用户提交了新的报修申请', NULL, 1);
    
    -- 插入分配维修人员的历史记录
    INSERT INTO RepairWorkflowHistory (RepairOrderId, UserId, Action, Comment, FromStatus, ToStatus)
    VALUES (@TestRepairOrderId, @AdminUserId, '分配维修人员', '将报修单分配给维修技术员', 1, 1);
    
    -- 插入开始维修的历史记录
    INSERT INTO RepairWorkflowHistory (RepairOrderId, UserId, Action, Comment, FromStatus, ToStatus)
    VALUES (@TestRepairOrderId, @AdminUserId, '开始维修', '维修技术员开始处理报修单', 1, 2);
END
*/

PRINT '工作流历史表创建完成！';
PRINT '包含以下对象：';
PRINT '  - 表: RepairWorkflowHistory';
PRINT '  - 视图: V_RepairWorkflowHistoryDetails';
PRINT '  - 存储过程: sp_AddWorkflowHistory';
PRINT '  - 存储过程: sp_GetRepairWorkflowHistory';
PRINT '  - 索引: IX_RepairWorkflowHistory_RepairOrderId, IX_RepairWorkflowHistory_CreatedAt';
