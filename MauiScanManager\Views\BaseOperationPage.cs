using MauiScanManager.Models;
using MauiScanManager.ViewModels;

namespace MauiScanManager.Views
{
    [QueryProperty(nameof(Operation), "Operation")]
    public abstract class BaseOperationPage : ContentPage
    {
        protected readonly BaseOperationViewModel _viewModel;
        private Operation _operation;

        protected BaseOperationPage(BaseOperationViewModel viewModel)
        {
            _viewModel = viewModel;
            BindingContext = viewModel;
        }

        public Operation Operation
        {
            set
            {
                _operation = value;
                Initialize(value);
            }
        }

        public virtual void Initialize(Operation operation)
        {
            //MainThread.BeginInvokeOnMainThread(() =>
            //{
            //    Shell.Current.Title = operation?.Description ?? string.Empty;
            //});
            _viewModel.Initialize(operation);
        }
    }
} 