# 权限控制系统使用说明

## 概述

本系统实现了完整的基于角色的权限控制（RBAC），用户需要登录后才能访问相应的功能页面。系统根据用户的权限动态显示导航菜单和页面内容。

## 系统架构

### 核心组件

1. **认证服务 (IAuthenticationService)**
   - 用户登录/注销
   - 权限验证
   - 用户状态管理

2. **权限控制组件**
   - `AuthGuard`: 认证守卫，保护需要登录的页面
   - `AuthorizeView`: 权限视图，根据权限显示内容
   - `RouteGuard`: 路由守卫，自动重定向未认证用户

3. **页面保护**
   - 所有受保护页面都使用 `AuthGuard` 包装
   - 特定功能使用 `AuthorizeView` 进行细粒度权限控制

## 测试账号

### 管理员账号
- **用户名**: admin
- **密码**: admin123
- **权限**: 全部功能访问权限
- **可访问页面**: 首页、计数器、天气预报、设备扫描、设备报修、摄像头测试、用户管理

### 操作员账号
- **用户名**: operator
- **密码**: op123
- **权限**: 设备相关功能
- **可访问页面**: 首页、设备扫描、设备报修、摄像头测试

### 访客账号
- **用户名**: viewer
- **密码**: view123
- **权限**: 仅查看基础信息
- **可访问页面**: 首页、天气预报

## 权限系统特性

### 1. 动态导航菜单
- 根据用户权限动态显示菜单项
- 未登录用户只显示登录链接
- 已登录用户显示有权限的功能菜单

### 2. 页面级权限控制
- 使用 `AuthGuard` 保护整个页面
- 未登录用户自动跳转到登录页面
- 登录成功后自动返回原页面

### 3. 功能级权限控制
- 使用 `AuthorizeView` 控制页面内特定功能
- 支持单个权限或多个权限验证
- 可配置权限不足时的提示内容

### 4. 自动状态同步
- 认证状态变化时自动更新UI
- 支持多个组件同时监听状态变化
- 注销时自动清理用户信息

## 使用方法

### 1. 启动应用
```bash
# Web版本
cd CoreHub.Web
dotnet run

# MAUI版本
cd CoreHub
dotnet build
```

### 2. 登录测试
1. 访问任意受保护页面，系统会自动跳转到登录页面
2. 使用测试账号登录
3. 登录成功后自动返回原页面

### 3. 权限测试
1. 使用不同角色的账号登录
2. 观察导航菜单的变化
3. 尝试访问不同的功能页面
4. 验证权限控制是否生效

## 权限配置

### 权限命名规范
```
功能模块.操作类型
例如：
- Home.View (首页查看)
- DeviceScanner.View (设备扫描查看)
- UserManagement.View (用户管理查看)
```

### 添加新权限
1. 在 `AuthenticationService.cs` 中为用户添加新权限
2. 在页面中使用 `AuthorizeView` 包装需要保护的内容
3. 在导航菜单中添加相应的权限检查

### 添加新用户角色
1. 在 `AuthenticationService.InitializeUsers()` 方法中添加新用户
2. 配置用户的权限列表
3. 测试新用户的权限是否正确

## 安全特性

### 1. 防止开放重定向
- 登录后返回URL经过验证
- 只允许相对路径重定向
- 防止恶意重定向攻击

### 2. 权限验证
- 前端和后端双重权限验证
- 权限信息存储在Claims中
- 支持细粒度权限控制

### 3. 会话管理
- 用户状态实时同步
- 支持多标签页状态一致性
- 注销时完全清理用户信息

## 扩展建议

### 1. 数据库集成
- 将用户信息存储到数据库
- 实现动态权限管理
- 支持权限的增删改查

### 2. JWT Token
- 使用JWT进行身份验证
- 支持Token刷新机制
- 实现跨域认证

### 3. 审计日志
- 记录用户登录日志
- 记录权限访问日志
- 实现安全审计功能

### 4. 密码策略
- 实现密码复杂度要求
- 支持密码过期策略
- 实现账户锁定机制

## 故障排除

### 常见问题

1. **登录后页面不跳转**
   - 检查 `NavigationManager` 是否正确注入
   - 验证返回URL是否正确编码

2. **权限检查不生效**
   - 确认权限名称拼写正确
   - 检查用户是否已正确登录
   - 验证权限是否已添加到用户Claims中

3. **导航菜单不更新**
   - 确认组件已订阅认证状态变化事件
   - 检查 `StateHasChanged()` 是否被调用

4. **页面权限保护失效**
   - 确认页面已使用 `AuthGuard` 包装
   - 检查权限服务是否正确注册

## 技术支持

如有问题，请检查：
1. 浏览器控制台错误信息
2. 应用程序调试输出
3. 网络请求状态
4. 权限配置是否正确

---

**注意**: 这是一个演示系统，生产环境中请根据实际需求进行安全加固和功能扩展。 