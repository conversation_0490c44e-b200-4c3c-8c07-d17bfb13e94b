﻿using System.Collections.Generic;

public class Header
{
    /// <summary>
    /// 
    /// </summary>
    public string version { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string documentType { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string messageId { get; set; }
}

public class BuyerIdentificationItem
{
    /// <summary>
    /// 
    /// </summary>
    public string type { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string value { get; set; }
}

public class SellerIdentificationItem
{
    /// <summary>
    /// 
    /// </summary>
    public string type { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string value { get; set; }
}

public class ContractIdentification
{
    /// <summary>
    /// 
    /// </summary>
    public int tradecardContractId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string contractNumber { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string poNumber { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string buyerMemberId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public List<BuyerIdentificationItem> buyerIdentification { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string sellerMemberId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public List<SellerIdentificationItem> sellerIdentification { get; set; }
}

public class OrderIdentification
{
    /// <summary>
    /// 
    /// </summary>
    public string tradecardContractId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string contractNumber { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string poNumber { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string buyerMemberId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public List<BuyerIdentificationItem> buyerIdentification { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string sellerMemberId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public List<SellerIdentificationItem> sellerIdentification { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public ContractIdentification contractIdentification { get; set; }
}

public class TradecardProductIdentification
{
    /// <summary>
    /// 
    /// </summary>
    public string transactionTypeCode { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string productOwner { get; set; }
}

public class PaymentTerms
{
    /// <summary>
    /// 
    /// </summary>
    public string paymentTenorDaysCode { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string paymentTenorStartDateCode { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string paymentTenorNotes { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string paymentTenorBaseDate { get; set; }
}

public class ShipmentDestination
{
    /// <summary>
    /// 
    /// </summary>
    public string destinationKey { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string longName { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public Address address { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string phone { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string fax { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public Contact contact { get; set; }
}

public class Reference
{
    /// <summary>
    /// 
    /// </summary>
    public string type { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string value { get; set; }
}

public class AdjustmentAllowedItem
{
    /// <summary>
    /// 
    /// </summary>
    public string adjustmentTypeCode { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string adjustmentValue { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string isFlatAmount { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string reasonType { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string reasonDescription { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string taxRate { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string adjuestmentkey { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public Reference reference { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string comment { get; set; }
}

public class OrderVariance
{
    /// <summary>
    /// 
    /// </summary>
    public string upperVariance { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string lowerVariance { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string varianceTypeCode { get; set; }
}

public class AdditionalConditionItem
{
    /// <summary>
    /// 
    /// </summary>
    public string additionalConditionText { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string additionalConditionAcknowledgementCode { get; set; }
}

public class AdditionalDocumentRequiredItem
{
    /// <summary>
    /// 
    /// </summary>
    public string documentName { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string responsiblePartyCode { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string referenceNumber { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string notes { get; set; }
}

public class PackageMarkDetail
{
    /// <summary>
    /// 
    /// </summary>
    public string markNumber { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string mark { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string instruction { get; set; }
}

public class OrderTerms
{
    /// <summary>
    /// 
    /// </summary>
    public string letterOfCreditNumber { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string issueDate { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string offerExpiryDate { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string cancelAfterDate { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string revisionNumber { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public List<ReferenceItem> reference { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string paymentInitiationTypeCode { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string settlementMethodCode { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string currencyCode { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public PaymentTerms paymentTerms { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string incotermCode { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string incotermLocationCode { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string isPartialShipmentAllowed { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public ShipmentDestination shipmentDestination { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string earliestDate { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string latestDate { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string isInspectionRequired { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string shipmentMethodCode { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public List<AdjustmentAllowedItem> adjustmentAllowed { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public OrderVariance orderVariance { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string isTaxRateCheckedForCompliance { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string beneficiaryStatement { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string beneficiaryStatementAcknowledgementCode { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public List<AdditionalConditionItem> additionalCondition { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string isPodRequired { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string podCompletedByCode { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string isPackingListRequired { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string packingListItemAllocationCode { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public List<AdditionalDocumentRequiredItem> additionalDocumentRequired { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string isTransShipmentAllowed { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string freightPaymentCode { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string freightPaymentExplanation { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public PackageMarkDetail packageMarkDetail { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string consigneeDocumentInstructions { get; set; }
}

public class Buyer
{
    /// <summary>
    /// 
    /// </summary>
    public string memberId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public List<IdentificationItem> identification { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string name { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public Contact contact { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public Address address { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public List<ReferenceItem> reference { get; set; }
}

public class Seller
{
    /// <summary>
    /// 
    /// </summary>
    public string memberId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public List<IdentificationItem> identification { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string name { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public Contact contact { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public Address address { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public List<ReferenceItem> reference { get; set; }
}

public class Address
{
    /// <summary>
    /// 
    /// </summary>
    public string addressLine1 { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string addressLine2 { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string city { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string stateOrProvince { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string postalCodeNumber { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string countryCode { get; set; }
}

public class InspectionCompany
{
    /// <summary>
    /// 
    /// </summary>
    public string memberId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public List<IdentificationItem> identification { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string name { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public Contact contact { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public Address address { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public List<ReferenceItem> reference { get; set; }
}

public class LogisticsProvider
{
    /// <summary>
    /// 
    /// </summary>
    public string memberId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public List<IdentificationItem> identification { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string name { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public Contact contact { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public Address address { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public List<ReferenceItem> reference { get; set; }
}

public class CoverageProvider
{
    /// <summary>
    /// 
    /// </summary>
    public string memberId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public List<IdentificationItem> identification { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string name { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public Contact contact { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public Address address { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public List<ReferenceItem> reference { get; set; }
}

public class BuyersAgent
{
    /// <summary>
    /// 
    /// </summary>
    public string memberId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public List<IdentificationItem> identification { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string name { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public Contact contact { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public Address address { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public List<ReferenceItem> reference { get; set; }
}

public class SellersAgent
{
    /// <summary>
    /// 
    /// </summary>
    public string memberId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public List<IdentificationItem> identification { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string name { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public Contact contact { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public Address address { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public List<ReferenceItem> reference { get; set; }
}

public class Carrier
{
    /// <summary>
    /// 
    /// </summary>
    public string memberId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public List<IdentificationItem> identification { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string name { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public Contact contact { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public Address address { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public List<ReferenceItem> reference { get; set; }
}

public class CustomsBroke
{
    /// <summary>
    /// 
    /// </summary>
    public string memberId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public List<IdentificationItem> identification { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string name { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public Contact contact { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public Address address { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public List<ReferenceItem> reference { get; set; }
}

public class Consignee
{
    /// <summary>
    /// 
    /// </summary>
    public string memberId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public List<IdentificationItem> identification { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string name { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public Contact contact { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public Address address { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public List<ReferenceItem> reference { get; set; }
}

public class ReferenceItem
{
    /// <summary>
    /// 
    /// </summary>
    public string type { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string value { get; set; }
}

public class ReceivedFrom
{
    /// <summary>
    /// 
    /// </summary>
    public string memberId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public List<IdentificationItem> identification { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string name { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public Contact contact { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public Address address { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public List<ReferenceItem> reference { get; set; }
}

public class Contact
{
    /// <summary>
    /// 
    /// </summary>
    public string name { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string department { get; set; }
}

public class NotifyParty
{
    /// <summary>
    /// 
    /// </summary>
    public string memberId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public List<IdentificationItem> identification { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string name { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public Contact contact { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public Address address { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public List<ReferenceItem> reference { get; set; }
}

public class IdentificationItem
{
    /// <summary>
    /// 
    /// </summary>
    public string type { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string value { get; set; }
}

public class AdditionalParty
{
    /// <summary>
    /// 
    /// </summary>
    public string memberId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public List<IdentificationItem> identification { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string name { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public Contact contact { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public Address address { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public List<ReferenceItem> reference { get; set; }
}

public class CustomsClassificationItem
{
    /// <summary>
    /// 
    /// </summary>
    public string classificationNumber { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string countryCode { get; set; }
}

public class ItemReferenceItem
{
    /// <summary>
    /// 
    /// </summary>
    public string type { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string value { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string displayCode { get; set; }
}

public class ItemVariance
{
    /// <summary>
    /// 
    /// </summary>
    public int upperVariance { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int lowerVariance { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string varianceTypeCode { get; set; }
}

public class BaseLineItem
{
    /// <summary>
    /// 
    /// </summary>
    public string itemSequenceNumber { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string buyerNumber { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string sellerNumber { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string shortDescription { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string longDescription { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string upcNumber { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string skuNumber { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string countryOfOriginCode { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public List<CustomsClassificationItem> customsClassification { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string quotaCategory { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public List<ItemReferenceItem> itemReference { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double quantity { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string unitOfMeasureCode { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string packMethodCode { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int quantityPerInnerPackage { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int quantityPerOuterPackage { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string destinationKey { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string earliestDate { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string latestDate { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string isInspectionRequired { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string shipmentMethodCode { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public ItemVariance itemVariance { get; set; }
}

public class LineItemPrice
{
    /// <summary>
    /// 
    /// </summary>
    public double pricePerUnit { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double totalPrice { get; set; }
}

public class LineItemTax
{
    /// <summary>
    /// 
    /// </summary>
    public double taxRate { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double taxAmount { get; set; }
}

public class OrderLineItemItem
{
    /// <summary>
    /// 
    /// </summary>
    public string itemKey { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string itemTypeCode { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string parentItemKey { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public BaseLineItem baseLineItem { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public LineItemPrice lineItemPrice { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public LineItemTax lineItemTax { get; set; }
}

public class AttachmentItem
{
    /// <summary>
    /// 
    /// </summary>
    public string name { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string encodingCode { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string mimeType { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string content { get; set; }
}

public class Totals
{
    /// <summary>
    /// 
    /// </summary>
    public double totalQuantity { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double totalMerchandiseAmount { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double totalTaxAmount { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double totalDocumentAmount { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double totalContractAmount { get; set; }
}

public class OrderParties
{
    /// <summary>
    /// 
    /// </summary>
    public Buyer buyer { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public Seller seller { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public InspectionCompany inspectionCompany { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public LogisticsProvider logisticsProvider { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public CoverageProvider coverageProvider { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public BuyersAgent buyersAgent { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public SellersAgent sellersAgent { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public Carrier carrier { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public CustomsBroke customsBroke { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public Consignee consignee { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public ReceivedFrom receivedFrom { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public NotifyParty notifyParty { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public AdditionalParty additionalParty { get; set; }


    /// <summary>
    /// 
    /// </summary>
    public List<AttachmentItem> attachment { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public Totals totals { get; set; }
}

public class OrderDetailItem
{
    /// <summary>
    /// 
    /// </summary>
    public string subMessageId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string messageFunctionCode { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string eventCode { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string eventDate { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string redirectUrl { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string validationErrorText { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public OrderIdentification orderIdentification { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public TradecardProductIdentification tradecardProductIdentification { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string poNumber { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string contractNumber { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int tradecardContractId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string effectiveDate { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string orderFunctionCode { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string debitBankAccountNumber { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string complianceTemplateCode { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public OrderTerms orderTerms { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public OrderParties orderParties { get; set; }


    /// <summary>
    /// ******** 
    /// </summary>
    public List<OrderLineItemItem> orderLineItem { get; set; }
}

public class Order
{
    /// <summary>
    /// 
    /// </summary>
    public Header header { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int count { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public List<OrderDetailItem> orderDetail { get; set; }
}

public class TALPO
{
    /// <summary>
    /// 
    /// </summary>
    public Order Order { get; set; }
}