﻿using CustomerWebAPI.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using System.Threading.Tasks;

namespace CustomerWebAPI.Controllers
{
    [Authorize, Route("api/[controller]"), ApiController]
    public class LoginController : Controller
    {
        private readonly IConfiguration _config;
        private readonly LoginService _loginService;

        public LoginController(IConfiguration config,LoginService loginService)
        {
            _config = config;
            _loginService = loginService;
        }
        /*
        /// <summary>
        /// 使用url参数进行提交
        /// </summary>
        /// <param name="username"></param>
        /// <param name="password"></param>
        /// <returns></returns>
        [AllowAnonymous, HttpGet]
        public IActionResult Login(string username, string password)
        {
            UserModel login = new UserModel();
            login.Username = username;
            login.Password = password;
            IActionResult response = Unauthorized();

            var user = AuthenticateUser(login);

            if (user != null)
            {
                var version = JsonHelper.ReadJSON();

                var tokenStr = GenarateJSONWebToken(user);
                response = Ok(new
                {
                    token = tokenStr,
                    version
                });
            }

            return response;
        }


        /// <summary>
        /// 使用body内容进行提交
        /// </summary>
        /// <param name="userModel"></param>
        /// <returns></returns>
        [AllowAnonymous, HttpPost("Authenticate")]
        public IActionResult Authenticate([FromBody]UserModel userModel)
        {
            UserModel login = new UserModel();
            login.Username = userModel.Username;
            login.Password = userModel.Password;
            IActionResult response = Unauthorized();

            var user = AuthenticateUser(login);
            if (user != null)
            {
                var tokenStr = GenarateJSONWebToken(user);
                response = Ok(new
                {
                    token = tokenStr
                });
            }

            return response;
        }


        private UserModel AuthenticateUser(UserModel login)
        {
            UserModel user = null;

            if (login.Username == "TAL" && login.Password == "Saintyeartex")
            {
                user = new UserModel
                {
                    Username = "TAL",
                    EmailAddress = "<EMAIL>",
                    Password = "Saintyeartex"
                };
            }

            return user;
        }


        private string GenarateJSONWebToken(UserModel userinfo)
        {
            var securitykey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_config["Jwt:Key"]));
            var credentials = new SigningCredentials(securitykey, SecurityAlgorithms.HmacSha256);

            var claims = new[]
            {
                new Claim(JwtRegisteredClaimNames.Sub, userinfo.Username),
                new Claim(JwtRegisteredClaimNames.Email, userinfo.EmailAddress),
                new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString())
            };

            var token = new JwtSecurityToken(_config["Jwt:Issuer"], _config["Jwt:Issuer"], claims, expires: DateTime.Now.AddMinutes(120), signingCredentials: credentials);

            var jsonWebToken = new JwtSecurityTokenHandler().WriteToken(token);
            return jsonWebToken;
        }

        // [Authorize]

        [HttpPost("Post")]
        public string Post()
        {
            var identity = HttpContext.User.Identity as ClaimsIdentity;
            IList<Claim> claim = identity.Claims.ToList();
            var userName = claim[0].Value;
            return "Welcome to:" + userName;
        }

        //[Authorize]
        [AllowAnonymous, HttpGet("GetValue")]
        public ActionResult<IEnumerable<string>> Get()
        {
            return new[]
            {
                "Value1",
                "Value2",
                "Value3"
            };
        }
        */
        //[AllowAnonymous, HttpGet("loginDomain")]
        //public ActionResult<IEnumerable<string>> Get2()
        //{
        //    return new[]
        //    {
        //        "Value1",
        //        "Value2",
        //        "Value3"
        //    };
        //}

        [HttpPost("AuthDomainUser")]
        public IActionResult AuthDomainUser(dynamic loginInfo)
        {
            (bool IsSuccess, string loginMsg) authResult = _loginService.ValidateLogin(loginInfo); ;

            return authResult.IsSuccess?Ok(JsonConvert.SerializeObject(authResult)):BadRequest(JsonConvert.SerializeObject(authResult));


        }


    }
}