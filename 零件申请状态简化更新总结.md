# 🔄 零件申请状态简化更新总结

## 📋 更新概述

根据用户需求，去掉了"已批准"状态，简化了零件申请的状态流转流程，使流程更加直接和高效。

## ✅ 状态设计变更

### 🔄 原状态设计（5个状态）
```
1 = 申请中
2 = 已批准    ← 删除此状态
3 = 已领用
4 = 已安装
5 = 已取消
```

### 🎯 新状态设计（4个状态）
```
1 = 申请中    -- 初始状态
2 = 已领用    -- 从仓库领取（原状态3）
3 = 已安装    -- 安装完成（原状态4）
4 = 已取消    -- 申请取消（原状态5）
```

### 🔄 状态流转规则简化

#### 原流转规则
```
申请中(1) → 已批准(2) → 已领用(3) → 已安装(4)
           ↓         ↓         ↓
         已取消(5)  已取消(5)  已取消(5)
```

#### 新流转规则
```
申请中(1) → 已领用(2) → 已安装(3)
           ↓         ↓
         已取消(4)  已取消(4)
```

## ✅ 已更新的文件

### 1. 实体模型
- **RepairOrderPartRequest.cs**
  - 状态注释更新
  - StatusName属性更新
  - 权限控制属性更新（去掉CanApprove）

### 2. 数据传输对象
- **PartReplacementRequestDto.cs**
  - 状态注释更新
  - StatusName属性更新
  - CompletedCount计算更新

### 3. 关联模型
- **RepairOrder.cs**
  - CompletedPartRequestsCount更新
  - CancelledPartRequestsCount更新

### 4. 服务实现
- **RepairOrderPartRequestService.cs**
  - 删除ApprovePartRequestAsync方法
  - 删除RejectPartRequestAsync方法
  - 更新ConfirmPartIssueAsync（从状态1直接到状态2）
  - 更新ConfirmPartInstallationAsync（从状态2到状态3）
  - 更新CancelPartRequestAsync（取消状态改为4）
  - 更新状态流转验证规则
  - 更新查询条件中的状态判断

### 5. 服务接口
- **IRepairOrderPartRequestService.cs**
  - 删除ApprovePartRequestAsync方法声明
  - 删除RejectPartRequestAsync方法声明

### 6. 数据库脚本
- **维修单零件更换记录字段迁移脚本.sql**
  - 更新状态注释
  - 更新视图中的状态名称映射
  - 更新统计视图中的状态计算

## 🔧 技术影响

### 1. 简化的业务流程
```csharp
// 原流程：申请 → 批准 → 发放 → 安装
await partRequestService.ApprovePartRequestAsync(id, approverId);
await partRequestService.ConfirmPartIssueAsync(id, issuerId, orderNumber);
await partRequestService.ConfirmPartInstallationAsync(id, installerId);

// 新流程：申请 → 发放 → 安装
await partRequestService.ConfirmPartIssueAsync(id, issuerId, orderNumber);
await partRequestService.ConfirmPartInstallationAsync(id, installerId);
```

### 2. 状态流转验证更新
```csharp
public bool IsValidStatusTransition(int currentStatus, int newStatus)
{
    var validTransitions = new Dictionary<int, List<int>>
    {
        { 1, new List<int> { 2, 4 } }, // 申请中 → 已领用或已取消
        { 2, new List<int> { 3, 4 } }, // 已领用 → 已安装或已取消
        { 3, new List<int>() },        // 已安装 → 无法变更
        { 4, new List<int>() }         // 已取消 → 无法变更
    };
    
    return validTransitions.ContainsKey(currentStatus) && 
           validTransitions[currentStatus].Contains(newStatus);
}
```

### 3. 数据库操作更新
```sql
-- 直接从申请中到已领用
UPDATE RepairOrderPartRequests 
SET Status = 2, IssuedBy = @IssuerId, IssuedAt = GETDATE()
WHERE Id = @Id AND Status = 1;

-- 从已领用到已安装
UPDATE RepairOrderPartRequests 
SET Status = 3, InstalledBy = @InstallerId, InstalledAt = GETDATE()
WHERE Id = @Id AND Status = 2;

-- 取消申请
UPDATE RepairOrderPartRequests 
SET Status = 4, UpdatedAt = GETDATE()
WHERE Id = @Id AND Status IN (1, 2);
```

## 📊 数据库查询更新

### 1. 状态统计查询
```sql
-- 新的状态统计
SELECT 
    SUM(CASE WHEN Status = 1 THEN 1 ELSE 0 END) as PendingRequests,    -- 申请中
    SUM(CASE WHEN Status = 2 THEN 1 ELSE 0 END) as IssuedRequests,     -- 已领用
    SUM(CASE WHEN Status = 3 THEN 1 ELSE 0 END) as InstalledRequests,  -- 已安装
    SUM(CASE WHEN Status = 4 THEN 1 ELSE 0 END) as CancelledRequests   -- 已取消
FROM RepairOrderPartRequests;
```

### 2. 完成率计算
```sql
-- 完成率计算（已安装/总数）
SELECT 
    COUNT(*) as TotalRequests,
    SUM(CASE WHEN Status = 3 THEN 1 ELSE 0 END) as CompletedRequests,
    CASE 
        WHEN COUNT(*) = 0 THEN 100
        ELSE CAST(SUM(CASE WHEN Status = 3 THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DECIMAL(5,2))
    END as CompletionPercentage
FROM RepairOrderPartRequests
WHERE RepairOrderId = @RepairOrderId;
```

### 3. 待处理记录查询
```sql
-- 获取未完成的记录（申请中和已领用）
SELECT * FROM RepairOrderPartRequests 
WHERE Status < 3  -- 状态1和2
ORDER BY RequestedAt;
```

## 🔄 外部系统集成影响

### 1. 状态回写更新
```sql
-- 外部系统回写时的状态映射
-- 原来的状态3（已领用）现在是状态2
-- 原来的状态4（已安装）现在是状态3
-- 原来的状态5（已取消）现在是状态4

UPDATE RepairOrderPartRequests 
SET 
    ExternalPartNumber = @ExternalPartNumber,
    Status = CASE @ExternalStatus
        WHEN 3 THEN 2  -- 外部系统的"已领用"映射到新的状态2
        WHEN 4 THEN 3  -- 外部系统的"已安装"映射到新的状态3
        WHEN 5 THEN 4  -- 外部系统的"已取消"映射到新的状态4
        ELSE @ExternalStatus
    END,
    UpdatedAt = GETDATE()
WHERE Id = @Id;
```

### 2. 查询接口保持不变
- 根据外部零件编号查询：`GetPartRequestByExternalPartNumberAsync`
- 根据外部领用单明细ID查询：`GetPartRequestByExternalRequisitionDetailIdAsync`
- 批量回写接口：`BatchUpdateFromExternalSystemAsync`

## 💡 业务优势

### 1. 流程简化
- 减少了一个审批环节，提高处理效率
- 降低了系统复杂度
- 减少了状态管理的复杂性

### 2. 用户体验改善
- 更直观的状态流转
- 减少了不必要的等待环节
- 简化了操作步骤

### 3. 系统维护
- 减少了状态相关的业务逻辑
- 简化了权限控制
- 降低了测试复杂度

## ⚠️ 注意事项

### 1. 数据迁移
如果系统中已有数据，需要执行状态迁移：
```sql
-- 将现有数据的状态进行映射
UPDATE RepairOrderPartRequests 
SET Status = CASE Status
    WHEN 2 THEN 1  -- 已批准 → 申请中（需要重新处理）
    WHEN 3 THEN 2  -- 已领用 → 已领用
    WHEN 4 THEN 3  -- 已安装 → 已安装
    WHEN 5 THEN 4  -- 已取消 → 已取消
    ELSE Status
END;
```

### 2. 外部系统适配
- 外部系统需要更新状态映射逻辑
- API文档需要更新状态定义
- 测试用例需要相应调整

### 3. 用户培训
- 需要通知用户状态变更
- 更新操作手册
- 调整用户界面提示

## 📁 影响的文件清单

### 核心文件
1. **RepairOrderPartRequest.cs** - 实体模型
2. **PartReplacementRequestDto.cs** - 数据传输对象
3. **RepairOrder.cs** - 关联模型
4. **RepairOrderPartRequestService.cs** - 服务实现
5. **IRepairOrderPartRequestService.cs** - 服务接口

### 数据库文件
6. **维修单零件更换记录字段迁移脚本.sql** - 数据库脚本

### 文档文件
7. **零件申请状态简化更新总结.md** - 本文档

## 🚀 部署建议

1. **测试环境验证**
   - 在测试环境中验证所有状态流转
   - 测试外部系统集成
   - 验证数据查询和统计

2. **生产环境部署**
   - 备份现有数据
   - 执行状态迁移脚本
   - 部署更新的代码
   - 验证功能正常

3. **监控和回滚**
   - 监控系统运行状态
   - 准备回滚方案
   - 收集用户反馈

---

**版本**: 2.2.0  
**更新日期**: 2025-01-07  
**状态**: ✅ 简化完成  
**影响**: 去掉"已批准"状态，简化业务流程
