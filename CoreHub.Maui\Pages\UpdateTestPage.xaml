<?xml version="1.0" encoding="utf-8" ?>
<ContentPage x:Class="CoreHub.Maui.Pages.UpdateTestPage"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             Title="更新测试">
    <ScrollView>
        <StackLayout Padding="20" Spacing="15">
            
            <!-- 标题 -->
            <Label Text="更新功能测试" 
                   FontSize="24" 
                   FontAttributes="Bold" 
                   HorizontalOptions="Center" 
                   Margin="0,0,0,20"/>
            
            <!-- 当前版本信息 -->
            <Frame BackgroundColor="LightBlue" Padding="15">
                <StackLayout>
                    <Label Text="当前版本信息" FontAttributes="Bold" FontSize="16"/>
                    <Label x:Name="CurrentVersionLabel" Text="版本: 加载中..." FontSize="14"/>
                    <Label x:Name="CurrentVersionCodeLabel" Text="版本代码: 加载中..." FontSize="14"/>
                </StackLayout>
            </Frame>
            
            <!-- 测试下载区域 -->
            <Frame BackgroundColor="LightGreen" Padding="15">
                <StackLayout>
                    <Label Text="直接下载测试" FontAttributes="Bold" FontSize="16"/>
                    
                    <!-- 下载模式选择 -->
                    <Label Text="下载模式:" FontSize="14"/>
                    <Picker x:Name="DownloadModePicker"
                            FontSize="14"
                            SelectedIndexChanged="OnDownloadModeChanged">
                        <Picker.Items>
                            <x:String>下载最新版本</x:String>
                            <x:String>下载指定版本</x:String>
                        </Picker.Items>
                    </Picker>

                    <!-- 下载URL显示 -->
                    <Label Text="下载URL:" FontSize="14"/>
                    <Entry x:Name="DownloadUrlEntry"
                           Text="/api/AppUpdate/download/android/latest"
                           IsReadOnly="True"
                           FontSize="14"/>

                    <!-- 版本号输入 -->
                    <Label x:Name="VersionLabel" Text="版本号:" FontSize="14" IsVisible="False"/>
                    <Entry x:Name="VersionNumberEntry"
                           Text="1.0.1"
                           Placeholder="输入版本号"
                           FontSize="14"
                           IsVisible="False"
                           TextChanged="OnVersionNumberChanged"/>
                    
                    <!-- 下载按钮 -->
                    <Button x:Name="DownloadButton" 
                            Text="开始下载" 
                            BackgroundColor="Green" 
                            TextColor="White"
                            Clicked="OnDownloadClicked"/>
                    
                    <!-- 进度条 -->
                    <ProgressBar x:Name="DownloadProgressBar" 
                                 Progress="0" 
                                 IsVisible="False"/>
                    
                    <!-- 进度文本 -->
                    <Label x:Name="ProgressLabel" 
                           Text="" 
                           FontSize="12" 
                           IsVisible="False"/>
                </StackLayout>
            </Frame>
            
            <!-- 安装区域 -->
            <Frame BackgroundColor="LightCoral" Padding="15">
                <StackLayout>
                    <Label Text="安装测试" FontAttributes="Bold" FontSize="16"/>
                    
                    <Label x:Name="DownloadedFileLabel" 
                           Text="下载的文件: 无" 
                           FontSize="14"/>
                    
                    <Button x:Name="InstallButton" 
                            Text="安装APK" 
                            BackgroundColor="Red" 
                            TextColor="White"
                            IsEnabled="False"
                            Clicked="OnInstallClicked"/>
                </StackLayout>
            </Frame>
            
            <!-- 日志区域 -->
            <Frame BackgroundColor="LightGray" Padding="15">
                <StackLayout>
                    <Label Text="操作日志" FontAttributes="Bold" FontSize="16"/>
                    
                    <Button Text="清空日志" 
                            BackgroundColor="Gray" 
                            TextColor="White"
                            Clicked="OnClearLogClicked"/>
                    
                    <ScrollView HeightRequest="200">
                        <Label x:Name="LogLabel" 
                               Text="等待操作..." 
                               FontSize="12" 
                               FontFamily="Courier"/>
                    </ScrollView>
                </StackLayout>
            </Frame>
            
        </StackLayout>
    </ScrollView>
</ContentPage>
