-- =============================================
-- 报修单状态一致性验证脚本
-- 验证数据库脚本与系统代码中的状态定义是否一致
-- =============================================

USE [CoreHub]
GO

PRINT '开始验证报修单状态定义一致性...'
PRINT '========================================'

-- 系统实际使用的状态定义（来自 RepairOrderStatusHelper.cs）
PRINT '系统状态定义（连续状态值）：'
PRINT '1 = 待处理'
PRINT '2 = 处理中'
PRINT '3 = 已完成'
PRINT '4 = 已作废'
PRINT '5 = 已关闭'
PRINT '6 = 待确认'
PRINT '注意：移除了"已暂停"状态，状态值现在是连续的'
PRINT ''

-- 测试状态映射
PRINT '测试状态名称映射：'

-- 创建测试数据
DECLARE @TestStatuses TABLE (StatusValue INT, ExpectedName NVARCHAR(10))
INSERT INTO @TestStatuses VALUES
(1, N'待处理'),
(2, N'处理中'),
(3, N'已完成'),
(4, N'已作废'),
(5, N'已关闭'),
(6, N'待确认'),
(7, N'未知'),    -- 状态7应该映射为"未知"
(8, N'未知')     -- 状态8应该映射为"未知"

-- 测试视图中的状态映射
PRINT '验证 V_RepairOrderDetails 视图状态映射：'

DECLARE @status INT = 1
WHILE @status <= 8
BEGIN
    DECLARE @expectedName NVARCHAR(10)
    DECLARE @actualName NVARCHAR(10)
    
    SELECT @expectedName = ExpectedName FROM @TestStatuses WHERE StatusValue = @status
    
    -- 测试状态映射逻辑
    SELECT @actualName = CASE @status
        WHEN 1 THEN N'待处理'
        WHEN 2 THEN N'处理中'
        WHEN 3 THEN N'已完成'
        WHEN 4 THEN N'已作废'
        WHEN 5 THEN N'已关闭'
        WHEN 6 THEN N'待确认'
        ELSE N'未知'
    END
    
    IF @actualName = @expectedName
        PRINT '✓ 状态 ' + CAST(@status AS VARCHAR(2)) + ' = ' + @actualName + ' (正确)'
    ELSE
        PRINT '❌ 状态 ' + CAST(@status AS VARCHAR(2)) + ' = ' + @actualName + ' (期望: ' + @expectedName + ')'
    
    SET @status = @status + 1
END

PRINT ''
PRINT '验证数据库表结构：'

-- 检查 RepairOrders 表是否存在
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[RepairOrders]') AND type in (N'U'))
    PRINT '✓ RepairOrders表存在'
ELSE
    PRINT '❌ RepairOrders表不存在'

-- 检查 Status 字段
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[RepairOrders]') AND name = 'Status')
    PRINT '✓ Status字段存在'
ELSE
    PRINT '❌ Status字段不存在'

-- 检查视图是否存在
IF EXISTS (SELECT * FROM sys.views WHERE object_id = OBJECT_ID(N'[dbo].[V_RepairOrderDetails]'))
    PRINT '✓ V_RepairOrderDetails视图存在'
ELSE
    PRINT '❌ V_RepairOrderDetails视图不存在'

-- 检查工作流历史视图
IF EXISTS (SELECT * FROM sys.views WHERE object_id = OBJECT_ID(N'[dbo].[V_RepairWorkflowHistoryDetails]'))
    PRINT '✓ V_RepairWorkflowHistoryDetails视图存在'
ELSE
    PRINT '❌ V_RepairWorkflowHistoryDetails视图不存在'

PRINT ''
PRINT '验证状态约束：'

-- 检查是否有无效状态的数据
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[RepairOrders]') AND type in (N'U'))
BEGIN
    DECLARE @invalidCount INT
    SELECT @invalidCount = COUNT(*) 
    FROM RepairOrders 
    WHERE Status NOT IN (1, 2, 3, 4, 5, 6)
    
    IF @invalidCount = 0
        PRINT '✓ 所有报修单状态值都有效'
    ELSE
        PRINT '❌ 发现 ' + CAST(@invalidCount AS VARCHAR(10)) + ' 个无效状态的报修单'
        
    -- 显示状态分布
    PRINT ''
    PRINT '当前数据库中的状态分布：'
    SELECT 
        Status,
        CASE Status
            WHEN 1 THEN N'待处理'
            WHEN 2 THEN N'处理中'
            WHEN 3 THEN N'已完成'
            WHEN 4 THEN N'已作废'
            WHEN 5 THEN N'已关闭'
            WHEN 6 THEN N'待确认'
            ELSE N'未知状态'
        END AS StatusName,
        COUNT(*) as Count
    FROM RepairOrders
    GROUP BY Status
    ORDER BY Status
END

PRINT ''
PRINT '========================================'
PRINT '状态一致性验证完成！'
PRINT ''
PRINT '验证工作流历史视图状态映射：'

-- 测试工作流历史视图中的状态映射
SET @status = 1
WHILE @status <= 8
BEGIN
    DECLARE @fromStatusName NVARCHAR(10)
    DECLARE @toStatusName NVARCHAR(10)

    -- 测试FromStatus映射
    SELECT @fromStatusName = CASE @status
        WHEN 1 THEN N'待处理'
        WHEN 2 THEN N'处理中'
        WHEN 3 THEN N'已完成'
        WHEN 4 THEN N'已作废'
        WHEN 5 THEN N'已关闭'
        WHEN 6 THEN N'已暂停'
        WHEN 8 THEN N'待确认'
        ELSE N'未知'
    END

    -- 测试ToStatus映射
    SELECT @toStatusName = CASE @status
        WHEN 1 THEN N'待处理'
        WHEN 2 THEN N'处理中'
        WHEN 3 THEN N'已完成'
        WHEN 4 THEN N'已作废'
        WHEN 5 THEN N'已关闭'
        WHEN 6 THEN N'待确认'
        ELSE N'未知'
    END

    SELECT @expectedName = ExpectedName FROM @TestStatuses WHERE StatusValue = @status

    IF @fromStatusName = @expectedName AND @toStatusName = @expectedName
        PRINT '✓ 工作流状态 ' + CAST(@status AS VARCHAR(2)) + ' = ' + @fromStatusName + ' (正确)'
    ELSE
        PRINT '❌ 工作流状态 ' + CAST(@status AS VARCHAR(2)) + ' 映射错误'

    SET @status = @status + 1
END

PRINT ''
PRINT '修复内容：'
PRINT '1. 移除了"已暂停"状态（状态6）'
PRINT '2. 将"待确认"状态从8改为6，实现连续状态值'
PRINT '3. 确保数据库脚本与系统代码一致'
PRINT '4. 更新了所有相关的状态映射'
PRINT '5. 修复了工作流历史视图中的状态映射'
PRINT '6. 移除了暂停和恢复相关的工作流方法'
PRINT '========================================'
