using System;
using System.Threading.Tasks;
using MauiScanManager.Services;
using MauiScanManager.Services.EventArgs;
using Microsoft.Maui.Graphics;
using Microsoft.Maui;
using System.Threading;

namespace MauiScanManager.Pages
{
    public partial class UpdatePage : ContentPage
    {
        private readonly IAppUpdateService _updateService;
        
        public UpdatePage(IAppUpdateService updateService)
        {
            InitializeComponent();
            _updateService = updateService;
            
            _updateService.UpdateProgress += OnUpdateProgress;
            StartUpdate();
        }
        
        private async void StartUpdate()
        {
            try
            {
                var success = await _updateService.DownloadAndInstallUpdate();
                if (!success)
                {
                    await DisplayAlert("错误", "安装更新失败", "确定");
                    await Navigation.PopModalAsync();
                }
                else
                {
                    // 添加延迟，确保安装对话框有时间显示
                    await Task.Delay(2000);
                    // 如果安装对话框没有显示，提示用户
                    var retry = await DisplayAlert("提示", 
                        "如果没有看到安装提示，是否重试？", 
                        "重试", "取消");
                    
                    if (retry)
                    {
                        StartUpdate();
                    }
                    else
                    {
                        await Navigation.PopModalAsync();
                    }
                }
            }
            catch (Exception ex)
            {
                LogService.Log($"更新过程出错: {ex.Message}");
                await DisplayAlert("错误", "更新过程出错", "确定");
                await Navigation.PopModalAsync();
            }
        }
        
        private void OnUpdateProgress(object sender, UpdateProgressEventArgs e)
        {
            MainThread.BeginInvokeOnMainThread(() =>
            {
                UpdateProgress.Progress = e.Progress;
                StatusLabel.Text = e.Status;
            });
        }
        
        protected override void OnDisappearing()
        {
            base.OnDisappearing();
            _updateService.UpdateProgress -= OnUpdateProgress;
        }

        private async void OnViewLogsClicked(object sender, EventArgs e)
        {
            var logs = LogService.GetLogs();
            await DisplayAlert("更新日志", logs, "确定");
        }
    }
}
