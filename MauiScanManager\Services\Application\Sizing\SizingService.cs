using MauiScanManager.Constants;
using MauiScanManager.Models;
using Microsoft.Extensions.Logging;

namespace MauiScanManager.Services;

public class SizingService : ISizingService
{
    private readonly IApiService _apiService;
    private readonly IPlatformLoadingService _loadingService;
    private readonly ILogger<SizingService> _logger;

    public SizingService(
        IApiService apiService,
        IPlatformLoadingService loadingService,
        ILogger<SizingService> logger)
    {
        _apiService = apiService;
        _loadingService = loadingService;
        _logger = logger;
    }

    public async Task<ServiceResult<SizingMachineUpInfo>> GetMachineUpInfoAsync(SizingMachine sizingMachine)
    {
        _loadingService.ShowNativeLoading("正在获取机台上机信息...");
        try
        {
            var response = await _apiService.PostAsync<SizingMachineUpInfo>(
                ApiEndpoints.Sizing.MachineUpInfo,
                sizingMachine);

            return response.Success 
                ? ServiceResult<SizingMachineUpInfo>.Success(response.Data)
                : ServiceResult<SizingMachineUpInfo>.Failure(response.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取机台上机信息失败");
            return ServiceResult<SizingMachineUpInfo>.Failure(ex.Message);
        }
        finally
        {
            _loadingService.HideNativeLoading();
        }
    }

    public async Task<ServiceResult<string>> UpMachineAsync(SizingUpMachine sizingUpMachine)
    {
        _loadingService.ShowNativeLoading("正在执行上机操作...");
        try
        {
            var model = new SizingUpMachine 
            { 
                SaveType = "up",
                SizingMachineId = sizingUpMachine.SizingMachineId,
                SizingBatchSerialNo = sizingUpMachine.SizingBatchSerialNo,
                WvCardNo = sizingUpMachine.WvCardNo
            };

            var response = await _apiService.PostAsync<string>(ApiEndpoints.Sizing.UpMachine, model);
            return response.Success 
                ? ServiceResult<string>.Success("上机成功")
                : ServiceResult<string>.Failure(response.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "上机失败");
            return ServiceResult<string>.Failure(ex.Message);
        }
        finally
        {
            _loadingService.HideNativeLoading();
        }
    }

    public async Task<ServiceResult<string>> DownMachineAsync(SizingUpMachine sizingUpMachine)
    {
        _loadingService.ShowNativeLoading("正在执行下机操作...");
        try
        {
            var model = new SizingUpMachine
            {
                SaveType = "down",
                SizingMachineId = sizingUpMachine.SizingMachineId,
                SizingBatchSerialNo = sizingUpMachine.SizingBatchSerialNo,
                WvCardNo = sizingUpMachine.WvCardNo
            };

            var response = await _apiService.PostAsync<string>(ApiEndpoints.Sizing.DownMachine, model);
            return response.Success
                ? ServiceResult<string>.Success("下机成功")
                : ServiceResult<string>.Failure(response.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "下机失败");
            return ServiceResult<string>.Failure(ex.Message);
        }
        finally
        {
            _loadingService.HideNativeLoading();
        }
    }

    public async Task<ServiceResult<string>> CancelUpMachineAsync(SizingCancelUpMachine sizingCancelUpMachine)
    {
        _loadingService.ShowNativeLoading("正在取消上机...");
        try
        {
            var response = await _apiService.PostAsync<string>(
                ApiEndpoints.Sizing.CancelUpMachine,
                sizingCancelUpMachine);

            return response.Success
                ? ServiceResult<string>.Success("取消上机成功")
                : ServiceResult<string>.Failure(response.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "取消上机失败");
            return ServiceResult<string>.Failure(ex.Message);
        }
        finally
        {
            _loadingService.HideNativeLoading();
        }
    }
} 