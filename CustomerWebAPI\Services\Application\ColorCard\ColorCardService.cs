using System;
using System.Threading.Tasks;
using System.Data;
using System.Linq;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using CustomerWebAPI.Models;
using CustomerWebAPI.Common;
using SqlSugar;
using Microsoft.Extensions.Logging;
using CustomerWebAPI.Database;

namespace CustomerWebAPI.Services
{
    public class ColorCardService : IColorCardService
    {
        private readonly DbContext _dbContext;
        private readonly ILogger<ColorCardService> _logger;

        public ColorCardService(
            DbContext dbContext,
            ILogger<ColorCardService> logger)
        {
            _dbContext = dbContext;
            _logger = logger;
        }

        public async Task<ApiResponse<bool>> UpdateColorCardLocationAsync(ColorCardLocation model)
        {
            try
            {
                var batchNo = string.Join(",", model.BatchNos);
                
                await _dbContext.ExecuteAsync(async db =>
                {
                    await db.Ado.UseStoredProcedure()
                        .ExecuteCommandAsync("YDMDB.dbo.usp_ydUpdateColorStandardCardLocation", new
                        {
                            BatchNos = batchNo,  // 传递逗号分隔的批次号字符串
                            Location = model.Location,
                            LocatePerson = model.LocatePerson
                        });
                    return true;
                });

                return ApiResponse<bool>.Ok(true, "更新成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新色卡位置失败");
                return ApiResponse<bool>.Fail(
                    $"更新失败: {ex.Message}", 
                    (int)ApiErrorCodes.ServerError);
            }
        }
    }
} 