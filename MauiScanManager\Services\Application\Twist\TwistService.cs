﻿using MauiScanManager.Constants;
using MauiScanManager.Models;
using Microsoft.Extensions.Logging;

namespace MauiScanManager.Services;

public class TwistService : ITwistService
{
    private readonly IApiService _apiService;
    private readonly IPlatformLoadingService _loadingService;
    private readonly ILogger<TwistService> _logger;

    public TwistService(
        IApiService apiService,
        IPlatformLoadingService loadingService,
        ILogger<TwistService> logger)
    {
        _apiService = apiService;
        _loadingService = loadingService;
        _logger = logger;
    }

    public async Task<ServiceResult<string>> CreateBoxByScanAsync(TwistCreateBox twistCreateBox)
    {
        _loadingService.ShowNativeLoading("正在执行装箱操作...");
        try
        {
            var model = new TwistCreateBox
            {
                TaskNO = twistCreateBox.TaskNO,
                ConeNum = twistCreateBox.ConeNum,
                BoxWeight = twistCreateBox.BoxWeight,
                BoxNum = twistCreateBox.BoxNum
            };

            var response = await _apiService.PostAsync<string>(ApiEndpoints.Twist.twCreateBox, model);
            return response.Success
                ? ServiceResult<string>.Success("装箱成功")
                : ServiceResult<string>.Failure(response.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "装箱失败");
            return ServiceResult<string>.Failure(ex.Message);
        }
        finally
        {
            _loadingService.HideNativeLoading();
        }
    }

    public async Task<ServiceResult<string>> AutoDeliveryByScanAsync(TwistAutoDelivery twistAutoDelivery)
    {
        _loadingService.ShowNativeLoading("正在执行出货操作...");
        try
        {
            var model = new TwistAutoDelivery
            {
                BoxNoList=twistAutoDelivery.BoxNoList
            };

            var response = await _apiService.PostAsync<string>(ApiEndpoints.Twist.twAutoDelivery, model);
            return response.Success
                ? ServiceResult<string>.Success("出货成功")
                : ServiceResult<string>.Failure(response.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "出货失败");
            return ServiceResult<string>.Failure(ex.Message);
        }
        finally
        {
            _loadingService.HideNativeLoading();
        }
    }

}

