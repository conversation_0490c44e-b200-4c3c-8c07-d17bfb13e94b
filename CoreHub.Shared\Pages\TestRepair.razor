@page "/test-repair/{DeviceCode}"
@inject NavigationManager Navigation

<PageTitle>测试报修页面 - @DeviceCode</PageTitle>

<div style="padding: 20px;">
    <h1>测试报修页面</h1>
    
    <div style="margin: 20px 0; padding: 15px; background-color: #f0f0f0; border-radius: 6px;">
        <h3>路由参数测试</h3>
        <p><strong>接收到的设备编号:</strong> @DeviceCode</p>
        <p><strong>参数是否为空:</strong> @(string.IsNullOrWhiteSpace(DeviceCode) ? "是" : "否")</p>
        <p><strong>参数长度:</strong> @(DeviceCode?.Length ?? 0)</p>
    </div>
    
    <div style="margin: 20px 0;">
        <MudButton Variant="Variant.Outlined" OnClick="GoBack" Class="mr-2">
            返回扫描页面
        </MudButton>
        <MudButton Variant="Variant.Filled" Color="Color.Primary" OnClick="GoToRealRepair">
            跳转到真实报修页面
        </MudButton>
    </div>
    
    @if (!string.IsNullOrEmpty(message))
    {
        <div style="margin-top: 15px; padding: 10px; background-color: #f6ffed; border: 1px solid #b7eb8f; border-radius: 4px;">
            @message
        </div>
    }
</div>

@code {
    [Parameter] public string DeviceCode { get; set; } = "";
    private string message = "";

    protected override void OnInitialized()
    {
        message = $"页面初始化完成，设备编号: '{DeviceCode}'";
    }

    private void GoBack()
    {
        Navigation.NavigateTo("/devicescanner");
    }

    private void GoToRealRepair()
    {
        if (!string.IsNullOrWhiteSpace(DeviceCode))
        {
            Navigation.NavigateTo($"/devicerepair/{DeviceCode}");
        }
        else
        {
            message = "设备编号为空，无法跳转到报修页面";
            StateHasChanged();
        }
    }
} 