using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using MauiScanManager.Models;
using MauiScanManager.Services;
using System.Collections.ObjectModel;

namespace MauiScanManager.ViewModels;

public partial class SizingCardTraceViewModel : BaseOperationViewModel
{
    private readonly IAudioService _audioService;
    private readonly ISizingService _sizingService;
    private readonly IPlatformLoadingService _loadingService;

    [ObservableProperty]
    private string machineNo = string.Empty;

    [ObservableProperty]
    private string wvCardNo = string.Empty;

    [ObservableProperty]
    private int batchSerialNo;

    [ObservableProperty]
    private bool isUpMachine;

    [ObservableProperty]
    private string scanPrompt = string.Empty;

    [ObservableProperty]
    private ObservableCollection<string> sizingMachines;

    [ObservableProperty]
    private bool isBusy;


    public SizingCardTraceViewModel(
        IScanService scanService,
        IDialogService dialogService,
        IAudioService audioService,
        ISizingService sizingService,
        IPlatformLoadingService loadingService)
        : base(scanService, dialogService)
    {
        _audioService = audioService;
        _sizingService = sizingService;
        _loadingService = loadingService;
        SizingMachines = new ObservableCollection<string> { "A01", "A02", "A03", "A04", "A05", "A06" };
    }

    public override async void Initialize(Operation operation)
    {
        base.Initialize(operation);
        ResetScanState();
        await LoadDefaultMachine();
    }

    protected override void ProcessScanResult(string code, string type, byte[] codeSource)
    {
        if (string.IsNullOrEmpty(code)) return;

        MainThread.BeginInvokeOnMainThread(async () =>
        {
            try
            {
                await ProcessScanStateAsync(code);
            }
            catch (Exception ex)
            {
                await _dialogService.ShowErrorAsync($"处理扫描结果失败：{ex.Message}");
            }
        });
    }

    private async Task ProcessScanStateAsync(string code)
    {
        if (string.IsNullOrWhiteSpace(code))
        {
            await _dialogService.ShowWarningAsync("无效的卡号");
            return;
        }

        WvCardNo = code;
    }

    private string GetFriendlyErrorMessage(Exception ex)
    {
        if (ex.Message.Contains("HttpClient.Timeout") || 
            ex.Message.Contains("The request was canceled"))
        {
            return "网络请求超时，请检查网络连接后重试";
        }
        return ex.Message;
    }

    private async Task UpdateMachineInfo(string machineNo, bool showLoadingIndicator = true)
    {
        if (string.IsNullOrEmpty(machineNo)) return;

        try
        {
            if (showLoadingIndicator)
            {
                _loadingService?.ShowNativeLoading();
            }

            var model = new SizingMachine { SizingMachineId = machineNo };
            var result = await _sizingService.GetMachineUpInfoAsync(model);
            
            if (!result.IsSuccess)
            {
                await _dialogService.ShowErrorAsync(result.ErrorMessage);
                return;
            }

            if (result.Data == null) return;

            BatchSerialNo = result.Data.SizingBatchSerialNo;
            WvCardNo = result.Data.WvCardNo;
            IsUpMachine = !string.IsNullOrEmpty(result.Data.WvCardNo);
            
            if (IsUpMachine)
            {
                await _dialogService.ShowInfoAsync($"机台 {machineNo} 已在上机中");
            }
        }
        catch (Exception ex)
        {
            await _dialogService.ShowErrorAsync($"获取机台信息失败：{GetFriendlyErrorMessage(ex)}");
        }
        finally
        {
            if (showLoadingIndicator)
            {
                _loadingService?.HideNativeLoading();
            }
        }
    }

    private async Task LoadDefaultMachine()
    {
        var defaultMachine = await SecureStorage.GetAsync("DefaultSizingMachine");
        if (!string.IsNullOrEmpty(defaultMachine))
        {
            MachineNo = defaultMachine;
            await UpdateMachineInfo(defaultMachine);
        }
    }

    [RelayCommand]
    private async Task SaveUpMachine()
    {
        if (string.IsNullOrEmpty(MachineNo) || string.IsNullOrEmpty(WvCardNo))
        {
            await _dialogService.ShowWarningAsync("请输入完整信息");
            return;
        }

        try
        {
            _loadingService?.ShowNativeLoading("正在保存...");
            var model = new SizingUpMachine
            {
                SizingMachineId = MachineNo,
                SizingBatchSerialNo = BatchSerialNo,
                WvCardNo = WvCardNo
            };

            var result = await _sizingService.UpMachineAsync(model);
            if (!result.IsSuccess)
            {
                await _dialogService.ShowErrorAsync(result.ErrorMessage);
                return;
            }

            IsUpMachine = true;
            await _dialogService.ShowSuccessAsync("上机成功！");
        }
        catch (Exception ex)
        {
            await _dialogService.ShowErrorAsync(GetFriendlyErrorMessage(ex));
        }
        finally
        {
            _loadingService?.HideNativeLoading();
        }
    }

    [RelayCommand]
    private async Task SaveDownMachine()
    {
        bool answer = await _dialogService.ShowConfirmAsync(
            "确认下机", 
            $"是否确认下机",
            "确认",
            "取消");

        if (!answer) return;

        try
        {
            _loadingService?.ShowNativeLoading("正在下机...");
            var model = new SizingUpMachine
            {
                SizingMachineId = MachineNo,
                SizingBatchSerialNo = BatchSerialNo,
                WvCardNo = WvCardNo
            };

            var result = await _sizingService.DownMachineAsync(model);
            if (!result.IsSuccess)
            {
                await _dialogService.ShowErrorAsync(result.ErrorMessage);
                return;
            }

            ResetScanState();
            await _dialogService.ShowSuccessAsync($"{MachineNo}下机成功！");
        }
        catch (Exception ex)
        {
            await _dialogService.ShowErrorAsync($"下机失败：{ex.Message}");
        }
        finally
        {
            _loadingService?.HideNativeLoading();
        }
    }

    private void ResetScanState()
    {
        WvCardNo = string.Empty;
        BatchSerialNo = 0;
        IsUpMachine = false;
    }

    [RelayCommand]
    private async Task CancelUpMachine()
    {
        try
        {
            bool answer = await _dialogService.ShowConfirmAsync(
                "确认取消上机", 
                $"是否确认取消织轴卡 {WvCardNo} 的上机状态？",
                "确认",
                "取消");

            if (!answer) return;

            _loadingService?.ShowNativeLoading("正在取消上机...");
            var model = new SizingCancelUpMachine
            {
                WvCardNo = WvCardNo
            };

            var result = await _sizingService.CancelUpMachineAsync(model);
            if (!result.IsSuccess)
            {
                await _dialogService.ShowErrorAsync($"取消上机失败：{result.ErrorMessage}");
                return;
            }

            await _dialogService.ShowSuccessAsync("取消上机成功");
            ResetScanState();
        }
        catch (Exception ex)
        {
            await _dialogService.ShowErrorAsync($"取消上机出错：{ex.Message}");
        }
        finally
        {
            _loadingService?.HideNativeLoading();
        }
    }

    [RelayCommand]
    private async Task MachineNoChanged()
    {
        if (string.IsNullOrEmpty(MachineNo)) return;
        
        await SecureStorage.SetAsync("DefaultSizingMachine", MachineNo);
        ResetScanState();
        await UpdateMachineInfo(MachineNo);
    }
} 