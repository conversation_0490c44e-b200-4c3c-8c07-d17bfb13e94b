using System;
using System.IO;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using System.Text.Json;
using Microsoft.Extensions.Logging;

public class UpdateService : IUpdateService
{
    private readonly IConfiguration _configuration;
    private readonly string _updatesPath;  // 添加回字段
    private readonly ILogger<UpdateService> _logger;
    
    public UpdateService(IWebHostEnvironment env, IConfiguration configuration, ILogger<UpdateService> logger)
    {
        _configuration = configuration;
        _updatesPath = Path.Combine(env.ContentRootPath, "Updates");  // 初始化路径
        _logger = logger;
    }

    public UpdateInfo GetLatestVersionInfo()
    {
        var androidConfig = _configuration.GetSection("AppUpdate:Android");
        var version = androidConfig["LatestVersion"];
        
        return new UpdateInfo
        {
            Version = version,
            MinimumVersion = androidConfig["MinimumVersion"],
            ForceUpdate = bool.Parse(androidConfig["ForceUpdate"] ?? "false"),
            UpdateUrl = $"/api/update/download?version={version}",
            ReleaseNotes = androidConfig["ReleaseNotes"],
            UpdateTime = androidConfig["UpdateTime"],
            FileName = $"MauiScanManager-v{version}-release.apk"
        };
    }

    public Stream GetUpdatePackage(string version, string platform = "android")
    {
        string fileName = platform.ToLower() switch
        {
            "android" => $"MauiScanManager-v{version}-release.apk",
            "windows" => $"MauiScanManager-v{version}-setup.exe",
            _ => throw new ArgumentException($"Unsupported platform: {platform}")
        };

        var filePath = Path.Combine(_updatesPath, fileName);
        
        if (!File.Exists(filePath))
        {
            _logger.LogError($"Update package not found at: {filePath}");
            throw new FileNotFoundException($"Update package not found: {filePath}");
        }

        return File.OpenRead(filePath);
    }

    public async Task<object> GetUpdateInfo(string platform)
    {
        var config = platform.ToLower() switch
        {
            "android" => _configuration.GetSection("AppUpdate:Android"),
            "ios" => _configuration.GetSection("AppUpdate:iOS"),
            "apk" => _configuration.GetSection("UpdateConfigAPK"),
            _ => null
        };

        if (config == null) return null;

        var version = config["LatestVersion"] ?? config["Version"];
        
        return new
        {
            Version = version,
            MinimumVersion = config["MinimumVersion"],
            ForceUpdate = bool.Parse(config["ForceUpdate"] ?? "false"),
            UpdateUrl = $"/api/update/download/{platform}?version={version}",
            ReleaseNotes = config["ReleaseNotes"],
            UpdateTime = config["UpdateTime"],
            FileName = platform.ToLower() switch
            {
                "android" => $"MauiScanManager-v{version}-release.apk",
                "windows" => $"MauiScanManager-v{version}-setup.exe",
                _ => throw new ArgumentException($"Unsupported platform: {platform}")
            }
        };
    }
}

public class UpdateConfig
{
    public AppUpdateConfig AppUpdate { get; set; }
}

public class AppUpdateConfig
{
    public PlatformUpdateConfig Android { get; set; }
    public PlatformUpdateConfig iOS { get; set; }
}

public class PlatformUpdateConfig
{
    public string LatestVersion { get; set; }
    public string MinimumVersion { get; set; }
    public bool ForceUpdate { get; set; }
    public string ReleaseNotes { get; set; }
    public string UpdateTime { get; set; }
}
