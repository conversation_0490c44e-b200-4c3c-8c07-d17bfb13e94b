﻿//using System;
//using System.Collections.Generic;
//using System.Linq;
//using System.Threading.Tasks;

//namespace CustomerWebAPI.Model.ASN
//{

//    public class Header
//    {
//        /// <summary>
//        /// 
//        /// </summary>
//        public string PurposeCode { get; set; }
//        /// <summary>
//        /// 
//        /// </summary>
//        public string AdvancedShipNoteNumber { get; set; }
//        /// <summary>
//        /// 
//        /// </summary>
//        public string HierarchicalStructureCode { get; set; }
//        /// <summary>
//        /// 
//        /// </summary>
//        public int DocumentIndex { get; set; }
//        /// <summary>
//        /// 
//        /// </summary>
//        public string TransactionTypeCode { get; set; }
//        /// <summary>
//        /// 
//        /// </summary>
//        public string SentDate { get; set; }
//        /// <summary>
//        /// 
//        /// </summary>
//        public string SentTime { get; set; }
//    }

//    public class WeightsItem
//    {
//        /// <summary>
//        /// 
//        /// </summary>
//        public string Qualifier { get; set; }
//        /// <summary>
//        /// 
//        /// </summary>
//        public double Value { get; set; }
//        /// <summary>
//        /// 
//        /// </summary>
//        public string Unit { get; set; }
//    }

//    public class CarrierDetails
//    {
//        //public CarrierDetails()
//        //{
//        //    Weights = new List<WeightsItem>();
//        //}
//        /// <summary>
//        /// 
//        /// </summary>
//        public string PackagingCode { get; set; }
//        /// <summary>
//        /// 
//        /// </summary>
//        public int LadingQuantity { get; set; }
//        /// <summary>
//        /// 
//        /// </summary>
//        public List<WeightsItem> Weights { get; set; }
//        /// <summary>
//        /// 
//        /// </summary>
//        public string TransportationTypeCode { get; set; }
//        /// <summary>
//        /// 
//        /// </summary>
//        public string ContainerNumber { get; set; }
//    }

//    public class Transportation
//    {
//        /// <summary>
//        /// 
//        /// </summary>
//        public string BillNumber { get; set; }
//        /// <summary>
//        /// 
//        /// </summary>
//        public string VoyageNumber { get; set; }
//        /// <summary>
//        /// 
//        /// </summary>
//        public string VesselNumber { get; set; }
//        /// <summary>
//        /// 
//        /// </summary>
//        public string ShipMode { get; set; }
//    }

//    public class Dates
//    {
//        /// <summary>
//        /// 
//        /// </summary>
//        public string SailDate { get; set; }
//        /// <summary>
//        /// 
//        /// </summary>
//        public string DeliveryDate { get; set; }
//        /// <summary>
//        /// 
//        /// </summary>
//        public string EstimatedArrivalDate { get; set; }
//    }


//    public class PartiesItem
//    {
//        //public PartiesItem()
//        //{
//        //    Address = new Address();
//        //}
//        /// <summary>
//        /// 
//        /// </summary>
//        public string Identifier { get; set; }
//        /// <summary>
//        /// 
//        /// </summary>
//        public string Name { get; set; }
//        /// <summary>
//        /// 
//        /// </summary>
//        public string Code { get; set; }
//        /// <summary>
//        /// 
//        /// </summary>
//        public Address Address { get; set; }
//    }

//    public class Address
//    {
//        /// <summary>
//        /// 
//        /// </summary>
//        public string AddressLine1 { get; set; }
//        /// <summary>
//        /// 
//        /// </summary>
//        public string AddressLine2 { get; set; }
//    }


//    public class MeasurementsItem
//    {
//        /// <summary>
//        /// 
//        /// </summary>
//        public string Name { get; set; }
//        /// <summary>
//        /// 
//        /// </summary>
//        public double Value { get; set; }
//        /// <summary>
//        /// 
//        /// </summary>
//        public string MeasurementUnit { get; set; }
//    }

//    public class PackagesItem
//    {
//        //public PackagesItem()
//        //{
//        //    Weights = new List<WeightsItem>();
//        //    Measurements = new List<MeasurementsItem>();
//        //}
//        /// <summary>
//        /// 
//        /// </summary>
//        public string AssignedId { get; set; }
//        /// <summary>
//        /// 
//        /// </summary>
//        public double Quantity { get; set; }
//        /// <summary>
//        /// 
//        /// </summary>
//        public string QuantityUnit { get; set; }
//        /// <summary>
//        /// 
//        /// </summary>
//        public List<WeightsItem> Weights { get; set; }
//        /// <summary>
//        /// 
//        /// </summary>
//        public double FabricWidth { get; set; }
//        /// <summary>
//        /// 
//        /// </summary>
//        public string WidthUnit { get; set; }
//        /// <summary>
//        /// 
//        /// </summary>
//        public string QaStatus { get; set; }
//        /// <summary>
//        /// 
//        /// </summary>
//        public string FabricLengthString { get; set; }
//        /// <summary>
//        /// 
//        /// </summary>
//        public List<MeasurementsItem> Measurements { get; set; }
//        /// <summary>
//        /// 
//        /// </summary>
//        public string UCC { get; set; }
//        /// <summary>
//        /// 
//        /// </summary>
//        public string RollNumber { get; set; }
//    }

//    public class ItemsItem
//    {
//        //public ItemsItem()
//        //{
//        //    Weights = new List<WeightsItem>();
//        //    Packages = new List<PackagesItem>();
//        //}
//        /// <summary>
//        /// 
//        /// </summary>
//        public string AssignedId { get; set; }
//        /// <summary>
//        /// 
//        /// </summary>
//        public int PoLineNumber { get; set; }
//        /// <summary>
//        /// 
//        /// </summary>
//        public string ItemNumber { get; set; }
//        /// <summary>
//        /// 
//        /// </summary>
//        public string ContractNumber { get; set; }
//        /// <summary>
//        /// 
//        /// </summary>
//        public string DyeLotSeries { get; set; }
//        /// <summary>
//        /// 
//        /// </summary>
//        public int SubLineNumber { get; set; }
//        /// <summary>
//        /// 
//        /// </summary>
//        public int PackQuantity { get; set; }
//        /// <summary>
//        /// 
//        /// </summary>
//        public string PackCode { get; set; }
//        /// <summary>
//        /// 
//        /// </summary>
//        public List<WeightsItem> Weights { get; set; }
//        /// <summary>
//        /// 
//        /// </summary>
//        public string DyeMatch { get; set; }
//        /// <summary>
//        /// 
//        /// </summary>
//        public string ColorDescription { get; set; }
//        /// <summary>
//        /// 
//        /// </summary>
//        public string FabricDescription { get; set; }
//        /// <summary>
//        /// 
//        /// </summary>
//        public double FreeOfChargeQuantity { get; set; }
//        /// <summary>
//        /// 
//        /// </summary>
//        public List<PackagesItem> Packages { get; set; }
//    }

//    public class OrdersItem
//    {
//        //public OrdersItem()
//        //{
//        //    Parties = new List<PartiesItem>();
//        //    Items = new List<ItemsItem>();
//        //}
//        /// <summary>
//        /// 
//        /// </summary>
//        public string PurchaseOrderNumber { get; set; }
//        /// <summary>
//        /// 
//        /// </summary>
//        public string PurchaseOrderReleaseNumber { get; set; }
//        /// <summary>
//        /// 
//        /// </summary>
//        public List<PartiesItem> Parties { get; set; }
//        /// <summary>
//        /// 
//        /// </summary>
//        public List<ItemsItem> Items { get; set; }
//    }

//    public class Detail
//    {
//        //public Detail()
//        //{
//        //    CarrierDetails = new CarrierDetails();
//        //    Transportation = new Transportation();
//        //    Dates = new Dates();
//        //    Parties = new List<PartiesItem>();
//        //    Orders = new List<OrdersItem>();
//        //}
//        /// <summary>
//        /// 
//        /// </summary>
//        public CarrierDetails CarrierDetails { get; set; }
//        /// <summary>
//        /// 
//        /// </summary>
//        public string DivisionCode { get; set; }
//        /// <summary>
//        /// 
//        /// </summary>
//        public Transportation Transportation { get; set; }
//        /// <summary>
//        /// 
//        /// </summary>
//        public Dates Dates { get; set; }
//        /// <summary>
//        /// 
//        /// </summary>
//        public List<PartiesItem> Parties { get; set; }
//        /// <summary>
//        /// 
//        /// </summary>
//        public List<OrdersItem> Orders { get; set; }
//    }

//    public class AdvancedShipNotesItem
//    {
//        //public AdvancedShipNotesItem()
//        //{
//        //    Header = new Header();
//        //    Detail = new Detail();
//        //}
//        /// <summary>
//        /// 
//        /// </summary>
//        public Header Header { get; set; }
//        /// <summary>
//        /// 
//        /// </summary>
//        public Detail Detail { get; set; }
//    }

//    public class RootASN
//    {
//        //public RootASN()
//        //{
//        //    AdvancedShipNotes = new List<AdvancedShipNotesItem>();
//        //}
//        /// <summary>
//        /// 
//        /// </summary>
//        public string SenderIdentifierId { get; set; }
//        /// <summary>
//        /// 
//        /// </summary>
//        public string SenderId { get; set; }
//        /// <summary>
//        /// 
//        /// </summary>
//        public string ReceiverIdentifierId { get; set; }
//        /// <summary>
//        /// 
//        /// </summary>
//        public string ReceiverId { get; set; }
//        /// <summary>
//        /// 
//        /// </summary>
//        public int TrackNumber { get; set; }
//        /// <summary>
//        /// 
//        /// </summary>
//        public string ProductionIndicator { get; set; }
//        /// <summary>
//        /// 
//        /// </summary>
//        public List<AdvancedShipNotesItem> AdvancedShipNotes { get; set; }
//    }

//    //public class TALASN
//    //{
//    //    public RootASN rootASN { get; set; }
//    //}


//}

