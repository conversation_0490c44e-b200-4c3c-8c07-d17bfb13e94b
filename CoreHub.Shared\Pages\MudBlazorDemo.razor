@page "/mudblazor-demo"

<PageTitle>MudBlazor 组件演示</PageTitle>

<MudContainer MaxWidth="MaxWidth.Large" Class="mt-4">
    <MudText Typo="Typo.h3" GutterBottom="true">MudBlazor 组件演示</MudText>
    <MudText Typo="Typo.subtitle1" Class="mb-4">展示常用的MudBlazor组件用法</MudText>

    <!-- 基础组件演示 -->
    <MudPaper Class="pa-4 ma-2">
        <MudText Typo="Typo.h5" Class="mb-3">1. 基础组件</MudText>
        
        <!-- 按钮 -->
        <MudText Typo="Typo.h6" Class="mb-2">按钮</MudText>
        <div class="mb-3">
            <MudButton Variant="Variant.Filled" Color="Color.Primary" Class="mr-2">主要按钮</MudButton>
            <MudButton Variant="Variant.Outlined" Color="Color.Secondary" Class="mr-2">次要按钮</MudButton>
            <MudButton Variant="Variant.Text" Color="Color.Info" Class="mr-2">文本按钮</MudButton>
            <MudIconButton Icon="@Icons.Material.Filled.Add" Color="Color.Success" />
        </div>

        <!-- 输入框 -->
        <MudText Typo="Typo.h6" Class="mb-2">输入框</MudText>
        <div class="mb-3">
            <MudTextField @bind-Value="textValue" Label="标准输入框" Variant="Variant.Outlined" Class="mb-2" />
            <MudTextField @bind-Value="passwordValue" Label="密码输入" InputType="InputType.Password" Variant="Variant.Outlined" />
        </div>

        <!-- 选择框 -->
        <MudText Typo="Typo.h6" Class="mb-2">选择框</MudText>
        <div class="mb-3">
            <MudSelect T="string" @bind-Value="selectedValue" Label="选择项目" Variant="Variant.Outlined">
                <MudSelectItem T="string" Value="@("option1")">选项 1</MudSelectItem>
                <MudSelectItem T="string" Value="@("option2")">选项 2</MudSelectItem>
                <MudSelectItem T="string" Value="@("option3")">选项 3</MudSelectItem>
            </MudSelect>
        </div>

        <!-- 开关和复选框 -->
        <MudText Typo="Typo.h6" Class="mb-2">开关和复选框</MudText>
        <div class="mb-3">
            <MudSwitch T="bool" @bind-Value="switchValue" Label="开关" Color="Color.Primary" />
            <MudCheckBox T="bool" @bind-Value="checkboxValue" Label="复选框" Color="Color.Primary" />
        </div>
    </MudPaper>

    <!-- 数据显示组件 -->
    <MudPaper Class="pa-4 ma-2">
        <MudText Typo="Typo.h5" Class="mb-3">2. 数据显示组件</MudText>
        
        <!-- 数据表格 -->
        <MudText Typo="Typo.h6" Class="mb-2">数据表格</MudText>
        <MudTable Items="@sampleData" Hover="true" Striped="true" Class="mb-3">
            <HeaderContent>
                <MudTh>姓名</MudTh>
                <MudTh>年龄</MudTh>
                <MudTh>职位</MudTh>
                <MudTh>操作</MudTh>
            </HeaderContent>
            <RowTemplate>
                <MudTd DataLabel="姓名">@context.Name</MudTd>
                <MudTd DataLabel="年龄">@context.Age</MudTd>
                <MudTd DataLabel="职位">@context.Position</MudTd>
                <MudTd DataLabel="操作">
                    <MudIconButton Icon="@Icons.Material.Filled.Edit" Size="Size.Small" />
                    <MudIconButton Icon="@Icons.Material.Filled.Delete" Size="Size.Small" Color="Color.Error" />
                </MudTd>
            </RowTemplate>
        </MudTable>

        <!-- 芯片组 -->
        <MudText Typo="Typo.h6" Class="mb-2">芯片组</MudText>
        <div class="mb-3">
            <MudChip T="string" Color="Color.Primary" Size="Size.Small">技术</MudChip>
            <MudChip T="string" Color="Color.Secondary" Size="Size.Small">设计</MudChip>
            <MudChip T="string" Color="Color.Tertiary" Size="Size.Small">管理</MudChip>
        </div>
    </MudPaper>

    <!-- 反馈组件 -->
    <MudPaper Class="pa-4 ma-2">
        <MudText Typo="Typo.h5" Class="mb-3">3. 反馈组件</MudText>
        
        <!-- 提醒 -->
        <MudText Typo="Typo.h6" Class="mb-2">提醒</MudText>
        <div class="mb-3">
            <MudAlert Severity="Severity.Success" Class="mb-2">这是一个成功消息</MudAlert>
            <MudAlert Severity="Severity.Info" Class="mb-2">这是一个信息消息</MudAlert>
            <MudAlert Severity="Severity.Warning" Class="mb-2">这是一个警告消息</MudAlert>
            <MudAlert Severity="Severity.Error">这是一个错误消息</MudAlert>
        </div>

        <!-- 进度条 -->
        <MudText Typo="Typo.h6" Class="mb-2">进度条</MudText>
        <div class="mb-3">
            <MudProgressLinear Value="progressValue" Color="Color.Primary" Class="mb-2" />
            <MudProgressCircular Value="progressValue" Color="Color.Secondary" Size="Size.Medium" />
        </div>

        <!-- 操作按钮 -->
        <MudText Typo="Typo.h6" Class="mb-2">交互演示</MudText>
        <div class="mb-3">
            <MudButton Variant="Variant.Filled" Color="Color.Primary" OnClick="ShowSnackbar" Class="mr-2">
                显示通知
            </MudButton>
            <MudButton Variant="Variant.Filled" Color="Color.Secondary" OnClick="ShowDialog">
                显示对话框
            </MudButton>
        </div>
    </MudPaper>

    <!-- TreeView 演示 -->
    <MudPaper Class="pa-4 ma-2">
        <MudText Typo="Typo.h5" Class="mb-3">4. TreeView 树形控件</MudText>
        
        <MudTreeView T="string">
            <MudTreeViewItem Value="@("root")" Text="根节点" Expanded="true">
                <MudTreeViewItem Value="@("folder1")" Text="文件夹 1">
                    <MudTreeViewItem Value="@("file1")" Text="文件 1.txt" Icon="@Icons.Material.Filled.Description" />
                    <MudTreeViewItem Value="@("file2")" Text="文件 2.txt" Icon="@Icons.Material.Filled.Description" />
                </MudTreeViewItem>
                <MudTreeViewItem Value="@("folder2")" Text="文件夹 2">
                    <MudTreeViewItem Value="@("subfolder1")" Text="子文件夹 1">
                        <MudTreeViewItem Value="@("file3")" Text="文件 3.txt" Icon="@Icons.Material.Filled.Description" />
                    </MudTreeViewItem>
                </MudTreeViewItem>
                <MudTreeViewItem Value="@("file4")" Text="根文件.txt" Icon="@Icons.Material.Filled.Description" />
            </MudTreeViewItem>
        </MudTreeView>
    </MudPaper>
</MudContainer>

@code {
    private string textValue = "";
    private string passwordValue = "";
    private string selectedValue = "";
    private bool switchValue = false;
    private bool checkboxValue = false;
    private int progressValue = 65;

    private List<SampleData> sampleData = new()
    {
        new SampleData { Name = "张三", Age = 28, Position = "前端开发" },
        new SampleData { Name = "李四", Age = 32, Position = "后端开发" },
        new SampleData { Name = "王五", Age = 29, Position = "UI设计师" },
        new SampleData { Name = "赵六", Age = 35, Position = "项目经理" }
    };

    [Inject] private ISnackbar Snackbar { get; set; } = default!;
    [Inject] private IDialogService Dialog { get; set; } = default!;

    private void ShowSnackbar()
    {
        Snackbar.Add("这是一个通知消息！", Severity.Success);
    }

    private async Task ShowDialog()
    {
        var result = await Dialog.ShowMessageBox(
            "确认",
            "这是一个对话框示例，您确定要继续吗？",
            yesText: "确定",
            cancelText: "取消");
            
        if (result == true)
        {
            Snackbar.Add("您点击了确定", Severity.Info);
        }
    }

    public class SampleData
    {
        public string Name { get; set; } = "";
        public int Age { get; set; }
        public string Position { get; set; } = "";
    }
} 