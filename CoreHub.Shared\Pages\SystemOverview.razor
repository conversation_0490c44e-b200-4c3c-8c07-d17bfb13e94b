@page "/system-overview"
@using CoreHub.Shared.Services
@using CoreHub.Shared.Models.Database
@using CoreHub.Shared.Components
@inject IUserManagementService UserManagementService

<PageTitle>系统概览</PageTitle>

<PermissionView RequiredPermission="System.Admin">
    <h3>系统概览</h3>
    <p class="text-muted">系统用户、角色和权限的统计信息</p>

    @if (isLoading)
    {
        <div class="text-center">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
        </div>
    }
    else
    {
        <!-- 统计卡片 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card text-white bg-primary">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 class="card-title">@totalUsers</h4>
                                <p class="card-text">总用户数</p>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-users fa-2x"></i>
                            </div>
                        </div>
                        <small>启用: @enabledUsers | 锁定: @lockedUsers</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-white bg-success">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 class="card-title">@totalRoles</h4>
                                <p class="card-text">总角色数</p>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-user-tag fa-2x"></i>
                            </div>
                        </div>
                        <small>启用: @enabledRoles | 系统: @systemRoles</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-white bg-warning">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 class="card-title">@totalPermissions</h4>
                                <p class="card-text">总权限数</p>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-key fa-2x"></i>
                            </div>
                        </div>
                        <small>启用: @enabledPermissions | 系统: @systemPermissions</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-white bg-info">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 class="card-title">@totalModules</h4>
                                <p class="card-text">功能模块</p>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-cubes fa-2x"></i>
                            </div>
                        </div>
                        <small>权限模块数量</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- 详细信息 -->
        <div class="row">
            <!-- 用户状态分布 -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-users"></i> 用户状态分布
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>状态</th>
                                        <th>数量</th>
                                        <th>百分比</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td><span class="badge bg-success">已启用</span></td>
                                        <td>@enabledUsers</td>
                                        <td>@(totalUsers > 0 ? Math.Round((double)enabledUsers / totalUsers * 100, 1) : 0)%</td>
                                    </tr>
                                    <tr>
                                        <td><span class="badge bg-danger">已禁用</span></td>
                                        <td>@(totalUsers - enabledUsers)</td>
                                        <td>@(totalUsers > 0 ? Math.Round((double)(totalUsers - enabledUsers) / totalUsers * 100, 1) : 0)%</td>
                                    </tr>
                                    <tr>
                                        <td><span class="badge bg-warning">已锁定</span></td>
                                        <td>@lockedUsers</td>
                                        <td>@(totalUsers > 0 ? Math.Round((double)lockedUsers / totalUsers * 100, 1) : 0)%</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 权限模块分布 -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-pie"></i> 权限模块分布
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>模块</th>
                                        <th>权限数</th>
                                        <th>百分比</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var module in moduleStats.OrderByDescending(m => m.Value))
                                    {
                                        <tr>
                                            <td><span class="badge bg-primary">@module.Key</span></td>
                                            <td>@module.Value</td>
                                            <td>@(totalPermissions > 0 ? Math.Round((double)module.Value / totalPermissions * 100, 1) : 0)%</td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 最近创建的用户 -->
        <div class="row mt-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-clock"></i> 最近创建的用户
                        </h5>
                    </div>
                    <div class="card-body">
                        @if (recentUsers.Any())
                        {
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>用户名</th>
                                            <th>显示名称</th>
                                            <th>邮箱</th>
                                            <th>状态</th>
                                            <th>创建时间</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var user in recentUsers)
                                        {
                                            <tr>
                                                <td><strong>@user.Username</strong></td>
                                                <td>@user.DisplayName</td>
                                                <td>@user.Email</td>
                                                <td>
                                                    <span class="badge @(user.IsEnabled ? "bg-success" : "bg-danger")">
                                                        @(user.IsEnabled ? "已启用" : "已禁用")
                                                    </span>
                                                    @if (user.IsLocked)
                                                    {
                                                        <span class="badge bg-warning">已锁定</span>
                                                    }
                                                </td>
                                                <td>@user.CreatedAt.ToString("yyyy-MM-dd HH:mm")</td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>
                        }
                        else
                        {
                            <p class="text-muted text-center">暂无用户数据</p>
                        }
                    </div>
                </div>
            </div>
        </div>

        <!-- 系统信息 -->
        <div class="row mt-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle"></i> 系统信息
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>数据库信息</h6>
                                <ul class="list-unstyled">
                                    <li><strong>ORM:</strong> SqlSugar</li>
                                    <li><strong>数据库:</strong> SQL Server</li>
                                    <li><strong>权限模型:</strong> RBAC (基于角色的访问控制)</li>
                                    <li><strong>认证方式:</strong> 存储过程验证</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>功能特性</h6>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-check text-success"></i> 用户管理 (CRUD)</li>
                                    <li><i class="fas fa-check text-success"></i> 角色管理 (CRUD)</li>
                                    <li><i class="fas fa-check text-success"></i> 权限管理 (CRUD)</li>
                                    <li><i class="fas fa-check text-success"></i> 权限分配</li>
                                    <li><i class="fas fa-check text-success"></i> 页面级权限控制</li>
                                    <li><i class="fas fa-check text-success"></i> 功能级权限控制</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }

    @if (!string.IsNullOrEmpty(message))
    {
        <div class="alert @(isError ? "alert-danger" : "alert-info") alert-dismissible fade show mt-3">
            <i class="fas @(isError ? "fa-exclamation-triangle" : "fa-info-circle")"></i>
            @message
            <button type="button" class="btn-close" @onclick="ClearMessage"></button>
        </div>
    }
</PermissionView>

@code {
    private bool isLoading = false;
    private string message = "";
    private bool isError = false;

    private int totalUsers = 0;
    private int enabledUsers = 0;
    private int lockedUsers = 0;
    private int totalRoles = 0;
    private int enabledRoles = 0;
    private int systemRoles = 0;
    private int totalPermissions = 0;
    private int enabledPermissions = 0;
    private int systemPermissions = 0;
    private int totalModules = 0;

    private List<User> recentUsers = new();
    private Dictionary<string, int> moduleStats = new();

    protected override async Task OnInitializedAsync()
    {
        await LoadSystemStats();
    }

    private async Task LoadSystemStats()
    {
        try
        {
            isLoading = true;
            StateHasChanged();

            // 加载用户统计
            var usersResult = await UserManagementService.GetUsersAsync(1, 1000);
            var allUsers = usersResult.Users;
            totalUsers = allUsers.Count;
            enabledUsers = allUsers.Count(u => u.IsEnabled);
            lockedUsers = allUsers.Count(u => u.IsLocked);

            // 获取最近创建的用户
            recentUsers = allUsers.OrderByDescending(u => u.CreatedAt).Take(5).ToList();

            // 加载角色统计
            var allRoles = await UserManagementService.GetAllRolesAsync();
            totalRoles = allRoles.Count;
            enabledRoles = allRoles.Count(r => r.IsEnabled);
            systemRoles = allRoles.Count(r => r.IsSystem);

            // 加载权限统计
            var allPermissions = await UserManagementService.GetAllPermissionsAsync();
            totalPermissions = allPermissions.Count;
            enabledPermissions = allPermissions.Count(p => p.IsEnabled);
            systemPermissions = allPermissions.Count(p => p.IsSystem);

            // 计算模块统计
            moduleStats = allPermissions
                .GroupBy(p => p.Module)
                .ToDictionary(g => g.Key, g => g.Count());
            totalModules = moduleStats.Count;

            ShowMessage($"系统统计加载完成 - 用户: {totalUsers}, 角色: {totalRoles}, 权限: {totalPermissions}", false);
        }
        catch (Exception ex)
        {
            ShowMessage($"加载系统统计失败: {ex.Message}", true);
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private void ShowMessage(string msg, bool error = false)
    {
        message = msg;
        isError = error;
        StateHasChanged();
    }

    private void ClearMessage()
    {
        message = "";
        isError = false;
    }
} 