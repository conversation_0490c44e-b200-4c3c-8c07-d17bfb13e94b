using System.Diagnostics;
using CoreHub.Web.Services;
using CoreHub.Shared.Services;

namespace CoreHub.Web.Middleware
{
    /// <summary>
    /// 性能日志中间件
    /// </summary>
    public class PerformanceLoggingMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly IApplicationLogger _logger;

        public PerformanceLoggingMiddleware(RequestDelegate next, IApplicationLogger logger)
        {
            _next = next;
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            var stopwatch = Stopwatch.StartNew();
            var request = context.Request;

            try
            {
                await _next(context);
            }
            finally
            {
                stopwatch.Stop();
                var duration = stopwatch.ElapsedMilliseconds;

                // 只记录非静态文件的性能数据
                if (!IsStaticFile(request.Path))
                {
                    var performanceData = new
                    {
                        Method = request.Method,
                        Path = request.Path.Value,
                        QueryString = request.QueryString.Value,
                        StatusCode = context.Response.StatusCode,
                        ContentLength = context.Response.ContentLength,
                        UserAgent = request.Headers.UserAgent.ToString(),
                        RemoteIpAddress = context.Connection.RemoteIpAddress?.ToString()
                    };

                    _logger.LogPerformance(
                        $"{request.Method} {request.Path}",
                        duration,
                        performanceData
                    );
                }
            }
        }

        /// <summary>
        /// 判断是否为静态文件请求
        /// </summary>
        /// <param name="path">请求路径</param>
        /// <returns>是否为静态文件</returns>
        private static bool IsStaticFile(PathString path)
        {
            var staticExtensions = new[] { ".css", ".js", ".png", ".jpg", ".jpeg", ".gif", ".ico", ".svg", ".woff", ".woff2", ".ttf", ".eot" };
            return staticExtensions.Any(ext => path.Value?.EndsWith(ext, StringComparison.OrdinalIgnoreCase) == true);
        }
    }

    /// <summary>
    /// 性能日志中间件扩展
    /// </summary>
    public static class PerformanceLoggingMiddlewareExtensions
    {
        public static IApplicationBuilder UsePerformanceLogging(this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<PerformanceLoggingMiddleware>();
        }
    }
}
