

namespace MauiScanManager.Services
{
    public interface IDialogService
    {
        Task ShowSuccessAsync(string message, string title = "成功");
        Task ShowErrorAsync(string message, string title = "错误");
        Task ShowWarningAsync(string message, string title = "警告");
        Task ShowInfoAsync(string message, string title = "提示");
        Task<bool> ShowConfirmAsync(
            string title,
            string message,
            string acceptText = "确认",
            string cancelText = "取消");
        Task ShowToastAsync(string message);
        Task ShowSnackbarAsync(
            string message,
            string actionText = null,
            Action action = null,
            TimeSpan? duration = null);
    }

    public interface IResourceProvider
    {
        int GetWarningIconResourceId();
        int GetInfoIconResourceId();
    }

    public class DialogService : IDialogService
    {
        private readonly IPlatformDialogService _platformDialog;
        private readonly IResourceProvider _resourceProvider;

        public DialogService(IPlatformDialogService platformDialog, IResourceProvider resourceProvider)
        {
            _platformDialog = platformDialog;
            _resourceProvider = resourceProvider;
        }

        public async Task ShowSuccessAsync(string message, string title = "成功")
        {
            await _platformDialog.ShowToastAsync(message);
        }

        public async Task ShowErrorAsync(string message, string title = "错误")
        {
            await Shell.Current.DisplayAlert(title, message, "确定");
        }

        public async Task ShowWarningAsync(string message, string title = "警告")
        {
            await Shell.Current.DisplayAlert(title, message, "确定");
        }

        public async Task ShowInfoAsync(string message, string title = "提示")
        {
            await _platformDialog.ShowSnackbarAsync(message);
        }

        public async Task<bool> ShowConfirmAsync(
            string title,
            string message,
            string acceptText = "确认",
            string cancelText = "取消")
        {
            return await Application.Current.MainPage.DisplayAlert(
                title,
                message,
                acceptText,
                cancelText);
            // int iconResourceId = _resourceProvider.GetInfoIconResourceId();
            // _platformDialog.ShowConfirmationDialog(title, message, iconResourceId, () => true, () => false);
        }

        public async Task ShowToastAsync(string message)
        {
            await _platformDialog.ShowToastAsync(message);
        }

        public async Task ShowSnackbarAsync(
            string message,
            string actionText = null,
            Action action = null,
            TimeSpan? duration = null)
        {
            await _platformDialog.ShowSnackbarAsync(message, actionText, action);
        }
    }
}