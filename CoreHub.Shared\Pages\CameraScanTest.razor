@page "/camera-test"
@inject IQrCodeScannerService QrCodeScanner
@inject NavigationManager Navigation

<PageTitle>摄像头扫描测试</PageTitle>

<MudContainer MaxWidth="MaxWidth.Medium">
    <MudText Typo="Typo.h3" Class="mb-4">摄像头二维码扫描测试</MudText>

    <!-- 扫描控制 -->
    <MudPaper Class="pa-4 mb-4">
        <MudText Typo="Typo.h5" GutterBottom="true">扫描控制</MudText>
        
        <div class="d-flex gap-2 mb-3">
            <MudButton Variant="Variant.Filled" 
                      Color="Color.Primary" 
                      OnClick="StartScan"
                      StartIcon="@Icons.Material.Filled.QrCodeScanner"
                      Disabled="isScanning">
                @(isScanning ? "扫描中..." : "开始扫描")
            </MudButton>
            
            <MudButton Variant="Variant.Outlined" 
                      OnClick="StopScan"
                      StartIcon="@Icons.Material.Filled.Stop"
                      Disabled="!isScanning">
                停止扫描
            </MudButton>
        </div>

        @if (isScanning)
        {
            <MudAlert Severity="Severity.Info" Class="mb-3">
                <div class="d-flex align-center">
                    <MudIcon Icon="@Icons.Material.Filled.CheckCircle" Class="mr-2" />
                    <span>摄像头已启动，请将二维码对准摄像头进行扫描</span>
                </div>
            </MudAlert>
        }
    </MudPaper>

    <!-- 扫描结果 -->
    @if (!string.IsNullOrEmpty(scanResult))
    {
        <MudPaper Class="pa-4 mb-4">
            <MudText Typo="Typo.h5" GutterBottom="true">扫描结果</MudText>
            
            <MudAlert Severity="@(isSuccess ? Severity.Success : Severity.Error)" Class="mb-3">
                <div class="d-flex align-center">
                    <MudIcon Icon="@(isSuccess ? Icons.Material.Filled.CheckCircle : Icons.Material.Filled.Error)" Class="mr-2" />
                    <span>@scanResult</span>
                </div>
            </MudAlert>

            @if (isSuccess && !string.IsNullOrEmpty(scannedCode))
            {
                <MudText Typo="Typo.body1" Class="mb-2">
                    <strong>扫描到的代码:</strong> @scannedCode
                </MudText>
                
                <div class="d-flex gap-2">
                    <MudButton Variant="Variant.Text" 
                              Color="Color.Secondary" 
                              OnClick="ClearResult"
                              StartIcon="@Icons.Material.Filled.Clear">
                        清除结果
                    </MudButton>
                    
                    <MudButton Variant="Variant.Text" 
                              Color="Color.Secondary" 
                              OnClick="CopyToClipboard"
                              StartIcon="@Icons.Material.Filled.ContentCopy">
                        复制代码
                    </MudButton>
                </div>
            }
        </MudPaper>
    }

    <!-- 使用说明 -->
    <MudPaper Class="pa-4 mb-4">
        <MudText Typo="Typo.h5" GutterBottom="true">使用说明</MudText>
        
        <div class="text-center mb-3">
            <MudIcon Icon="@Icons.Material.Filled.CameraAlt" 
                    Size="Size.Large" 
                    Color="Color.Primary" />
        </div>
        
        <MudList T="string">
            <MudListItem T="string" Icon="@Icons.Material.Filled.TouchApp">
                <MudText>点击"开始扫描"按钮启动摄像头</MudText>
            </MudListItem>
            <MudListItem T="string" Icon="@Icons.Material.Filled.CenterFocusStrong">
                <MudText>将二维码放在摄像头前方，保持稳定</MudText>
            </MudListItem>
            <MudListItem T="string" Icon="@Icons.Material.Filled.Speed">
                <MudText>系统会自动识别并显示扫描结果</MudText>
            </MudListItem>
            <MudListItem T="string" Icon="@Icons.Material.Filled.Stop">
                <MudText>扫描完成后点击"停止扫描"关闭摄像头</MudText>
            </MudListItem>
        </MudList>
    </MudPaper>

    <!-- 返回按钮 -->
    <div class="text-center">
        <MudButton Variant="Variant.Outlined" 
                  OnClick="GoBack"
                  StartIcon="@Icons.Material.Filled.ArrowBack">
            返回上一页
        </MudButton>
    </div>
</MudContainer>

@code {
    private bool isScanning = false;
    private string scanResult = "";
    private string scannedCode = "";
    private bool isSuccess = false;

    private async Task StartScan()
    {
        try
        {
            isScanning = true;
            scanResult = "";
            StateHasChanged();

            var result = await QrCodeScanner.ScanQrCodeAsync();
            
            if (!string.IsNullOrEmpty(result))
            {
                scannedCode = result;
                scanResult = "扫描成功！";
                isSuccess = true;
            }
            else
            {
                scanResult = "扫描被取消或没有检测到二维码";
                isSuccess = false;
            }
        }
        catch (Exception ex)
        {
            scanResult = $"扫描失败: {ex.Message}";
            isSuccess = false;
        }
        finally
        {
            isScanning = false;
            StateHasChanged();
        }
    }

    private void StopScan()
    {
        isScanning = false;
        scanResult = "扫描已停止";
        isSuccess = false;
        StateHasChanged();
    }

    private void ClearResult()
    {
        scanResult = "";
        scannedCode = "";
        isSuccess = false;
        StateHasChanged();
    }

    private async Task CopyToClipboard()
    {
        try
        {
            // 这里可以添加复制到剪贴板的逻辑
            await Task.Delay(100); // 模拟复制操作
            scanResult = "代码已复制到剪贴板";
        }
        catch (Exception ex)
        {
            scanResult = $"复制失败: {ex.Message}";
        }
    }

    private void GoBack()
    {
        Navigation.NavigateTo("/devicescanner");
    }
} 