using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using CoreHub.Shared.Services;
using CoreHub.Shared.Models.AppUpdate;
using CoreHub.Web.Services;
using System.Security.Claims;

namespace CoreHub.Web.Controllers
{
    /// <summary>
    /// 应用更新管理控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class AppUpdateController : ControllerBase
    {
        private readonly IAppUpdateService _updateService;
        private readonly IApplicationLogger _logger;

        public AppUpdateController(IAppUpdateService updateService, IApplicationLogger logger)
        {
            _updateService = updateService;
            _logger = logger;
        }

        /// <summary>
        /// 检查更新 (公开接口，不需要认证)
        /// </summary>
        /// <param name="request">更新检查请求</param>
        /// <returns>更新检查响应</returns>
        [HttpPost("check")]
        [AllowAnonymous]
        public async Task<ActionResult<UpdateCheckResponse>> CheckForUpdate([FromBody] UpdateCheckRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var response = await _updateService.CheckForUpdateAsync(request);
                
                _logger.LogInformation("更新检查完成: 平台={Platform}, 版本={Version}, 有更新={HasUpdate}", 
                    request.Platform, request.CurrentVersion, response.HasUpdate);

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查更新时发生异常");
                return StatusCode(500, new { message = "检查更新失败" });
            }
        }

        /// <summary>
        /// 下载最新版本文件 (公开接口，支持断点续传)
        /// </summary>
        /// <param name="platform">平台类型</param>
        /// <returns>文件流</returns>
        [HttpGet("download/{platform}/latest")]
        [AllowAnonymous]
        public async Task<IActionResult> DownloadLatestUpdate(string platform)
        {
            try
            {
                // 获取最新版本
                var latestVersion = await _updateService.GetLatestVersionAsync(platform);

                if (latestVersion == null)
                {
                    return NotFound(new { message = "未找到可用的最新版本" });
                }

                // 构建文件路径 (文件存储在 wwwroot/updates 目录下)
                var updatesDir = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "updates");
                var fileName = $"{platform}_{latestVersion.VersionNumber}.apk";
                var filePath = Path.Combine(updatesDir, fileName);

                // 确保更新目录存在
                if (!Directory.Exists(updatesDir))
                {
                    _logger.LogWarning("更新文件目录不存在: {UpdatesDir}", updatesDir);
                    return NotFound(new { message = "更新文件目录不存在" });
                }

                if (!System.IO.File.Exists(filePath))
                {
                    _logger.LogWarning("更新文件不存在: {FilePath}", filePath);
                    return NotFound(new { message = "更新文件不存在" });
                }

                // 验证文件完整性
                if (!string.IsNullOrEmpty(latestVersion.FileMd5))
                {
                    var isValid = await _updateService.ValidateFileIntegrityAsync(filePath, latestVersion.FileMd5);
                    if (!isValid)
                    {
                        _logger.LogError(new Exception("文件完整性验证失败"), "更新文件完整性验证失败: {FilePath}", filePath);
                        return StatusCode(500, new { message = "文件完整性验证失败" });
                    }
                }

                _logger.LogInformation("开始下载最新版本文件: {Platform} {Version}", platform, latestVersion.VersionNumber);

                // 支持断点续传
                var fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read);
                var contentType = "application/vnd.android.package-archive";

                return File(fileStream, contentType, fileName, enableRangeProcessing: true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "下载最新版本文件时发生异常: {Platform}", platform);
                return StatusCode(500, new { message = "下载失败" });
            }
        }

        /// <summary>
        /// 下载更新文件 (公开接口，支持断点续传)
        /// </summary>
        /// <param name="platform">平台类型</param>
        /// <param name="version">版本号</param>
        /// <returns>文件流</returns>
        [HttpGet("download/{platform}/{version}")]
        [AllowAnonymous]
        public async Task<IActionResult> DownloadUpdate(string platform, string version)
        {
            try
            {
                // 获取版本信息
                var versions = await _updateService.GetVersionsAsync(platform);
                var targetVersion = versions.Versions.FirstOrDefault(v => v.VersionNumber == version && v.Status == "Released");

                if (targetVersion == null)
                {
                    return NotFound(new { message = "版本不存在或未发布" });
                }

                // 构建文件路径 (文件存储在 wwwroot/updates 目录下)
                var updatesDir = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "updates");
                var fileName = $"{platform}_{version}.apk";
                var filePath = Path.Combine(updatesDir, fileName);

                // 确保更新目录存在
                if (!Directory.Exists(updatesDir))
                {
                    _logger.LogWarning("更新文件目录不存在: {UpdatesDir}", updatesDir);
                    return NotFound(new { message = "更新文件目录不存在" });
                }

                if (!System.IO.File.Exists(filePath))
                {
                    _logger.LogWarning("更新文件不存在: {FilePath}", filePath);
                    return NotFound(new { message = "更新文件不存在" });
                }

                // 验证文件完整性
                if (!string.IsNullOrEmpty(targetVersion.FileMd5))
                {
                    var isValid = await _updateService.ValidateFileIntegrityAsync(filePath, targetVersion.FileMd5);
                    if (!isValid)
                    {
                        _logger.LogError(new Exception("文件完整性验证失败"), "更新文件完整性验证失败: {FilePath}", filePath);
                        return StatusCode(500, new { message = "文件完整性验证失败" });
                    }
                }

                _logger.LogInformation("开始下载更新文件: {Platform} {Version}", platform, version);

                // 支持断点续传
                var fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read);
                var contentType = "application/vnd.android.package-archive";
                
                return File(fileStream, contentType, fileName, enableRangeProcessing: true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "下载更新文件时发生异常: {Platform} {Version}", platform, version);
                return StatusCode(500, new { message = "下载失败" });
            }
        }

        /// <summary>
        /// 获取版本列表 (需要管理员权限)
        /// </summary>
        /// <param name="platform">平台类型</param>
        /// <param name="pageIndex">页码</param>
        /// <param name="pageSize">页大小</param>
        /// <returns>版本列表</returns>
        [HttpGet("versions")]
        [Authorize]
        public async Task<ActionResult> GetVersions(string? platform = null, int pageIndex = 1, int pageSize = 20)
        {
            try
            {
                // 检查管理员权限
                if (!IsAdmin())
                {
                    return Forbid();
                }

                var (versions, totalCount) = await _updateService.GetVersionsAsync(platform, pageIndex, pageSize);
                
                return Ok(new
                {
                    versions,
                    totalCount,
                    pageIndex,
                    pageSize,
                    totalPages = (int)Math.Ceiling((double)totalCount / pageSize)
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取版本列表时发生异常");
                return StatusCode(500, new { message = "获取版本列表失败" });
            }
        }

        /// <summary>
        /// 创建新版本 (需要管理员权限)
        /// </summary>
        /// <param name="version">版本信息</param>
        /// <returns>创建结果</returns>
        [HttpPost("versions")]
        [Authorize]
        public async Task<ActionResult> CreateVersion([FromBody] AppVersion version)
        {
            try
            {
                if (!IsAdmin())
                {
                    return Forbid();
                }

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var userId = GetCurrentUserId();
                version.CreatedBy = userId;

                var (isSuccess, errorMessage, versionId) = await _updateService.CreateVersionAsync(version);

                if (isSuccess)
                {
                    _logger.LogInformation("创建版本成功: {Platform} {Version}, 操作者: {UserId}", 
                        version.Platform, version.VersionNumber, userId);
                    return Ok(new { message = "创建成功", versionId });
                }

                return BadRequest(new { message = errorMessage });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建版本时发生异常");
                return StatusCode(500, new { message = "创建版本失败" });
            }
        }

        /// <summary>
        /// 更新版本信息 (需要管理员权限)
        /// </summary>
        /// <param name="id">版本ID</param>
        /// <param name="version">版本信息</param>
        /// <returns>更新结果</returns>
        [HttpPut("versions/{id}")]
        [Authorize]
        public async Task<ActionResult> UpdateVersion(int id, [FromBody] AppVersion version)
        {
            try
            {
                if (!IsAdmin())
                {
                    return Forbid();
                }

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                version.Id = id;
                version.UpdatedBy = GetCurrentUserId();

                var (isSuccess, errorMessage) = await _updateService.UpdateVersionAsync(version);

                if (isSuccess)
                {
                    _logger.LogInformation("更新版本成功: {VersionId}, 操作者: {UserId}", id, GetCurrentUserId());
                    return Ok(new { message = "更新成功" });
                }

                return BadRequest(new { message = errorMessage });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新版本时发生异常: {VersionId}", id);
                return StatusCode(500, new { message = "更新版本失败" });
            }
        }

        /// <summary>
        /// 发布版本 (需要管理员权限)
        /// </summary>
        /// <param name="id">版本ID</param>
        /// <returns>发布结果</returns>
        [HttpPost("versions/{id}/publish")]
        [Authorize]
        public async Task<ActionResult> PublishVersion(int id)
        {
            try
            {
                if (!IsAdmin())
                {
                    return Forbid();
                }

                var userId = GetCurrentUserId();
                var (isSuccess, errorMessage) = await _updateService.PublishVersionAsync(id, userId);

                if (isSuccess)
                {
                    _logger.LogInformation("发布版本成功: {VersionId}, 操作者: {UserId}", id, userId);
                    return Ok(new { message = "发布成功" });
                }

                return BadRequest(new { message = errorMessage });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "发布版本时发生异常: {VersionId}", id);
                return StatusCode(500, new { message = "发布版本失败" });
            }
        }

        /// <summary>
        /// 撤回版本 (需要管理员权限)
        /// </summary>
        /// <param name="id">版本ID</param>
        /// <returns>撤回结果</returns>
        [HttpPost("versions/{id}/withdraw")]
        [Authorize]
        public async Task<ActionResult> WithdrawVersion(int id)
        {
            try
            {
                if (!IsAdmin())
                {
                    return Forbid();
                }

                var userId = GetCurrentUserId();
                var (isSuccess, errorMessage) = await _updateService.WithdrawVersionAsync(id, userId);

                if (isSuccess)
                {
                    _logger.LogInformation("撤回版本成功: {VersionId}, 操作者: {UserId}", id, userId);
                    return Ok(new { message = "撤回成功" });
                }

                return BadRequest(new { message = errorMessage });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "撤回版本时发生异常: {VersionId}", id);
                return StatusCode(500, new { message = "撤回版本失败" });
            }
        }

        /// <summary>
        /// 删除版本 (需要管理员权限)
        /// </summary>
        /// <param name="id">版本ID</param>
        /// <returns>删除结果</returns>
        [HttpDelete("versions/{id}")]
        [Authorize]
        public async Task<ActionResult> DeleteVersion(int id)
        {
            try
            {
                if (!IsAdmin())
                {
                    return Forbid();
                }

                var (isSuccess, errorMessage) = await _updateService.DeleteVersionAsync(id);

                if (isSuccess)
                {
                    _logger.LogInformation("删除版本成功: {VersionId}, 操作者: {UserId}", id, GetCurrentUserId());
                    return Ok(new { message = "删除成功" });
                }

                return BadRequest(new { message = errorMessage });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除版本时发生异常: {VersionId}", id);
                return StatusCode(500, new { message = "删除版本失败" });
            }
        }

        /// <summary>
        /// 上传更新文件 (需要管理员权限)
        /// </summary>
        /// <param name="file">上传的文件</param>
        /// <param name="platform">平台类型</param>
        /// <param name="version">版本号</param>
        /// <returns>上传结果</returns>
        [HttpPost("upload")]
        [Authorize]
        [RequestSizeLimit(104857600)] // 100MB限制
        public async Task<ActionResult> UploadUpdateFile(IFormFile file, [FromForm] string platform, [FromForm] string version)
        {
            try
            {
                if (!IsAdmin())
                {
                    return Forbid();
                }

                if (file == null || file.Length == 0)
                {
                    return BadRequest(new { message = "请选择要上传的文件" });
                }

                // 验证文件类型
                var allowedExtensions = new[] { ".apk", ".ipa", ".msix", ".dmg" };
                var fileExtension = Path.GetExtension(file.FileName).ToLowerInvariant();

                if (!allowedExtensions.Contains(fileExtension))
                {
                    return BadRequest(new { message = $"不支持的文件类型。支持的类型: {string.Join(", ", allowedExtensions)}" });
                }

                // 验证文件大小 (100MB)
                if (file.Length > 104857600)
                {
                    return BadRequest(new { message = "文件大小不能超过100MB" });
                }

                // 创建上传目录（如果不存在则自动创建）
                var uploadsPath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "updates");
                if (!Directory.Exists(uploadsPath))
                {
                    Directory.CreateDirectory(uploadsPath);
                    _logger.LogInformation("自动创建更新文件目录: {UploadsPath}", uploadsPath);
                }

                // 生成文件名
                var fileName = $"{platform}_{version}{fileExtension}";
                var filePath = Path.Combine(uploadsPath, fileName);

                // 如果文件已存在，先删除
                if (System.IO.File.Exists(filePath))
                {
                    System.IO.File.Delete(filePath);
                }

                // 保存文件
                using (var stream = new FileStream(filePath, FileMode.Create))
                {
                    await file.CopyToAsync(stream);
                }

                // 计算文件MD5
                var md5Hash = await _updateService.CalculateFileMd5Async(filePath);

                _logger.LogInformation("文件上传成功: {FileName}, 大小: {FileSize}, MD5: {MD5}",
                    fileName, file.Length, md5Hash);

                return Ok(new
                {
                    message = "文件上传成功",
                    fileName = fileName,
                    fileSize = file.Length,
                    fileMd5 = md5Hash,
                    downloadUrl = $"/api/AppUpdate/download/{platform}/{version}"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "上传文件时发生异常");
                return StatusCode(500, new { message = "文件上传失败" });
            }
        }

        /// <summary>
        /// 检查当前用户是否为管理员
        /// </summary>
        private bool IsAdmin()
        {
            var username = User.Identity?.Name;
            return !string.IsNullOrEmpty(username) && username.ToLower() == "admin";
        }

        /// <summary>
        /// 获取当前用户ID
        /// </summary>
        private int GetCurrentUserId()
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
            return int.TryParse(userIdClaim?.Value, out var userId) ? userId : 0;
        }
    }
}
