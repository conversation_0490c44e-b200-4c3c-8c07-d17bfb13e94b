-- =============================================
-- 快速修复用户权限脚本
-- 解决"添加零件"按钮没有反应的问题
-- =============================================

USE [EquipmentManagement]
GO

PRINT '开始修复用户权限问题...'
PRINT '========================================'

-- 1. 检查当前用户和角色情况
PRINT '1. 检查当前用户情况:'
SELECT 
    u.Id as '用户ID',
    u.Username as '用户名',
    u.DisplayName as '显示名',
    u.IsEnabled as '是否启用'
FROM Users u
WHERE u.IsEnabled = 1
ORDER BY u.Id

PRINT ''
PRINT '2. 检查用户角色分配情况:'
SELECT 
    u.Id as '用户ID',
    u.Username as '用户名',
    r.Id as '角色ID',
    r.Name as '角色名',
    ur.IsEnabled as '分配是否启用'
FROM Users u
LEFT JOIN UserRoles ur ON u.Id = ur.UserId AND ur.IsEnabled = 1
LEFT JOIN Roles r ON ur.RoleId = r.Id
WHERE u.IsEnabled = 1
ORDER BY u.Id

PRINT ''
PRINT '3. 检查角色部门分配情况:'
SELECT 
    r.Id as '角色ID',
    r.Name as '角色名',
    d.Id as '部门ID',
    d.Name as '部门名',
    rda.IsEnabled as '分配是否启用'
FROM Roles r
LEFT JOIN RoleDepartmentAssignments rda ON r.Id = rda.RoleId AND rda.IsEnabled = 1
LEFT JOIN Departments d ON rda.DepartmentId = d.Id
WHERE r.IsEnabled = 1
ORDER BY r.Id, d.Id

-- 4. 为第一个用户分配设备操作员角色（如果没有角色的话）
PRINT ''
PRINT '4. 修复用户权限:'

DECLARE @FirstUserId INT
DECLARE @OperatorRoleId INT
DECLARE @FirstDepartmentId INT

-- 获取第一个启用的用户
SELECT TOP 1 @FirstUserId = Id FROM Users WHERE IsEnabled = 1 ORDER BY Id

-- 获取设备操作员角色ID
SELECT @OperatorRoleId = Id FROM Roles WHERE Code = 'EQUIPMENT_OPERATOR' AND IsEnabled = 1

-- 获取第一个启用的部门
SELECT TOP 1 @FirstDepartmentId = Id FROM Departments WHERE IsEnabled = 1 ORDER BY Id

IF @FirstUserId IS NOT NULL AND @OperatorRoleId IS NOT NULL
BEGIN
    -- 检查用户是否已有角色
    IF NOT EXISTS (SELECT 1 FROM UserRoles WHERE UserId = @FirstUserId AND RoleId = @OperatorRoleId AND IsEnabled = 1)
    BEGIN
        INSERT INTO UserRoles (UserId, RoleId, AssignedAt, IsEnabled, CreatedAt)
        VALUES (@FirstUserId, @OperatorRoleId, GETDATE(), 1, GETDATE())
        
        PRINT '✓ 为用户 ' + CAST(@FirstUserId AS VARCHAR) + ' 分配了设备操作员角色'
    END
    ELSE
    BEGIN
        PRINT '○ 用户 ' + CAST(@FirstUserId AS VARCHAR) + ' 已有设备操作员角色'
    END
    
    -- 检查角色是否已有部门权限
    IF @FirstDepartmentId IS NOT NULL
    BEGIN
        IF NOT EXISTS (SELECT 1 FROM RoleDepartmentAssignments WHERE RoleId = @OperatorRoleId AND DepartmentId = @FirstDepartmentId AND IsEnabled = 1)
        BEGIN
            INSERT INTO RoleDepartmentAssignments (RoleId, DepartmentId, AssignedAt, IsEnabled, CreatedAt)
            VALUES (@OperatorRoleId, @FirstDepartmentId, GETDATE(), 1, GETDATE())
            
            PRINT '✓ 为设备操作员角色分配了部门 ' + CAST(@FirstDepartmentId AS VARCHAR) + ' 的权限'
        END
        ELSE
        BEGIN
            PRINT '○ 设备操作员角色已有部门 ' + CAST(@FirstDepartmentId AS VARCHAR) + ' 的权限'
        END
    END
    ELSE
    BEGIN
        PRINT '❌ 没有找到可用的部门'
    END
END
ELSE
BEGIN
    IF @FirstUserId IS NULL
        PRINT '❌ 没有找到可用的用户'
    IF @OperatorRoleId IS NULL
        PRINT '❌ 没有找到设备操作员角色'
END

-- 5. 验证修复结果
PRINT ''
PRINT '5. 验证修复结果:'

-- 模拟获取用户可访问部门的查询
SELECT DISTINCT
    u.Id as '用户ID',
    u.Username as '用户名',
    d.Id as '可访问部门ID',
    d.Name as '可访问部门名'
FROM Users u
INNER JOIN UserRoles ur ON u.Id = ur.UserId AND ur.IsEnabled = 1
INNER JOIN RoleDepartmentAssignments rda ON ur.RoleId = rda.RoleId AND rda.IsEnabled = 1
INNER JOIN Departments d ON rda.DepartmentId = d.Id AND d.IsEnabled = 1
WHERE u.IsEnabled = 1
ORDER BY u.Id, d.Id

-- 6. 检查是否还有没有权限的用户
PRINT ''
PRINT '6. 检查没有权限的用户:'

SELECT 
    u.Id as '用户ID',
    u.Username as '用户名',
    u.DisplayName as '显示名'
FROM Users u
WHERE u.IsEnabled = 1
  AND NOT EXISTS (
      SELECT 1 
      FROM UserRoles ur 
      INNER JOIN RoleDepartmentAssignments rda ON ur.RoleId = rda.RoleId AND rda.IsEnabled = 1
      WHERE ur.UserId = u.Id AND ur.IsEnabled = 1
  )

PRINT ''
PRINT '========================================'
PRINT '权限修复完成！'
PRINT ''
PRINT '如果还有用户没有权限，请手动为他们分配角色和部门权限：'
PRINT '1. 进入"角色部门分配管理"页面'
PRINT '2. 为用户分配角色（如"设备操作员"）'
PRINT '3. 为角色分配可访问的部门'
PRINT '4. 刷新页面重新测试'
PRINT '========================================'
