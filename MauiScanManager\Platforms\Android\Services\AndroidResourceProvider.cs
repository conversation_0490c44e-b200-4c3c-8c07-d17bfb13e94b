using Android.App;
using Android.Content;
using AndroidX.Core.Content;
using MauiScanManager.Services; // ȷ��ʹ����ȷ�������ռ�



namespace MauiScanManager.Platforms.Android.Services
{
    public class AndroidResourceProvider : IResourceProvider
    {
        public int GetWarningIconResourceId()
        {
            return global::Android.Resource.Drawable.IcDialogAlert; 
        }

        public int GetInfoIconResourceId()
        {
            return global::Android.Resource.Drawable.IcDialogInfo; 
        }
    }
}