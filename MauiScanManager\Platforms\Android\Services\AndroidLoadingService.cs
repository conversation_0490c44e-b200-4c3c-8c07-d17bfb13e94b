using Android.App;
using Android.Content;
using Android.Graphics.Drawables;
using Microsoft.Maui.Platform;
using MauiScanManager.Services;

namespace MauiScanManager.Platforms.Android.Services
{
    public class AndroidLoadingService : IPlatformLoadingService
    {
        private ProgressDialog _loadingDialog;

        public void ShowNativeLoading(string message = "加载中...")
        {
            var activity = Platform.CurrentActivity;
            if (activity == null) return;

            MainThread.InvokeOnMainThreadAsync(() =>
            {
                _loadingDialog ??= new ProgressDialog(activity)
                {
                    Indeterminate = true
                };
                _loadingDialog.SetCancelable(false);
                _loadingDialog.SetMessage(message);
                _loadingDialog.Show();
            });
        }

        public void HideNativeLoading()
        {
            MainThread.InvokeOnMainThreadAsync(() =>
            {
                try
                {
                    if (_loadingDialog?.IsShowing == true)
                    {
                        _loadingDialog.Dismiss();
                        _loadingDialog.Dispose();
                        _loadingDialog = null;
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Error hiding loading dialog: {ex.Message}");
                }
            });
        }

        
    }
} 