using CustomerWebAPI.Models;
using System.Threading.Tasks;

namespace CustomerWebAPI.Services
{
    public interface ISizingService
    {
        Task<ApiResponse<string>> SaveSizingUpMachine(SizingUpMachine model);
        Task<ApiResponse<SizingMachineUpInfo>> GetSizingMachineUpInfo(SizingMachine model);
        Task<ApiResponse<string>> SaveSizingDownMachine(SizingUpMachine model);
        Task<ApiResponse<string>> SaveSizingCancelUpMachine(SizingCancelUpMachine model);
    }
} 