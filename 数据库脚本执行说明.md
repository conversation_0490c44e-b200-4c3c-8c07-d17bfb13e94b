# 数据库脚本执行说明

## 问题解决

刚才遇到的错误：
```
消息 1088，级别 16，状态 12，第 4 行
找不到对象"dbo.MaintenancePersonnel"，因为它不存在或者您没有所需的权限。
```

**原因**：数据库脚本中有遗留的 `MaintenancePersonnel` 表索引创建语句，但该表已经被移除。

**解决方案**：已修复 `数据库脚本_完整版.sql` 文件，移除了对不存在表的索引创建语句。

## 执行步骤

### 1. 清理现有数据库
```sql
USE master;
GO

-- 如果数据库存在，先删除
IF EXISTS (SELECT name FROM sys.databases WHERE name = 'CoreHub')
BEGIN
    ALTER DATABASE [CoreHub] SET SINGLE_USER WITH ROLLBACK IMMEDIATE;
    DROP DATABASE [CoreHub];
    PRINT '已删除现有 CoreHub 数据库';
END
```

### 2. 执行修复后的完整脚本
1. **打开 SQL Server Management Studio**
2. **连接到 SQL Server 实例**
3. **打开修复后的** `数据库脚本_完整版.sql`
4. **执行脚本**（F5）

### 3. 验证执行结果

#### 检查数据库创建：
```sql
-- 检查数据库是否创建成功
SELECT name FROM sys.databases WHERE name = 'CoreHub';

-- 检查表数量
USE CoreHub;
SELECT COUNT(*) as TableCount FROM INFORMATION_SCHEMA.TABLES;
```

#### 检查关键表：
```sql
-- 检查用户表
SELECT COUNT(*) as UserCount FROM Users;

-- 检查部门表
SELECT COUNT(*) as DepartmentCount FROM Departments;

-- 检查权限表
SELECT COUNT(*) as PermissionCount FROM Permissions;

-- 检查菜单表
SELECT COUNT(*) as MenuCount FROM MenuItems;
```

#### 检查新增功能：
```sql
-- 检查用户部门分配权限
SELECT * FROM Permissions WHERE Code = 'UserManagement.AssignDepartment';

-- 检查用户部门分配菜单
SELECT * FROM MenuItems WHERE Code = 'UserDepartmentAssignmentManagement';

-- 检查用户部门分配情况
SELECT u.Username, u.DisplayName, d.Name as DepartmentName
FROM Users u
LEFT JOIN Departments d ON u.DepartmentId = d.Id;
```

### 4. 预期结果

执行成功后应该看到：

#### 默认用户：
- `admin` - 系统管理员（整理部）
- `operator` - 设备操作员（整理部）
- `viewer` - 电气维修员（工程部）

#### 默认部门：
- 整理部（生产部门）
- 工程部（维修部门）
- 动力部（维修部门）
- 安全部（支持部门）

#### 新增权限：
- `UserManagement.AssignDepartment` - 用户部门分配

#### 新增菜单：
- 用户部门分配管理（在系统管理菜单下）

## 启动应用程序

### 1. 重新启动应用
数据库脚本执行完成后，重新启动 CoreHub 应用程序。

### 2. 登录测试
使用默认管理员账号：
- **用户名**：`admin`
- **密码**：`admin123`

### 3. 验证功能

#### 检查菜单：
在系统管理菜单下应该能看到"用户部门分配"菜单项。

#### 测试用户管理：
1. 访问 `/users` 页面
2. 编辑用户，检查"所属部门"选择框
3. 在用户列表中查看"所属部门"列

#### 测试部门分配管理：
1. 访问 `/user-department-assignment-management` 页面
2. 查看统计信息
3. 测试搜索和筛选功能
4. 测试部门分配功能

## 故障排除

### 如果仍然遇到错误：

#### 1. 检查 SQL Server 版本
确保使用的是 SQL Server 2016 或更高版本。

#### 2. 检查权限
确保 SQL Server 登录用户有创建数据库的权限。

#### 3. 逐步执行
如果整个脚本执行失败，可以分段执行：
1. 先执行表创建部分
2. 再执行数据插入部分
3. 最后执行索引创建部分

#### 4. 查看详细错误
如果遇到其他错误，请提供完整的错误信息。

## 重要提醒

1. **备份重要数据**：如果有重要数据，请先备份
2. **测试环境验证**：建议先在测试环境执行
3. **权限检查**：确保应用程序连接字符串中的用户有足够权限
4. **重启应用**：数据库更新后必须重启应用程序

## 成功标志

如果看到以下内容，说明执行成功：
- ✅ 数据库 CoreHub 创建成功
- ✅ 所有表创建成功（约20+个表）
- ✅ 默认数据插入成功
- ✅ 用户部门分配权限和菜单创建成功
- ✅ 应用程序启动正常
- ✅ 可以正常登录和使用新功能
