using CoreHub.Shared.Models.AppUpdate;
using CoreHub.Shared.Services;

namespace CoreHub.Web.Services
{
    /// <summary>
    /// Web平台的客户端更新服务实现（空实现，Web平台不支持客户端更新）
    /// </summary>
    public class WebClientUpdateService : IClientUpdateService
    {
        private readonly IApplicationLogger _logger;

        public WebClientUpdateService(IApplicationLogger logger)
        {
            _logger = logger;
        }

        public event EventHandler<UpdateCheckResponse>? UpdateAvailable;
        public event EventHandler<DownloadProgress>? DownloadProgressChanged;
        public event EventHandler<DownloadCompletedEventArgs>? DownloadCompleted;

        public Task<UpdateCheckResponse> CheckForUpdateAsync(bool silent = true)
        {
            _logger.LogInformation("Web平台不支持客户端更新检查");
            
            // 返回一个表示没有更新的响应
            var response = new UpdateCheckResponse
            {
                HasUpdate = false,
                LatestVersion = null,
                IsForceUpdate = false,
                Message = "Web平台不支持客户端更新"
            };
            
            return Task.FromResult(response);
        }

        public Task<(bool IsSuccess, string? FilePath, string? ErrorMessage)> DownloadUpdateAsync(
            VersionInfo versionInfo, 
            IProgress<DownloadProgress>? progress = null, 
            CancellationToken cancellationToken = default)
        {
            _logger.LogWarning("Web平台不支持下载更新");
            return Task.FromResult<(bool IsSuccess, string? FilePath, string? ErrorMessage)>((false, null, "Web平台不支持下载更新"));
        }

        public Task<(bool IsSuccess, string? ErrorMessage)> InstallUpdateAsync(string filePath)
        {
            _logger.LogWarning("Web平台不支持安装更新");
            return Task.FromResult<(bool IsSuccess, string? ErrorMessage)>((false, "Web平台不支持安装更新"));
        }

        public Task<DeviceInfo> GetDeviceInfoAsync()
        {
            return Task.FromResult(new DeviceInfo
            {
                Model = "Web Browser",
                OsVersion = "Web",
                Language = "zh",
                TimeZone = "Asia/Shanghai"
            });
        }

        public Task<(string VersionNumber, int VersionCode)> GetCurrentVersionAsync()
        {
            // Web平台返回固定版本信息
            return Task.FromResult(("1.0.0", 1));
        }

        public Task<bool> ValidateUpdateFileAsync(string filePath, string expectedMd5)
        {
            _logger.LogWarning("Web平台不支持验证更新文件");
            return Task.FromResult(false);
        }

        public Task<bool> CleanupOldUpdatesAsync()
        {
            _logger.LogInformation("Web平台不需要清理更新文件");
            return Task.FromResult(true);
        }

        public void SetUpdateCheckInterval(int intervalHours)
        {
            _logger.LogInformation("Web平台不支持设置更新检查间隔");
        }

        public void StartAutoUpdateCheck()
        {
            _logger.LogInformation("Web平台不支持自动更新检查");
        }

        public void StopAutoUpdateCheck()
        {
            _logger.LogInformation("Web平台不支持自动更新检查");
        }
    }
}
