# 技术栈

## 框架和运行时
- **.NET 8.0** - 所有项目的目标框架
- **C#** - 主要编程语言，启用可空引用类型
- **Blazor Server** - 用于交互式服务器端渲染的 Web UI 框架
- **.NET MAUI** - 移动和桌面应用的跨平台框架

## UI 和组件
- **MudBlazor 7.8.0** - 主要 UI 组件库
- **Blazor Components** - CoreHub.Shared 中的自定义共享组件
- **响应式设计** - 移动优先的自适应布局方法

## 数据和 ORM
- **SQL Server** - 主数据库（开发环境支持 LocalDB）
- **SqlSugar 5.1.4.196** - 数据访问的 ORM 框架
- **实体模型** - 位于 CoreHub.Shared/Models

## 身份验证和安全
- **ASP.NET Core Identity** - 身份验证框架
- **BCrypt.Net** - 密码哈希
- **基于角色的授权** - 自定义权限系统
- **JWT 令牌** - 用于 API 身份验证

## 日志和监控
- **Serilog** - 结构化日志框架
- **文件日志** - logs/ 目录中的滚动日志文件
- **控制台日志** - 开发和调试输出

## 平台特定库
- **ZXing.Net.Maui** - 二维码和条形码扫描
- **平台 API** - 每个平台的原生通知服务

## 构建和开发

### 常用命令
```bash
# 构建整个解决方案
dotnet build

# 运行 Web 应用程序
cd CoreHub.Web
dotnet run

# 运行 MAUI 应用程序（平台特定）
cd CoreHub.Maui
dotnet build -f net8.0-windows10.0.19041.0  # Windows
dotnet build -f net8.0-android              # Android
dotnet build -f net8.0-ios                  # iOS
dotnet build -f net8.0-maccatalyst          # macOS

# 还原包
dotnet restore

# 清理构建产物
dotnet clean
```

### 开发环境
- **Visual Studio 2022** 或 **Visual Studio Code**
- 需要 **.NET 8.0 SDK**
- 数据库需要 **SQL Server** 或 **LocalDB**

### 配置
- **appsettings.json** - Web 应用程序配置
- **数据库连接字符串** - 环境特定设置
- **SSL 证书** - 位于 CoreHub.Web/Certificates/

## 代码风格和约定
- 所有项目启用 **隐式 using**
- 启用 **可空引用类型**
- **中文注释** - 文档和注释使用中文
- **服务注册** - 在 Program.cs/MauiProgram.cs 中进行依赖注入
- **异步/等待模式** - 用于所有 I/O 操作