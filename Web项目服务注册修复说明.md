# Web项目服务注册修复说明

## 🔧 问题描述

在Web项目中出现了以下错误：
```
InvalidOperationException: Cannot provide a value for property 'UpdateUIService' on type 'CoreHub.Shared.Pages.Home'. There is no registered service of type 'CoreHub.Shared.Services.IUpdateUIService'.
```

## 🎯 问题原因

`Home.razor`页面中注入了`IUpdateUIService`服务：
```csharp
@inject IUpdateUIService UpdateUIService
```

但是在Web项目的`Program.cs`中没有注册这个服务的实现，导致依赖注入失败。

## ✅ 解决方案

### 1. 创建Web平台的更新UI服务实现

**文件**: `CoreHub.Web/Services/WebUpdateUIService.cs`

```csharp
public class WebUpdateUIService : IUpdateUIService
{
    private readonly IApplicationLogger _logger;

    public WebUpdateUIService(IApplicationLogger logger)
    {
        _logger = logger;
    }

    public async Task CheckForUpdateAsync()
    {
        _logger.LogInformation("Web平台不支持自动更新功能");
        await Task.CompletedTask;
    }

    public async Task ShowUpdatePageAsync()
    {
        _logger.LogInformation("Web平台不支持显示更新页面");
        await Task.CompletedTask;
    }
}
```

### 2. 创建Web平台的客户端更新服务实现

**文件**: `CoreHub.Web/Services/WebClientUpdateService.cs`

```csharp
public class WebClientUpdateService : IClientUpdateService
{
    // 空实现，Web平台不支持客户端更新
    public Task<UpdateCheckResponse> CheckForUpdateAsync(bool silent = true)
    {
        var response = new UpdateCheckResponse
        {
            HasUpdate = false,
            LatestVersion = null,
            IsForceUpdate = false,
            Message = "Web平台不支持客户端更新"
        };
        return Task.FromResult(response);
    }
    
    // ... 其他方法的空实现
}
```

### 3. 在Program.cs中注册服务

**文件**: `CoreHub.Web/Program.cs`

```csharp
// 注册Web平台的更新UI服务
builder.Services.AddScoped<CoreHub.Shared.Services.IUpdateUIService, CoreHub.Web.Services.WebUpdateUIService>();

// 注册Web平台的客户端更新服务（空实现）
builder.Services.AddScoped<CoreHub.Shared.Services.IClientUpdateService, CoreHub.Web.Services.WebClientUpdateService>();
```

## 🏗️ 架构设计说明

### 平台特定服务实现

不同平台对更新功能的支持不同：

#### MAUI平台 (Android/iOS/Windows)
- **AndroidUpdateUIService**: 支持真实的APK下载和安装
- **DefaultUpdateUIService**: 其他平台的默认实现，显示不支持提示

#### Web平台
- **WebUpdateUIService**: 空实现，记录日志
- **WebClientUpdateService**: 空实现，返回不支持更新的响应

### 服务注册策略

#### MAUI项目 (MauiProgram.cs)
```csharp
#if ANDROID
    builder.Services.AddSingleton<IUpdateUIService, AndroidUpdateUIService>();
    builder.Services.AddSingleton<IClientUpdateService, AndroidUpdateService>();
#else
    builder.Services.AddSingleton<IUpdateUIService, DefaultUpdateUIService>();
#endif
```

#### Web项目 (Program.cs)
```csharp
builder.Services.AddScoped<IUpdateUIService, WebUpdateUIService>();
builder.Services.AddScoped<IClientUpdateService, WebClientUpdateService>();
```

## 🔍 代码兼容性处理

### Home.razor中的安全调用

```csharp
// 安全获取客户端更新服务（可能为null）
var clientUpdateService = ServiceProvider.GetService(typeof(IClientUpdateService)) as IClientUpdateService;
if (clientUpdateService != null)
{
    // 使用服务
}
else
{
    // 降级处理
}
```

这种设计确保了：
1. **跨平台兼容性**: 同一个Blazor组件可以在不同平台运行
2. **优雅降级**: Web平台不支持的功能会有合适的提示
3. **类型安全**: 通过依赖注入确保服务可用性

## 🔧 接口实现完整性修复

### 修复的编译错误
1. **缺失方法**: `ValidateUpdateFileAsync(string, string)`
2. **缺失方法**: `CleanupOldUpdatesAsync()`
3. **缺失方法**: `SetUpdateCheckInterval(int)`
4. **方法签名不匹配**: `GetCurrentVersionAsync()` 返回值元组名称

### 完整的接口实现
```csharp
public class WebClientUpdateService : IClientUpdateService
{
    // 所有必需的方法都已实现
    public Task<UpdateCheckResponse> CheckForUpdateAsync(bool silent = true);
    public Task<(bool IsSuccess, string? FilePath, string? ErrorMessage)> DownloadUpdateAsync(...);
    public Task<(bool IsSuccess, string? ErrorMessage)> InstallUpdateAsync(string filePath);
    public Task<bool> ValidateUpdateFileAsync(string filePath, string expectedMd5);
    public Task<(string VersionNumber, int VersionCode)> GetCurrentVersionAsync();
    public Task<DeviceInfo> GetDeviceInfoAsync();
    public Task<bool> CleanupOldUpdatesAsync();
    public void SetUpdateCheckInterval(int intervalHours);
    public void StartAutoUpdateCheck();
    public void StopAutoUpdateCheck();

    // 所有必需的事件都已声明
    public event EventHandler<UpdateCheckResponse>? UpdateAvailable;
    public event EventHandler<DownloadProgress>? DownloadProgressChanged;
    public event EventHandler<DownloadCompletedEventArgs>? DownloadCompleted;
}
```

## ✅ 修复验证

修复后，Web项目应该能够正常启动，不再出现服务注册错误和编译错误。

### 测试步骤
1. 启动Web项目
2. 访问首页 (/)
3. 点击"测试应用更新"按钮
4. 应该看到"Web平台不支持自动更新功能"的日志信息

## 📝 总结

通过为Web平台创建适当的服务实现并注册到依赖注入容器中，解决了跨平台共享组件在不同环境下的服务依赖问题。这种设计模式确保了代码的可维护性和平台兼容性。
