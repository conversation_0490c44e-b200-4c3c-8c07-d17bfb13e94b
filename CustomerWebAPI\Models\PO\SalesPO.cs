﻿using System;
using Dapper.Contrib.Extensions;

namespace CustomerWebAPI.Models
{
    [Table("SalesPO")]
    public class SalesPO
    {
        public int Id { get; set; }

        [ExplicitKey]
        public string TrackingId { get; set; }

        public string PoJsonText { get; set; }
        public string? CreatedBy { get; set; }
        public DateTime? CreatedDate { get; set; }
        public string? ModifiedBy { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public bool IsCancelled { get; set; }
        public bool IsSyncedToERP { get; set; }
        public bool IsCreatedPO { get; set; }
    }
}