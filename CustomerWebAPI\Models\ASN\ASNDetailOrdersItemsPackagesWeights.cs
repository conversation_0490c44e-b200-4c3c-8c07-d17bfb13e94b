using System;
using Dapper.Contrib.Extensions;

namespace CustomerWebAPI.Models
{
    /// <summary>
    /// 
    /// </summary>
    /// 
    [Table("ASNDetailOrdersItemsPackagesWeights")]
    public class ASNDetailOrdersItemsPackagesWeights
    {
        /// <summary>
        /// 
        /// </summary>
        /// 
        [ExplicitKey]
        public Guid Id { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public Guid? ASNDetailOrdersItemsPackagesId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string Qualifier { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public decimal? Value { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string Unit { get; set; }
    }
}