@using CoreHub.Shared.Models.Database
@using CoreHub.Shared.Services
@using FluentValidation
@inject ILocationService LocationService
@inject IDepartmentService DepartmentService
@inject ISnackbar Snackbar

<MudDialog>
    <DialogContent>
        <MudForm @ref="form" Model="@Location" Validation="@(new LocationValidator())">
            <MudGrid>
                <MudItem xs="12" md="6">
                    <MudTextField @bind-Value="Location.Code"
                                For="@(() => Location.Code)"
                                Label="位置编码"
                                Required="true"
                                Immediate="true"
                                Disabled="@IsEdit" />
                </MudItem>
                <MudItem xs="12" md="6">
                    <MudTextField @bind-Value="Location.Name"
                                For="@(() => Location.Name)"
                                Label="位置名称"
                                Required="true"
                                Immediate="true" />
                </MudItem>
                <MudItem xs="12" md="6">
                    <MudSelect T="int" @bind-Value="Location.DepartmentId"
                             For="@(() => Location.DepartmentId)"
                             Label="所属部门"
                             Required="true">
                        @foreach (var dept in departments)
                        {
                            <MudSelectItem T="int" Value="@dept.Id">@dept.Name</MudSelectItem>
                        }
                    </MudSelect>
                </MudItem>
                <MudItem xs="12" md="6">
                    <MudSelect T="int?" @bind-Value="Location.ParentId"
                             Label="上级位置"
                             Clearable="true">
                        @foreach (var loc in parentLocations)
                        {
                            <MudSelectItem T="int?" Value="@(loc.Id)">@loc.Name</MudSelectItem>
                        }
                    </MudSelect>
                </MudItem>
                <MudItem xs="12">
                    <MudTextField @bind-Value="Location.Address"
                                For="@(() => Location.Address)"
                                Label="详细地址"
                                Immediate="true" />
                </MudItem>
                <MudItem xs="12">
                    <MudTextField @bind-Value="Location.Description"
                                For="@(() => Location.Description)"
                                Label="描述"
                                Lines="3"
                                Immediate="true" />
                </MudItem>
                <MudItem xs="12">
                    <MudSwitch T="bool" @bind-Value="Location.IsEnabled"
                             Label="启用状态"
                             Color="Color.Primary" />
                </MudItem>
                <MudItem xs="12">
                    <MudTextField @bind-Value="Location.Remark"
                                For="@(() => Location.Remark)"
                                Label="备注"
                                Lines="2"
                                Immediate="true" />
                </MudItem>
            </MudGrid>
        </MudForm>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="Cancel">取消</MudButton>
        <MudButton Color="Color.Primary" 
                 Variant="Variant.Filled" 
                 OnClick="Submit"
                 Disabled="@saving">
            @if (saving)
            {
                <MudProgressCircular Class="ms-n1" Size="Size.Small" Indeterminate="true"/>
                <MudText Class="ms-2">保存中...</MudText>
            }
            else
            {
                <MudText>@(IsEdit ? "更新" : "创建")</MudText>
            }
        </MudButton>
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter] MudDialogInstance MudDialog { get; set; } = null!;
    [Parameter] public Location Location { get; set; } = new();
    [Parameter] public bool IsEdit { get; set; } = false;

    private MudForm form = null!;
    private bool saving = false;
    private List<Department> departments = new();
    private List<Location> parentLocations = new();

    protected override async Task OnInitializedAsync()
    {
        await LoadDepartments();
        await LoadParentLocations();
        
        if (!IsEdit)
        {
            Location.IsEnabled = true;
        }
    }

    private async Task LoadDepartments()
    {
        try
        {
            departments = await DepartmentService.GetEnabledDepartmentsAsync();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载部门数据失败: {ex.Message}", Severity.Error);
        }
    }

    private async Task LoadParentLocations()
    {
        try
        {
            var allLocations = await LocationService.GetEnabledLocationsAsync();
            
            // 如果是编辑模式，排除自己和自己的子位置
            if (IsEdit)
            {
                parentLocations = allLocations
                    .Where(l => l.Id != Location.Id && !IsChildLocation(l, Location.Id))
                    .ToList();
            }
            else
            {
                parentLocations = allLocations.ToList();
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载上级位置失败: {ex.Message}", Severity.Error);
        }
    }

    private bool IsChildLocation(Location location, int parentId)
    {
        // 简单的递归检查，实际项目中可能需要更复杂的逻辑
        return location.ParentId == parentId;
    }

    private void Cancel()
    {
        MudDialog.Cancel();
    }

    private async Task Submit()
    {
        await form.Validate();
        if (!form.IsValid) return;

        saving = true;
        try
        {
            (bool IsSuccess, string ErrorMessage) result;
            
            if (IsEdit)
            {
                Location.UpdatedAt = DateTime.Now;
                result = await LocationService.UpdateLocationAsync(Location);
            }
            else
            {
                result = await LocationService.CreateLocationAsync(Location);
            }

            if (result.IsSuccess)
            {
                Snackbar.Add($"位置{(IsEdit ? "更新" : "创建")}成功", Severity.Success);
                MudDialog.Close(DialogResult.Ok(true));
            }
            else
            {
                Snackbar.Add($"操作失败: {result.ErrorMessage}", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"操作失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            saving = false;
        }
    }

    public class LocationValidator : AbstractValidator<Location>
    {
        public LocationValidator()
        {
            RuleFor(x => x.Code)
                .NotEmpty().WithMessage("位置编码不能为空")
                .MaximumLength(50).WithMessage("位置编码长度不能超过50个字符");

            RuleFor(x => x.Name)
                .NotEmpty().WithMessage("位置名称不能为空")
                .MaximumLength(100).WithMessage("位置名称长度不能超过100个字符");

            RuleFor(x => x.DepartmentId)
                .GreaterThan(0).WithMessage("请选择所属部门");

            RuleFor(x => x.Address)
                .MaximumLength(200).WithMessage("详细地址长度不能超过200个字符");

            RuleFor(x => x.Description)
                .MaximumLength(500).WithMessage("描述长度不能超过500个字符");

            RuleFor(x => x.Remark)
                .MaximumLength(1000).WithMessage("备注长度不能超过1000个字符");
        }

        public Func<object, string, Task<IEnumerable<string>>> ValidateValue => async (model, propertyName) =>
        {
            var result = await ValidateAsync(ValidationContext<Location>.CreateWithOptions((Location)model, x => x.IncludeProperties(propertyName)));
            if (result.IsValid)
                return Array.Empty<string>();
            return result.Errors.Select(e => e.ErrorMessage);
        };
    }
}
