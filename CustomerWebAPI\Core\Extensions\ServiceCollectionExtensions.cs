using CustomerWebAPI.Common;
using CustomerWebAPI.Configurations;
using CustomerWebAPI.Constants;
using CustomerWebAPI.Database;
using CustomerWebAPI.Filters;
using CustomerWebAPI.Services;
using FluentValidation;
using FluentValidation.AspNetCore;
using Microsoft.AspNetCore.Authentication;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System.Net.Http;
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// 添加基本身份认证服务
    /// 配置使用BasicAuth认证方案，通过BasicAuthHandler处理身份验证
    /// </summary>
    public static IServiceCollection AddBasicAuthentication(this IServiceCollection services)
    {
        services.AddAuthentication("BasicAuth")
            .AddScheme<AuthenticationSchemeOptions, BasicAuthHandler>("BasicAuth", null);
        return services;
    }

    /// <summary>
    /// 注册应用程序核心服务
    /// 包括订单、销售、ASN、化学品、浆纱、登录等业务服务
    /// 根据服务特性使用不同的生命周期(Scoped/Singleton)
    /// </summary>
    public static IServiceCollection AddApplicationServices(this IServiceCollection services)
    {
        // 数据访问相关服务使用 Scoped
        services.AddScoped<IOrderService, OrderService>();
        services.AddScoped<IChemicalService, ChemicalService>();
        services.AddScoped<IDepartmentService, DepartmentService>();
        services.AddScoped<IColorCardService, ColorCardService>();
        services.AddScoped<ISizingService, SizingService>();
        services.AddScoped<IHireWorkerService, HireWorkerService>();
        services.AddScoped<IYarnCarLocationService, YarnCarLocationService>();

        services.AddScoped<ITwistService, TwistService>();

        // 其他服务使用 Singleton
        services.AddSingleton<ISalesService, SalesService>();
        services.AddSingleton<IASNService, ASNService>();
        services.AddSingleton<LoginService>();
        services.AddSingleton<IUpdateService, UpdateService>();
        
        return services;
    }

    /// <summary>
    /// 配置HTTP客户端服务
    /// 为ASN服务配置HTTP客户端，设置自动重定向和证书选项
    /// </summary>
    public static IServiceCollection AddConfiguredHttpClients(this IServiceCollection services)
    {
        services.AddHttpClient<ASNService>()
            .ConfigurePrimaryHttpMessageHandler(() => new HttpClientHandler
            {
                AllowAutoRedirect = true,
                ClientCertificateOptions = ClientCertificateOption.Automatic
            });
        return services;
    }

    /// <summary>
    /// 添加目录服务
    /// 配置目录设置并注册目录初始化服务
    /// </summary>
    public static IServiceCollection AddDirectoryServices(this IServiceCollection services, IConfiguration configuration)
    {
        services.Configure<DirectorySettings>(
            configuration.GetSection(DirectorySettings.SectionName));
        services.AddSingleton<IDirectoryInitializationService, DirectoryInitializationService>();
        
        return services;
    }

    /// <summary>
    /// 添加FluentValidation验证服务
    /// 自动扫描程序集中的验证器并配置验证选项
    /// 启用数据注解验证
    /// </summary>
    public static IServiceCollection AddFluentValidationServices(this IServiceCollection services)
    {
        services.AddValidatorsFromAssemblyContaining<Program>();
        services.AddFluentValidationAutoValidation(config => 
        {
            // 启用数据注解验证
            config.DisableDataAnnotationsValidation = false;
        });

        return services;
    }

    /// <summary>
    /// 配置API控制器
    /// 添加验证过滤器，禁用默认模型验证
    /// 使用自定义验证过滤器处理验证响应
    /// </summary>
    public static IServiceCollection AddApiControllerConfiguration(this IServiceCollection services)
    {
        services.AddControllers(options =>
        {
            options.Filters.Add<ValidationFilter>();
        })
        .ConfigureApiBehaviorOptions(options =>
        {
            // 禁用默认的模型验证响应，使用我们的自定义验证过滤器
            options.SuppressModelStateInvalidFilter = true;
        });

        return services;
    }

    /// <summary>
    /// 添加CORS策略配置
    /// 配置允许所有来源、方法和头部的跨域请求策略
    /// </summary>
    public static IServiceCollection AddCorsPolicy(this IServiceCollection services)
    {
        services.AddCors(options => options.AddPolicy(PolicyNames.AllowAllOrigin,
            builder => builder.AllowAnyOrigin()
                             .AllowAnyMethod()
                             .AllowAnyHeader()));
        return services;
    }

    /// <summary>
    /// 添加数据库服务
    /// 配置数据库设置并注册数据库上下文
    /// 使用 Scoped 生命周期注册 DbContext，确保每个请求使用独立的实例
    /// </summary>
    public static IServiceCollection AddDatabaseServices(this IServiceCollection services, IConfiguration configuration)
    {
        // 注册数据库配置
        services.Configure<DbConfig>(configuration.GetSection("Database"));
        
        // 注册数据库上下文 - 使用 Scoped 而不是 Singleton
        services.AddScoped<DbContext>();
        
        return services;
    }
} 