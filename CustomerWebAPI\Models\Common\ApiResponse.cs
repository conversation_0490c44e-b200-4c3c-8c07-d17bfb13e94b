namespace CustomerWebAPI.Models
{
    public class ApiResponse
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public int Code { get; set; }
    }

    public class ApiResponse<T> : ApiResponse
    {
        public T? Data { get; set; }

        public static ApiResponse<T> Ok(T data, string message = "")
        {
            return new ApiResponse<T>
            {
                Success = true,
                Message = message,
                Code = 200,
                Data = data
            };
        }

        public static ApiResponse<T> Fail(string message, int code = 400)
        {
            return new ApiResponse<T>
            {
                Success = false,
                Message = message,
                Code = code
            };
        }
    }
} 