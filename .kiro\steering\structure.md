# 项目结构

## 解决方案架构

CoreHub 解决方案采用共享库架构，包含三个主要项目：

```
CoreHub.sln
├── CoreHub.Maui/          # 跨平台移动/桌面应用
├── CoreHub.Shared/        # 共享业务逻辑和组件
└── CoreHub.Web/           # ASP.NET Core Blazor Server Web 应用
```

## CoreHub.Shared (共享库)

**用途**: 包含 Web 和移动应用程序共同使用的所有共享业务逻辑、组件和模型。

```
CoreHub.Shared/
├── Components/            # 可重用的 Blazor 组件
├── Configuration/         # 配置类和设置
├── Data/                  # 数据库上下文和数据访问
├── Layout/                # 共享布局组件
├── Models/                # 实体模型和 DTO
├── Pages/                 # 共享页面组件
├── Services/              # 业务逻辑服务
├── Utils/                 # 工具类和辅助方法
├── wwwroot/              # 静态 Web 资源
├── Routes.razor          # 路由定义
└── _Imports.razor        # 全局 using 语句
```

## CoreHub.Web (Web 应用程序)

**用途**: 面向 Web 浏览器的 ASP.NET Core Blazor Server 应用程序。

```
CoreHub.Web/
├── Components/           # Web 特定组件
├── Configuration/        # Web 应用配置
├── Controllers/          # API 控制器
├── Extensions/           # 扩展方法
├── Middleware/           # 自定义中间件
├── Services/             # Web 特定服务
├── wwwroot/             # 静态 Web 资源
├── Certificates/        # SSL 证书
├── logs/                # 应用程序日志
├── Program.cs           # 应用程序入口点
└── appsettings.json     # 配置设置
```

## CoreHub.Maui (移动/桌面应用程序)

**用途**: 支持 Android、iOS、Windows 和 macOS 的 .NET MAUI 混合应用程序。

```
CoreHub.Maui/
├── Platforms/           # 平台特定代码
│   ├── Android/         # Android 特定实现
│   ├── iOS/             # iOS 特定实现
│   ├── MacCatalyst/     # macOS 特定实现
│   └── Windows/         # Windows 特定实现
├── Resources/           # 应用资源（图标、字体、图片）
├── Services/            # MAUI 特定服务
├── Helpers/             # 平台辅助类
├── Pages/               # MAUI 特定页面
├── Components/          # MAUI 特定组件
├── wwwroot/            # 混合应用的 Web 资源
├── App.xaml            # 应用程序定义
├── AppShell.xaml       # Shell 导航
├── MainPage.xaml       # 主页面
└── MauiProgram.cs      # MAUI 应用配置
```

## 关键架构模式

### 依赖注入
- 服务在 `Program.cs` (Web) 和 `MauiProgram.cs` (MAUI) 中注册
- 基于接口的服务契约位于 CoreHub.Shared
- 平台特定实现位于各自的项目中

### 服务层模式
- 业务逻辑封装在服务类中
- 服务位于 `CoreHub.Shared/Services/`
- 接口定义遵循 `I{ServiceName}Service` 约定

### 仓储模式
- 通过 SqlSugar ORM 进行数据访问
- 数据库上下文位于 `CoreHub.Shared/Data/DatabaseContext`
- 实体模型位于 `CoreHub.Shared/Models/`

### 组件架构
- 共享 Blazor 组件位于 `CoreHub.Shared/Components/`
- 平台特定组件位于各自的项目文件夹中
- 布局组件确保一致的 UI 结构

## 文件命名约定

### 服务
- 接口: `I{Name}Service.cs`
- 实现: `{Name}Service.cs`
- 平台特定: `{Platform}{Name}Service.cs`

### 组件
- Blazor 组件: `{Name}.razor`
- 代码后置: `{Name}.razor.cs`
- 共享布局: `{Name}Layout.razor`

### 模型
- 实体模型: `{EntityName}.cs`
- DTO: `{Name}Dto.cs`
- 视图模型: `{Name}ViewModel.cs`

### 页面
- 页面组件: `{PageName}.razor`
- 适用时嵌套在功能文件夹中

## 配置管理

### Web 应用程序
- `appsettings.json` - 基础配置
- `appsettings.Development.json` - 开发环境覆盖配置
- 敏感数据使用环境变量

### MAUI 应用程序
- 通过 `DatabaseConfig.GetConfigurationData()` 进行配置
- 平台特定设置位于各自的平台文件夹中

## 数据库脚本和文档

根目录文件包含数据库脚本、文档和实现指南：
- `*.sql` - 数据库脚本和存储过程
- `*.md` - 功能实现文档和指南
- `*.cs` - 独立测试文件和工具

## 构建产物

### 从源代码控制中排除
- 所有项目中的 `bin/` 和 `obj/` 文件夹
- `logs/` 目录内容
- `wwwroot/updates/` 目录（运行时生成）
- 平台特定的构建输出

### 证书和安全
- SSL 证书位于 `CoreHub.Web/Certificates/`
- 通过专用服务进行证书管理
- 启动时自动证书验证