@using CoreHub.Shared.Models.Database
@using CoreHub.Shared.Services
@inject IJobTypeService JobTypeService
@inject ISnackbar Snackbar

<MudDialog>
    <DialogContent>
        <MudContainer MaxWidth="MaxWidth.Medium">
            <MudText Typo="Typo.h6" Class="mb-4">
                <MudIcon Icon="@Icons.Material.Filled.Edit" Class="mr-2" />
                编辑用户工种信息
            </MudText>

            <MudForm @ref="form" @bind-IsValid="@isValid">
                <MudStack Spacing="3">
                    <!-- 用户信息（只读） -->
                    <MudCard>
                        <MudCardContent>
                            <MudText Typo="Typo.subtitle1" Class="mb-2">用户信息</MudText>
                            <MudStack Row Spacing="4">
                                <MudStack Spacing="1">
                                    <MudText Typo="Typo.body2" Color="Color.Secondary">用户名</MudText>
                                    <MudText Typo="Typo.body1">@User?.Username</MudText>
                                </MudStack>
                                <MudStack Spacing="1">
                                    <MudText Typo="Typo.body2" Color="Color.Secondary">显示名称</MudText>
                                    <MudText Typo="Typo.body1">@User?.DisplayName</MudText>
                                </MudStack>
                            </MudStack>
                        </MudCardContent>
                    </MudCard>

                    <!-- 工种信息（只读） -->
                    <MudCard>
                        <MudCardContent>
                            <MudText Typo="Typo.subtitle1" Class="mb-2">工种信息</MudText>
                            <MudStack Row Spacing="4">
                                <MudStack Spacing="1">
                                    <MudText Typo="Typo.body2" Color="Color.Secondary">工种名称</MudText>
                                    <MudText Typo="Typo.body1">@JobType?.Name</MudText>
                                </MudStack>
                                <MudStack Spacing="1">
                                    <MudText Typo="Typo.body2" Color="Color.Secondary">工种编码</MudText>
                                    <MudText Typo="Typo.body1">@JobType?.Code</MudText>
                                </MudStack>
                            </MudStack>
                        </MudCardContent>
                    </MudCard>

                    <!-- 可编辑字段 -->
                    <MudCard>
                        <MudCardContent>
                            <MudText Typo="Typo.subtitle1" Class="mb-3">工种详情</MudText>
                            <MudStack Spacing="3">
                                <!-- 是否为主要工种 -->
                                <MudSwitch @bind-Value="editModel.IsPrimary" 
                                         Label="设为主要工种" 
                                         Color="Color.Primary" />

                                <!-- 熟练程度 -->
                                <MudStack Spacing="1">
                                    <MudText Typo="Typo.body2">熟练程度</MudText>
                                    <MudRating @bind-SelectedValue="editModel.SkillLevel" 
                                             MaxValue="5" 
                                             Size="Size.Large" />
                                    <MudText Typo="Typo.caption" Color="Color.Secondary">
                                        @GetSkillLevelText(editModel.SkillLevel)
                                    </MudText>
                                </MudStack>

                                <!-- 获得时间 -->
                                <MudDatePicker @bind-Date="editModel.AcquiredAt" 
                                             Label="获得时间" 
                                             Required="true"
                                             RequiredError="请选择获得时间" />

                                <!-- 备注 -->
                                <MudTextField @bind-Value="editModel.Remark" 
                                            Label="备注" 
                                            Lines="3" 
                                            Placeholder="请输入备注信息..." />

                                <!-- 状态 -->
                                <MudSwitch @bind-Value="editModel.IsEnabled" 
                                         Label="启用状态" 
                                         Color="Color.Success" />
                            </MudStack>
                        </MudCardContent>
                    </MudCard>
                </MudStack>
            </MudForm>
        </MudContainer>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="Cancel">取消</MudButton>
        <MudButton Color="Color.Primary" 
                 Variant="Variant.Filled" 
                 OnClick="Save"
                 Disabled="@(!isValid || saving)">
            @if (saving)
            {
                <MudProgressCircular Class="ms-n1" Size="Size.Small" Indeterminate="true" />
                <MudText Class="ms-2">保存中...</MudText>
            }
            else
            {
                <MudText>保存</MudText>
            }
        </MudButton>
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter] MudDialogInstance MudDialog { get; set; } = null!;
    [Parameter] public UserJobType? UserJobType { get; set; }
    [Parameter] public User? User { get; set; }
    [Parameter] public JobType? JobType { get; set; }

    private MudForm form = null!;
    private bool isValid = false;
    private bool saving = false;
    private UserJobTypeEditModel editModel = new();

    public class UserJobTypeEditModel
    {
        public bool IsPrimary { get; set; }
        public int SkillLevel { get; set; } = 1;
        public DateTime? AcquiredAt { get; set; }
        public string? Remark { get; set; }
        public bool IsEnabled { get; set; } = true;
    }

    protected override void OnInitialized()
    {
        if (UserJobType != null)
        {
            editModel = new UserJobTypeEditModel
            {
                IsPrimary = UserJobType.IsPrimary,
                SkillLevel = UserJobType.SkillLevel,
                AcquiredAt = UserJobType.AcquiredAt,
                Remark = UserJobType.Remark,
                IsEnabled = UserJobType.IsEnabled
            };
        }
    }

    private string GetSkillLevelText(int level)
    {
        return level switch
        {
            1 => "初学者",
            2 => "入门",
            3 => "熟练",
            4 => "精通",
            5 => "专家",
            _ => "未知"
        };
    }

    private async Task Save()
    {
        if (UserJobType == null) return;

        saving = true;
        try
        {
            // 更新模型
            UserJobType.IsPrimary = editModel.IsPrimary;
            UserJobType.SkillLevel = editModel.SkillLevel;
            UserJobType.AcquiredAt = editModel.AcquiredAt ?? DateTime.Now;
            UserJobType.Remark = editModel.Remark;
            UserJobType.IsEnabled = editModel.IsEnabled;

            var result = await JobTypeService.UpdateUserJobTypeAsync(UserJobType);
            if (result)
            {
                Snackbar.Add("用户工种信息更新成功", Severity.Success);
                MudDialog.Close(DialogResult.Ok(true));
            }
            else
            {
                Snackbar.Add("更新失败", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"更新失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            saving = false;
        }
    }

    void Cancel() => MudDialog.Cancel();
}
