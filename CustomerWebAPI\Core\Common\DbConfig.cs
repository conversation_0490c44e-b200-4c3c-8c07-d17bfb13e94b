namespace CustomerWebAPI.Common
{
    public class DbConfig
    {
        public string ProdDatabase { get; set; }
        public string PublicDatabase { get; set; }
        public int CommandTimeout { get; set; } = 30;
        public bool EnableLogging { get; set; } = true;
        public int MaxRetries { get; set; } = 3;
        public int RetryDelayMs { get; set; } = 1000;
        public int PoolMinSize { get; set; } = 10;
        public int PoolMaxSize { get; set; } = 100;
        public int PoolStepSize { get; set; } = 5;
        public int ConnectionTimeout { get; set; } = 10;
    }
} 