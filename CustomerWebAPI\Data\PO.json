﻿{
	"Order":{
		"header":{
			"version":******************123,
			"documentType":"",
			"messageId":""
		},
		"count":1,
		"orderDetail":[
			{
				"subMessageId":"",
				"messageFunctionCode":"",
				"eventCode":"",
				"eventDate":"",
				"redirectUrl":"",
				"validationErrorText":"",
				"orderIdentification":{
					"tradecardContractId":*********,
					"contractNumber":"",
					"poNumber":"",
					"buyerMemberId":*********,
					"buyerIdentification":[
						{
							"type":"",
							"value":""
						}
					],
					"sellerMemberId":*********,
					"sellerIdentification":[
						{
							"type":"",
							"value":""
						}
					],
					"contractIdentification":{
						"tradecardContractId":*********,
						"contractNumber":"",
						"poNumber":"",
						"buyerMemberId":*********,
						"buyerIdentification":[
							{
								"type":"",
								"value":""
							}
						],
						"sellerMemberId":*********,
						"sellerIdentification":[
							{
								"type":"",
								"value":""
							}
						]
					}
				},
				"tradecardProductIdentification":{
					"transactionTypeCode":"",
					"productOwner":""
				},
				"poNumber":"",
				"contractNumber":"",
				"tradecardContractId":*********,
				"effectiveDate":"",
				"orderFunctionCode":"",
				"debitBankAccountNumber":"",
				"complianceTemplateCode":"",
				"orderTerms":{
					"letterOfCreditNumber":"",
					"issueDate":"",
					"offerExpiryDate":"",
					"cancelAfterDate":"",
					"revisionNumber":"",
					"reference":[
						{
							"type":"",
							"value":""
						}
					],
					"paymentInitiationTypeCode":"",
					"settlementMethodCode":"",
					"currencyCode":"",
					"paymentTerms":{
						"paymentTenorDaysCode":"",
						"paymentTenorStartDateCode":"",
						"paymentTenorNotes":"",
						"paymentTenorBaseDate":""
					},
					"incotermCode":"",
					"incotermLocationCode":"",
					"isPartialShipmentAllowed":"",
					"shipmentDestination":{
						"destinationKey":"",
						"longName":"",
						"address":{
							"addressLine1":"",
							"addressLine2":"",
							"city":"",
							"stateOrProvince":"",
							"postalCodeNumber":"",
							"countryCode":""
						},
						"phone":"",
						"fax":"",
						"contact":{
							"name":"",
							"title":"",
							"emailAddress":"",
							"phone":"",
							"fax":"",
							"department":"",
							"region":""
						}
					},
					"earliestDate":"",
					"latestDate":"",
					"isInspectionRequired":"",
					"shipmentMethodCode":"",
					"adjustmentAllowed":[
						{
							"adjustmentTypeCode":"",
							"adjustmentValue":"",
							"isFlatAmount":"",
							"reasonType":"",
							"reasonDescription":"",
							"taxRate":"",
							"adjuestmentkey":"",
							"reference":{
								"type":"",
								"value":""
							},
							"comment":""
						}
					],
					"orderVariance":{
						"upperVariance":"",
						"lowerVariance":"",
						"varianceTypeCode":""
					},
					"isTaxRateCheckedForCompliance":"",
					"beneficiaryStatement":"",
					"beneficiaryStatementAcknowledgementCode":"",
					"additionalCondition":[
						{
							"additionalConditionText":"",
							"additionalConditionAcknowledgementCode":""
						}
					],
					"isPodRequired":"",
					"podCompletedByCode":"",
					"isPackingListRequired":"",
					"packingListItemAllocationCode":"",
					"additionalDocumentRequired":[
						{
							"documentName":"",
							"responsiblePartyCode":"",
							"referenceNumber":"",
							"notes":""
						}
					],
					"isTransShipmentAllowed":"",
					"freightPaymentCode":"",
					"freightPaymentExplanation":"",
					"packageMarkDetail":{
						"markNumber":"",
						"mark":"",
						"instruction":""
					},
					"consigneeDocumentInstructions":""
				},
				"orderParties":{
					"buyer":{
						"memberId":*********,
						"identification":[
							{
								"type":"",
								"value":""
							}
						],
						"name":"",
						"contact":{
							"name":"",
							"department":""
						},
						"address":{
							"addressLine1":"",
							"addressLine2":"",
							"city":"",
							"stateOrProvince":"",
							"postalCodeNumber":"",
							"countryCode":""
						},
						"reference":[
							{
								"type":"",
								"value":""
							}
						]
					},
					"seller":{
						"memberId":*********,
						"identification":[
							{
								"type":"111",
								"value":"11101"
							},
							{
								"type":"222",
								"value":"22202"
							}
						],
						"name":"",
						"contact":{
							"name":"",
							"department":""
						},
						"address":{
							"addressLine1":"",
							"addressLine2":"",
							"city":"",
							"stateOrProvince":"",
							"postalCodeNumber":"",
							"countryCode":""
						},
						"reference":[
							{
								"type":"",
								"value":""
							}
						]
					},
					"inspectionCompany":{
						"memberId":*********,
						"identification":[
							{
								"type":"",
								"value":""
							}
						],
						"name":"",
						"contact":{
							"name":"",
							"department":""
						},
						"address":{
							"addressLine1":"",
							"addressLine2":"",
							"city":"",
							"stateOrProvince":"",
							"postalCodeNumber":"",
							"countryCode":""
						},
						"reference":[
							{
								"type":"",
								"value":""
							}
						]
					},
					"logisticsProvider":{
						"memberId":*********,
						"identification":[
							{
								"type":"",
								"value":""
							}
						],
						"name":"",
						"contact":{
							"name":"",
							"department":""
						},
						"address":{
							"addressLine1":"",
							"addressLine2":"",
							"city":"",
							"stateOrProvince":"",
							"postalCodeNumber":"",
							"countryCode":""
						},
						"reference":[
							{
								"type":"",
								"value":""
							}
						]
					},
					"coverageProvider":{
						"memberId":*********,
						"identification":[
							{
								"type":"",
								"value":""
							}
						],
						"name":"",
						"contact":{
							"name":"",
							"department":""
						},
						"address":{
							"addressLine1":"",
							"addressLine2":"",
							"city":"",
							"stateOrProvince":"",
							"postalCodeNumber":"",
							"countryCode":""
						},
						"reference":[
							{
								"type":"",
								"value":""
							}
						]
					},
					"buyersAgent":{
						"memberId":*********,
						"identification":[
							{
								"type":"",
								"value":""
							}
						],
						"name":"",
						"contact":{
							"name":"",
							"department":""
						},
						"address":{
							"addressLine1":"",
							"addressLine2":"",
							"city":"",
							"stateOrProvince":"",
							"postalCodeNumber":"",
							"countryCode":""
						},
						"reference":[
							{
								"type":"",
								"value":""
							}
						]
					},
					"sellersAgent":{
						"memberId":*********,
						"identification":[
							{
								"type":"",
								"value":""
							}
						],
						"name":"",
						"contact":{
							"name":"",
							"department":""
						},
						"address":{
							"addressLine1":"",
							"addressLine2":"",
							"city":"",
							"stateOrProvince":"",
							"postalCodeNumber":"",
							"countryCode":""
						},
						"reference":[
							{
								"type":"",
								"value":""
							}
						]
					},
					"carrier":{
						"memberId":*********,
						"identification":[
							{
								"type":"",
								"value":""
							}
						],
						"name":"",
						"contact":{
							"name":"",
							"department":""
						},
						"address":{
							"addressLine1":"",
							"addressLine2":"",
							"city":"",
							"stateOrProvince":"",
							"postalCodeNumber":"",
							"countryCode":""
						},
						"reference":[
							{
								"type":"",
								"value":""
							}
						]
					},
					"customsBroke":{
						"memberId":*********,
						"identification":[
							{
								"type":"",
								"value":""
							}
						],
						"name":"",
						"contact":{
							"name":"",
							"department":""
						},
						"address":{
							"addressLine1":"",
							"addressLine2":"",
							"city":"",
							"stateOrProvince":"",
							"postalCodeNumber":"",
							"countryCode":""
						},
						"reference":[
							{
								"type":"",
								"value":""
							}
						]
					},
					"consignee":{
						"memberId":*********,
						"identification":[
							{
								"type":"",
								"value":""
							}
						],
						"name":"",
						"contact":{
							"name":"",
							"department":""
						},
						"address":{
							"addressLine1":"",
							"addressLine2":"",
							"city":"",
							"stateOrProvince":"",
							"postalCodeNumber":"",
							"countryCode":""
						},
						"reference":[
							{
								"type":"",
								"value":""
							}
						]
					},
					"receivedFrom":{
						"memberId":*********,
						"identification":[
							{
								"type":"",
								"value":""
							}
						],
						"name":"",
						"contact":{
							"name":"",
							"department":""
						},
						"address":{
							"addressLine1":"",
							"addressLine2":"",
							"city":"",
							"stateOrProvince":"",
							"postalCodeNumber":"",
							"countryCode":""
						},
						"reference":[
							{
								"type":"",
								"value":""
							}
						]
					},
					"notifyParty":{
						"memberId":*********,
						"identification":[
							{
								"type":"",
								"value":""
							}
						],
						"name":"",
						"contact":{
							"name":"",
							"department":""
						},
						"address":{
							"addressLine1":"",
							"addressLine2":"",
							"city":"",
							"stateOrProvince":"",
							"postalCodeNumber":"",
							"countryCode":""
						},
						"reference":[
							{
								"type":"",
								"value":""
							}
						]
					},
					"additionalParty":{
						"memberId":*********,
						"identification":[
							{
								"type":"",
								"value":""
							}
						],
						"name":"",
						"contact":{
							"name":"",
							"department":""
						},
						"address":{
							"addressLine1":"",
							"addressLine2":"",
							"city":"",
							"stateOrProvince":"",
							"postalCodeNumber":"",
							"countryCode":""
						},
						"reference":[
							{
								"type":"",
								"value":""
							}
						]
					},
					"orderLineItem":[
						{
							"itemKey":"",
							"itemTypeCode":"",
							"parentItemKey":"",
							"baseLineItem":{
								"itemSequenceNumber":"",
								"buyerNumber":"",
								"sellerNumber":"",
								"shortDescription":"",
								"longDescription":"",
								"upcNumber":"",
								"skuNumber":"",
								"countryOfOriginCode":"",
								"customsClassification":[
									{
										"classificationNumber":"",
										"countryCode":""
									}
								],
								"quotaCategory":"",
								"itemReference":[
									{
										"type":"",
										"value":"",
										"displayCode":""
									}
								],
								"quantity":0.12,
								"unitOfMeasureCode":"",
								"packMethodCode":"",
								"quantityPerInnerPackage":0,
								"quantityPerOuterPackage":0,
								"destinationKey":"",
								"earliestDate":"",
								"latestDate":"",
								"isInspectionRequired":"",
								"shipmentMethodCode":"",
								"itemVariance":{
									"upperVariance":0,
									"lowerVariance":0,
									"varianceTypeCode":""
								}
							},
							"lineItemPrice":{
								"pricePerUnit":0.12,
								"totalPrice":0.12
							},
							"lineItemTax":{
								"taxRate":0.12,
								"taxAmount":0.12
							}
						}
					],
					"attachment":[
						{
							"name":"",
							"encodingCode":"",
							"mimeType":"",
							"content":""
						}
					],
					"totals":{
						"totalQuantity":0.123,
						"totalMerchandiseAmount":0.123,
						"totalTaxAmount":0.123,
						"totalDocumentAmount":0.123,
						"totalContractAmount":0.123
					}
				}
			}
		]
	}
}