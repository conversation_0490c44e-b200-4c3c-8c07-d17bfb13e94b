using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using MauiScanManager.Models;
using MauiScanManager.Services;
using MauiScanManager.Constants;
using System.Collections.Generic;

namespace MauiScanManager.ViewModels;

public partial class ChemicalBoxCheckViewModel : BaseOperationViewModel
{
    private readonly IAudioService _audioService;
    private readonly IChemicalService _chemicalService;

    // 定义扫描状态
    private enum ScanState
    {
        WaitingForBatchNo,    // 等待扫描缸号
        WaitingForBoxNo,      // 等待扫描箱号
        Completed             // 扫描完成
    }

    [ObservableProperty]
    private string batchNo = string.Empty;

    [ObservableProperty]
    private string boxNo = string.Empty;

    [ObservableProperty]
    private string scanPrompt = string.Empty;

    [ObservableProperty]
    private string resultMessage = string.Empty;

    [ObservableProperty]
    private string backgroundColor = ColorConstants.White;  // 默认白色

    [ObservableProperty]
    private int remainingBoxes = 0;

    [ObservableProperty]
    private int scannedBoxes = 0;

    [ObservableProperty]
    private bool isDebugMode = false;  // 默认开启调试模式

    [ObservableProperty]
    private int totalBoxes = 0;

    private ScanState _currentState = ScanState.WaitingForBatchNo;
    private string _currentBatchNo = string.Empty;
    private HashSet<string> _verifiedBoxNos = new HashSet<string>();  // 已校验通过的箱号列表

    // 测试数据
    private readonly string[] _testBatchNos = { "C24124579" };
    private readonly string[] _testBoxNos = { "M2408010012", "M2408030003" };
    private int _currentTestBatchIndex = 0;
    private int _currentTestBoxIndex = 0;

    public ChemicalBoxCheckViewModel(
        IScanService scanService,
        IDialogService dialogService,
        IAudioService audioService,
        IChemicalService chemicalService) 
        : base(scanService, dialogService)
    {
        _audioService = audioService;
        _chemicalService = chemicalService;
    }

    [RelayCommand]
    private async Task SimulateBatchScan()
    {
        string batchNo = _testBatchNos[_currentTestBatchIndex];
        _currentTestBatchIndex = (_currentTestBatchIndex + 1) % _testBatchNos.Length;
        await ProcessScanStateAsync(batchNo);
    }

    [RelayCommand]
    private async Task SimulateBoxScan()
    {
        if (_currentState != ScanState.WaitingForBoxNo)
        {
            await _dialogService.ShowErrorAsync("请先扫描缸号");
            return;
        }

        string boxNo = _testBoxNos[_currentTestBoxIndex];
        _currentTestBoxIndex = (_currentTestBoxIndex + 1) % _testBoxNos.Length;
        await ProcessScanStateAsync(boxNo);
    }

    [RelayCommand]
    private void Reset()
    {
        ResetScanState(true);
    }

    public override async void Initialize(Operation operation)
    {
        base.Initialize(operation);
        ResetScanState();
        await _audioService.PlayAudioByName("CheckChemicalBoxPrompt");
    }

    protected override void ProcessScanResult(string code, string type, byte[] codeSource)
    {
        if (string.IsNullOrEmpty(code)) return;

        MainThread.BeginInvokeOnMainThread(async () =>
        {
            try
            {
                await ProcessScanStateAsync(code);
            }
            catch (Exception ex)
            {
                await _dialogService.ShowErrorAsync($"处理扫描结果失败：{ex.Message}");
            }
        });
    }

    private async Task ProcessScanStateAsync(string code)
    {
        switch (_currentState)
        {
            case ScanState.WaitingForBatchNo:
                BatchNo = code;
                _currentState = ScanState.WaitingForBoxNo;
                // 如果扫描了新的缸号，重置所有状态
                if (_currentBatchNo != code)
                {
                    _currentBatchNo = code;
                    ScannedBoxes = 0;
                    TotalBoxes = 0;
                    ResultMessage = string.Empty;
                    BackgroundColor = ColorConstants.White;
                    BoxNo = string.Empty; // 只在新缸号时清空箱号
                    _verifiedBoxNos.Clear(); // 清空已校验通过的箱号列表
                }
                UpdateScanPrompt();
                await _dialogService.ShowInfoAsync("请扫描染料箱号");
                break;

            case ScanState.WaitingForBoxNo:
                BoxNo = code;
                // 检查是否已经校验通过
                if (_verifiedBoxNos.Contains(code))
                {
                    await _audioService.PlayAudioByName("BoxNoRepeatScan");
                    ResultMessage = "此箱已校验通过";
                    BackgroundColor = ColorConstants.Success;
                    return;
                }
                await CheckChemicalBox();
                break;

            case ScanState.Completed:
                // 开始新一轮扫描，重置状态
                ResetScanState();
                await ProcessScanStateAsync(code); // 处理新扫描的代码
                break;
        }
    }

    private async Task CheckChemicalBox()
    {
        try
        {
            var result = await _chemicalService.CheckChemicalBoxConsistencyAsync(new ChemicalBatchCheck
            {
                BatchNo = BatchNo,
                ChemicalBoxNo = BoxNo
            });

            if (result.IsSuccess)
            {
                var checkResult = result.Data;
                BackgroundColor = checkResult.IsConsitent == "相同" ? ColorConstants.Success : ColorConstants.Error;
                ResultMessage = checkResult.IsConsitent == "相同" ? "染料箱号匹配" : "染料箱号不匹配";
                
                if (checkResult.IsConsitent == "相同")
                {
                    // 更新总箱数（如果有变化）
                    if (TotalBoxes != checkResult.BoxNum)
                    {
                        TotalBoxes = checkResult.BoxNum;
                    }
                    
                    // 添加到已校验通过的箱号列表
                    _verifiedBoxNos.Add(BoxNo);
                    ScannedBoxes++;
                    
                    // 计算剩余箱数
                    RemainingBoxes = TotalBoxes - ScannedBoxes;

                    if (RemainingBoxes == 0)
                    {
                        await _audioService.PlaySuccessSound();
                        await _dialogService.ShowSuccessAsync("全部校验通过！");
                    }
                    else
                    {
                        // 根据剩余箱数播放对应的语音
                        if (RemainingBoxes >= 1 && RemainingBoxes <= 4)
                        {
                            // 播放剩余箱数语音
                            string audioName = $"Remain{RemainingBoxes}Box";
                            await _audioService.PlayAudioByName(audioName);
                        }
                        else
                        {
                            await _audioService.PlayCheckPrompt();
                        }
                        await _dialogService.ShowInfoAsync($"校验通过，还需扫描 {RemainingBoxes} 箱");
                    }
                }
                else
                {
                    await _audioService.PlayFailureSound();
                }
            }
            else
            {
                BackgroundColor = ColorConstants.Error;
                ResultMessage = result.ErrorMessage;
                await _audioService.PlayFailureSound();
            }
        }
        catch (Exception ex)
        {
            await _dialogService.ShowErrorAsync($"校验失败：{ex.Message}");
            await _audioService.PlayFailureSound();
        }
    }

    private void ResetScanState(bool resetTestIndex = false)
    {
        BatchNo = string.Empty;
        BoxNo = string.Empty;
        ResultMessage = string.Empty;
        BackgroundColor = ColorConstants.White;
        RemainingBoxes = 0;
        ScannedBoxes = 0;
        TotalBoxes = 0;
        _currentState = ScanState.WaitingForBatchNo;
        _currentBatchNo = string.Empty;
        _verifiedBoxNos.Clear(); // 清空已校验通过的箱号列表

        if (resetTestIndex)
        {
            _currentTestBatchIndex = 0;
            _currentTestBoxIndex = 0;
        }

        UpdateScanPrompt();
    }

    private void UpdateScanPrompt()
    {
        ScanPrompt = _currentState switch
        {
            ScanState.WaitingForBatchNo => "请扫描缸号",
            ScanState.WaitingForBoxNo => "请扫描染料箱号",
            ScanState.Completed => string.Empty,
            _ => string.Empty
        };
    }
} 