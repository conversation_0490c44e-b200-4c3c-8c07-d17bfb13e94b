using System.Net.Http;
using System.Net.Http.Json;
using Microsoft.Extensions.Logging;
using MauiScanManager.Models;
using System.Text.Json;
using MauiScanManager.Services;

namespace MauiScanManager.Services
{
    public class ApiService : IApiService
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<ApiService> _logger;
        private readonly INetworkService _networkService;
        private readonly IPlatformLoadingService _loadingService;
        private const int MaxRetries = 2;

        public ApiService(
            IHttpClientFactory httpClientFactory,
            ILogger<ApiService> logger,
            INetworkService networkService,
            IPlatformLoadingService loadingService)
        {
            _httpClient = httpClientFactory.CreateClient("API");
            _logger = logger;
            _networkService = networkService;
            _loadingService = loadingService;
        }

        public async Task<ApiResponse<T>> GetAsync<T>(string endpoint)
        {
            for (int i = 0; i <= MaxRetries; i++)
            {
                try
                {
                    if (!await _networkService.CheckNetworkAsync())
                    {
                        return new ApiResponse<T>
                        {
                            Success = false,
                            Message = "网络连接失败，请检查网络设置"
                        };
                    }

                    var response = await _httpClient.GetFromJsonAsync<ApiResponse<T>>(endpoint);
                    return response ?? new ApiResponse<T> 
                    { 
                        Success = false, 
                        Message = "无效的响应" 
                    };
                }
                catch (HttpRequestException ex)
                {
                    _logger.LogWarning(ex, "网络请求失败，尝试次数: {Attempt}", i + 1);
                    if (i == MaxRetries)
                    {
                        _networkService.ResetNetworkState();
                        return new ApiResponse<T>
                        {
                            Success = false,
                            Message = GetErrorMessage(ex)
                        };
                    }
                    await Task.Delay(1000 * (i + 1));
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "GET request to {Endpoint} failed", endpoint);
                    return new ApiResponse<T>
                    {
                        Success = false,
                        Message = GetErrorMessage(ex)
                    };
                }
            }
            return new ApiResponse<T> { Success = false, Message = "请求失败" };
        }

        public async Task<ApiResponse<T>> PostAsync<T>(string endpoint, object data)
        {
            for (int i = 0; i <= MaxRetries; i++)
            {
                try
                {
                    if (!await _networkService.CheckNetworkAsync())
                    {
                        return new ApiResponse<T>
                        {
                            Success = false,
                            Message = "网络连接失败，请检查网络设置"
                        };
                    }

                    var fullUrl = $"{_httpClient.BaseAddress}{endpoint}";
                    _logger.LogDebug("POST Request URL: {Url}", fullUrl);
                    _logger.LogDebug("POST Request Data: {Data}", 
                        JsonSerializer.Serialize(data));

                    var response = await _httpClient.PostAsJsonAsync(endpoint, data);
                    var content = await response.Content.ReadAsStringAsync();
                    _logger.LogDebug("POST Response Content: {Content}", content);

                    var options = new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    };

                    var apiResponse = await response.Content.ReadFromJsonAsync<ApiResponse<T>>(options)
                        ?? new ApiResponse<T> { Success = false, Message = "无效的响应" };

                    if (apiResponse.Success)
                    {
                        _logger.LogDebug("请求成功: {Message}", apiResponse.Message);
                    }
                    else
                    {
                        _logger.LogWarning("请求失败: {Message}", apiResponse.Message);
                    }

                    return apiResponse;
                }
                catch (HttpRequestException ex)
                {
                    _logger.LogWarning(ex, "网络请求失败，尝试次数: {Attempt}", i + 1);
                    if (i == MaxRetries)
                    {
                        _networkService.ResetNetworkState();
                        return new ApiResponse<T>
                        {
                            Success = false,
                            Message = GetErrorMessage(ex)
                        };
                    }
                    await Task.Delay(1000 * (i + 1));
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "请求异常");
                    return new ApiResponse<T>
                    {
                        Success = false,
                        Message = GetErrorMessage(ex)
                    };
                }
            }
            return new ApiResponse<T> { Success = false, Message = "请求失败" };
        }

        private string GetErrorMessage(Exception ex) => ex switch
        {
            HttpRequestException httpEx => "网络连接失败，请检查网络设置",
            TimeoutException timeoutEx => "请求超时，请稍后重试",
            OperationCanceledException cancelEx => "操作已取消",
            _ => "系统错误，请稍后重试"
        };
    }
} 