{"id": "#", "definitions": {"AdvancedShipNotes": {"type": "array", "items": {"allOf": [{"$ref": "#/definitions/AdvancedShipNote"}]}}, "AdvancedShipNote": {"type": "object", "title": "AdvancedShipNotes", "required": ["Header", "Detail"], "properties": {"Header": {"title": "Header", "allOf": [{"$ref": "#/definitions/ASNHeader"}]}, "Detail": {"title": "Detail", "allOf": [{"$ref": "#/definitions/ASNDetail"}]}}}, "ASNHeader": {"type": "object", "title": "Header", "required": ["DocumentIndex", "PurposeCode", "AdvancedShipNoteNumber", "SentDate", "SentTime", "HierarchicalStructureCode", "TransactionTypeCode"], "properties": {"DocumentIndex": {"type": "integer", "minimum": 1}, "PurposeCode": {"enum": ["00", "05"], "documentation": "00-Original, 05-<PERSON><PERSON>"}, "AdvancedShipNoteNumber": {"type": "string", "maxLength": 30}, "SentDate": {"type": "string", "format": "date", "documentation": "ISO 8601 yyyy-mm-dd E8601DNw"}, "SentTime": {"type": "string", "format": "time", "documentation": "ISO 8601 hh:mm:ss"}, "HierarchicalStructureCode": {"enum": ["0002"], "documentation": "0001-Shipment, Order, Packaging, Item; 0002-Shipment, Order, Item, Packaging; 0003-Shipment, Packaging, Order, Item; we only accept 0002"}, "TransactionTypeCode": {"enum": ["AS"], "documentation": "AS for Shipment Advice"}}}, "ASNDetail": {"type": "object", "title": "Detail", "required": ["CarrierDetails", "Transportation", "Dates", "Parties", "Orders"], "properties": {"CarrierDetails": {"title": "CarrierDetails", "allOf": [{"$ref": "#/definitions/CarrierDetails"}]}, "POType": {"enum": ["GO2000", "AS400", "USA"], "documentation": "GO2000 for GO2000 system, AS400 for AS400 system, USA for GO+ system, (same as REF02 [REF*06] of 850)"}, "DivisionCode": {"type": "string", "minLength": 1, "maxLength": 30, "documentation": "most likely means the warehouse shipped to"}, "Transportation": {"title": "Transportation", "allOf": [{"$ref": "#/definitions/Transportation"}]}, "Dates": {"title": "Dates", "allOf": [{"$ref": "#/definitions/Dates"}]}, "Parties": {"title": "Parties", "allOf": [{"$ref": "#/definitions/Parties"}]}, "Orders": {"title": "Orders", "allOf": [{"$ref": "#/definitions/Orders"}]}}}, "CarrierDetails": {"type": "object", "title": "CarrierDetails", "required": ["PackagingCode", "LadingQuantity", "Weights", "TransportationTypeCode", "ContainerNumber"], "properties": {"PackagingCode": {"enum": ["ROL"]}, "LadingQuantity": {"type": "integer", "minimum": 1, "maximum": 9999999}, "Weights": {"title": "Weights", "allOf": [{"$ref": "#/definitions/Weights"}]}, "CarrierIdentificationCodeQualifier": {"type": "string", "enum": ["2"], "minLength": 1}, "CarrierIdentificationCode": {"type": "string", "minLength": 2, "maxLength": 80}, "TransportationTypeCode": {"enum": ["A", "S", "L"], "documentation": "A-air, S-sea, L-land"}, "Routing": {"type": "string", "minLength": 1, "maxLength": 35}, "ContainerNumber": {"type": "string", "minLength": 1, "maxLength": 12}}}, "Transportation": {"type": "object", "title": "Transportation", "required": ["<PERSON><PERSON><PERSON><PERSON>", "VoyageNumber", "VesselNumber", "ShipMode"], "properties": {"BillNumber": {"type": "string", "minLength": 1, "maxLength": 30, "documentation": "For Air: Airway Bill, For Sea: BOL, For Land: Plate Number"}, "VoyageNumber": {"type": "string", "minLength": 1, "maxLength": 30, "documentation": "For Air: Airline, For Sea: Voyage Number, For Land: Truck Name"}, "VesselNumber": {"type": "string", "minLength": 1, "maxLength": 30, "documentation": "For Air: Flight Number, For Sea: Vessel Name, For Land: Truck Number"}, "ShipMode": {"type": "string", "enum": ["CONSOLIDATION WAREHOUSE", "DIRECT SHIPMENT"], "minLength": 1, "maxLength": 30, "documentation": "For Air: Flight Number, For Sea: Vessel Name, For Land: Truck Number"}}}, "Dates": {"type": "object", "title": "Dates", "required": ["SailDate", "DeliveryDate", "EstimatedArrivalDate"], "properties": {"SailDate": {"type": "string", "format": "date", "documentation": "ISO 8601 yyyy-mm-dd E8601DNw"}, "DeliveryDate": {"type": "string", "format": "date", "documentation": "ISO 8601 yyyy-mm-dd E8601DNw"}, "EstimatedArrivalDate": {"type": "string", "format": "date", "documentation": "ISO 8601 yyyy-mm-dd E8601DNw"}}}, "Parties": {"type": "array", "items": {"anyOf": [{"$ref": "#/definitions/Party"}, {"$ref": "#/definitions/PartyOPA"}, {"$ref": "#/definitions/Party9C"}]}}, "PartyCore": {"type": "object", "title": "Party", "required": ["Identifier"], "properties": {"Identifier": {"enum": ["Shipper", "Country of Origin", "Country of Destination", "OPA Factory", "Consignee"]}, "Code": {"type": "string", "minLength": 2, "maxLength": 80}, "Name": {"type": "string", "minLength": 1, "maxLength": 60}, "Address": {"type": "object", "title": "Address", "required": ["AddressLine1"], "properties": {"AddressLine1": {"type": "string", "minLength": 1, "maxLength": 55}, "AddressLine2": {"type": "string", "minLength": 1, "maxLength": 55}}}}}, "Party9C": {"allOf": [{"$ref": "#/definitions/PartyCore", "required": ["Identifier", "Code"], "properties": {"Identifier": {"enum": ["Country of Destination"]}}}]}, "Party": {"allOf": [{"$ref": "#/definitions/PartyCore", "required": ["Identifier", "Code", "Name"], "properties": {"Identifier": {"enum": ["Shipper", "Country of Origin", "Consignee"]}}}]}, "PartyOPA": {"allOf": [{"$ref": "#/definitions/PartyCore", "required": ["Identifier"], "properties": {"Identifier": {"enum": ["OPA Factory"]}}}]}, "Orders": {"type": "array", "items": {"required": ["PurchaseOrderNumber", "PurchaseOrderReleaseNumber", "Parties", "Items"], "properties": {"PurchaseOrderNumber": {"type": "string", "minLength": 1, "maxLength": 22}, "PurchaseOrderReleaseNumber": {"type": "string", "minLength": 1, "maxLength": 22}, "Parties": {"title": "Parties", "allOf": [{"$ref": "#/definitions/Parties"}]}, "Items": {"title": "Items", "allOf": [{"$ref": "#/definitions/Items"}]}, "SupplierReferenceNumber": {"type": "string", "minLength": 1, "maxLength": 30}}}}, "Items": {"type": "array", "items": {"required": ["PoLineNumber", "ItemNumber", "SubLineNumber", "Weights", "Packages"], "properties": {"AssignedID": {"type": "string", "minLength": 1, "maxLength": 20}, "ContractNumber": {"type": "string", "minLength": 1, "maxLength": 48}, "DyeLotSeries": {"type": "string", "minLength": 1, "maxLength": 48}, "FPM": {"type": "string", "minLength": 1, "maxLength": 48}, "ITSvalue": {"type": "string", "minLength": 1, "maxLength": 48}, "SeasonCode": {"type": "string", "minLength": 1, "maxLength": 48}, "PackQuantity": {"type": "integer", "minimum": 0, "maximum": 999999}, "PackCode": {"type": "string", "minLength": 3, "maxLength": 5, "enum": ["CTN", "PKG", "ROL", "PAL"], "documentation": "CTN-Carton, PKG-Package, ROL-Roll, PAL-Pallet"}, "DyeMatch": {"type": "string", "minLength": 1, "maxLength": 80, "documentation": "Dye lot series + space + Dye Lot Number"}, "ColorDescription": {"type": "string", "minLength": 1, "maxLength": 80}, "PatternNumber": {"type": "string", "minLength": 1, "maxLength": 80}, "FabricDescription": {"type": "string", "minLength": 1, "maxLength": 80}, "FreeOfChargeQuantity": {"type": "number"}, "PoLineNumber": {"type": "integer", "minimum": 0, "maximum": 99999999}, "ItemNumber": {"type": "string", "minLength": 1, "maxLength": 80, "documentation": "Item Number MMITNO has max length 15 in ERP DB"}, "SubLineNumber": {"type": "integer", "minimum": 0, "maximum": 99999999}, "Weights": {"title": "Weights", "allOf": [{"$ref": "#/definitions/Weights"}]}, "Packages": {"title": "Packages", "allOf": [{"$ref": "#/definitions/Packages"}]}}}}, "Packages": {"type": "array", "items": {"required": ["Quantity", "QuantityUnit", "Weights", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "WidthUnit", "FabricLengthString", "Measurements", "UCC", "RollNumber"], "properties": {"AssignedID": {"type": "string", "minLength": 1, "maxLength": 20}, "QaStatus": {"type": "string", "minLength": 1, "maxLength": 80, "enum": ["ACCEPT", "LOG"]}, "FabricSize": {"type": "string", "minLength": 1, "maxLength": 12}, "Quantity": {"type": "number", "documentation": "real number must not exceed 10 characters length"}, "QuantityUnit": {"type": "string", "minLength": 2, "maxLength": 3}, "Weights": {"title": "Weights", "allOf": [{"$ref": "#/definitions/Weights"}]}, "FabricWidth": {"type": "number", "documentation": "real number must not exceed 8 characters length"}, "WidthUnit": {"type": "string", "minLength": 2, "maxLength": 3, "enum": ["IN", "CM"], "documentation": "IN-inch, CM-centimeter"}, "FabricLengthString": {"type": "string", "minLength": 16, "maxLength": 16, "documentation": "1~13 character fabric length, 14~16 UOM (YD, IN, M)"}, "Measurements": {"title": "Measurements", "allOf": [{"$ref": "#/definitions/Measurements"}]}, "UCC": {"type": "string", "minLength": 1, "maxLength": 48}, "RollNumber": {"type": "string", "minLength": 1, "maxLength": 48}}}}, "Measurements": {"type": "array", "items": {"required": ["Name", "Value", "MeasurementUnit"], "properties": {"Name": {"type": "string"}, "Value": {"type": "number", "documentation": "real number must not exceed 20 characters length"}, "MeasurementUnit": {"type": "string", "minLength": 2, "maxLength": 3, "enum": ["IN", "CM"], "documentation": "IN-inch, CM-centimeter"}}}}, "Weights": {"type": "array", "items": {"required": ["Qualifier", "Value", "Unit"], "properties": {"Qualifier": {"enum": ["E", "G"], "documentation": "E-Net, G-Gross"}, "Value": {"type": "number", "documentation": "decimal or integer, less than 9 characters including dp"}, "Unit": {"type": "string", "enum": ["LB", "KG"], "minLength": 2, "maxLength": 3, "documentation": "LB-pound, KG-kilogram"}}}}}, "type": "object", "required": ["AdvancedShipNotes", "SenderIdentifierId", "SenderId", "ReceiverIdentifierId", "ReceiverId", "TrackNumber"], "properties": {"AdvancedShipNotes": {"$ref": "#/definitions/AdvancedShipNotes"}, "SenderIdentifierId": {"type": "string", "minLength": 2, "maxLength": 2}, "SenderId": {"type": "string", "minLength": 1, "maxLength": 15}, "ReceiverIdentifierId": {"type": "string", "minLength": 2, "maxLength": 2}, "ReceiverId": {"type": "string", "minLength": 1, "maxLength": 15}, "ProductionIndicator": {"type": "string", "enum": ["Production", "Test"]}}}