using MauiScanManager.Models;

namespace MauiScanManager.Services
{
    public class OperationNavigationService : IOperationNavigationService
    {
        private readonly Dictionary<string, string> _operationRoutes = new();

        public void RegisterOperation(string operationCode, string route)
        {
            _operationRoutes[operationCode] = route;
        }

        public async Task NavigateToOperation(Operation operation)
        {
            if (!_operationRoutes.TryGetValue(operation.Code, out var route))
            {
                throw new InvalidOperationException($"No route found for operation code: {operation.Code}");
            }

            var parameters = new Dictionary<string, object>
            {
                { "Operation", operation }
            };

            await Shell.Current.GoToAsync(route, parameters);
        }
    }
}
