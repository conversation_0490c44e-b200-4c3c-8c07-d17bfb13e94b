{"id": "#", "definitions": {"Order": {"type": "object", "title": "Order", "required": ["header"], "properties": {"header": {"title": "header", "allOf": [{"$ref": "#/definitions/Header"}], "propertyType": "element", "elementName": {"localPart": "header", "namespaceURI": ""}}, "count": {"title": "count", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/integer"}], "propertyType": "element", "elementName": {"localPart": "count", "namespaceURI": ""}}, "orderDetail": {"title": "orderDetail", "allOf": [{"type": "array", "items": {"$ref": "#/definitions/OrderDetail"}, "minItems": 0}], "propertyType": "element", "elementName": {"localPart": "orderDetail", "namespaceURI": ""}}}, "typeType": "classInfo", "typeName": {"localPart": "Order", "namespaceURI": ""}, "propertiesOrder": ["header", "count", "orderDetail"]}, "DocumentTotals": {"type": "object", "title": "DocumentTotals", "properties": {"totalQuantity": {"title": "totalQuantity", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/decimal"}], "propertyType": "element", "elementName": {"localPart": "totalQuantity", "namespaceURI": ""}}, "totalMerchandiseAmount": {"title": "totalMerchandiseAmount", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/decimal"}], "propertyType": "element", "elementName": {"localPart": "totalMerchandiseAmount", "namespaceURI": ""}}, "totalTaxAmount": {"title": "totalTaxAmount", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/decimal"}], "propertyType": "element", "elementName": {"localPart": "totalTaxAmount", "namespaceURI": ""}}, "totalDocumentAmount": {"title": "totalDocumentAmount", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/decimal"}], "propertyType": "element", "elementName": {"localPart": "totalDocumentAmount", "namespaceURI": ""}}, "totalContractAmount": {"title": "totalContractAmount", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/decimal"}], "propertyType": "element", "elementName": {"localPart": "totalContractAmount", "namespaceURI": ""}}}, "typeType": "classInfo", "typeName": {"localPart": "DocumentTotals", "namespaceURI": ""}, "propertiesOrder": ["totalQuantity", "totalMerchandiseAmount", "totalTaxAmount", "totalDocumentAmount", "totalContractAmount"]}, "ContractIdentification": {"type": "object", "title": "ContractIdentification", "properties": {"tradecardContractId": {"title": "tradecardContractId", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/long"}], "propertyType": "element", "elementName": {"localPart": "tradecardContractId", "namespaceURI": ""}}, "contractNumber": {"title": "contractNumber", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "contractNumber", "namespaceURI": ""}}, "poNumber": {"title": "poNumber", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "poNumber", "namespaceURI": ""}}, "buyerMemberId": {"title": "buyerMemberId", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "buyerMemberId", "namespaceURI": ""}}, "buyerIdentification": {"title": "buyerIdentification", "allOf": [{"type": "array", "items": {"$ref": "#/definitions/Identification"}, "minItems": 0}], "propertyType": "element", "elementName": {"localPart": "buyerIdentification", "namespaceURI": ""}}, "sellerMemberId": {"title": "sellerMemberId", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "sellerMemberId", "namespaceURI": ""}}, "sellerIdentification": {"title": "sellerIdentification", "allOf": [{"type": "array", "items": {"$ref": "#/definitions/Identification"}, "minItems": 0}], "propertyType": "element", "elementName": {"localPart": "sellerIdentification", "namespaceURI": ""}}}, "typeType": "classInfo", "typeName": {"localPart": "ContractIdentification", "namespaceURI": ""}, "propertiesOrder": ["tradecardContractId", "contractNumber", "poNumber", "buyerMemberId", "buyerIdentification", "sellerMemberId", "sellerIdentification"]}, "Member": {"type": "object", "title": "Member", "properties": {"memberId": {"title": "memberId", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "memberId", "namespaceURI": ""}}, "identification": {"title": "identification", "allOf": [{"type": "array", "items": {"$ref": "#/definitions/Identification"}, "minItems": 0}], "propertyType": "element", "elementName": {"localPart": "identification", "namespaceURI": ""}}, "name": {"title": "name", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "name", "namespaceURI": ""}}, "contact": {"title": "contact", "allOf": [{"$ref": "#/definitions/OrganizationContact"}], "propertyType": "element", "elementName": {"localPart": "contact", "namespaceURI": ""}}, "address": {"title": "address", "allOf": [{"$ref": "#/definitions/Address"}], "propertyType": "element", "elementName": {"localPart": "address", "namespaceURI": ""}}, "reference": {"title": "reference", "allOf": [{"type": "array", "items": {"$ref": "#/definitions/Reference"}, "minItems": 0}], "propertyType": "element", "elementName": {"localPart": "reference", "namespaceURI": ""}}}, "typeType": "classInfo", "typeName": {"localPart": "Member", "namespaceURI": ""}, "propertiesOrder": ["memberId", "identification", "name", "contact", "address", "reference"]}, "Address": {"type": "object", "title": "Address", "properties": {"addressLine1": {"title": "addressLine1", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "addressLine1", "namespaceURI": ""}}, "addressLine2": {"title": "addressLine2", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "addressLine2", "namespaceURI": ""}}, "city": {"title": "city", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "city", "namespaceURI": ""}}, "stateOrProvince": {"title": "stateOrProvince", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "stateOrProvince", "namespaceURI": ""}}, "postalCodeNumber": {"title": "postalCodeNumber", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "postalCodeNumber", "namespaceURI": ""}}, "countryCode": {"title": "countryCode", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "countryCode", "namespaceURI": ""}}}, "typeType": "classInfo", "typeName": {"localPart": "Address", "namespaceURI": ""}, "propertiesOrder": ["addressLine1", "addressLine2", "city", "stateOrProvince", "postalCodeNumber", "countryCode"]}, "OrderTerms": {"type": "object", "title": "OrderTerms", "properties": {"letterOfCreditNumber": {"title": "letterOfCreditNumber", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "letterOfCreditNumber", "namespaceURI": ""}}, "issueDate": {"title": "issueDate", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "issueDate", "namespaceURI": ""}}, "offerExpiryDate": {"title": "offerExpiryDate", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "offerExpiryDate", "namespaceURI": ""}}, "cancelAfterDate": {"title": "cancelAfterDate", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "cancelAfterDate", "namespaceURI": ""}}, "revisionNumber": {"title": "revisionNumber", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "revisionNumber", "namespaceURI": ""}}, "reference": {"title": "reference", "allOf": [{"type": "array", "items": {"$ref": "#/definitions/Reference"}, "minItems": 0}], "propertyType": "element", "elementName": {"localPart": "reference", "namespaceURI": ""}}, "paymentInitiationTypeCode": {"title": "paymentInitiationTypeCode", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "paymentInitiationTypeCode", "namespaceURI": ""}}, "settlementMethodCode": {"title": "settlementMethodCode", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "settlementMethodCode", "namespaceURI": ""}}, "currencyCode": {"title": "currencyCode", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "currencyCode", "namespaceURI": ""}}, "paymentTerms": {"title": "paymentTerms", "allOf": [{"$ref": "#/definitions/PaymentTerms"}], "propertyType": "element", "elementName": {"localPart": "paymentTerms", "namespaceURI": ""}}, "incotermCode": {"title": "incotermCode", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "incotermCode", "namespaceURI": ""}}, "incotermLocationCode": {"title": "incotermLocationCode", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "incotermLocationCode", "namespaceURI": ""}}, "isPartialShipmentAllowed": {"title": "isPartialShipmentAllowed", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "isPartialShipmentAllowed", "namespaceURI": ""}}, "shipmentDestination": {"title": "shipmentDestination", "allOf": [{"$ref": "#/definitions/ShipmentDestination"}], "propertyType": "element", "elementName": {"localPart": "shipmentDestination", "namespaceURI": ""}}, "earliestDate": {"title": "earliestDate", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "earliestDate", "namespaceURI": ""}}, "latestDate": {"title": "latestDate", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "latestDate", "namespaceURI": ""}}, "isInspectionRequired": {"title": "isInspectionRequired", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "isInspectionRequired", "namespaceURI": ""}}, "shipmentMethodCode": {"title": "shipmentMethodCode", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "shipmentMethodCode", "namespaceURI": ""}}, "adjustmentAllowed": {"title": "adjustmentAllowed", "allOf": [{"type": "array", "items": {"$ref": "#/definitions/Adjustment"}, "minItems": 0}], "propertyType": "element", "elementName": {"localPart": "adjustmentAllowed", "namespaceURI": ""}}, "orderVariance": {"title": "orderVariance", "allOf": [{"$ref": "#/definitions/Variance"}], "propertyType": "element", "elementName": {"localPart": "orderVariance", "namespaceURI": ""}}, "isTaxRateCheckedForCompliance": {"title": "isTaxRateCheckedForCompliance", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "isTaxRateCheckedForCompliance", "namespaceURI": ""}}, "beneficiaryStatement": {"title": "beneficiaryStatement", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "beneficiaryStatement", "namespaceURI": ""}}, "beneficiaryStatementAcknowledgementCode": {"title": "beneficiaryStatementAcknowledgementCode", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "beneficiaryStatementAcknowledgementCode", "namespaceURI": ""}}, "additionalCondition": {"title": "additionalCondition", "allOf": [{"type": "array", "items": {"$ref": "#/definitions/AdditionalCondition"}, "minItems": 0}], "propertyType": "element", "elementName": {"localPart": "additionalCondition", "namespaceURI": ""}}, "isPodRequired": {"title": "isPodRequired", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "isPodRequired", "namespaceURI": ""}}, "podCompletedByCode": {"title": "podCompletedByCode", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "podCompletedByCode", "namespaceURI": ""}}, "isPackingListRequired": {"title": "isPackingListRequired", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "isPackingListRequired", "namespaceURI": ""}}, "packingListItemAllocationCode": {"title": "packingListItemAllocationCode", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "packingListItemAllocationCode", "namespaceURI": ""}}, "additionalDocumentRequired": {"title": "additionalDocumentRequired", "allOf": [{"type": "array", "items": {"$ref": "#/definitions/AdditionalDocumentRequired"}, "minItems": 0}], "propertyType": "element", "elementName": {"localPart": "additionalDocumentRequired", "namespaceURI": ""}}, "isTransShipmentAllowed": {"title": "isTransShipmentAllowed", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "isTransShipmentAllowed", "namespaceURI": ""}}, "freightPaymentCode": {"title": "freightPaymentCode", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "freightPaymentCode", "namespaceURI": ""}}, "freightPaymentExplanation": {"title": "freightPaymentExplanation", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "freightPaymentExplanation", "namespaceURI": ""}}, "packageMarkDetail": {"title": "packageMarkDetail", "allOf": [{"$ref": "#/definitions/PackageMarkDetail"}], "propertyType": "element", "elementName": {"localPart": "packageMarkDetail", "namespaceURI": ""}}, "consigneeDocumentInstructions": {"title": "consigneeDocumentInstructions", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "consigneeDocumentInstructions", "namespaceURI": ""}}}, "typeType": "classInfo", "typeName": {"localPart": "OrderTerms", "namespaceURI": ""}, "propertiesOrder": ["letterOfCreditNumber", "issueDate", "offerExpiryDate", "cancelAfterDate", "revisionNumber", "reference", "paymentInitiationTypeCode", "settlementMethodCode", "currencyCode", "paymentTerms", "incotermCode", "incotermLocationCode", "isPartialShipmentAllowed", "shipmentDestination", "earliestDate", "latestDate", "isInspectionRequired", "shipmentMethodCode", "adjustmentAllowed", "orderVariance", "isTaxRateCheckedForCompliance", "beneficiaryStatement", "beneficiaryStatementAcknowledgementCode", "additionalCondition", "isPodRequired", "podCompletedByCode", "isPackingListRequired", "packingListItemAllocationCode", "additionalDocumentRequired", "isTransShipmentAllowed", "freightPaymentCode", "freightPaymentExplanation", "packageMarkDetail", "consigneeDocumentInstructions"]}, "LineItemPrice": {"type": "object", "title": "LineItemPrice", "properties": {"pricePerUnit": {"title": "pricePerUnit", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/decimal"}], "propertyType": "element", "elementName": {"localPart": "pricePerUnit", "namespaceURI": ""}}, "totalPrice": {"title": "totalPrice", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/decimal"}], "propertyType": "element", "elementName": {"localPart": "totalPrice", "namespaceURI": ""}}}, "typeType": "classInfo", "typeName": {"localPart": "LineItemPrice", "namespaceURI": ""}, "propertiesOrder": ["pricePerUnit", "totalPrice"]}, "OrderIdentification": {"type": "object", "title": "OrderIdentification", "properties": {"tradecardContractId": {"title": "tradecardContractId", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/long"}], "propertyType": "element", "elementName": {"localPart": "tradecardContractId", "namespaceURI": ""}}, "contractNumber": {"title": "contractNumber", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "contractNumber", "namespaceURI": ""}}, "poNumber": {"title": "poNumber", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "poNumber", "namespaceURI": ""}}, "buyerMemberId": {"title": "buyerMemberId", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "buyerMemberId", "namespaceURI": ""}}, "buyerIdentification": {"title": "buyerIdentification", "allOf": [{"type": "array", "items": {"$ref": "#/definitions/Identification"}, "minItems": 0}], "propertyType": "element", "elementName": {"localPart": "buyerIdentification", "namespaceURI": ""}}, "sellerMemberId": {"title": "sellerMemberId", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "sellerMemberId", "namespaceURI": ""}}, "sellerIdentification": {"title": "sellerIdentification", "allOf": [{"type": "array", "items": {"$ref": "#/definitions/Identification"}, "minItems": 0}], "propertyType": "element", "elementName": {"localPart": "sellerIdentification", "namespaceURI": ""}}, "contractIdentification": {"title": "contractIdentification", "allOf": [{"$ref": "#/definitions/ContractIdentification"}], "propertyType": "element", "elementName": {"localPart": "contractIdentification", "namespaceURI": ""}}}, "typeType": "classInfo", "typeName": {"localPart": "OrderIdentification", "namespaceURI": ""}, "propertiesOrder": ["tradecardContractId", "contractNumber", "poNumber", "buyerMemberId", "buyerIdentification", "sellerMemberId", "sellerIdentification", "contractIdentification"]}, "LineItemTax": {"type": "object", "title": "LineItemTax", "properties": {"taxRate": {"title": "taxRate", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/decimal"}], "propertyType": "element", "elementName": {"localPart": "taxRate", "namespaceURI": ""}}, "taxAmount": {"title": "taxAmount", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/decimal"}], "propertyType": "element", "elementName": {"localPart": "taxAmount", "namespaceURI": ""}}}, "typeType": "classInfo", "typeName": {"localPart": "LineItemTax", "namespaceURI": ""}, "propertiesOrder": ["taxRate", "taxAmount"]}, "NonMember": {"type": "object", "title": "NonMember", "properties": {"memberId": {"title": "memberId", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "memberId", "namespaceURI": ""}}, "identification": {"title": "identification", "allOf": [{"type": "array", "items": {"$ref": "#/definitions/Identification"}, "minItems": 0}], "propertyType": "element", "elementName": {"localPart": "identification", "namespaceURI": ""}}, "name": {"title": "name", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "name", "namespaceURI": ""}}, "contact": {"title": "contact", "allOf": [{"$ref": "#/definitions/OrganizationContact"}], "propertyType": "element", "elementName": {"localPart": "contact", "namespaceURI": ""}}, "address": {"title": "address", "allOf": [{"$ref": "#/definitions/Address"}], "propertyType": "element", "elementName": {"localPart": "address", "namespaceURI": ""}}, "reference": {"title": "reference", "allOf": [{"type": "array", "items": {"$ref": "#/definitions/Reference"}, "minItems": 0}], "propertyType": "element", "elementName": {"localPart": "reference", "namespaceURI": ""}}}, "typeType": "classInfo", "typeName": {"localPart": "NonMember", "namespaceURI": ""}, "propertiesOrder": ["memberId", "identification", "name", "contact", "address", "reference"]}, "AdditionalDocumentRequired": {"type": "object", "title": "AdditionalDocumentRequired", "required": ["documentName"], "properties": {"documentName": {"title": "documentName", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "documentName", "namespaceURI": ""}}, "responsiblePartyCode": {"title": "responsiblePartyCode", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "responsiblePartyCode", "namespaceURI": ""}}, "referenceNumber": {"title": "referenceNumber", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "referenceNumber", "namespaceURI": ""}}, "notes": {"title": "notes", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "notes", "namespaceURI": ""}}}, "typeType": "classInfo", "typeName": {"localPart": "AdditionalDocumentRequired", "namespaceURI": ""}, "propertiesOrder": ["documentName", "responsiblePartyCode", "referenceNumber", "notes"]}, "Variance": {"type": "object", "title": "<PERSON><PERSON><PERSON>", "required": ["varianceTypeCode"], "properties": {"upperVariance": {"title": "upperVariance", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/integer"}], "propertyType": "element", "elementName": {"localPart": "upperVariance", "namespaceURI": ""}}, "lowerVariance": {"title": "lowerVariance", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/integer"}], "propertyType": "element", "elementName": {"localPart": "lowerVariance", "namespaceURI": ""}}, "varianceTypeCode": {"title": "varianceTypeCode", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "varianceTypeCode", "namespaceURI": ""}}}, "typeType": "classInfo", "typeName": {"localPart": "<PERSON><PERSON><PERSON>", "namespaceURI": ""}, "propertiesOrder": ["upperVariance", "lowerVariance", "varianceTypeCode"]}, "OrderParties": {"type": "object", "title": "OrderParties", "required": ["buyer"], "properties": {"buyer": {"title": "buyer", "allOf": [{"$ref": "#/definitions/Member"}], "propertyType": "element", "elementName": {"localPart": "buyer", "namespaceURI": ""}}, "seller": {"title": "seller", "allOf": [{"$ref": "#/definitions/Member"}], "propertyType": "element", "elementName": {"localPart": "seller", "namespaceURI": ""}}, "inspectionCompany": {"title": "inspectionCompany", "allOf": [{"$ref": "#/definitions/Member"}], "propertyType": "element", "elementName": {"localPart": "inspectionCompany", "namespaceURI": ""}}, "logisticsProvider": {"title": "logisticsProvider", "allOf": [{"type": "array", "items": {"$ref": "#/definitions/Member"}, "minItems": 0}], "propertyType": "element", "elementName": {"localPart": "logisticsProvider", "namespaceURI": ""}}, "coverageProvider": {"title": "coverageProvider", "allOf": [{"$ref": "#/definitions/Member"}], "propertyType": "element", "elementName": {"localPart": "coverageProvider", "namespaceURI": ""}}, "buyersAgent": {"title": "buyersAgent", "allOf": [{"$ref": "#/definitions/Member"}], "propertyType": "element", "elementName": {"localPart": "buyersAgent", "namespaceURI": ""}}, "sellersAgent": {"title": "sellersAgent", "allOf": [{"$ref": "#/definitions/NonMember"}], "propertyType": "element", "elementName": {"localPart": "sellersAgent", "namespaceURI": ""}}, "carrier": {"title": "carrier", "allOf": [{"type": "array", "items": {"$ref": "#/definitions/NonMember"}, "minItems": 0}], "propertyType": "element", "elementName": {"localPart": "carrier", "namespaceURI": ""}}, "customsBroker": {"title": "customsBroker", "allOf": [{"$ref": "#/definitions/NonMember"}], "propertyType": "element", "elementName": {"localPart": "customsBroker", "namespaceURI": ""}}, "consignee": {"title": "consignee", "allOf": [{"$ref": "#/definitions/NonMember"}], "propertyType": "element", "elementName": {"localPart": "consignee", "namespaceURI": ""}}, "receivedFrom": {"title": "receivedFrom", "allOf": [{"$ref": "#/definitions/NonMember"}], "propertyType": "element", "elementName": {"localPart": "receivedFrom", "namespaceURI": ""}}, "notifyParty": {"title": "notify<PERSON><PERSON><PERSON>", "allOf": [{"$ref": "#/definitions/NonMember"}], "propertyType": "element", "elementName": {"localPart": "notify<PERSON><PERSON><PERSON>", "namespaceURI": ""}}, "additionalParty": {"title": "additionalParty", "allOf": [{"type": "array", "items": {"$ref": "#/definitions/Member"}, "minItems": 0}], "propertyType": "element", "elementName": {"localPart": "additionalParty", "namespaceURI": ""}}}, "typeType": "classInfo", "typeName": {"localPart": "OrderParties", "namespaceURI": ""}, "propertiesOrder": ["buyer", "seller", "inspectionCompany", "logisticsProvider", "coverageProvider", "buyersAgent", "sellersAgent", "carrier", "customsBroker", "consignee", "receivedFrom", "notify<PERSON><PERSON><PERSON>", "additionalParty"]}, "CustomsClassification": {"type": "object", "title": "CustomsClassification", "required": ["classificationNumber"], "properties": {"classificationNumber": {"title": "classificationNumber", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "classificationNumber", "namespaceURI": ""}}, "countryCode": {"title": "countryCode", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "countryCode", "namespaceURI": ""}}}, "typeType": "classInfo", "typeName": {"localPart": "CustomsClassification", "namespaceURI": ""}, "propertiesOrder": ["classificationNumber", "countryCode"]}, "Attachment": {"type": "object", "title": "Attachment", "required": ["name", "encodingCode", "mimeType", "content"], "properties": {"name": {"title": "name", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "name", "namespaceURI": ""}}, "encodingCode": {"title": "encodingCode", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "encodingCode", "namespaceURI": ""}}, "mimeType": {"title": "mimeType", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "mimeType", "namespaceURI": ""}}, "content": {"title": "content", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "content", "namespaceURI": ""}}}, "typeType": "classInfo", "typeName": {"localPart": "Attachment", "namespaceURI": ""}, "propertiesOrder": ["name", "encodingCode", "mimeType", "content"]}, "OrganizationContact": {"type": "object", "title": "OrganizationContact", "properties": {"name": {"title": "name", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "name", "namespaceURI": ""}}, "title": {"title": "title", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "title", "namespaceURI": ""}}, "emailAddress": {"title": "emailAddress", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "emailAddress", "namespaceURI": ""}}, "phone": {"title": "phone", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "phone", "namespaceURI": ""}}, "fax": {"title": "fax", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "fax", "namespaceURI": ""}}, "department": {"title": "department", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "department", "namespaceURI": ""}}, "region": {"title": "region", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "region", "namespaceURI": ""}}}, "typeType": "classInfo", "typeName": {"localPart": "OrganizationContact", "namespaceURI": ""}, "propertiesOrder": ["name", "title", "emailAddress", "phone", "fax", "department", "region"]}, "Adjustment": {"type": "object", "title": "Adjustment", "required": ["adjustmentTypeCode", "adjustmentValue", "isFlatAmount", "reasonDescription"], "properties": {"adjustmentTypeCode": {"title": "adjustmentTypeCode", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "adjustmentTypeCode", "namespaceURI": ""}}, "adjustmentValue": {"title": "adjustmentValue", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/decimal"}], "propertyType": "element", "elementName": {"localPart": "adjustmentValue", "namespaceURI": ""}}, "isFlatAmount": {"title": "isFlatAmount", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "isFlatAmount", "namespaceURI": ""}}, "reasonType": {"title": "reasonType", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "reasonType", "namespaceURI": ""}}, "reasonDescription": {"title": "reasonDescription", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "reasonDescription", "namespaceURI": ""}}, "taxRate": {"title": "taxRate", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/decimal"}], "propertyType": "element", "elementName": {"localPart": "taxRate", "namespaceURI": ""}}, "adjustmentKey": {"title": "<PERSON><PERSON><PERSON>", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "<PERSON><PERSON><PERSON>", "namespaceURI": ""}}, "reference": {"title": "reference", "allOf": [{"type": "array", "items": {"$ref": "#/definitions/Reference"}, "minItems": 0}], "propertyType": "element", "elementName": {"localPart": "reference", "namespaceURI": ""}}, "comment": {"title": "comment", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "comment", "namespaceURI": ""}}}, "typeType": "classInfo", "typeName": {"localPart": "Adjustment", "namespaceURI": ""}, "propertiesOrder": ["adjustmentTypeCode", "adjustmentValue", "isFlatAmount", "reasonType", "reasonDescription", "taxRate", "<PERSON><PERSON><PERSON>", "reference", "comment"]}, "ShipmentDestination": {"type": "object", "title": "ShipmentDestination", "required": ["destinationKey"], "properties": {"destinationKey": {"title": "destinationKey", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "destinationKey", "namespaceURI": ""}}, "longName": {"title": "<PERSON><PERSON><PERSON>", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "<PERSON><PERSON><PERSON>", "namespaceURI": ""}}, "address": {"title": "address", "allOf": [{"$ref": "#/definitions/Address"}], "propertyType": "element", "elementName": {"localPart": "address", "namespaceURI": ""}}, "phone": {"title": "phone", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "phone", "namespaceURI": ""}}, "fax": {"title": "fax", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "fax", "namespaceURI": ""}}, "contact": {"title": "contact", "allOf": [{"$ref": "#/definitions/OrganizationContact"}], "propertyType": "element", "elementName": {"localPart": "contact", "namespaceURI": ""}}}, "typeType": "classInfo", "typeName": {"localPart": "ShipmentDestination", "namespaceURI": ""}, "propertiesOrder": ["destinationKey", "<PERSON><PERSON><PERSON>", "address", "phone", "fax", "contact"]}, "Header": {"type": "object", "title": "Header", "required": ["version", "documentType"], "properties": {"version": {"title": "version", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/integer"}], "propertyType": "element", "elementName": {"localPart": "version", "namespaceURI": ""}}, "documentType": {"title": "documentType", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "documentType", "namespaceURI": ""}}, "messageId": {"title": "messageId", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "messageId", "namespaceURI": ""}}}, "typeType": "classInfo", "typeName": {"localPart": "Header", "namespaceURI": ""}, "propertiesOrder": ["version", "documentType", "messageId"]}, "TradeCardProductIdentification": {"type": "object", "title": "TradeCardProductIdentification", "properties": {"transactionTypeCode": {"title": "transactionTypeCode", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "transactionTypeCode", "namespaceURI": ""}}, "productOwner": {"title": "productOwner", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "productOwner", "namespaceURI": ""}}}, "typeType": "classInfo", "typeName": {"localPart": "TradeCardProductIdentification", "namespaceURI": ""}, "propertiesOrder": ["transactionTypeCode", "productOwner"]}, "ItemReference": {"type": "object", "title": "ItemReference", "required": ["type"], "properties": {"type": {"title": "type", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "type", "namespaceURI": ""}}, "value": {"title": "value", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "value", "namespaceURI": ""}}, "displayCode": {"title": "displayCode", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "displayCode", "namespaceURI": ""}}}, "typeType": "classInfo", "typeName": {"localPart": "ItemReference", "namespaceURI": ""}, "propertiesOrder": ["type", "value", "displayCode"]}, "BaseLineItem": {"type": "object", "title": "BaseLineItem", "properties": {"itemSequenceNumber": {"title": "itemSequenceNumber", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "itemSequenceNumber", "namespaceURI": ""}}, "buyerNumber": {"title": "buyerNumber", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "buyerNumber", "namespaceURI": ""}}, "sellerNumber": {"title": "sellerNumber", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "sellerNumber", "namespaceURI": ""}}, "shortDescription": {"title": "shortDescription", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "shortDescription", "namespaceURI": ""}}, "longDescription": {"title": "longDescription", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "longDescription", "namespaceURI": ""}}, "upcNumber": {"title": "upcNumber", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "upcNumber", "namespaceURI": ""}}, "skuNumber": {"title": "skuNumber", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "skuNumber", "namespaceURI": ""}}, "countryOfOriginCode": {"title": "countryOfOriginCode", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "countryOfOriginCode", "namespaceURI": ""}}, "customsClassification": {"title": "customsClassification", "allOf": [{"type": "array", "items": {"$ref": "#/definitions/CustomsClassification"}, "minItems": 0}], "propertyType": "element", "elementName": {"localPart": "customsClassification", "namespaceURI": ""}}, "quotaCategory": {"title": "quotaCategory", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "quotaCategory", "namespaceURI": ""}}, "itemReference": {"title": "itemReference", "allOf": [{"type": "array", "items": {"$ref": "#/definitions/ItemReference"}, "minItems": 0}], "propertyType": "element", "elementName": {"localPart": "itemReference", "namespaceURI": ""}}, "quantity": {"title": "quantity", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/decimal"}], "propertyType": "element", "elementName": {"localPart": "quantity", "namespaceURI": ""}}, "unitOfMeasureCode": {"title": "unitOfMeasureCode", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "unitOfMeasureCode", "namespaceURI": ""}}, "packMethodCode": {"title": "packMethodCode", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "packMethodCode", "namespaceURI": ""}}, "quantityPerInnerPackage": {"title": "quantityPerInnerPackage", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/integer"}], "propertyType": "element", "elementName": {"localPart": "quantityPerInnerPackage", "namespaceURI": ""}}, "quantityPerOuterPackage": {"title": "quantityPerOuterPackage", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/integer"}], "propertyType": "element", "elementName": {"localPart": "quantityPerOuterPackage", "namespaceURI": ""}}, "destinationKey": {"title": "destinationKey", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "destinationKey", "namespaceURI": ""}}, "earliestDate": {"title": "earliestDate", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "earliestDate", "namespaceURI": ""}}, "latestDate": {"title": "latestDate", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "latestDate", "namespaceURI": ""}}, "isInspectionRequired": {"title": "isInspectionRequired", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "isInspectionRequired", "namespaceURI": ""}}, "shipmentMethodCode": {"title": "shipmentMethodCode", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "shipmentMethodCode", "namespaceURI": ""}}, "itemVariance": {"title": "itemVariance", "allOf": [{"$ref": "#/definitions/Variance"}], "propertyType": "element", "elementName": {"localPart": "itemVariance", "namespaceURI": ""}}}, "typeType": "classInfo", "typeName": {"localPart": "BaseLineItem", "namespaceURI": ""}, "propertiesOrder": ["itemSequenceNumber", "buyerNumber", "sellerNumber", "shortDescription", "longDescription", "upcNumber", "skuNumber", "countryOfOriginCode", "customsClassification", "quotaCategory", "itemReference", "quantity", "unitOfMeasureCode", "packMethodCode", "quantityPerInnerPackage", "quantityPerOuterPackage", "destinationKey", "earliestDate", "latestDate", "isInspectionRequired", "shipmentMethodCode", "itemVariance"]}, "AdditionalCondition": {"type": "object", "title": "AdditionalCondition", "required": ["additionalConditionText"], "properties": {"additionalConditionText": {"title": "additionalConditionText", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "additionalConditionText", "namespaceURI": ""}}, "additionalConditionAcknowledgementCode": {"title": "additionalConditionAcknowledgementCode", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "additionalConditionAcknowledgementCode", "namespaceURI": ""}}}, "typeType": "classInfo", "typeName": {"localPart": "AdditionalCondition", "namespaceURI": ""}, "propertiesOrder": ["additionalConditionText", "additionalConditionAcknowledgementCode"]}, "OrderDetail": {"type": "object", "title": "OrderDetail", "required": ["poNumber", "orderTerms", "orderParties"], "properties": {"subMessageId": {"title": "subMessageId", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "subMessageId", "namespaceURI": ""}}, "messageFunctionCode": {"title": "messageFunctionCode", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "messageFunctionCode", "namespaceURI": ""}}, "eventCode": {"title": "eventCode", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "eventCode", "namespaceURI": ""}}, "eventDate": {"title": "eventDate", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "eventDate", "namespaceURI": ""}}, "redirectUrl": {"title": "redirectUrl", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "redirectUrl", "namespaceURI": ""}}, "validationErrorText": {"title": "validationErrorText", "allOf": [{"type": "array", "items": {"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}, "minItems": 0}], "propertyType": "element", "elementName": {"localPart": "validationErrorText", "namespaceURI": ""}}, "orderIdentification": {"title": "orderIdentification", "allOf": [{"$ref": "#/definitions/OrderIdentification"}], "propertyType": "element", "elementName": {"localPart": "orderIdentification", "namespaceURI": ""}}, "tradecardProductIdentification": {"title": "tradecardProductIdentification", "allOf": [{"$ref": "#/definitions/TradeCardProductIdentification"}], "propertyType": "element", "elementName": {"localPart": "tradecardProductIdentification", "namespaceURI": ""}}, "poNumber": {"title": "poNumber", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "poNumber", "namespaceURI": ""}}, "contractNumber": {"title": "contractNumber", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "contractNumber", "namespaceURI": ""}}, "tradecardContractId": {"title": "tradecardContractId", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/long"}], "propertyType": "element", "elementName": {"localPart": "tradecardContractId", "namespaceURI": ""}}, "effectiveDate": {"title": "effectiveDate", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "effectiveDate", "namespaceURI": ""}}, "orderFunctionCode": {"title": "orderFunctionCode", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "orderFunctionCode", "namespaceURI": ""}}, "debitBankAccountNumber": {"title": "debitBankAccountNumber", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "debitBankAccountNumber", "namespaceURI": ""}}, "complianceTemplateCode": {"title": "complianceTemplateCode", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "complianceTemplateCode", "namespaceURI": ""}}, "orderTerms": {"title": "orderTerms", "allOf": [{"$ref": "#/definitions/OrderTerms"}], "propertyType": "element", "elementName": {"localPart": "orderTerms", "namespaceURI": ""}}, "orderParties": {"title": "orderParties", "allOf": [{"$ref": "#/definitions/OrderParties"}], "propertyType": "element", "elementName": {"localPart": "orderParties", "namespaceURI": ""}}, "orderLineItem": {"title": "orderLineItem", "allOf": [{"type": "array", "items": {"$ref": "#/definitions/OrderLineItem"}, "minItems": 0}], "propertyType": "element", "elementName": {"localPart": "orderLineItem", "namespaceURI": ""}}, "attachment": {"title": "attachment", "allOf": [{"type": "array", "items": {"$ref": "#/definitions/Attachment"}, "minItems": 0}], "propertyType": "element", "elementName": {"localPart": "attachment", "namespaceURI": ""}}, "totals": {"title": "totals", "allOf": [{"$ref": "#/definitions/DocumentTotals"}], "propertyType": "element", "elementName": {"localPart": "totals", "namespaceURI": ""}}}, "typeType": "classInfo", "typeName": {"localPart": "OrderDetail", "namespaceURI": ""}, "propertiesOrder": ["subMessageId", "messageFunctionCode", "eventCode", "eventDate", "redirectUrl", "validationErrorText", "orderIdentification", "tradecardProductIdentification", "poNumber", "contractNumber", "tradecardContractId", "effectiveDate", "orderFunctionCode", "debitBankAccountNumber", "complianceTemplateCode", "orderTerms", "orderParties", "orderLineItem", "attachment", "totals"]}, "Reference": {"type": "object", "title": "Reference", "required": ["type"], "properties": {"type": {"title": "type", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "type", "namespaceURI": ""}}, "value": {"title": "value", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "value", "namespaceURI": ""}}}, "typeType": "classInfo", "typeName": {"localPart": "Reference", "namespaceURI": ""}, "propertiesOrder": ["type", "value"]}, "PaymentTerms": {"type": "object", "title": "PaymentTerms", "properties": {"paymentTenorDaysCode": {"title": "paymentTenorDaysCode", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "paymentTenorDaysCode", "namespaceURI": ""}}, "paymentTenorStartDateCode": {"title": "paymentTenorStartDateCode", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "paymentTenorStartDateCode", "namespaceURI": ""}}, "paymentTenorNotes": {"title": "paymentTenorNotes", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "paymentTenorNotes", "namespaceURI": ""}}, "paymentTenorBaseDate": {"title": "paymentTenorBaseDate", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "paymentTenorBaseDate", "namespaceURI": ""}}}, "typeType": "classInfo", "typeName": {"localPart": "PaymentTerms", "namespaceURI": ""}, "propertiesOrder": ["paymentTenorDaysCode", "paymentTenorStartDateCode", "paymentTenorNotes", "paymentTenorBaseDate"]}, "Identification": {"type": "object", "title": "Identification", "required": ["type", "value"], "properties": {"type": {"title": "type", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "type", "namespaceURI": ""}}, "value": {"title": "value", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "value", "namespaceURI": ""}}}, "typeType": "classInfo", "typeName": {"localPart": "Identification", "namespaceURI": ""}, "propertiesOrder": ["type", "value"]}, "OrderLineItem": {"type": "object", "title": "OrderLineItem", "required": ["itemKey", "baseLineItem"], "properties": {"itemKey": {"title": "itemKey", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "itemKey", "namespaceURI": ""}}, "itemTypeCode": {"title": "itemTypeCode", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "itemTypeCode", "namespaceURI": ""}}, "parentItemKey": {"title": "parentItemKey", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "parentItemKey", "namespaceURI": ""}}, "baseLineItem": {"title": "baseLineItem", "allOf": [{"$ref": "#/definitions/BaseLineItem"}], "propertyType": "element", "elementName": {"localPart": "baseLineItem", "namespaceURI": ""}}, "lineItemPrice": {"title": "lineItemPrice", "allOf": [{"$ref": "#/definitions/LineItemPrice"}], "propertyType": "element", "elementName": {"localPart": "lineItemPrice", "namespaceURI": ""}}, "lineItemTax": {"title": "lineItemTax", "allOf": [{"$ref": "#/definitions/LineItemTax"}], "propertyType": "element", "elementName": {"localPart": "lineItemTax", "namespaceURI": ""}}}, "typeType": "classInfo", "typeName": {"localPart": "OrderLineItem", "namespaceURI": ""}, "propertiesOrder": ["itemKey", "itemTypeCode", "parentItemKey", "baseLineItem", "lineItemPrice", "lineItemTax"]}, "PackageMarkDetail": {"type": "object", "title": "PackageMarkDetail", "properties": {"markNumber": {"title": "<PERSON><PERSON><PERSON><PERSON>", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "<PERSON><PERSON><PERSON><PERSON>", "namespaceURI": ""}}, "mark": {"title": "mark", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "mark", "namespaceURI": ""}}, "instruction": {"title": "instruction", "allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/string"}], "propertyType": "element", "elementName": {"localPart": "instruction", "namespaceURI": ""}}}, "typeType": "classInfo", "typeName": {"localPart": "PackageMarkDetail", "namespaceURI": ""}, "propertiesOrder": ["<PERSON><PERSON><PERSON><PERSON>", "mark", "instruction"]}}, "anyOf": [{"type": "object", "properties": {"name": {"allOf": [{"$ref": "http://www.jsonix.org/jsonschemas/w3c/2001/XMLSchema.jsonschema#/definitions/QName"}, {"type": "object", "properties": {"localPart": {"enum": ["Order"]}, "namespaceURI": {"enum": [""]}}}]}, "value": {"$ref": "#/definitions/Order"}}, "elementName": {"localPart": "Order", "namespaceURI": ""}}], "type": "object", "properties": {"Order": {"$ref": "#/definitions/Order"}}, "required": ["Order"]}