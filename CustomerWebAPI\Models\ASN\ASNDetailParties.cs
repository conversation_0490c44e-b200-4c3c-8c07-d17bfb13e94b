using System;
using Dapper.Contrib.Extensions;

namespace CustomerWebAPI.Models
{
    /// <summary>
    /// 
    /// </summary>
    /// 
    [Table("ASNDetailParties")]
    public class ASNDetailParties
    {
        /// <summary>
        /// 
        /// </summary>
        /// 
        [Explicit<PERSON>ey]
        public Guid Id { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public Guid? ASNDetailId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string? Identifier { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string? Code { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string? AddressLine1 { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string? AddressLine2 { get; set; }
    }
}