using System;
using Dapper.Contrib.Extensions;

namespace CustomerWebAPI.Models
{
    /// <summary>
    /// 
    /// </summary>
    /// 
    [Table("ASNDetailOrdersItemsPackagesMeasurements")]
    public class ASNDetailOrdersItemsPackagesMeasurements
    {
        /// <summary>
        /// 
        /// </summary>
        /// 
        [ExplicitKey]
        public Guid Id { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public Guid? ASNDetailOrdersItemsPackagesId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public decimal? Value { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string MeasurementUnit { get; set; }
    }
}