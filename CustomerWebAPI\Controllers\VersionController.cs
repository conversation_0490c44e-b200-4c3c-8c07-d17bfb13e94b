﻿using System;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc;
using System.IO;
using Microsoft.Extensions.Configuration;

namespace CustomerWebAPI.Controllers
{

    [ApiController]
    [Route("api/[controller]")]
    public class VersionController : ControllerBase
    {
        private readonly IConfiguration _configuration;
        private readonly string _updatesPath;

        public VersionController(IWebHostEnvironment env,IConfiguration configuration)
        {
            _configuration = configuration;
            _updatesPath = Path.Combine(env.ContentRootPath, "Updates");
        }

        [HttpGet("latest")]
        public IActionResult GetLatestVersion()
        {

            //            ValidIssuer = Configuration["Jwt:Issuer"],
            //            ValidAudience = Configuration["Jwt:Issuer"],

            var LatestVersion = _configuration["UpdateConfigAPK:Version"];
            //var isForceUpdate = _configuration["UpdateConfigAPK:ForceUpdate"];
            var isForceUpdate = bool.TryParse(_configuration["UpdateConfigAPK:ForceUpdate"], out bool result) ? result : false;

            var latestVersionInfo = new
            {
                Version = LatestVersion,
                ForceUpdate = (isForceUpdate),
                UpdateUrl = Url.Action("DownloadLatestApk", "Version")
            };
            return Ok(latestVersionInfo);
        }

        //下载最新版本APK
        [HttpGet("download")]
        public IActionResult DownloadLatestApk()
        {
            var filePath = Path.Combine(_updatesPath, "latest.apk");
            if (!System.IO.File.Exists(filePath))
            {
                return NotFound();
            }

            var stream = new FileStream(filePath, FileMode.Open, FileAccess.Read);
            return File(stream, "application/vnd.android.package-archive", "latest.apk");
        }
    }
}
