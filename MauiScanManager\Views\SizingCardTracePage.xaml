<?xml version="1.0" encoding="utf-8" ?>
<views:BaseOperationPage 
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:toolkit="http://schemas.microsoft.com/dotnet/2022/maui/toolkit"
             xmlns:viewmodels="clr-namespace:MauiScanManager.ViewModels"
             xmlns:views="clr-namespace:MauiScanManager.Views"
             x:Class="MauiScanManager.Views.SizingCardTracePage"
             x:DataType="viewmodels:SizingCardTraceViewModel"
             Title="{Binding Operation.Description}">

    <Grid>
    <ScrollView>
        <VerticalStackLayout Spacing="5" Padding="10">
            <!-- 浆纱机台选择 -->
            <Label Text="浆纱机台" FontSize="20" FontAttributes="Bold"/>
            <Picker ItemsSource="{Binding SizingMachines}"
                    SelectedItem="{Binding MachineNo}"
                    Title="选择机台"
                    FontSize="20"
                    HeightRequest="45">
                <Picker.Behaviors>
                    <toolkit:EventToCommandBehavior 
                        EventName="SelectedIndexChanged"
                        Command="{Binding MachineNoChangedCommand}"/>
                </Picker.Behaviors>
            </Picker>

            <!-- 浆缸序号 -->
            <Label Text="浆缸序号" FontSize="20" FontAttributes="Bold"/>
            <Entry Text="{Binding BatchSerialNo}"
                   Keyboard="Numeric"
                   IsEnabled="{Binding IsUpMachine, Converter={StaticResource InverseBoolConverter}}"
                   FontSize="20"
                   HeightRequest="45"/>

            <!-- 织轴卡号 -->
            <Label Text="织轴卡号" FontSize="20" FontAttributes="Bold"/>
            <Entry Text="{Binding WvCardNo}"
                   IsReadOnly="True"
                   IsEnabled="False"
                   FontSize="20"
                   HeightRequest="45"/>

            <!-- 操作按钮 - 垂直排列 -->
            <VerticalStackLayout Spacing="6" 
                               Margin="0,8,0,0">
                <!-- 上机按钮 -->
                <Button Text="上机"
                        Command="{Binding SaveUpMachineCommand}"
                        IsEnabled="{Binding IsUpMachine, Converter={StaticResource InverseBoolConverter}}"
                        BackgroundColor="{StaticResource Primary}"
                        HeightRequest="48"
                        FontSize="20"
                        FontAttributes="Bold"
                        HorizontalOptions="Fill"/>

                <!-- 下机按钮 -->
                <Button Text="下机"
                        Command="{Binding SaveDownMachineCommand}"
                        IsEnabled="{Binding IsUpMachine}"
                        BackgroundColor="{StaticResource Primary}"
                        HeightRequest="48"
                        FontSize="20"
                        FontAttributes="Bold"
                        HorizontalOptions="Fill"/>

                <!-- 取消上机按钮 -->
                <Button Text="取消上机"
                        Command="{Binding CancelUpMachineCommand}"
                        IsEnabled="{Binding IsUpMachine}"
                        BackgroundColor="{StaticResource Primary}"
                        HeightRequest="48"
                        FontSize="20"
                        FontAttributes="Bold"
                        HorizontalOptions="Fill"/>
            </VerticalStackLayout>
        </VerticalStackLayout>
    </ScrollView>
    <ActivityIndicator 
        IsRunning="{Binding IsBusy}"
        IsVisible="{Binding IsBusy}"
        HorizontalOptions="Center"
        VerticalOptions="Center"
        Color="{StaticResource Primary}"/>
        </Grid>
</views:BaseOperationPage> 