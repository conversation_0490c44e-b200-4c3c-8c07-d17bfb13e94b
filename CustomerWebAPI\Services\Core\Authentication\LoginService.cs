﻿using CustomerWebAPI.Helpers;
using CustomerWebAPI.Models;
using Newtonsoft.Json;
using System;
using System.Threading.Tasks;

namespace CustomerWebAPI.Services
{
    public class LoginService
    {
        public (bool, string) ValidateLogin(dynamic loginInfo)
        {
            try
            {
                UserModel userModel = JsonConvert.DeserializeObject<UserModel>(loginInfo.ToString()) ?? throw new Exception("Parameter Json format not correct !");
                if (string.IsNullOrEmpty(userModel.Username)) { return (false, "用户不能为空，请检查"); }
                var rst = LDAPUtil.ValidateUser(userModel.Username, userModel.Password); ;
                return rst ? (true, "login successful!") : (false, "login failed!");
            }
            catch (Exception e)
            {
                var errorMsg = "login failed!" + Environment.NewLine + e.Message;
                return (false, errorMsg);
            }
        }
    }
}
