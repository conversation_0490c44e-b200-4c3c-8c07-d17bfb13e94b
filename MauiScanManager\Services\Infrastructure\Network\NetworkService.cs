using MauiScanManager.Services;
using Microsoft.Extensions.Logging;

namespace MauiScanManager.Services
{
    public class NetworkService : INetworkService
    {
        private readonly HttpClient _httpClient;
        private readonly AppSettings _settings;
        private readonly IDialogService _dialogService;
        private readonly IPlatformLoadingService _loadingService;
        private readonly ILogger<NetworkService> _logger;
        private bool _isConnected;
        private DateTime _lastCheckTime;
        
        // 根据不同场景设置不同的检查间隔
        private static readonly TimeSpan NormalCheckInterval = TimeSpan.FromMinutes(5);    // 正常情况下5分钟
        private static readonly TimeSpan ErrorCheckInterval = TimeSpan.FromMinutes(1);     // 出错后1分钟
        private TimeSpan _currentCheckInterval = NormalCheckInterval;

        public NetworkService(
            HttpClient httpClient, 
            AppSettings settings,
            IDialogService dialogService,
            IPlatformLoadingService loadingService,
            ILogger<NetworkService> logger)
        {
            _httpClient = httpClient;
            _settings = settings;
            _dialogService = dialogService;
            _loadingService = loadingService;
            _logger = logger;
        }

        public bool IsConnected => _isConnected;

        public void ResetNetworkState()
        {
            _isConnected = false;
            _lastCheckTime = DateTime.MinValue;
            _currentCheckInterval = ErrorCheckInterval; // 重置后使用较短的检查间隔
            _logger.LogInformation("网络状态已重置，下次检查间隔: {Interval}秒", _currentCheckInterval.TotalSeconds);
        }

        public async Task<bool> CheckNetworkAsync()
        {
            try
            {
                var timeSinceLastCheck = DateTime.Now - _lastCheckTime;
                
                // 如果已连接且未超过检查间隔，直接返回
                if (_isConnected && timeSinceLastCheck < _currentCheckInterval)
                {
                    _logger.LogDebug("跳过网络检查，距离上次检查: {Time}秒", timeSinceLastCheck.TotalSeconds);
                    return true;
                }

                _loadingService?.ShowNativeLoading("正在检查网络连接...");
                _logger.LogInformation("开始检查网络状态，距离上次检查: {Time}秒", timeSinceLastCheck.TotalSeconds);

                if (Connectivity.NetworkAccess != NetworkAccess.Internet)
                {
                    _logger.LogWarning("设备无网络连接");
                    ResetNetworkState();
                    await _dialogService.ShowWarningAsync("请检查网络连接");
                    return false;
                }

                _loadingService?.ShowNativeLoading("正在连接服务器...");
                using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(10));
                
                try
                {
                    var response = await _httpClient.GetAsync($"{_settings.BaseUrl}/api/health", cts.Token);
                    _isConnected = response.IsSuccessStatusCode;
                    _lastCheckTime = DateTime.Now;
                    
                    if (_isConnected)
                    {
                        _currentCheckInterval = NormalCheckInterval; // 连接成功后恢复正常检查间隔
                        _logger.LogInformation("网络检查成功，恢复正常检查间隔: {Interval}分钟", _currentCheckInterval.TotalMinutes);
                    }
                    else
                    {
                        _logger.LogWarning("服务器连接失败: {StatusCode}", response.StatusCode);
                        await _dialogService.ShowWarningAsync("无法连接到服务器，请检查网络设置");
                    }

                    return _isConnected;
                }
                catch (TaskCanceledException)
                {
                    _logger.LogWarning("服务器连接超时");
                    ResetNetworkState();
                    await _dialogService.ShowWarningAsync("连接服务器超时，请检查网络");
                    return false;
                }
            }
            catch (OperationCanceledException)
            {
                _logger.LogWarning("网络检查被取消");
                ResetNetworkState();
                await _dialogService.ShowWarningAsync("连接服务器超时，请检查网络");
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "网络检查发生异常");
                ResetNetworkState();
                await _dialogService.ShowErrorAsync($"网络检查失败: {ex.Message}");
                return false;
            }
            finally
            {
                _loadingService?.HideNativeLoading();
            }
        }
    }
}
