<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:viewmodels="clr-namespace:MauiScanManager.ViewModels"
             x:Class="MauiScanManager.Views.DepartmentSelectionPage"
             Title="操作选择"
             BackgroundColor="White">

    <!-- 在标题栏右侧添加版本号 -->
    <NavigationPage.TitleView>
        <Grid BackgroundColor="Transparent">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            <Label Text="操作选择" 
                   VerticalOptions="Center"
                   HorizontalOptions="Start"
                   TextColor="White"
                   FontAttributes="Bold"
                   FontSize="20"/>
            <Label Grid.Column="1" 
                   Text="{Binding AppVersion}"
                   VerticalOptions="Center"
                   HorizontalOptions="End"
                   TextColor="LightGray"
                   Margin="0,0,15,0"
                   FontSize="14"/>
        </Grid>
    </NavigationPage.TitleView>

    <RefreshView IsRefreshing="{Binding IsRefreshing}"
                 Command="{Binding RefreshCommand}">
        <ScrollView>
            <VerticalStackLayout Padding="15" Spacing="15">
                <!-- 加载指示器 -->
                <ActivityIndicator IsVisible="{Binding IsLoading}"
                                IsRunning="{Binding IsLoading}"
                                HorizontalOptions="Center"
                                Margin="0,5"/>

                <!-- 部门 -->
                <Label Text="部门"
                       FontSize="18"
                       Margin="5,0"/>
                
                <Picker ItemsSource="{Binding Departments}"
                        ItemDisplayBinding="{Binding Description}"
                        SelectedItem="{Binding SelectedDepartment}"
                        Title="请选择"
                        FontSize="20"
                        HeightRequest="60"/>

                <!-- 操作 -->
                <Label Text="操作"
                       FontSize="18"
                       Margin="5,0"/>
                
                <Picker ItemsSource="{Binding Operations}"
                        ItemDisplayBinding="{Binding Description}"
                        SelectedItem="{Binding SelectedOperation}"
                        Title="请选择"
                        FontSize="20"
                        HeightRequest="60"
                        IsEnabled="{Binding HasSelectedDepartment}"/>

                <!-- 确认按钮 -->
                <Button Text="确认"
                        Command="{Binding ConfirmCommand}"
                        IsEnabled="{Binding CanConfirm}"
                        HeightRequest="60"
                        FontSize="20"
                        Margin="0,20,0,0"/>

                <!-- 打印测试按钮（仅在DEBUG模式下显示） -->
                <Button Text="打印测试"
                        Command="{Binding NavigateToPrintTestCommand}"
                        BackgroundColor="LightBlue"
                        TextColor="Black"
                        HeightRequest="50"
                        FontSize="18"
                        Margin="0,10,0,0"
                        IsVisible="{Binding IsDebugMode}"/>

                <!-- 在底部添加版本号 -->
                <Label Text="{Binding AppVersion}"
                       HorizontalOptions="End"
                       TextColor="Gray"
                       FontSize="14"
                       Margin="0,10,0,0"
                       VerticalOptions="End"/>
            </VerticalStackLayout>
        </ScrollView>
    </RefreshView>

</ContentPage>
