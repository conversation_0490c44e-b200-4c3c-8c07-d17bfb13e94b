using System.ComponentModel.DataAnnotations;

namespace CoreHub.Shared.Models.AppUpdate
{
    /// <summary>
    /// 更新检查请求
    /// </summary>
    public class UpdateCheckRequest
    {
        /// <summary>
        /// 当前版本号
        /// </summary>
        [Required(ErrorMessage = "当前版本号不能为空")]
        public string CurrentVersion { get; set; } = string.Empty;

        /// <summary>
        /// 当前版本代码
        /// </summary>
        [Required(ErrorMessage = "当前版本代码不能为空")]
        public int CurrentVersionCode { get; set; }

        /// <summary>
        /// 平台类型
        /// </summary>
        [Required(ErrorMessage = "平台类型不能为空")]
        public string Platform { get; set; } = string.Empty;

        /// <summary>
        /// 设备ID
        /// </summary>
        public string? DeviceId { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        public int? UserId { get; set; }

        /// <summary>
        /// 部门ID
        /// </summary>
        public int? DepartmentId { get; set; }

        /// <summary>
        /// 应用包名
        /// </summary>
        public string? PackageName { get; set; }

        /// <summary>
        /// 设备信息
        /// </summary>
        public DeviceInfo? Device { get; set; }
    }

    /// <summary>
    /// 设备信息
    /// </summary>
    public class DeviceInfo
    {
        /// <summary>
        /// 设备型号
        /// </summary>
        public string? Model { get; set; }

        /// <summary>
        /// 操作系统版本
        /// </summary>
        public string? OsVersion { get; set; }

        /// <summary>
        /// 应用版本
        /// </summary>
        public string? AppVersion { get; set; }

        /// <summary>
        /// 设备语言
        /// </summary>
        public string? Language { get; set; }

        /// <summary>
        /// 时区
        /// </summary>
        public string? TimeZone { get; set; }
    }
}
