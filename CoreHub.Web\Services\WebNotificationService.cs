using MudBlazor;

namespace CoreHub.Web.Services
{
    public class WebNotificationService : Shared.Services.INotificationService
    {
        private readonly ISnackbar _snackbar;

        public WebNotificationService(ISnackbar snackbar)
        {
            _snackbar = snackbar;
        }

        public async Task<bool> RequestPermissionAsync()
        {
            // Web端不需要权限请求，直接返回true
            return await Task.FromResult(true);
        }

        public async Task SendNotificationAsync(string title, string message)
        {
            try
            {
                // 使用MudBlazor的Snackbar组件
                _snackbar.Add($"{title}: {message}", Severity.Info, configure: config =>
                {
                    config.VisibleStateDuration = 3000; // 3秒后自动关闭
                    config.ShowCloseIcon = true;
                });
                
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Web通知发送失败: {ex.Message}");
                throw;
            }
        }
    }
} 