﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Http;
using System;
using System.Threading.Tasks;
using CustomerWebAPI.Services;
using CustomerWebAPI.Models;
using CustomerWebAPI.Common;

namespace CustomerWebAPI.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class ChemicalController : ControllerBase
    {
        private readonly IChemicalService _chemicalService;
        private readonly ILogger<ChemicalController> _logger;

        public ChemicalController(
            IChemicalService chemicalService,
            ILogger<ChemicalController> logger)
        {
            _chemicalService = chemicalService;
            _logger = logger;
        }

        [HttpPost("NewChemicalBoxNo")]
        [ProducesResponseType(typeof(ApiResponse<string>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ApiResponse<string>), StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<ApiResponse<string>>> GetNewChemicalBoxNo([FromBody] Chemical chemicalCode)
        {
            try
            {
                _logger.LogInformation($"Getting new chemical box no for chemical code: {chemicalCode.ChemicalCode}");
                var response = await _chemicalService.GetNewChemicalBoxNo(chemicalCode);

                if (!response.Success)
                {
                    _logger.LogWarning($"Failed to get new chemical box no: {response.Message}");
                }

                return response.Success ? Ok(response) : BadRequest(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting new chemical box no");
                return StatusCode(StatusCodes.Status500InternalServerError,
                    ApiResponse<string>.Fail("获取新染料箱号时发生错误", (int)ApiErrorCodes.ServerError));
            }
        }

        [HttpPost("ChemicalChange")]
        [ProducesResponseType(typeof(ApiResponse<string>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ApiResponse<string>), StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<ApiResponse<string>>> CheckChemicalChange([FromBody] ChemicalCheck chemicalCheck)
        {
            try
            {
                _logger.LogInformation($"Checking chemical change for box no: {chemicalCheck.ChemicalBoxNo}");
                var response = await _chemicalService.CheckChemicalChange(chemicalCheck);

                if (!response.Success)
                {
                    _logger.LogWarning($"Failed to check chemical change: {response.Message}");
                }

                return response.Success ? Ok(response) : BadRequest(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking chemical change");
                return StatusCode(StatusCodes.Status500InternalServerError,
                    ApiResponse<string>.Fail("校验染料更换时发生错误", (int)ApiErrorCodes.ServerError));
            }
        }

        [HttpPost("check-box-consistency")]
        [ProducesResponseType(typeof(ApiResponse<ChemicalBoxCheckResult>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ApiResponse<ChemicalBoxCheckResult>), StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<ApiResponse<ChemicalBoxCheckResult>>> CheckChemicalBoxConsistency([FromBody] ChemicalBatchCheck batchCheck)
        {
            try
            {
                _logger.LogInformation($"Checking chemical box consistency for batch: {batchCheck.BatchNo}, box: {batchCheck.ChemicalBoxNo}");
                var response = await _chemicalService.CheckChemicalBoxConsistency(batchCheck);

                if (!response.Success)
                {
                    _logger.LogWarning($"Failed to check chemical box consistency: {response.Message}");
                }

                return response.Success ? Ok(response) : BadRequest(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking chemical box consistency");
                return StatusCode(StatusCodes.Status500InternalServerError,
                    ApiResponse<ChemicalBoxCheckResult>.Fail("校验染料箱号时发生错误", (int)ApiErrorCodes.ServerError));
            }
        }
    }
}
