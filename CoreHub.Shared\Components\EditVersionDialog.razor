@using CoreHub.Shared.Models.AppUpdate
@using CoreHub.Shared.Services
@inject IAppUpdateService UpdateService
@inject ISnackbar Snackbar

<MudDialog>
    <DialogContent>
        <MudContainer Style="max-width: 600px;">
            <MudGrid>
                <MudItem xs="12" sm="6">
                    <MudTextField @bind-Value="version.VersionNumber" 
                                 Label="版本号" 
                                 Required="true"
                                 Disabled="@(version.Status == "Released")" />
                </MudItem>
                <MudItem xs="12" sm="6">
                    <MudNumericField @bind-Value="version.VersionCode" 
                                    Label="版本代码" 
                                    Required="true"
                                    Min="1"
                                    Disabled="@(version.Status == "Released")" />
                </MudItem>
                <MudItem xs="12" sm="6">
                    <MudSelect @bind-Value="version.Platform" 
                              Label="平台" 
                              Required="true"
                              Disabled="@(version.Status == "Released")">
                        <MudSelectItem Value="@("Android")">Android</MudSelectItem>
                        <MudSelectItem Value="@("iOS")">iOS</MudSelectItem>
                        <MudSelectItem Value="@("Windows")">Windows</MudSelectItem>
                        <MudSelectItem Value="@("MacOS")">MacOS</MudSelectItem>
                    </MudSelect>
                </MudItem>
                <MudItem xs="12" sm="6">
                    <MudSelect @bind-Value="version.UpdateType" Label="更新类型" Required="true">
                        <MudSelectItem Value="@("Major")">重大更新</MudSelectItem>
                        <MudSelectItem Value="@("Minor")">功能更新</MudSelectItem>
                        <MudSelectItem Value="@("Patch")">修复更新</MudSelectItem>
                        <MudSelectItem Value="@("Hotfix")">紧急修复</MudSelectItem>
                    </MudSelect>
                </MudItem>
                <MudItem xs="12">
                    <MudTextField @bind-Value="version.Title" 
                                 Label="更新标题" 
                                 Required="true" />
                </MudItem>
                <MudItem xs="12">
                    <MudTextField @bind-Value="version.Description" 
                                 Label="更新描述" 
                                 Lines="4" />
                </MudItem>
                <MudItem xs="12">
                    <MudTextField @bind-Value="version.DownloadUrl" 
                                 Label="下载URL" 
                                 Required="true" />
                </MudItem>
                <MudItem xs="12" sm="6">
                    <MudNumericField @bind-Value="version.FileSize" 
                                    Label="文件大小 (字节)" 
                                    Required="true"
                                    Min="0" />
                </MudItem>
                <MudItem xs="12" sm="6">
                    <MudTextField @bind-Value="version.FileMd5" 
                                 Label="文件MD5 (可选)" />
                </MudItem>
                <MudItem xs="12" sm="6">
                    <MudSwitch @bind-Value="version.IsForceUpdate" 
                              Label="强制更新" 
                              Color="Color.Warning" />
                </MudItem>
                <MudItem xs="12" sm="6">
                    <MudSelect @bind-Value="version.Status" Label="状态">
                        <MudSelectItem Value="@("Draft")">草稿</MudSelectItem>
                        <MudSelectItem Value="@("Testing")">测试</MudSelectItem>
                        <MudSelectItem Value="@("Released")">已发布</MudSelectItem>
                        <MudSelectItem Value="@("Archived")">已归档</MudSelectItem>
                    </MudSelect>
                </MudItem>
            </MudGrid>
        </MudContainer>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="Cancel">取消</MudButton>
        <MudButton Color="Color.Primary" 
                  OnClick="Submit" 
                  Disabled="@(!IsValid())">
            保存
        </MudButton>
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter] MudDialogInstance MudDialog { get; set; } = null!;
    [Parameter] public AppVersion Version { get; set; } = new();

    private AppVersion version = new();

    protected override void OnInitialized()
    {
        // 创建副本以避免直接修改原对象
        version = new AppVersion
        {
            Id = Version.Id,
            VersionNumber = Version.VersionNumber,
            VersionCode = Version.VersionCode,
            Platform = Version.Platform,
            Title = Version.Title,
            Description = Version.Description,
            UpdateType = Version.UpdateType,
            IsForceUpdate = Version.IsForceUpdate,
            DownloadUrl = Version.DownloadUrl,
            FileSize = Version.FileSize,
            FileMd5 = Version.FileMd5,
            MinSupportedVersionCode = Version.MinSupportedVersionCode,
            Status = Version.Status,
            TargetAudience = Version.TargetAudience,
            TargetDepartmentIds = Version.TargetDepartmentIds,
            ReleaseTime = Version.ReleaseTime,
            CreatedAt = Version.CreatedAt,
            CreatedBy = Version.CreatedBy,
            UpdatedAt = Version.UpdatedAt,
            UpdatedBy = Version.UpdatedBy,
            IsEnabled = Version.IsEnabled,
            Remark = Version.Remark
        };
    }

    private void Cancel()
    {
        MudDialog.Cancel();
    }

    private async Task Submit()
    {
        try
        {
            version.UpdatedBy = 1; // 临时设置，实际应该从当前用户获取
            var (isSuccess, errorMessage) = await UpdateService.UpdateVersionAsync(version);
            
            if (isSuccess)
            {
                Snackbar.Add("版本更新成功", Severity.Success);
                MudDialog.Close(DialogResult.Ok(version));
            }
            else
            {
                Snackbar.Add($"更新失败: {errorMessage}", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"更新失败: {ex.Message}", Severity.Error);
        }
    }

    private bool IsValid()
    {
        return !string.IsNullOrWhiteSpace(version.VersionNumber) &&
               version.VersionCode > 0 &&
               !string.IsNullOrWhiteSpace(version.Platform) &&
               !string.IsNullOrWhiteSpace(version.Title) &&
               !string.IsNullOrWhiteSpace(version.DownloadUrl) &&
               version.FileSize > 0;
    }
}
