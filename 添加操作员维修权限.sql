-- 添加操作员角色的维修权限
-- 这个脚本用于修复维修仪表板没有数据的问题

USE [CoreHub]
GO

-- 检查并添加操作员角色对整理部的维修权限
IF NOT EXISTS (
    SELECT 1 FROM RoleDepartmentPermissions rdp
    INNER JOIN Roles r ON rdp.RoleId = r.Id
    INNER JOIN Departments d ON rdp.DepartmentId = d.Id
    WHERE r.Code = 'Operator' 
    AND d.Code = 'ZLB' 
    AND rdp.PermissionType = 3  -- 可维修设备
)
BEGIN
    INSERT INTO [dbo].[RoleDepartmentPermissions] ([RoleId], [DepartmentId], [PermissionType], [IsEnabled])
    SELECT r.Id, d.Id, 3, 1 -- 可维修设备
    FROM Roles r, Departments d
    WHERE r.Code = 'Operator' AND d.Code = 'ZLB'
    
    PRINT '已添加操作员角色对整理部的维修权限'
END
ELSE
BEGIN
    PRINT '操作员角色对整理部的维修权限已存在'
END

-- 验证权限是否添加成功
SELECT 
    r.Name AS RoleName,
    d.Name AS DepartmentName,
    rdp.PermissionType,
    CASE rdp.PermissionType
        WHEN 1 THEN '可报修设备'
        WHEN 2 THEN '可接收报修'
        WHEN 3 THEN '可维修设备'
        ELSE '未知权限'
    END AS PermissionTypeName,
    rdp.IsEnabled
FROM RoleDepartmentPermissions rdp
INNER JOIN Roles r ON rdp.RoleId = r.Id
INNER JOIN Departments d ON rdp.DepartmentId = d.Id
WHERE r.Code = 'Operator'
ORDER BY d.Name, rdp.PermissionType

GO
