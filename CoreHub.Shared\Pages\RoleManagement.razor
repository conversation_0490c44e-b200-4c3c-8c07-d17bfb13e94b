@page "/roles"
@using CoreHub.Shared.Services
@using CoreHub.Shared.Models.Database
@using CoreHub.Shared.Components
@inject IUserManagementService UserManagementService
@inject ISnackbar Snackbar
@inject IDialogService DialogService

<PageTitle>角色管理</PageTitle>

<PermissionView RequiredPermission="RoleManagement.View">
    <ChildContent>
        <MudContainer MaxWidth="MaxWidth.ExtraExtraLarge">
            <MudPaper Elevation="2" Class="pa-4">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                    <MudText Typo="Typo.h4" Color="Color.Primary">角色管理</MudText>
                    <MudSpacer />
                    <div style="display: flex; gap: 12px; align-items: center;">
                        <MudTextField @bind-Value="searchKeyword"
                                      @onkeypress="OnSearchKeyPress"
                                      Label="搜索角色名称"
                                      Variant="Variant.Outlined"
                                      Adornment="Adornment.End"
                                      AdornmentIcon="@Icons.Material.Filled.Search"
                                      Style="width: 300px;" />
                        <MudButton OnClick="SearchRoles" 
                                   Color="Color.Primary" 
                                   Variant="Variant.Outlined"
                                   StartIcon="@Icons.Material.Filled.Search">
                            搜索
                        </MudButton>
                        <MudButton OnClick="LoadRoles" 
                                   Color="Color.Default" 
                                   Variant="Variant.Outlined"
                                   StartIcon="@Icons.Material.Filled.Refresh">
                            刷新
                        </MudButton>
                        <PermissionView RequiredPermission="RoleManagement.Create">
                            <MudButton Variant="Variant.Filled" 
                                       Color="Color.Primary" 
                                       StartIcon="@Icons.Material.Filled.Add"
                                       OnClick="ShowCreateModal">
                                新增角色
                            </MudButton>
                        </PermissionView>
                    </div>
                </div>

                @if (isLoading)
                {
                    <div style="display: flex; justify-content: center; padding: 40px;">
                        <MudProgressCircular Color="Color.Primary" Indeterminate="true" />
                    </div>
                }
                else
                {
                    <MudDataGrid Items="@roles" 
                                 Hover="true" 
                                 Striped="true"
                                 Dense="true"
                                 Elevation="0">
                        <Columns>
                            <PropertyColumn Property="x => x.Code" Title="角色编码">
                                <CellTemplate>
                                    <MudText Typo="Typo.body2" Style="font-family: monospace;">@context.Item.Code</MudText>
                                </CellTemplate>
                            </PropertyColumn>
                            
                            <PropertyColumn Property="x => x.Name" Title="角色名称" />
                            
                            <PropertyColumn Property="x => x.Description" Title="描述" />
                            
                            <PropertyColumn Property="x => x.SortOrder" Title="排序" />
                            
                            <PropertyColumn Property="x => x.IsEnabled" Title="状态">
                                <CellTemplate>
                                    @if (context.Item.IsEnabled)
                                    {
                                        <MudChip T="string" Color="Color.Success" Size="Size.Small">启用</MudChip>
                                    }
                                    else
                                    {
                                        <MudChip T="string" Color="Color.Error" Size="Size.Small">禁用</MudChip>
                                    }
                                </CellTemplate>
                            </PropertyColumn>
                            
                            <PropertyColumn Property="x => x.IsSystem" Title="系统角色">
                                <CellTemplate>
                                    @if (context.Item.IsSystem)
                                    {
                                        <MudChip T="string" Color="Color.Info" Size="Size.Small">系统角色</MudChip>
                                    }
                                    else
                                    {
                                        <MudChip T="string" Color="Color.Default" Size="Size.Small">自定义</MudChip>
                                    }
                                </CellTemplate>
                            </PropertyColumn>
                            
                            <PropertyColumn Property="x => x.CreatedAt" Title="创建时间">
                                <CellTemplate>
                                    <MudText Typo="Typo.body2">@context.Item.CreatedAt.ToString("yyyy-MM-dd HH:mm")</MudText>
                                </CellTemplate>
                            </PropertyColumn>
                            
                            <TemplateColumn Title="操作" Sortable="false">
                                <CellTemplate>
                                    <div style="display: flex; gap: 4px;">
                                        <MudIconButton Icon="@Icons.Material.Filled.Visibility"
                                                      Size="Size.Small"
                                                      Color="Color.Info"
                                                      title="查看详情"
                                                      OnClick="() => ViewRoleDetail(context.Item)" />
                                        
                                        <PermissionView RequiredPermission="RoleManagement.Edit">
                                            <MudIconButton Icon="@Icons.Material.Filled.Edit"
                                                          Size="Size.Small"
                                                          Color="Color.Warning"
                                                          title="编辑"
                                                          OnClick="() => ShowEditModal(context.Item)" />
                                        </PermissionView>
                                        
                                        @if (!context.Item.IsSystem)
                                        {
                                            <PermissionView RequiredPermission="RoleManagement.Delete">
                                                <MudIconButton Icon="@Icons.Material.Filled.Delete"
                                                              Size="Size.Small"
                                                              Color="Color.Error"
                                                              title="删除"
                                                              OnClick="() => DeleteRole(context.Item)" />
                                            </PermissionView>
                                        }
                                        
                                        <PermissionView RequiredPermission="RoleManagement.AssignPermissions">
                                            <MudIconButton Icon="@Icons.Material.Filled.Key"
                                                          Size="Size.Small"
                                                          Color="Color.Success"
                                                          title="权限配置"
                                                          OnClick="() => ShowPermissionModal(context.Item)" />
                                        </PermissionView>
                                    </div>
                                </CellTemplate>
                            </TemplateColumn>
                        </Columns>
                    </MudDataGrid>
                }

                @if (roles.Count == 0 && !isLoading)
                {
                    <div style="display: flex; flex-direction: column; align-items: center; padding: 60px 20px;">
                        <MudIcon Icon="@Icons.Material.Filled.Group" 
                                 Size="Size.Large" 
                                 Color="Color.Default" 
                                 Style="font-size: 4rem; margin-bottom: 16px;" />
                        <MudText Typo="Typo.h6" Color="Color.Default">没有找到角色数据</MudText>
                    </div>
                }
            </MudPaper>
        </MudContainer>
    </ChildContent>
    
    <NotAuthorized>
        <MudContainer MaxWidth="MaxWidth.Medium">
            <MudPaper Elevation="2" Class="pa-8" Style="text-align: center;">
                <MudIcon Icon="@Icons.Material.Filled.Lock" 
                         Size="Size.Large" 
                         Color="Color.Error" 
                         Style="font-size: 4rem; margin-bottom: 16px;" />
                <MudText Typo="Typo.h4" Color="Color.Error" Class="mb-4">权限不足</MudText>
                <MudText Typo="Typo.body1" Color="Color.Default">您没有权限访问角色管理页面</MudText>
            </MudPaper>
        </MudContainer>
    </NotAuthorized>
</PermissionView>

<!-- 创建/编辑角色对话框 -->
@if (showModal)
{
    <MudOverlay @bind-Visible="showModal" DarkBackground="true" Absolute="false">
        <MudPaper Class="pa-6" Style="width: 600px; max-width: 90vw; max-height: 90vh; overflow-y: auto; margin: 20px auto; position: relative;">
            <div style="display: flex; align-items: center; margin-bottom: 20px;">
                <MudIcon Icon="@(isEditMode ? Icons.Material.Filled.Edit : Icons.Material.Filled.Add)" Class="mr-3" />
                <MudText Typo="Typo.h5">@(isEditMode ? "编辑角色" : "新增角色")</MudText>
                <MudSpacer />
                <MudIconButton Icon="@Icons.Material.Filled.Close" OnClick="HideModal" />
            </div>

            <EditForm Model="currentRole" OnValidSubmit="HandleRoleSubmit">
                <DataAnnotationsValidator />
                
                <MudGrid>
                    <MudItem xs="12">
                        <MudTextField @bind-Value="currentRole.Code"
                                      Label="角色编码"
                                      Required="true"
                                      RequiredError="请输入角色编码"
                                      Disabled="@(isEditMode && currentRole.IsSystem)"
                                      HelperText="角色编码用于系统内部识别，建议使用英文"
                                      Variant="Variant.Outlined" />
                    </MudItem>
                    
                    <MudItem xs="12">
                        <MudTextField @bind-Value="currentRole.Name"
                                      Label="角色名称"
                                      Required="true"
                                      RequiredError="请输入角色名称"
                                      Variant="Variant.Outlined" />
                    </MudItem>
                    
                    <MudItem xs="12">
                        <MudTextField @bind-Value="currentRole.Description"
                                      Label="角色描述"
                                      Lines="3"
                                      Variant="Variant.Outlined" />
                    </MudItem>
                    
                    <MudItem xs="12" sm="6">
                        <MudNumericField T="int" @bind-Value="currentRole.SortOrder"
                                         Label="排序号"
                                         HelperText="数字越小排序越靠前"
                                         Variant="Variant.Outlined" />
                    </MudItem>
                    
                    <MudItem xs="12" sm="6">
                        <MudCheckBox @bind-Value="currentRole.IsEnabled"
                                     Label="启用角色"
                                     Color="Color.Primary" />
                    </MudItem>
                    
                    <MudItem xs="12">
                        <MudTextField @bind-Value="currentRole.Remark"
                                      Label="备注"
                                      Lines="2"
                                      Variant="Variant.Outlined" />
                    </MudItem>
                </MudGrid>
                
                <ValidationSummary />
                
                <div style="display: flex; justify-content: flex-end; gap: 12px; margin-top: 24px;">
                    <MudButton OnClick="HideModal" Color="Color.Default">取消</MudButton>
                    <MudButton ButtonType="ButtonType.Submit" 
                               Color="Color.Primary" 
                               Variant="Variant.Filled"
                               Disabled="@isSaving">
                        @if (isSaving)
                        {
                            <MudProgressCircular Class="ms-n1" Size="Size.Small" Indeterminate="true" />
                            <MudText Class="ms-2">@(isEditMode ? "更新" : "创建")中...</MudText>
                        }
                        else
                        {
                            <MudText>@(isEditMode ? "更新" : "创建")</MudText>
                        }
                    </MudButton>
                </div>
            </EditForm>
        </MudPaper>
    </MudOverlay>
}

<!-- 权限配置对话框 -->
@if (showPermissionModal)
{
    <MudOverlay @bind-Visible="showPermissionModal" DarkBackground="true" Absolute="false">
        <MudPaper Class="pa-6" Style="width: 1000px; max-width: 90vw; max-height: 90vh; overflow-y: auto; margin: 20px auto; position: relative;">
            <div style="display: flex; align-items: center; margin-bottom: 20px;">
                <MudIcon Icon="@Icons.Material.Filled.Key" Class="mr-3" />
                <MudText Typo="Typo.h5">权限配置 - @selectedRole?.Name</MudText>
                <MudSpacer />
                <MudIconButton Icon="@Icons.Material.Filled.Close" OnClick="HidePermissionModal" />
            </div>

            @if (isLoadingPermissions)
            {
                <div style="display: flex; justify-content: center; padding: 40px;">
                    <MudProgressCircular Color="Color.Primary" Indeterminate="true" />
                </div>
            }
            else
            {
                <MudGrid>
                    @foreach (var module in permissionsByModule)
                    {
                        <MudItem xs="12" md="6">
                            <MudCard>
                                <MudCardHeader>
                                    <CardHeaderContent>
                                        <MudText Typo="Typo.h6">@module.Key 模块</MudText>
                                    </CardHeaderContent>
                                </MudCardHeader>
                                <MudCardContent>
                                    @foreach (var permission in module.Value)
                                    {
                                        <div style="margin-bottom: 8px;">
                                            <MudCheckBox Value="@rolePermissions.Contains(permission.Id)"
                                                         ValueChanged="@((bool value) => TogglePermission(permission.Id, value))"
                                                         Label="@permission.Name"
                                                         Color="Color.Primary" />
                                            <MudText Typo="Typo.caption" Color="Color.Default" Class="ml-6">@permission.Description</MudText>
                                        </div>
                                    }
                                </MudCardContent>
                            </MudCard>
                        </MudItem>
                    }
                </MudGrid>
            }
            
            <div style="display: flex; justify-content: flex-end; gap: 12px; margin-top: 24px;">
                <MudButton OnClick="HidePermissionModal" Color="Color.Default">取消</MudButton>
                <MudButton Color="Color.Primary" 
                           Variant="Variant.Filled"
                           Disabled="@isSavingPermissions"
                           OnClick="SaveRolePermissions">
                    @if (isSavingPermissions)
                    {
                        <MudProgressCircular Class="ms-n1" Size="Size.Small" Indeterminate="true" />
                        <MudText Class="ms-2">保存权限中...</MudText>
                    }
                    else
                    {
                        <MudText>保存权限</MudText>
                    }
                </MudButton>
            </div>
        </MudPaper>
    </MudOverlay>
}

@code {
    private List<Role> roles = new();
    private List<Permission> allPermissions = new();
    private Dictionary<string, List<Permission>> permissionsByModule = new();
    private HashSet<int> rolePermissions = new();
    private bool isLoading = false;
    private bool isLoadingPermissions = false;
    private bool isSaving = false;
    private bool isSavingPermissions = false;
    private string searchKeyword = "";
    private bool showModal = false;
    private bool showPermissionModal = false;
    private bool isEditMode = false;
    private Role currentRole = new Role();
    private Role? selectedRole;

    protected override async Task OnInitializedAsync()
    {
        await LoadRoles();
        await LoadPermissions();
    }

    private async Task LoadRoles()
    {
        try
        {
            isLoading = true;
            StateHasChanged();

            roles = await UserManagementService.GetAllRolesAsync();
            @* Snackbar.Add($"加载了 {roles.Count} 个角色", Severity.Success); *@
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载角色失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task LoadPermissions()
    {
        try
        {
            allPermissions = await UserManagementService.GetAllPermissionsAsync();
            permissionsByModule = allPermissions
                .GroupBy(p => p.Module)
                .ToDictionary(g => g.Key, g => g.OrderBy(p => p.SortOrder).ToList());
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载权限失败: {ex.Message}", Severity.Error);
        }
    }

    private async Task SearchRoles()
    {
        try
        {
            isLoading = true;
            StateHasChanged();

            if (string.IsNullOrWhiteSpace(searchKeyword))
            {
                await LoadRoles();
                return;
            }

            roles = (await UserManagementService.GetAllRolesAsync())
                .Where(r => r.Name.Contains(searchKeyword, StringComparison.OrdinalIgnoreCase) ||
                           r.Code.Contains(searchKeyword, StringComparison.OrdinalIgnoreCase))
                .ToList();

            Snackbar.Add($"搜索到 {roles.Count} 个角色", Severity.Info);
        }
        catch (Exception ex)
        {
            Snackbar.Add($"搜索失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task OnSearchKeyPress(KeyboardEventArgs e)
    {
        if (e.Key == "Enter")
        {
            await SearchRoles();
        }
    }

    private void ShowCreateModal()
    {
        currentRole = new Role { IsEnabled = true, SortOrder = 0 };
        isEditMode = false;
        showModal = true;
        StateHasChanged();
    }

    private void ShowEditModal(Role role)
    {
        currentRole = new Role
        {
            Id = role.Id,
            Code = role.Code,
            Name = role.Name,
            Description = role.Description,
            SortOrder = role.SortOrder,
            IsEnabled = role.IsEnabled,
            IsSystem = role.IsSystem,
            Remark = role.Remark
        };
        isEditMode = true;
        showModal = true;
        StateHasChanged();
    }

    private void HideModal()
    {
        showModal = false;
        currentRole = new Role();
        isEditMode = false;
        isSaving = false;
        StateHasChanged();
    }

    private async Task HandleRoleSubmit()
    {
        await SaveRole();
    }

    private async Task SaveRole()
    {
        try
        {
            isSaving = true;
            StateHasChanged();

            if (isEditMode)
            {
                var result = await UserManagementService.UpdateRoleAsync(currentRole);
                if (result.IsSuccess)
                {
                    Snackbar.Add("角色更新成功", Severity.Success);
                    HideModal();
                    await LoadRoles();
                }
                else
                {
                    Snackbar.Add($"角色更新失败: {result.ErrorMessage}", Severity.Error);
                }
            }
            else
            {
                var result = await UserManagementService.CreateRoleAsync(currentRole);
                if (result.IsSuccess)
                {
                    Snackbar.Add("角色创建成功", Severity.Success);
                    HideModal();
                    await LoadRoles();
                }
                else
                {
                    Snackbar.Add($"角色创建失败: {result.ErrorMessage}", Severity.Error);
                }
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"保存角色失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            isSaving = false;
            StateHasChanged();
        }
    }

    private async Task DeleteRole(Role role)
    {
        var result = await DialogService.ShowMessageBox(
            "确认删除",
            $"确定要删除角色 '{role.Name}' 吗？此操作不可恢复。",
            yesText: "删除", cancelText: "取消");

        if (result == true)
        {
            try
            {
                var deleteResult = await UserManagementService.DeleteRoleAsync(role.Id);
                if (deleteResult.IsSuccess)
                {
                    Snackbar.Add("角色删除成功", Severity.Success);
                    await LoadRoles();
                }
                else
                {
                    Snackbar.Add($"角色删除失败: {deleteResult.ErrorMessage}", Severity.Error);
                }
            }
            catch (Exception ex)
            {
                Snackbar.Add($"删除角色失败: {ex.Message}", Severity.Error);
            }
        }
    }

    private async Task ShowPermissionModal(Role role)
    {
        selectedRole = role;
        isLoadingPermissions = true;
        showPermissionModal = true;
        StateHasChanged();

        try
        {
            var permissions = await UserManagementService.GetRolePermissionsAsync(role.Id);
            rolePermissions = permissions.Select(p => p.Id).ToHashSet();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载角色权限失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            isLoadingPermissions = false;
            StateHasChanged();
        }
    }

    private void HidePermissionModal()
    {
        showPermissionModal = false;
        selectedRole = null;
        rolePermissions.Clear();
        isLoadingPermissions = false;
        isSavingPermissions = false;
        StateHasChanged();
    }

    private void TogglePermission(int permissionId, bool isChecked)
    {
        if (isChecked)
        {
            rolePermissions.Add(permissionId);
        }
        else
        {
            rolePermissions.Remove(permissionId);
        }
    }

    private async Task SaveRolePermissions()
    {
        if (selectedRole == null) return;

        try
        {
            isSavingPermissions = true;
            StateHasChanged();

            var result = await UserManagementService.UpdateRolePermissionsAsync(selectedRole.Id, rolePermissions.ToList());
            if (result.IsSuccess)
            {
                Snackbar.Add("权限配置保存成功", Severity.Success);
                HidePermissionModal();
            }
            else
            {
                Snackbar.Add($"权限配置保存失败: {result.ErrorMessage}", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"保存权限配置失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            isSavingPermissions = false;
            StateHasChanged();
        }
    }

    private void ViewRoleDetail(Role role)
    {
        Snackbar.Add($"角色详情 - {role.Name}: 编码={role.Code}, 描述={role.Description}, 排序={role.SortOrder}", Severity.Info);
    }
}