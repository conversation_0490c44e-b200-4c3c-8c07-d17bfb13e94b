using Android.Content;
using Microsoft.Maui.Platform;
using MauiScanManager.Services;
using System.Threading.Tasks;
using Java.Lang;
using Android.Graphics;

namespace MauiScanManager.Platforms.Android.Services;

public class PrintService : IPrintService
{
    private Java.Lang.Object _printUtil;
    private readonly IServiceProvider _serviceProvider;
    private bool _isInitialized;
    private bool _isBlackMarkMode = false; // 当前黑标模式状态

    // 默认参数 - 基于demo代码
    private const int DEFAULT_FEED_PAPER_SPACE = 1000; // 走纸距离1000mm（demo默认值）
    private int _defaultUnwindPaperLen = 60; // 回纸长度，初始化时从SDK获取

    // 用于等待打印结果的TaskCompletionSource
    private TaskCompletionSource<PrintEventArgs> _currentPrintTask;
    private readonly object _printLock = new object();

    public event EventHandler<PrintEventArgs> OnPrintResult;
    public bool IsReady => _isInitialized && _printUtil != null;

    public PrintService(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
    }

    public void Initialize()
    {
        if (_isInitialized && _printUtil != null) return;

        try
        {
            System.Diagnostics.Debug.WriteLine("正在初始化打印服务...");
            
            var context = Platform.CurrentActivity ?? Microsoft.Maui.ApplicationModel.Platform.CurrentActivity;
            if (context == null)
            {
                System.Diagnostics.Debug.WriteLine("无法获取Android Context");
                return;
            }

            // 使用反射获取SDK类 - 基于demo代码的包名
            try
            {
                // 根据demo代码：import com.example.lc_print_sdk.PrintUtil;
                var printUtilClass = Java.Lang.Class.ForName("com.example.lc_print_sdk.PrintUtil");
                
                // 调用getInstance方法获取实例
                var getInstanceMethod = printUtilClass.GetMethod("getInstance", Java.Lang.Class.FromType(typeof(Context)));
                _printUtil = getInstanceMethod.Invoke(null, context) as Java.Lang.Object;
                
                if (_printUtil != null)
                {
                    System.Diagnostics.Debug.WriteLine("PrintUtil实例创建成功");
                    
                    // 设置打印事件监听器
                    SetPrintEventListener();
                    
                    // 获取SDK默认的回纸长度
                    try
                    {
                        var getUnwindPaperLenMethod = printUtilClass.GetMethod("getUnwindPaperLen");
                        if (getUnwindPaperLenMethod != null)
                        {
                            var result = getUnwindPaperLenMethod.Invoke(_printUtil);
                            if (result != null)
                            {
                                _defaultUnwindPaperLen = (int)result;
                                System.Diagnostics.Debug.WriteLine($"获取SDK默认回纸长度: {_defaultUnwindPaperLen}mm");
                            }
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine("未找到getUnwindPaperLen方法，使用默认值60mm");
                            _defaultUnwindPaperLen = 60;
                        }
                    }
                    catch (System.Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"获取默认回纸长度失败: {ex.Message}");
                        if (ex.InnerException != null)
                        {
                            System.Diagnostics.Debug.WriteLine($"内部异常: {ex.InnerException.Message}");
                        }
                        // 使用默认值
                        _defaultUnwindPaperLen = 60;
                        System.Diagnostics.Debug.WriteLine("使用默认回纸长度: 60mm");
                    }
                    
                    _isInitialized = true;
                    System.Diagnostics.Debug.WriteLine("打印服务初始化完成");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("PrintUtil实例创建失败");
                }
            }
            catch (System.Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载PrintUtil类失败: {ex.Message}");
                if (ex.InnerException != null)
                {
                    System.Diagnostics.Debug.WriteLine($"内部异常: {ex.InnerException.Message}");
                }
            }
        }
        catch (System.Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"打印服务初始化失败: {ex.Message}");
            if (ex.InnerException != null)
            {
                System.Diagnostics.Debug.WriteLine($"内部异常: {ex.InnerException.Message}");
            }
            _isInitialized = false;
        }
    }

    private void SetPrintEventListener()
    {
        try
        {
            if (_printUtil != null)
            {
                // 基于demo代码: printUtil.setPrintEventListener(this);
                var printUtilClass = _printUtil.Class;
                
                // 获取PrinterBinderListener接口类
                var printerBinderListenerClass = Java.Lang.Class.ForName("com.example.lc_print_sdk.PrintUtil$PrinterBinderListener");
                var setPrintEventListenerMethod = printUtilClass.GetMethod("setPrintEventListener", printerBinderListenerClass);
                
                if (setPrintEventListenerMethod != null)
                {
                    // 创建Java代理对象来实现PrinterBinderListener接口
                    var listenerProxy = Java.Lang.Reflect.Proxy.NewProxyInstance(
                        printerBinderListenerClass.ClassLoader,
                        new Java.Lang.Class[] { printerBinderListenerClass },
                        new PrinterBinderListenerInvocationHandler(this)
                    );
                    
                    setPrintEventListenerMethod.Invoke(_printUtil, listenerProxy);
                    System.Diagnostics.Debug.WriteLine("打印事件监听器设置成功");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("未找到setPrintEventListener方法");
                }
            }
        }
        catch (System.Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"设置打印事件监听器失败: {ex.Message}");
            if (ex.InnerException != null)
            {
                System.Diagnostics.Debug.WriteLine($"内部异常: {ex.InnerException.Message}");
            }
        }
    }

    public void Dispose()
    {
        if (_printUtil != null)
        {
            try
            {
                // 基于demo代码: printUtil.removePrintListener(this);
                var printUtilClass = _printUtil.Class;
                var removePrintListenerMethod = printUtilClass.GetMethod("removePrintListener", Java.Lang.Class.FromType(typeof(Java.Lang.Object)));
                if (removePrintListenerMethod != null)
                {
                    // 传递null来移除所有监听器
                    removePrintListenerMethod.Invoke(_printUtil, (Java.Lang.Object)null);
                    System.Diagnostics.Debug.WriteLine("打印监听器移除成功");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("未找到removePrintListener方法");
                }
            }
            catch (System.Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"释放打印资源失败: {ex.Message}");
            }
            _printUtil = null;
        }
        _isInitialized = false;
    }

    public async Task<bool> PrintTextAsync(string text)
    {
        return await PrintTextAsync(text, new PrintConfig());
    }

    public async Task<bool> PrintTextAsync(string text, PrintConfig config)
    {
        return await PrintTextAsync(text, config, false);
    }

    public async Task<bool> PrintTextAsync(string text, PrintConfig config, bool enableBlackMark)
    {
        return await PrintTextAsync(text, config, enableBlackMark, DEFAULT_FEED_PAPER_SPACE, _defaultUnwindPaperLen);
    }

    public async Task<bool> PrintTextAsync(string text, PrintConfig config, bool enableBlackMark, int feedPaperSpace, int unwindPaperLen)
    {
        if (!EnsureServiceReady())
        {
            return false;
        }

        try
        {
            System.Diagnostics.Debug.WriteLine($"开始打印文本: {text}, 黑标模式: {enableBlackMark}");
            
            // 设置打印模式 - 基于demo代码
            SetPrintMode(enableBlackMark);

            // 如果是黑标模式，设置走纸参数 - 基于demo代码
            if (enableBlackMark)
            {
                SetFeedPaperSpace(feedPaperSpace);
                SetUnwindPaperLen(unwindPaperLen);
                System.Diagnostics.Debug.WriteLine($"设置走纸距离: {feedPaperSpace}mm, 回纸长度: {unwindPaperLen}mm");
            }

            // 设置浓度 - 基于demo代码（注意：demo中这行被注释了，可能有问题）
            // SetPrintConcentration(25); // 暂时注释掉，参考demo代码
            
            // 调用printText方法 - 基于demo代码
            int align = ConvertTextAlign(config.Align);
            int fontSize = ConvertFontSize(config.Size);
            
            var printUtilClass = _printUtil.Class;
            var printTextMethod = printUtilClass.GetMethod("printText", 
                Java.Lang.Integer.Type, Java.Lang.Integer.Type, Java.Lang.Boolean.Type, Java.Lang.Boolean.Type, Java.Lang.Class.FromType(typeof(Java.Lang.String)));
            
            printTextMethod.Invoke(_printUtil, 
                Java.Lang.Integer.ValueOf(align), 
                Java.Lang.Integer.ValueOf(fontSize), 
                Java.Lang.Boolean.ValueOf(config.Bold), 
                Java.Lang.Boolean.ValueOf(config.Underline), 
                new Java.Lang.String(text));
            
            // 根据模式选择走纸方式 - 基于demo代码和开发指南
            System.Diagnostics.Debug.WriteLine("开始走纸操作...");
            if (enableBlackMark)
            {
                // 黑标模式：调用printGoToNextMark走纸
                var printGoToNextMarkMethod = printUtilClass.GetMethod("printGoToNextMark", Java.Lang.Integer.Type);
                printGoToNextMarkMethod.Invoke(_printUtil, Java.Lang.Integer.ValueOf(feedPaperSpace));
                System.Diagnostics.Debug.WriteLine("调用printGoToNextMark走纸完成");
            }
            else
            {
                // 无标签模式：添加空行后调用start
                var printLineMethod = printUtilClass.GetMethod("printLine", Java.Lang.Integer.Type);
                printLineMethod.Invoke(_printUtil, Java.Lang.Integer.ValueOf(5));
                
                var startMethod = printUtilClass.GetMethod("start");
                startMethod.Invoke(_printUtil);
                System.Diagnostics.Debug.WriteLine("调用start开始打印完成");
            }
            
            System.Diagnostics.Debug.WriteLine("调用SDK printText成功，等待回调...");
            return true;
        }
        catch (System.Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"打印文本失败: {ex.Message}");
            // 不触发OnPrintResult事件，直接返回false，让SDK回调处理所有错误
            return false;
        }
    }

    public async Task<bool> PrintBarcodeAsync(string content, BarcodeType barcodeType = BarcodeType.Code128, bool enableBlackMark = false)
    {
        return await PrintBarcodeAsync(content, barcodeType, enableBlackMark, DEFAULT_FEED_PAPER_SPACE, _defaultUnwindPaperLen);
    }

    public async Task<bool> PrintBarcodeAsync(string content, BarcodeType barcodeType, bool enableBlackMark, int feedPaperSpace, int unwindPaperLen)
    {
        return await PrintBarcodeAsync(content, barcodeType, HRIPosition.Below, enableBlackMark, feedPaperSpace, unwindPaperLen);
    }

    public async Task<bool> PrintBarcodeAsync(string content, BarcodeType barcodeType, HRIPosition hriPosition, bool enableBlackMark = false)
    {
        return await PrintBarcodeAsync(content, barcodeType, hriPosition, enableBlackMark, DEFAULT_FEED_PAPER_SPACE, _defaultUnwindPaperLen);
    }

    public async Task<bool> PrintBarcodeAsync(string content, BarcodeType barcodeType, HRIPosition hriPosition, bool enableBlackMark, int feedPaperSpace, int unwindPaperLen)
    {
        if (!EnsureServiceReady())
        {
            return false;
        }

        if (string.IsNullOrEmpty(content))
        {
            System.Diagnostics.Debug.WriteLine("条码内容为空");
            // 不触发OnPrintResult事件，直接返回false，让SDK回调处理所有错误
            return false;
        }

        try
        {
            System.Diagnostics.Debug.WriteLine($"开始打印条码: {content}, 类型: {barcodeType}, HRI位置: {hriPosition}, 黑标模式: {enableBlackMark}");
            
            // 设置打印模式 - 基于demo代码
            SetPrintMode(enableBlackMark);

            // 如果是黑标模式，设置走纸参数 - 基于demo代码
            if (enableBlackMark)
            {
                SetFeedPaperSpace(feedPaperSpace);
                SetUnwindPaperLen(unwindPaperLen);
                System.Diagnostics.Debug.WriteLine($"设置走纸距离: {feedPaperSpace}mm, 回纸长度: {unwindPaperLen}mm");
            }

            // 设置浓度 - 基于demo代码（注意：demo中这行被注释了，可能有问题）
            // SetPrintConcentration(25); // 暂时注释掉，参考demo代码
            
            // 调用printBarcode方法 - 基于demo代码和开发指南5.23
            int sdkBarcodeType = ConvertBarcodeType(barcodeType);
            int alignCenter = GetAlignConstant("ALIGN_CENTER");
            int hriPositionValue = ConvertHRIPosition(hriPosition);
            
            var printUtilClass = _printUtil.Class;
            var printBarcodeMethod = printUtilClass.GetMethod("printBarcode", 
                Java.Lang.Integer.Type, Java.Lang.Integer.Type, Java.Lang.Class.FromType(typeof(Java.Lang.String)), 
                Java.Lang.Integer.Type, Java.Lang.Integer.Type);
            
            printBarcodeMethod.Invoke(_printUtil, 
                Java.Lang.Integer.ValueOf(alignCenter), 
                Java.Lang.Integer.ValueOf(100), 
                new Java.Lang.String(content), 
                Java.Lang.Integer.ValueOf(sdkBarcodeType), 
                Java.Lang.Integer.ValueOf(hriPositionValue));
            
            System.Diagnostics.Debug.WriteLine($"条码参数: align={alignCenter}, height=100, type={sdkBarcodeType}, hri={hriPositionValue}");
            
            // 根据模式选择走纸方式 - 基于demo代码和开发指南
            if (enableBlackMark)
            {
                // 黑标模式：调用printGoToNextMark走纸
                var printGoToNextMarkMethod = printUtilClass.GetMethod("printGoToNextMark", Java.Lang.Integer.Type);
                printGoToNextMarkMethod.Invoke(_printUtil, Java.Lang.Integer.ValueOf(feedPaperSpace));
                System.Diagnostics.Debug.WriteLine("调用printGoToNextMark走纸");
            }
            else
            {
                // 无标签模式：添加空行后调用start
                var printLineMethod = printUtilClass.GetMethod("printLine", Java.Lang.Integer.Type);
                printLineMethod.Invoke(_printUtil, Java.Lang.Integer.ValueOf(6));
                
                var startMethod = printUtilClass.GetMethod("start");
                startMethod.Invoke(_printUtil);
                System.Diagnostics.Debug.WriteLine("调用start开始打印");
            }
            
            System.Diagnostics.Debug.WriteLine("调用SDK printBarcode成功，等待回调...");
            return true;
        }
        catch (System.Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"打印条码失败: {ex.Message}");
            // 不触发OnPrintResult事件，直接返回false，让SDK回调处理所有错误
            return false;
        }
    }

    public async Task<bool> PrintQRCodeAsync(string content, bool enableBlackMark = false)
    {
        return await PrintQRCodeAsync(content, 184, enableBlackMark);
    }

    public async Task<bool> PrintQRCodeAsync(string content, int height, bool enableBlackMark = false)
    {
        return await PrintQRCodeAsync(content, height, enableBlackMark, DEFAULT_FEED_PAPER_SPACE, _defaultUnwindPaperLen);
    }

    public async Task<bool> PrintQRCodeAsync(string content, int height, bool enableBlackMark, int feedPaperSpace, int unwindPaperLen)
    {
        return await PrintQRCodeAsync(content, PrintConfig.TextAlign.Center, height, enableBlackMark, feedPaperSpace, unwindPaperLen);
    }

    public async Task<bool> PrintQRCodeAsync(string content, PrintConfig.TextAlign align, int height = 184, bool enableBlackMark = false)
    {
        return await PrintQRCodeAsync(content, align, height, enableBlackMark, DEFAULT_FEED_PAPER_SPACE, _defaultUnwindPaperLen);
    }

    public async Task<bool> PrintQRCodeAsync(string content, PrintConfig.TextAlign align, int height, bool enableBlackMark, int feedPaperSpace, int unwindPaperLen)
    {
        if (!EnsureServiceReady())
        {
            return false;
        }

        if (string.IsNullOrEmpty(content))
        {
            System.Diagnostics.Debug.WriteLine("二维码内容为空");
            // 不触发OnPrintResult事件，直接返回false，让SDK回调处理所有错误
            return false;
        }

        try
        {
            System.Diagnostics.Debug.WriteLine($"开始打印二维码: {content}, 对齐: {align}, 高度: {height}, 黑标模式: {enableBlackMark}");
            
            // 设置打印模式 - 基于demo代码
            SetPrintMode(enableBlackMark);

            // 如果是黑标模式，设置走纸参数 - 基于demo代码
            if (enableBlackMark)
            {
                SetFeedPaperSpace(feedPaperSpace);
                SetUnwindPaperLen(unwindPaperLen);
                System.Diagnostics.Debug.WriteLine($"设置走纸距离: {feedPaperSpace}mm, 回纸长度: {unwindPaperLen}mm");
            }

            // 设置浓度 - 基于demo代码（注意：demo中这行被注释了，可能有问题）
            // SetPrintConcentration(25); // 暂时注释掉，参考demo代码
            
            // 调用printQR方法 - 基于demo代码和开发指南5.25
            int alignValue = ConvertTextAlign(align);
            
            var printUtilClass = _printUtil.Class;
            var printQRMethod = printUtilClass.GetMethod("printQR", 
                Java.Lang.Integer.Type, Java.Lang.Integer.Type, Java.Lang.Class.FromType(typeof(Java.Lang.String)));
            
            printQRMethod.Invoke(_printUtil, 
                Java.Lang.Integer.ValueOf(alignValue), 
                Java.Lang.Integer.ValueOf(height),
                new Java.Lang.String(content));
            
            System.Diagnostics.Debug.WriteLine($"二维码参数: align={alignValue}, height={height}");
            
            // 根据模式选择走纸方式 - 基于demo代码和开发指南
            if (enableBlackMark)
            {
                // 黑标模式：调用printGoToNextMark走纸
                var printGoToNextMarkMethod = printUtilClass.GetMethod("printGoToNextMark", Java.Lang.Integer.Type);
                printGoToNextMarkMethod.Invoke(_printUtil, Java.Lang.Integer.ValueOf(feedPaperSpace));
                System.Diagnostics.Debug.WriteLine("调用printGoToNextMark走纸");
            }
            else
            {
                // 无标签模式：二维码打印减少空行（避免内容被切分到下一页）
                var printLineMethod = printUtilClass.GetMethod("printLine", Java.Lang.Integer.Type);
                printLineMethod.Invoke(_printUtil, Java.Lang.Integer.ValueOf(2)); // 二维码只需要2行空行
                
                var startMethod = printUtilClass.GetMethod("start");
                startMethod.Invoke(_printUtil);
                System.Diagnostics.Debug.WriteLine("调用start开始打印（二维码）");
            }
            
            System.Diagnostics.Debug.WriteLine("调用SDK printQR成功，等待回调...");
            return true;
        }
        catch (System.Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"打印二维码失败: {ex.Message}");
            // 不触发OnPrintResult事件，直接返回false，让SDK回调处理所有错误
            return false;
        }
    }

    public async Task<bool> PrintBitmapAsync(byte[] bitmapData)
    {
        return await PrintBitmapAsync(bitmapData, false);
    }

    public async Task<bool> PrintBitmapAsync(byte[] bitmapData, bool enableBlackMark)
    {
        return await PrintBitmapAsync(bitmapData, enableBlackMark, DEFAULT_FEED_PAPER_SPACE, _defaultUnwindPaperLen);
    }

    public async Task<bool> PrintBitmapAsync(byte[] bitmapData, bool enableBlackMark, int feedPaperSpace, int unwindPaperLen)
    {
        if (!EnsureServiceReady())
        {
            return false;
        }

        if (bitmapData == null)
        {
            System.Diagnostics.Debug.WriteLine("位图数据为空");
            // 不触发OnPrintResult事件，直接返回false，让SDK回调处理所有错误
            return false;
        }

        try
        {
            System.Diagnostics.Debug.WriteLine($"开始打印图片, 黑标模式: {enableBlackMark}");
            
            var bitmap = BitmapFactory.DecodeByteArray(bitmapData, 0, bitmapData.Length);
            if (bitmap == null)
            {
                System.Diagnostics.Debug.WriteLine("无法解码位图数据");
                // 不触发OnPrintResult事件，直接返回false，让SDK回调处理所有错误
                return false;
            }

            // 设置打印模式 - 基于demo代码
            SetPrintMode(enableBlackMark);

            // 如果是黑标模式，设置走纸参数 - 基于demo代码
            if (enableBlackMark)
            {
                SetFeedPaperSpace(feedPaperSpace);
                SetUnwindPaperLen(unwindPaperLen);
                System.Diagnostics.Debug.WriteLine($"设置走纸距离: {feedPaperSpace}mm, 回纸长度: {unwindPaperLen}mm");
            }

            var printUtilClass = _printUtil.Class;
            var printBitmapMethod = printUtilClass.GetMethod("printBitmap", Java.Lang.Class.FromType(typeof(Bitmap)));
            printBitmapMethod.Invoke(_printUtil, bitmap);
            
            // 根据模式选择走纸方式 - 基于demo代码和开发指南
            if (enableBlackMark)
            {
                // 黑标模式：调用printGoToNextMark走纸
                var printGoToNextMarkMethod = printUtilClass.GetMethod("printGoToNextMark", Java.Lang.Integer.Type);
                printGoToNextMarkMethod.Invoke(_printUtil, Java.Lang.Integer.ValueOf(feedPaperSpace));
                System.Diagnostics.Debug.WriteLine("调用printGoToNextMark走纸");
            }
            else
            {
                // 无标签模式：添加空行后调用start
                var printLineMethod = printUtilClass.GetMethod("printLine", Java.Lang.Integer.Type);
                printLineMethod.Invoke(_printUtil, Java.Lang.Integer.ValueOf(6));
                
                var startMethod = printUtilClass.GetMethod("start");
                startMethod.Invoke(_printUtil);
                System.Diagnostics.Debug.WriteLine("调用start开始打印");
            }
            
            System.Diagnostics.Debug.WriteLine("调用SDK printBitmap成功");
            return true;
        }
        catch (System.Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"打印图片失败: {ex.Message}");
            // 不触发OnPrintResult事件，直接返回false，让SDK回调处理所有错误
            return false;
        }
    }

    public async Task<bool> PrintTemplateAsync(PrintTemplate template)
    {
        return await PrintTemplateAsync(template, false);
    }

    public async Task<bool> PrintTemplateAsync(PrintTemplate template, bool enableBlackMark)
    {
        return await PrintTemplateAsync(template, enableBlackMark, DEFAULT_FEED_PAPER_SPACE, _defaultUnwindPaperLen);
    }

    public async Task<bool> PrintTemplateAsync(PrintTemplate template, bool enableBlackMark, int feedPaperSpace, int unwindPaperLen)
    {
        if (!EnsureServiceReady())
        {
            return false;
        }

        if (template == null)
        {
            System.Diagnostics.Debug.WriteLine("模板为空");
            // 不触发OnPrintResult事件，直接返回false，让SDK回调处理所有错误
            return false;
        }

        try
        {
            System.Diagnostics.Debug.WriteLine($"开始打印模板: {template.Title}, 黑标模式: {enableBlackMark}");

            // 设置打印模式 - 基于demo代码
            SetPrintMode(enableBlackMark);

            // 如果是黑标模式，设置走纸参数 - 基于demo代码
            if (enableBlackMark)
            {
                SetFeedPaperSpace(feedPaperSpace);
                SetUnwindPaperLen(unwindPaperLen);
                System.Diagnostics.Debug.WriteLine($"设置走纸距离: {feedPaperSpace}mm, 回纸长度: {unwindPaperLen}mm");
            }

            // 根据模板内容逐步调用SDK方法
            foreach (var item in template.Items)
            {
                switch (item.Type)
                {
                    case PrintTemplate.PrintItemType.Text:
                        await PrintTemplateTextItem(item);
                        break;
                        
                    case PrintTemplate.PrintItemType.Line:
                        await PrintTemplateLineItem(item);
                        break;
                        
                    case PrintTemplate.PrintItemType.Image:
                        await PrintTemplateImageItem(item);
                        break;
                        
                    case PrintTemplate.PrintItemType.Barcode:
                        await PrintTemplateBarcodeItem(item);
                        break;
                        
                    case PrintTemplate.PrintItemType.QRCode:
                        await PrintTemplateQRCodeItem(item);
                        break;
                }
            }
            
            // 根据模式选择走纸方式 - 基于demo代码和开发指南
            var printUtilClass = _printUtil.Class;
            if (enableBlackMark)
            {
                // 黑标模式：调用printGoToNextMark走纸
                var printGoToNextMarkMethod = printUtilClass.GetMethod("printGoToNextMark", Java.Lang.Integer.Type);
                printGoToNextMarkMethod.Invoke(_printUtil, Java.Lang.Integer.ValueOf(feedPaperSpace));
                System.Diagnostics.Debug.WriteLine("调用printGoToNextMark走纸");
            }
            else
            {
                // 无标签模式：添加空行后调用start
                var printLineMethod = printUtilClass.GetMethod("printLine", Java.Lang.Integer.Type);
                printLineMethod.Invoke(_printUtil, Java.Lang.Integer.ValueOf(6));
                
                var startMethod = printUtilClass.GetMethod("start");
                startMethod.Invoke(_printUtil);
                System.Diagnostics.Debug.WriteLine("调用start开始打印");
            }
            
            System.Diagnostics.Debug.WriteLine("调用SDK printTemplate成功");
            return true;
        }
        catch (System.Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"打印模板失败: {ex.Message}");
            // 不触发OnPrintResult事件，直接返回false，让SDK回调处理所有错误
            return false;
        }
    }

    private async Task PrintTemplateTextItem(PrintTemplate.PrintItem item)
    {
        try
        {
            var printUtilClass = _printUtil.Class;
            var printTextMethod = printUtilClass.GetMethod("printText", 
                Java.Lang.Integer.Type, Java.Lang.Integer.Type, Java.Lang.Boolean.Type, Java.Lang.Boolean.Type, Java.Lang.Class.FromType(typeof(Java.Lang.String)));
            
            if (item.Config != null)
            {
                int align = ConvertTextAlign(item.Config.Align);
                int fontSize = ConvertFontSize(item.Config.Size);
                
                printTextMethod.Invoke(_printUtil, 
                    Java.Lang.Integer.ValueOf(align), 
                    Java.Lang.Integer.ValueOf(fontSize), 
                    Java.Lang.Boolean.ValueOf(item.Config.Bold), 
                    Java.Lang.Boolean.ValueOf(item.Config.Underline), 
                    new Java.Lang.String(item.Content));
            }
            else
            {
                // 使用默认格式
                printTextMethod.Invoke(_printUtil, 
                    Java.Lang.Integer.ValueOf(GetAlignConstant("ALIGN_LEFT")), 
                    Java.Lang.Integer.ValueOf(GetFontSizeConstant("TOP_FONT_SIZE_MIDDLE")), 
                    Java.Lang.Boolean.ValueOf(false), 
                    Java.Lang.Boolean.ValueOf(false), 
                    new Java.Lang.String(item.Content));
            }
        }
        catch (System.Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"打印文本项失败: {ex.Message}");
        }
    }

    private async Task PrintTemplateLineItem(PrintTemplate.PrintItem item)
    {
        try
        {
            if (int.TryParse(item.Content, out int lines))
            {
                var printUtilClass = _printUtil.Class;
                var printLineMethod = printUtilClass.GetMethod("printLine", Java.Lang.Integer.Type);
                printLineMethod.Invoke(_printUtil, Java.Lang.Integer.ValueOf(lines));
            }
        }
        catch (System.Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"打印空行失败: {ex.Message}");
        }
    }

    private async Task PrintTemplateImageItem(PrintTemplate.PrintItem item)
    {
        try
        {
            if (item.ImageData != null)
            {
                var bitmap = BitmapFactory.DecodeByteArray(item.ImageData, 0, item.ImageData.Length);
                if (bitmap != null)
                {
                    var printUtilClass = _printUtil.Class;
                    var printBitmapMethod = printUtilClass.GetMethod("printBitmap", Java.Lang.Class.FromType(typeof(Bitmap)));
                    printBitmapMethod.Invoke(_printUtil, bitmap);
                }
            }
        }
        catch (System.Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"打印图片项失败: {ex.Message}");
        }
    }

    private async Task PrintTemplateBarcodeItem(PrintTemplate.PrintItem item)
    {
        try
        {
            if (!string.IsNullOrEmpty(item.Content))
            {
                int barcodeType = ConvertBarcodeType(item.BarcodeType);
                int alignCenter = GetAlignConstant("ALIGN_CENTER");
                int hriPositionValue = ConvertHRIPosition(item.HRIPosition);
                
                var printUtilClass = _printUtil.Class;
                var printBarcodeMethod = printUtilClass.GetMethod("printBarcode", 
                    Java.Lang.Integer.Type, Java.Lang.Integer.Type, Java.Lang.Class.FromType(typeof(Java.Lang.String)), 
                    Java.Lang.Integer.Type, Java.Lang.Integer.Type);
                
                printBarcodeMethod.Invoke(_printUtil, 
                    Java.Lang.Integer.ValueOf(alignCenter), 
                    Java.Lang.Integer.ValueOf(100), 
                    new Java.Lang.String(item.Content), 
                    Java.Lang.Integer.ValueOf(barcodeType), 
                    Java.Lang.Integer.ValueOf(hriPositionValue));
            }
        }
        catch (System.Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"打印条码项失败: {ex.Message}");
        }
    }

    private async Task PrintTemplateQRCodeItem(PrintTemplate.PrintItem item)
    {
        try
        {
            if (!string.IsNullOrEmpty(item.Content))
            {
                int alignValue = ConvertTextAlign(item.Align);
                
                var printUtilClass = _printUtil.Class;
                var printQRMethod = printUtilClass.GetMethod("printQR", 
                    Java.Lang.Integer.Type, Java.Lang.Integer.Type, Java.Lang.Class.FromType(typeof(Java.Lang.String)));
                
                printQRMethod.Invoke(_printUtil, 
                    Java.Lang.Integer.ValueOf(alignValue), 
                    Java.Lang.Integer.ValueOf(384), 
                    new Java.Lang.String(item.Content));
            }
        }
        catch (System.Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"打印二维码项失败: {ex.Message}");
        }
    }

    // 获取对齐常量值 - 基于demo代码中的PrintConfig常量
    private int GetAlignConstant(string alignName)
    {
        try
        {
            // 根据demo代码：import android.bld.print.configuration.PrintConfig;
            var printConfigClass = Java.Lang.Class.ForName("android.bld.print.configuration.PrintConfig");
            var alignClass = printConfigClass.GetDeclaredClasses().FirstOrDefault(c => c.SimpleName.Equals("Align"));
            
            if (alignClass != null)
            {
                var field = alignClass.GetDeclaredField(alignName);
                if (field != null)
                {
                    field.Accessible = true;
                    var value = field.Get(null);
                    if (value is Java.Lang.Integer intValue)
                    {
                        System.Diagnostics.Debug.WriteLine($"获取对齐常量 {alignName} = {intValue.IntValue()}");
                        return intValue.IntValue();
                    }
                }
            }
        }
        catch (System.Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"获取对齐常量失败: {ex.Message}");
        }
        
        // 如果无法获取常量，使用demo中的默认值
        return alignName switch
        {
            "ALIGN_LEFT" => 0,
            "ALIGN_CENTER" => 1,
            "ALIGN_RIGHT" => 2,
            _ => 1 // 默认居中
        };
    }

    // 获取字体大小常量值 - 基于demo代码中的PrintConfig常量
    private int GetFontSizeConstant(string fontSizeName)
    {
        try
        {
            // 根据demo代码：import android.bld.print.configuration.PrintConfig;
            var printConfigClass = Java.Lang.Class.ForName("android.bld.print.configuration.PrintConfig");
            var fontSizeClass = printConfigClass.GetDeclaredClasses().FirstOrDefault(c => c.SimpleName.Equals("FontSize"));
            
            if (fontSizeClass != null)
            {
                var field = fontSizeClass.GetDeclaredField(fontSizeName);
                if (field != null)
                {
                    field.Accessible = true;
                    var value = field.Get(null);
                    if (value is Java.Lang.Integer intValue)
                    {
                        System.Diagnostics.Debug.WriteLine($"获取字体大小常量 {fontSizeName} = {intValue.IntValue()}");
                        return intValue.IntValue();
                    }
                }
            }
        }
        catch (System.Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"获取字体大小常量失败: {ex.Message}");
        }
        
        // 如果无法获取常量，使用demo中的默认值
        return fontSizeName switch
        {
            "TOP_FONT_SIZE_MIDDLE" => 0,
            "TOP_FONT_SIZE_LARGE" => 1,
            "TOP_FONT_SIZE_XLARGE" => 2,
            _ => 0 // 默认中等
        };
    }

    // 获取HRI位置常量值 - 基于demo代码中的PrintConfig常量
    private int GetHRIPositionConstant(string positionName)
    {
        try
        {
            // 根据demo代码：import android.bld.print.configuration.PrintConfig;
            var printConfigClass = Java.Lang.Class.ForName("android.bld.print.configuration.PrintConfig");
            var hriPositionClass = printConfigClass.GetDeclaredClasses().FirstOrDefault(c => c.SimpleName.Equals("HRIPosition"));
            
            if (hriPositionClass != null)
            {
                var field = hriPositionClass.GetDeclaredField(positionName);
                if (field != null)
                {
                    field.Accessible = true;
                    var value = field.Get(null);
                    if (value is Java.Lang.Integer intValue)
                    {
                        System.Diagnostics.Debug.WriteLine($"获取HRI位置常量 {positionName} = {intValue.IntValue()}");
                        return intValue.IntValue();
                    }
                }
            }
        }
        catch (System.Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"获取HRI位置常量失败: {ex.Message}");
        }
        
        // 如果无法获取常量，使用demo中的默认值
        return positionName switch
        {
            "POSITION_NONE" => 0,
            "POSITION_BELOW" => 1,
            "POSITION_ABOVE" => 2,
            _ => 1 // 默认在下方
        };
    }

    // 获取条码类型常量值 - 基于demo代码中的PrintConfig常量
    private int GetBarcodeTypeConstant(string barcodeTypeName)
    {
        try
        {
            // 根据demo代码：import android.bld.print.configuration.PrintConfig;
            var printConfigClass = Java.Lang.Class.ForName("android.bld.print.configuration.PrintConfig");
            var barcodeTypeClass = printConfigClass.GetDeclaredClasses().FirstOrDefault(c => c.SimpleName.Equals("BarCodeType"));
            
            if (barcodeTypeClass != null)
            {
                var field = barcodeTypeClass.GetDeclaredField(barcodeTypeName);
                if (field != null)
                {
                    field.Accessible = true;
                    var value = field.Get(null);
                    if (value is Java.Lang.Integer intValue)
                    {
                        System.Diagnostics.Debug.WriteLine($"获取条码类型常量 {barcodeTypeName} = {intValue.IntValue()}");
                        return intValue.IntValue();
                    }
                }
            }
        }
        catch (System.Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"获取条码类型常量失败: {ex.Message}");
        }
        
        // 如果无法获取常量，使用demo中的默认值
        return barcodeTypeName switch
        {
            "TOP_TYPE_UPCA" => 0,
            "TOP_TYPE_EAN8" => 1,
            "TOP_TYPE_EAN13" => 2,
            "TOP_TYPE_CODE39" => 3,
            "TOP_TYPE_ITF" => 4,
            "TOP_TYPE_CODE128" => 6,
            _ => 6 // 默认使用CODE128
        };
    }

    // 转换文本对齐方式 - 基于demo代码中的PrintConfig.Align常量
    private int ConvertTextAlign(PrintConfig.TextAlign align)
    {
        return align switch
        {
            PrintConfig.TextAlign.Left => GetAlignConstant("ALIGN_LEFT"),
            PrintConfig.TextAlign.Center => GetAlignConstant("ALIGN_CENTER"),  
            PrintConfig.TextAlign.Right => GetAlignConstant("ALIGN_RIGHT"),
            _ => GetAlignConstant("ALIGN_LEFT")
        };
    }

    // 转换字体大小 - 基于demo代码中的PrintConfig.FontSize常量
    private int ConvertFontSize(PrintConfig.FontSize size)
    {
        return size switch
        {
            PrintConfig.FontSize.Small => GetFontSizeConstant("TOP_FONT_SIZE_MIDDLE"),
            PrintConfig.FontSize.Normal => GetFontSizeConstant("TOP_FONT_SIZE_LARGE"),
            PrintConfig.FontSize.Large => GetFontSizeConstant("TOP_FONT_SIZE_XLARGE"),
            _ => GetFontSizeConstant("TOP_FONT_SIZE_MIDDLE")
        };
    }

    // 转换条码类型 - 基于demo代码中的PrintConfig.BarCodeType常量
    private int ConvertBarcodeType(BarcodeType barcodeType)
    {
        return barcodeType switch
        {
            BarcodeType.UpcA => GetBarcodeTypeConstant("TOP_TYPE_UPCA"),
            BarcodeType.Ean8 => GetBarcodeTypeConstant("TOP_TYPE_EAN8"),
            BarcodeType.Ean13 => GetBarcodeTypeConstant("TOP_TYPE_EAN13"),
            BarcodeType.Code39 => GetBarcodeTypeConstant("TOP_TYPE_CODE39"),
            BarcodeType.Itf => GetBarcodeTypeConstant("TOP_TYPE_ITF"),
            BarcodeType.Code128 => GetBarcodeTypeConstant("TOP_TYPE_CODE128"),
            _ => GetBarcodeTypeConstant("TOP_TYPE_CODE128") // 默认使用CODE128
        };
    }

    // 转换HRI位置 - 基于demo代码中的PrintConfig.HRIPosition常量
    private int ConvertHRIPosition(HRIPosition hriPosition)
    {
        return hriPosition switch
        {
            HRIPosition.None => GetHRIPositionConstant("POSITION_NONE"),
            HRIPosition.Below => GetHRIPositionConstant("POSITION_BELOW"),
            HRIPosition.Above => GetHRIPositionConstant("POSITION_ABOVE"),
            _ => GetHRIPositionConstant("POSITION_BELOW") // 默认在下方
        };
    }

    // 设置打印模式 - 基于demo代码
    private void SetPrintMode(bool enableBlackMark)
    {
        if (_isBlackMarkMode == enableBlackMark) return; // 如果模式没有变化，不需要重新设置

        try
        {
            var printUtilClass = _printUtil.Class;
            var printEnableMarkMethod = printUtilClass.GetMethod("printEnableMark", Java.Lang.Boolean.Type);
            printEnableMarkMethod.Invoke(_printUtil, Java.Lang.Boolean.ValueOf(enableBlackMark));
            _isBlackMarkMode = enableBlackMark;
            System.Diagnostics.Debug.WriteLine($"设置打印模式: {(enableBlackMark ? "黑标模式" : "无标签模式")}");
        }
        catch (System.Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"设置打印模式失败: {ex.Message}");
        }
    }

    // 设置走纸距离 - 基于demo代码
    private void SetFeedPaperSpace(int feedPaperSpace)
    {
        try
        {
            var printUtilClass = _printUtil.Class;
            var setFeedPaperSpaceMethod = printUtilClass.GetMethod("setFeedPaperSpace", Java.Lang.Integer.Type);
            setFeedPaperSpaceMethod.Invoke(_printUtil, Java.Lang.Integer.ValueOf(feedPaperSpace));
        }
        catch (System.Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"设置走纸距离失败: {ex.Message}");
        }
    }

    // 设置回纸长度 - 基于demo代码
    private void SetUnwindPaperLen(int unwindPaperLen)
    {
        try
        {
            var printUtilClass = _printUtil.Class;
            var setUnwindPaperLenMethod = printUtilClass.GetMethod("setUnwindPaperLen", Java.Lang.Integer.Type);
            setUnwindPaperLenMethod.Invoke(_printUtil, Java.Lang.Integer.ValueOf(unwindPaperLen));
        }
        catch (System.Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"设置回纸长度失败: {ex.Message}");
        }
    }

    // 设置打印浓度 - 基于demo代码
    private void SetPrintConcentration(int concentration)
    {
        try
        {
            var printUtilClass = _printUtil.Class;
            var printConcentrationMethod = printUtilClass.GetMethod("printConcentration", Java.Lang.Integer.Type);
            printConcentrationMethod.Invoke(_printUtil, Java.Lang.Integer.ValueOf(concentration));
            System.Diagnostics.Debug.WriteLine($"设置打印浓度: {concentration}");
        }
        catch (System.Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"设置打印浓度失败: {ex.Message}");
        }
    }

    // 确保打印服务就绪，如果未就绪则尝试初始化
    private bool EnsureServiceReady()
    {
        if (IsReady)
        {
            return true;
        }

        System.Diagnostics.Debug.WriteLine("打印服务未就绪，尝试自动初始化...");
        Initialize();
        
        // 初始化后再次检查
        if (!IsReady)
        {
            System.Diagnostics.Debug.WriteLine("打印服务初始化失败");
            // 不触发OnPrintResult事件，直接返回false，让SDK回调处理所有错误
            return false;
        }
        
        System.Diagnostics.Debug.WriteLine("打印服务自动初始化成功");
        return true;
    }

    // 新增的等待结果方法
    public async Task<PrintEventArgs> PrintTextAndWaitAsync(string text, PrintConfig config = null, bool enableBlackMark = false)
    {
        return await ExecutePrintAndWaitAsync(async () =>
        {
            config = config ?? new PrintConfig();
            return await PrintTextAsync(text, config, enableBlackMark);
        });
    }

    public async Task<PrintEventArgs> PrintBarcodeAndWaitAsync(string content, BarcodeType barcodeType = BarcodeType.Code128, HRIPosition hriPosition = HRIPosition.Below, bool enableBlackMark = false)
    {
        return await ExecutePrintAndWaitAsync(async () =>
        {
            return await PrintBarcodeAsync(content, barcodeType, hriPosition, enableBlackMark);
        });
    }

    public async Task<PrintEventArgs> PrintQRCodeAndWaitAsync(string content, PrintConfig.TextAlign align = PrintConfig.TextAlign.Center, int height = 184, bool enableBlackMark = false)
    {
        return await ExecutePrintAndWaitAsync(async () =>
        {
            return await PrintQRCodeAsync(content, align, height, enableBlackMark);
        });
    }

    public async Task<PrintEventArgs> PrintBitmapAndWaitAsync(byte[] bitmapData, bool enableBlackMark = false)
    {
        return await ExecutePrintAndWaitAsync(async () =>
        {
            return await PrintBitmapAsync(bitmapData, enableBlackMark);
        });
    }

    public async Task<PrintEventArgs> PrintTemplateAndWaitAsync(PrintTemplate template, bool enableBlackMark = false)
    {
        return await ExecutePrintAndWaitAsync(async () =>
        {
            return await PrintTemplateAsync(template, enableBlackMark);
        });
    }

    /// <summary>
    /// 执行打印操作并等待结果
    /// </summary>
    /// <param name="printAction">打印操作</param>
    /// <returns>打印结果</returns>
    private async Task<PrintEventArgs> ExecutePrintAndWaitAsync(Func<Task<bool>> printAction)
    {
        lock (_printLock)
        {
            // 如果有正在进行的打印任务，返回错误
            if (_currentPrintTask != null && !_currentPrintTask.Task.IsCompleted)
            {
                return new PrintEventArgs
                {
                    Result = PrintResult.DeviceBusy,
                    Message = "设备忙",
                    ErrorCode = 1
                };
            }

            // 创建新的等待任务
            _currentPrintTask = new TaskCompletionSource<PrintEventArgs>();
        }

        try
        {
            // 执行打印操作
            var success = await printAction();
            
            if (!success)
            {
                // 打印调用失败（数据验证失败等）
                lock (_printLock)
                {
                    _currentPrintTask?.SetResult(new PrintEventArgs
                    {
                        Result = PrintResult.DataError,
                        Message = "打印调用失败",
                        ErrorCode = -1
                    });
                }
            }

            // 等待SDK回调结果，设置超时时间
            var timeoutTask = Task.Delay(TimeSpan.FromSeconds(30));
            var completedTask = await Task.WhenAny(_currentPrintTask.Task, timeoutTask);

            if (completedTask == timeoutTask)
            {
                // 超时
                lock (_printLock)
                {
                    _currentPrintTask?.SetResult(new PrintEventArgs
                    {
                        Result = PrintResult.UnknownError,
                        Message = "超时",
                        ErrorCode = 169
                    });
                }
            }

            return await _currentPrintTask.Task;
        }
        catch (System.Exception ex)
        {
            // 异常处理
            var errorResult = new PrintEventArgs
            {
                Result = PrintResult.UnknownError,
                Message = "未知错误",
                ErrorCode = 255
            };

            lock (_printLock)
            {
                _currentPrintTask?.SetResult(errorResult);
            }

            return errorResult;
        }
        finally
        {
            // 清理任务
            lock (_printLock)
            {
                _currentPrintTask = null;
            }
        }
    }

    // Java代理调用处理器 - 用于实现PrintUtil.PrinterBinderListener接口
    private class PrinterBinderListenerInvocationHandler : Java.Lang.Object, Java.Lang.Reflect.IInvocationHandler
    {
        private readonly PrintService _printService;

        public PrinterBinderListenerInvocationHandler(PrintService printService)
        {
            _printService = printService;
        }

        public Java.Lang.Object Invoke(Java.Lang.Object proxy, Java.Lang.Reflect.Method method, Java.Lang.Object[] args)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"Java代理调用方法: {method.Name}");
                
                switch (method.Name)
                {
                    case "onPrintCallback":
                        if (args != null && args.Length > 0 && args[0] is Java.Lang.Integer state)
                        {
                            OnPrintCallback(state.IntValue());
                        }
                        break;
                        
                    case "onVersion":
                        if (args != null && args.Length > 0 && args[0] is Java.Lang.String version)
                        {
                            OnVersion(version.ToString());
                        }
                        break;
                        
                    default:
                        System.Diagnostics.Debug.WriteLine($"未处理的方法调用: {method.Name}");
                        break;
                }
            }
            catch (System.Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"代理方法调用失败: {ex.Message}");
            }
            
            return null;
        }

        private void OnPrintCallback(int state)
        {
            System.Diagnostics.Debug.WriteLine($"===== 收到打印回调 =====");
            System.Diagnostics.Debug.WriteLine($"打印回调状态: {state}");
            System.Diagnostics.Debug.WriteLine($"当前时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}");
            
            PrintResult result;
            string message;
            
            // 基于demo代码中的错误码处理 - 使用常量
            switch (state)
            {
                case 0: // ERROR_NO_ERROR - 打印成功
                    result = PrintResult.Success;
                    message = "打印成功";
                    break;
                case 1: // ERROR_DEV_IS_BUSY - 设备忙
                    result = PrintResult.DeviceBusy;
                    message = "设备忙";
                    break;
                case 2: // ERROR_PRINT_HOT - 高温
                    result = PrintResult.UnknownError;
                    message = "高温";
                    break;
                case 3: // ERROR_PRINT_NOPAPER - 缺纸
                    result = PrintResult.NoPaper;
                    message = "缺纸";
                    break;
                case 4: // ERROR_DEV_NO_BATTERY - 低电
                    result = PrintResult.UnknownError;
                    message = "低电";
                    break;
                case 5: // ERROR_DEV_FEED - 正在走纸
                    result = PrintResult.UnknownError;
                    message = "正在走纸";
                    break;
                case 6: // ERROR_DEV_PRINT - 正在打印
                    result = PrintResult.UnknownError;
                    message = "正在打印";
                    break;
                case 7: // ERROR_DEV_BMARK - 黑标检测异常
                    result = PrintResult.UnknownError;
                    message = "黑标检测异常";
                    break;
                case 16: // ERROR_DEV_NOT_OPEN - 设备未打开
                    result = PrintResult.DeviceNotOpen;
                    message = "设备未打开";
                    break;
                case 17: // ERROR_NO_DATA - 打印数据不能为空
                    result = PrintResult.DataError;
                    message = "打印数据不能为空";
                    break;
                case 18: // ERROR_DATA_INVALID - 数据非法
                    result = PrintResult.DataError;
                    message = "数据非法";
                    break;
                case 19: // ERROR_CMD - 指令错误
                    result = PrintResult.CommandError;
                    message = "指令错误";
                    break;
                case 20: // ERROR_GRAY_INVALID - 浓度非法
                    result = PrintResult.DataError;
                    message = "浓度非法";
                    break;
                case 160: // ERROR_PRINT_TEXT - 打印文本错误
                    result = PrintResult.UnknownError;
                    message = "打印文本错误";
                    break;
                case 161: // ERROR_PRINT_BITMAP - 打印位图错误
                    result = PrintResult.UnknownError;
                    message = "打印位图错误";
                    break;
                case 162: // ERROR_PRINT_BARCODE - 打印条码错误
                    result = PrintResult.UnknownError;
                    message = "打印条码错误";
                    break;
                case 163: // ERROR_PRINT_QRCODE - 打印二维码错误
                    result = PrintResult.UnknownError;
                    message = "打印二维码错误";
                    break;
                case 164: // ERROR_PRINT_BITMAP_WIDTH_OVERFLOW - 打印位图宽度溢出
                    result = PrintResult.UnknownError;
                    message = "打印位图宽度溢出";
                    break;
                case 165: // ERROR_DATA_INPUT - 输入参数错误
                    result = PrintResult.DataError;
                    message = "输入参数错误";
                    break;
                case 166: // ERROR_PRINT_ILLEGAL_ARGUMENT - 参数错误
                    result = PrintResult.DataError;
                    message = "参数错误";
                    break;
                case 167: // ERROR_PRINT_DATA_MAC - Mac校验错误
                    result = PrintResult.DataError;
                    message = "Mac校验错误";
                    break;
                case 168: // ERROR_RESULT_EXIST - 结果已存在
                    result = PrintResult.UnknownError;
                    message = "结果已存在";
                    break;
                case 169: // ERROR_TIME_OUT - 超时
                    result = PrintResult.UnknownError;
                    message = "超时";
                    break;
                case 255: // ERROR_PRINT_UNKNOWN - 未知错误
                    result = PrintResult.UnknownError;
                    message = "未知错误";
                    break;
                default:
                    result = PrintResult.UnknownError;
                    message = "未知错误"; // 使用SDK定义的错误消息，不显示具体状态码
                    break;
            }
            
            System.Diagnostics.Debug.WriteLine($"准备触发回调事件: {message}");
            
            var printEventArgs = new PrintEventArgs
            {
                Result = result,
                Message = message,
                ErrorCode = state
            };
            
            // 触发传统事件
            _printService.OnPrintResult?.Invoke(_printService, printEventArgs);
            
            // 完成等待的任务
            lock (_printService._printLock)
            {
                if (_printService._currentPrintTask != null && !_printService._currentPrintTask.Task.IsCompleted)
                {
                    _printService._currentPrintTask.SetResult(printEventArgs);
                }
            }
            
            System.Diagnostics.Debug.WriteLine($"回调事件已触发");
        }

        private void OnVersion(string version)
        {
            System.Diagnostics.Debug.WriteLine($"打印SDK版本: {version}");
        }
    }
} 