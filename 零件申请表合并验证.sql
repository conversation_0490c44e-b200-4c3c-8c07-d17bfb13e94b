-- =============================================
-- 零件申请表合并验证脚本
-- 用于验证零件申请表是否正确合并到完整版数据库脚本中
-- =============================================

USE [CoreHub]
GO

PRINT '开始验证零件申请表合并结果...'
PRINT '========================================'

-- 验证表是否存在
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[RepairOrderPartRequests]') AND type in (N'U'))
    PRINT '✓ RepairOrderPartRequests表存在'
ELSE
    PRINT '❌ RepairOrderPartRequests表不存在'

-- 验证索引是否存在
IF EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[RepairOrderPartRequests]') AND name = N'IX_RepairOrderPartRequests_RepairOrderId')
    PRINT '✓ RepairOrderId索引存在'
ELSE
    PRINT '❌ RepairOrderId索引不存在'

IF EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[RepairOrderPartRequests]') AND name = N'IX_RepairOrderPartRequests_Status')
    PRINT '✓ Status索引存在'
ELSE
    PRINT '❌ Status索引不存在'

-- 验证视图是否存在
IF EXISTS (SELECT * FROM sys.views WHERE object_id = OBJECT_ID(N'[dbo].[V_RepairOrderPartRequestDetails]'))
    PRINT '✓ V_RepairOrderPartRequestDetails视图存在'
ELSE
    PRINT '❌ V_RepairOrderPartRequestDetails视图不存在'

IF EXISTS (SELECT * FROM sys.views WHERE object_id = OBJECT_ID(N'[dbo].[V_RepairOrderPartSummary]'))
    PRINT '✓ V_RepairOrderPartSummary视图存在'
ELSE
    PRINT '❌ V_RepairOrderPartSummary视图不存在'

-- 验证外键约束是否存在
IF EXISTS (SELECT * FROM sys.foreign_keys WHERE object_id = OBJECT_ID(N'[dbo].[FK_RepairOrderPartRequests_RepairOrders]'))
    PRINT '✓ RepairOrders外键约束存在'
ELSE
    PRINT '❌ RepairOrders外键约束不存在'

-- 验证字段结构
PRINT ''
PRINT '表字段验证：'

-- 检查关键字段
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[RepairOrderPartRequests]') AND name = 'PartName')
    PRINT '✓ PartName字段存在'
ELSE
    PRINT '❌ PartName字段不存在'

IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[RepairOrderPartRequests]') AND name = 'ExternalPartNumber')
    PRINT '✓ ExternalPartNumber字段存在（外部系统集成）'
ELSE
    PRINT '❌ ExternalPartNumber字段不存在'

-- 确认无审批字段
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[RepairOrderPartRequests]') AND name = 'ApprovedBy')
    PRINT '✓ 确认无ApprovedBy字段（无审批流程）'
ELSE
    PRINT '❌ 发现ApprovedBy字段（应该已移除）'

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[RepairOrderPartRequests]') AND name = 'ApprovedAt')
    PRINT '✓ 确认无ApprovedAt字段（无审批流程）'
ELSE
    PRINT '❌ 发现ApprovedAt字段（应该已移除）'

-- 测试视图查询
PRINT ''
PRINT '视图查询测试：'

BEGIN TRY
    SELECT TOP 1 * FROM V_RepairOrderPartRequestDetails
    PRINT '✓ V_RepairOrderPartRequestDetails视图查询正常'
END TRY
BEGIN CATCH
    PRINT '○ V_RepairOrderPartRequestDetails视图查询测试（可能无数据）'
END CATCH

BEGIN TRY
    SELECT TOP 1 * FROM V_RepairOrderPartSummary
    PRINT '✓ V_RepairOrderPartSummary视图查询正常'
END TRY
BEGIN CATCH
    PRINT '○ V_RepairOrderPartSummary视图查询测试（可能无数据）'
END CATCH

PRINT ''
PRINT '========================================'
PRINT '零件申请表合并验证完成！'
PRINT ''
PRINT '合并内容包括：'
PRINT '1. RepairOrderPartRequests表结构'
PRINT '2. 完整的索引配置'
PRINT '3. 两个查询视图'
PRINT '4. 外键约束关系'
PRINT '5. 外部系统集成字段'
PRINT '6. 无审批流程设计'
PRINT '========================================'
