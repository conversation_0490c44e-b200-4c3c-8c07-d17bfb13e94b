using Microsoft.Extensions.Options;
using CustomerWebAPI.Configurations;
using System;
using System.IO;
using System.Linq;
using Microsoft.Extensions.Logging;

namespace CustomerWebAPI.Services
{
    public class DirectoryInitializationService : IDirectoryInitializationService
    {
        private readonly DirectorySettings _settings;
        private readonly ILogger<DirectoryInitializationService> _logger;
        
        public DirectoryInitializationService(
            IOptions<DirectorySettings> settings,
            ILogger<DirectoryInitializationService> logger)
        {
            _settings = settings.Value;
            _logger = logger;
        }
        
        public void EnsureDirectoriesExist()
        {
            try
            {
                if (_settings.RequiredDirectories == null || !_settings.RequiredDirectories.Any())
                {
                    _logger.LogWarning("No required directories configured");
                    return;
                }

                foreach (var dir in _settings.RequiredDirectories)
                {
                    if (string.IsNullOrWhiteSpace(dir))
                    {
                        _logger.LogWarning("Empty directory path found in configuration");
                        continue;
                    }

                    var fullPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, dir);
                    CreateDirectoryIfNotExists(fullPath);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating directories");
                throw;
            }
        }

        private void CreateDirectoryIfNotExists(string path)
        {
            try
            {
                if (!Directory.Exists(path))
                {
                    Directory.CreateDirectory(path);
                    _logger.LogInformation("Created directory {DirectoryPath}", path);
                }
                else
                {
                    _logger.LogDebug("Directory already exists {DirectoryPath}", path);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to create directory {DirectoryPath}", path);
                throw;
            }
        }
    }
} 