using CoreHub.Shared.Data;
using CoreHub.Shared.Models.AppUpdate;
using SqlSugar;
using System.Security.Cryptography;
using System.Text.Json;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;

namespace CoreHub.Shared.Services
{
    /// <summary>
    /// 应用更新服务实现
    /// </summary>
    public class AppUpdateService : IAppUpdateService
    {
        private readonly DatabaseContext _dbContext;
        private readonly IApplicationLogger _logger;
        private readonly IConfiguration _configuration;

        public AppUpdateService(DatabaseContext dbContext, IApplicationLogger logger, IConfiguration configuration)
        {
            _dbContext = dbContext;
            _logger = logger;
            _configuration = configuration;
        }

        /// <summary>
        /// 检查更新
        /// </summary>
        public async Task<UpdateCheckResponse> CheckForUpdateAsync(UpdateCheckRequest request)
        {
            try
            {
                _logger.LogInformation("检查更新请求: 平台={Platform}, 当前版本={CurrentVersion}, 版本代码={CurrentVersionCode}", 
                    request.Platform, request.CurrentVersion, request.CurrentVersionCode);

                // 确定目标用户群体
                var targetAudience = "All";
                if (request.UserId.HasValue)
                {
                    // 可以根据用户角色或部门确定目标群体
                    // 这里简化处理，实际可以根据业务需求扩展
                    targetAudience = "All";
                }

                // 获取最新版本
                var latestVersion = await GetLatestVersionAsync(request.Platform, targetAudience, request.DepartmentId);

                if (latestVersion == null)
                {
                    _logger.LogWarning("未找到平台 {Platform} 的可用版本", request.Platform);
                    return new UpdateCheckResponse
                    {
                        HasUpdate = false,
                        Message = "暂无可用更新"
                    };
                }

                // 比较版本
                var hasUpdate = latestVersion.VersionCode > request.CurrentVersionCode;

                if (!hasUpdate)
                {
                    _logger.LogInformation("当前版本已是最新版本: {CurrentVersion}", request.CurrentVersion);
                    return new UpdateCheckResponse
                    {
                        HasUpdate = false,
                        Message = "当前已是最新版本"
                    };
                }

                // 检查最低支持版本
                if (latestVersion.MinSupportedVersionCode.HasValue && 
                    request.CurrentVersionCode < latestVersion.MinSupportedVersionCode.Value)
                {
                    _logger.LogWarning("当前版本 {CurrentVersionCode} 低于最低支持版本 {MinSupportedVersionCode}", 
                        request.CurrentVersionCode, latestVersion.MinSupportedVersionCode.Value);
                    
                    return new UpdateCheckResponse
                    {
                        HasUpdate = true,
                        IsForceUpdate = true,
                        Strategy = UpdateStrategy.Force,
                        LatestVersion = MapToVersionInfo(latestVersion),
                        Message = "当前版本过低，必须更新才能继续使用"
                    };
                }

                // 确定更新策略
                var strategy = latestVersion.IsForceUpdate ? UpdateStrategy.Force : 
                              latestVersion.UpdateType == "Hotfix" ? UpdateStrategy.Recommended : 
                              UpdateStrategy.Optional;

                _logger.LogInformation("发现可用更新: {LatestVersion}, 策略: {Strategy}", 
                    latestVersion.VersionNumber, strategy);

                return new UpdateCheckResponse
                {
                    HasUpdate = true,
                    IsForceUpdate = latestVersion.IsForceUpdate,
                    Strategy = strategy,
                    LatestVersion = MapToVersionInfo(latestVersion),
                    Message = latestVersion.IsForceUpdate ? "发现重要更新，请立即更新" : "发现新版本可用"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查更新时发生异常");
                return new UpdateCheckResponse
                {
                    HasUpdate = false,
                    Message = "检查更新失败，请稍后重试"
                };
            }
        }

        /// <summary>
        /// 获取版本列表
        /// </summary>
        public async Task<(List<AppVersion> Versions, int TotalCount)> GetVersionsAsync(string? platform = null, int pageIndex = 1, int pageSize = 20)
        {
            try
            {
                var query = _dbContext.Db.Queryable<AppVersion>()
                    .Where(v => v.IsEnabled);

                if (!string.IsNullOrEmpty(platform))
                {
                    query = query.Where(v => v.Platform == platform);
                }

                var totalCount = await query.CountAsync();
                var versions = await query
                    .OrderByDescending(v => v.VersionCode)
                    .Skip((pageIndex - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                return (versions, totalCount);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取版本列表时发生异常");
                return (new List<AppVersion>(), 0);
            }
        }

        /// <summary>
        /// 根据ID获取版本信息
        /// </summary>
        public async Task<AppVersion?> GetVersionByIdAsync(int id)
        {
            try
            {
                return await _dbContext.Db.Queryable<AppVersion>()
                    .Where(v => v.Id == id && v.IsEnabled)
                    .FirstAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取版本信息时发生异常: {VersionId}", id);
                return null;
            }
        }

        /// <summary>
        /// 创建新版本
        /// </summary>
        public async Task<(bool IsSuccess, string ErrorMessage, int? VersionId)> CreateVersionAsync(AppVersion version)
        {
            try
            {
                // 验证版本号是否已存在
                var existingVersion = await _dbContext.Db.Queryable<AppVersion>()
                    .Where(v => v.Platform == version.Platform && v.VersionNumber == version.VersionNumber && v.IsEnabled)
                    .FirstAsync();

                if (existingVersion != null)
                {
                    return (false, $"平台 {version.Platform} 的版本 {version.VersionNumber} 已存在", null);
                }

                // 验证版本代码是否已存在
                var existingVersionCode = await _dbContext.Db.Queryable<AppVersion>()
                    .Where(v => v.Platform == version.Platform && v.VersionCode == version.VersionCode && v.IsEnabled)
                    .FirstAsync();

                if (existingVersionCode != null)
                {
                    return (false, $"平台 {version.Platform} 的版本代码 {version.VersionCode} 已存在", null);
                }

                version.CreatedAt = DateTime.UtcNow;
                var result = await _dbContext.Db.Insertable(version).ExecuteReturnIdentityAsync();

                _logger.LogInformation("创建新版本成功: {Platform} {VersionNumber}", version.Platform, version.VersionNumber);
                return (true, string.Empty, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建版本时发生异常");
                return (false, "创建版本失败", null);
            }
        }

        /// <summary>
        /// 更新版本信息
        /// </summary>
        public async Task<(bool IsSuccess, string ErrorMessage)> UpdateVersionAsync(AppVersion version)
        {
            try
            {
                version.UpdatedAt = DateTime.UtcNow;
                var result = await _dbContext.Db.Updateable(version).ExecuteCommandAsync();

                if (result > 0)
                {
                    _logger.LogInformation("更新版本成功: {VersionId}", version.Id);
                    return (true, string.Empty);
                }

                return (false, "版本不存在或更新失败");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新版本时发生异常: {VersionId}", version.Id);
                return (false, "更新版本失败");
            }
        }

        /// <summary>
        /// 删除版本
        /// </summary>
        public async Task<(bool IsSuccess, string ErrorMessage)> DeleteVersionAsync(int id)
        {
            try
            {
                var result = await _dbContext.Db.Updateable<AppVersion>()
                    .SetColumns(v => v.IsEnabled == false)
                    .SetColumns(v => v.UpdatedAt == DateTime.UtcNow)
                    .Where(v => v.Id == id)
                    .ExecuteCommandAsync();

                if (result > 0)
                {
                    _logger.LogInformation("删除版本成功: {VersionId}", id);
                    return (true, string.Empty);
                }

                return (false, "版本不存在或删除失败");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除版本时发生异常: {VersionId}", id);
                return (false, "删除版本失败");
            }
        }

        /// <summary>
        /// 发布版本
        /// </summary>
        public async Task<(bool IsSuccess, string ErrorMessage)> PublishVersionAsync(int id, int operatorId)
        {
            try
            {
                var result = await _dbContext.Db.Updateable<AppVersion>()
                    .SetColumns(v => v.Status == "Released")
                    .SetColumns(v => v.ReleaseTime == DateTime.UtcNow)
                    .SetColumns(v => v.UpdatedAt == DateTime.UtcNow)
                    .SetColumns(v => v.UpdatedBy == operatorId)
                    .Where(v => v.Id == id && v.IsEnabled)
                    .ExecuteCommandAsync();

                if (result > 0)
                {
                    _logger.LogInformation("发布版本成功: {VersionId}, 操作者: {OperatorId}", id, operatorId);
                    return (true, string.Empty);
                }

                return (false, "版本不存在或发布失败");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "发布版本时发生异常: {VersionId}", id);
                return (false, "发布版本失败");
            }
        }

        /// <summary>
        /// 撤回版本
        /// </summary>
        public async Task<(bool IsSuccess, string ErrorMessage)> WithdrawVersionAsync(int id, int operatorId)
        {
            try
            {
                var result = await _dbContext.Db.Updateable<AppVersion>()
                    .SetColumns(v => v.Status == "Draft")
                    .SetColumns(v => v.UpdatedAt == DateTime.UtcNow)
                    .SetColumns(v => v.UpdatedBy == operatorId)
                    .Where(v => v.Id == id && v.IsEnabled)
                    .ExecuteCommandAsync();

                if (result > 0)
                {
                    _logger.LogInformation("撤回版本成功: {VersionId}, 操作者: {OperatorId}", id, operatorId);
                    return (true, string.Empty);
                }

                return (false, "版本不存在或撤回失败");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "撤回版本时发生异常: {VersionId}", id);
                return (false, "撤回版本失败");
            }
        }

        /// <summary>
        /// 获取最新版本
        /// </summary>
        public async Task<AppVersion?> GetLatestVersionAsync(string platform, string targetAudience = "All", int? departmentId = null)
        {
            try
            {
                var query = _dbContext.Db.Queryable<AppVersion>()
                    .Where(v => v.Platform == platform && v.Status == "Released" && v.IsEnabled);

                // 根据目标用户群体过滤
                if (targetAudience == "Department" && departmentId.HasValue)
                {
                    query = query.Where(v => v.TargetAudience == "All" || 
                                           v.TargetAudience == "Department" && 
                                           v.TargetDepartmentIds != null && 
                                           v.TargetDepartmentIds.Contains(departmentId.Value.ToString()));
                }
                else if (targetAudience != "All")
                {
                    query = query.Where(v => v.TargetAudience == "All" || v.TargetAudience == targetAudience);
                }

                return await query
                    .OrderByDescending(v => v.VersionCode)
                    .FirstAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取最新版本时发生异常: {Platform}", platform);
                return null;
            }
        }

        /// <summary>
        /// 验证文件完整性
        /// </summary>
        public async Task<bool> ValidateFileIntegrityAsync(string filePath, string expectedMd5)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    return false;
                }

                var actualMd5 = await CalculateFileMd5Async(filePath);
                return string.Equals(actualMd5, expectedMd5, StringComparison.OrdinalIgnoreCase);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证文件完整性时发生异常: {FilePath}", filePath);
                return false;
            }
        }

        /// <summary>
        /// 上传文件到服务器
        /// </summary>
        /// <param name="file">上传的文件</param>
        /// <param name="platform">平台类型</param>
        /// <param name="version">版本号</param>
        /// <returns>上传结果</returns>
        public async Task<(bool Success, string Message, string FileName, long FileSize, string FileMd5, string DownloadUrl)> UploadFileAsync(
            Microsoft.AspNetCore.Components.Forms.IBrowserFile file, string platform, string version)
        {
            try
            {
                // 验证文件
                if (file == null || file.Size == 0)
                {
                    return (false, "请选择要上传的文件", "", 0, "", "");
                }

                // 验证文件类型
                var allowedExtensions = new[] { ".apk", ".ipa", ".msix", ".dmg" };
                var fileExtension = Path.GetExtension(file.Name).ToLowerInvariant();

                if (!allowedExtensions.Contains(fileExtension))
                {
                    return (false, $"不支持的文件类型。支持的类型: {string.Join(", ", allowedExtensions)}", "", 0, "", "");
                }

                // 验证文件大小 (100MB)
                if (file.Size > 104857600)
                {
                    return (false, "文件大小不能超过100MB", "", 0, "", "");
                }

                // 创建上传目录
                var uploadsPath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "updates");
                if (!Directory.Exists(uploadsPath))
                {
                    Directory.CreateDirectory(uploadsPath);
                    _logger.LogInformation("自动创建更新文件目录: {UploadsPath}", uploadsPath);
                }

                // 生成文件名
                var fileName = $"{platform}_{version}{fileExtension}";
                var filePath = Path.Combine(uploadsPath, fileName);

                // 如果文件已存在，先删除
                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                }

                // 保存文件
                using (var stream = new FileStream(filePath, FileMode.Create))
                {
                    await file.OpenReadStream(maxAllowedSize: 104857600).CopyToAsync(stream);
                }

                // 计算文件MD5
                var md5Hash = await CalculateFileMd5Async(filePath);

                // 生成完整的下载URL
                var baseUrl = _configuration["UpdateService:BaseUrl"] ?? "https://172.16.9.111:8081";
                var downloadUrl = $"{baseUrl}/api/AppUpdate/download/{platform}/{version}";

                _logger.LogInformation("文件上传成功: {FileName}, 大小: {FileSize}, MD5: {MD5}, URL: {DownloadUrl}",
                    fileName, file.Size, md5Hash, downloadUrl);

                return (true, "文件上传成功", fileName, file.Size, md5Hash, downloadUrl);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "上传文件时发生异常");
                return (false, $"文件上传失败: {ex.Message}", "", 0, "", "");
            }
        }

        /// <summary>
        /// 计算文件MD5
        /// </summary>
        public async Task<string> CalculateFileMd5Async(string filePath)
        {
            using var md5 = MD5.Create();
            using var stream = File.OpenRead(filePath);
            var hash = await Task.Run(() => md5.ComputeHash(stream));
            return BitConverter.ToString(hash).Replace("-", "").ToLowerInvariant();
        }

        /// <summary>
        /// 获取文件大小
        /// </summary>
        public async Task<long> GetFileSizeAsync(string filePath)
        {
            return await Task.Run(() =>
            {
                var fileInfo = new FileInfo(filePath);
                return fileInfo.Exists ? fileInfo.Length : 0;
            });
        }

        /// <summary>
        /// 映射到版本信息
        /// </summary>
        private static VersionInfo MapToVersionInfo(AppVersion version)
        {
            return new VersionInfo
            {
                VersionNumber = version.VersionNumber,
                VersionCode = version.VersionCode,
                Title = version.Title,
                Description = version.Description,
                UpdateType = version.UpdateType,
                DownloadUrl = version.DownloadUrl,
                FileSize = version.FileSize,
                FileMd5 = version.FileMd5,
                ReleaseTime = version.ReleaseTime
            };
        }
    }
}
