using System;
using Dapper.Contrib.Extensions;

namespace CustomerWebAPI.Models
{
    /// <summary>
    /// 
    /// </summary>
    [Table("ASNDetail")]
    public class ASNDetail
    {
        /// <summary>
        /// 
        /// </summary>
        [ExplicitKey]
        public Guid Id { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public Guid? ASNItemsId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string PackagingCode { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public int LadingQuantity { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string WeightsQualifier { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public decimal? WeightsValue { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string WeightsUnit { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string TransportationTypeCode { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string ContainerNumber { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string DivisionCode { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string BillNumber { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string VoyageNumber { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string VesselNumber { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string ShipMode { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string SailDate { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string DeliveryDate { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string EstimatedArrivalDate { get; set; }
    }
}