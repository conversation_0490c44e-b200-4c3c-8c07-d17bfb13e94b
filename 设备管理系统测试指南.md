# 🧪 设备管理系统测试指南

## 📋 测试前准备

### 1. 数据库配置
确保您的SQL Server数据库已正确配置：

```sql
-- 1. 创建数据库（如果不存在）
CREATE DATABASE [YourDatabaseName];

-- 2. 执行数据库结构脚本
-- 运行文件: 设备管理系统数据库脚本.sql

-- 3. 插入基础数据
-- 运行文件: 设备管理系统基础数据.sql
```

### 2. 连接字符串配置
检查 `CoreHub.Shared/Configuration/DatabaseConfig.cs` 中的连接字符串：

```csharp
public static string ConnectionString => 
    "Server=your-server;Database=your-database;Trusted_Connection=true;TrustServerCertificate=true;";
```

### 3. 启动项目
```bash
# 进入Web项目目录
cd CoreHub.Web

# 启动项目
dotnet run
```

## 🚀 测试步骤

### 第一步：基础功能测试

1. **访问测试页面**
   - 打开浏览器访问：`http://localhost:5000/equipment-test`
   - 这个页面专门用于测试新功能

2. **测试数据库连接**
   - 点击"测试数据库连接"按钮
   - 查看测试结果，确保所有表都能正常访问

3. **创建测试数据**
   - 点击"创建测试数据"按钮
   - 系统会自动创建一些测试用的部门和设备型号

4. **查看数据统计**
   - 观察页面上的统计卡片
   - 确认各类数据的数量显示正确

### 第二步：部门管理测试

1. **访问部门管理**
   - 点击"部门管理"按钮或访问：`/department-management`

2. **测试功能**
   - ✅ 查看部门列表
   - ✅ 新增部门（点击"新增部门"）
   - ✅ 编辑部门（点击编辑图标）
   - ✅ 搜索部门（使用搜索框）
   - ✅ 启用/禁用部门（点击状态切换）
   - ✅ 删除部门（点击删除图标）

3. **测试数据验证**
   - 尝试创建重复编码的部门（应该失败）
   - 尝试创建空名称的部门（应该失败）
   - 测试层级关系（父子部门）

### 第三步：设备型号管理测试

1. **访问设备型号管理**
   - 访问：`/equipment-model-management`

2. **测试功能**
   - ✅ 查看型号列表
   - ✅ 按类别过滤
   - ✅ 新增设备型号
   - ✅ 编辑设备型号
   - ✅ 搜索功能
   - ✅ 状态管理

### 第四步：位置管理测试

1. **访问位置管理**
   - 访问：`/location-management`

2. **测试功能**
   - ✅ 查看位置列表
   - ✅ 按部门过滤
   - ✅ 新增位置
   - ✅ 编辑位置
   - ✅ 层级关系测试

### 第五步：设备管理测试

1. **访问设备管理**
   - 访问：`/equipment-management`

2. **测试功能**
   - ✅ 查看设备列表和统计
   - ✅ 高级搜索功能
   - ✅ 新增设备（需要先有部门、型号、位置）
   - ✅ 编辑设备
   - ✅ 查看设备详情
   - ✅ 设备状态管理

### 第六步：报修功能测试

1. **创建报修单**
   - 访问：`/create-repair-order`
   - 选择设备
   - 填写故障描述
   - 选择紧急程度
   - 提交报修

2. **管理报修单**
   - 访问：`/repair-order-management`
   - 查看报修单列表
   - 搜索和过滤
   - 查看详情
   - 作废报修单

## 🔍 测试检查点

### 数据完整性检查
- [ ] 所有外键关系正确
- [ ] 数据验证规则生效
- [ ] 重复数据检查有效

### 用户界面检查
- [ ] 响应式设计在不同屏幕尺寸下正常
- [ ] 所有按钮和链接可点击
- [ ] 表单验证提示清晰
- [ ] 加载状态显示正确

### 功能逻辑检查
- [ ] 搜索功能准确
- [ ] 分页功能正常
- [ ] 状态切换正确
- [ ] 权限控制有效

### 性能检查
- [ ] 页面加载速度合理
- [ ] 大量数据下的响应时间
- [ ] 内存使用情况

## 🐛 常见问题排查

### 1. 数据库连接失败
```
错误：无法连接到数据库
解决：检查连接字符串和SQL Server服务状态
```

### 2. 表不存在错误
```
错误：Invalid object name 'Departments'
解决：确保已执行数据库脚本创建表结构
```

### 3. 服务注册错误
```
错误：Unable to resolve service for type 'IDepartmentService'
解决：检查Program.cs中的服务注册
```

### 4. 路由错误
```
错误：页面404
解决：检查路由配置和页面@page指令
```

## 📊 测试数据示例

### 部门数据
```
编码: IT001, 名称: 信息技术部
编码: HR001, 名称: 人力资源部
编码: FIN001, 名称: 财务部
```

### 设备型号数据
```
编码: PC001, 名称: 台式电脑, 类别: 计算机设备
编码: PR001, 名称: 激光打印机, 类别: 办公设备
```

### 位置数据
```
编码: LOC001, 名称: 一楼大厅, 部门: 信息技术部
编码: LOC002, 名称: 二楼办公区, 部门: 人力资源部
```

## 🎯 测试完成标准

- ✅ 所有6个管理界面都能正常访问
- ✅ 增删改查功能都能正常工作
- ✅ 搜索和过滤功能准确
- ✅ 数据验证规则生效
- ✅ 错误处理和提示友好
- ✅ 界面响应速度合理

## 📞 技术支持

如果在测试过程中遇到问题，请检查：

1. **浏览器控制台**：查看JavaScript错误
2. **服务器日志**：查看后端错误信息
3. **数据库日志**：查看SQL执行情况
4. **网络请求**：使用F12开发者工具检查API调用

测试愉快！🚀
