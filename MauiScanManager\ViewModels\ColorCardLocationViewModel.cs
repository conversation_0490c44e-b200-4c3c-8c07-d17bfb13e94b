using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using MauiScanManager.Models;
using MauiScanManager.Services;
using System.Collections.ObjectModel;
namespace MauiScanManager.ViewModels
{
    public partial class ColorCardLocationViewModel : BaseOperationViewModel
    {
        private readonly IColorCardService _colorCardService;
        private readonly IDialogService _dialogService;

        [ObservableProperty]
        private string? _currentBatchNo;

        [ObservableProperty]
        private string? selectedLocation;

        [ObservableProperty]
        private bool _canAdd;

        public ObservableCollection<string> BatchNos { get; } = new();

        public ObservableCollection<string> Locations { get; } = new()
        {
            "手样组",
            "大货组",
            "染纱QC",
            "坯布QC",
            "成品QC",
            "业务部",
            "总经办"
        };

        private string _locatePerson;
        public string LocatePerson
        {
            get => _locatePerson;
            set => SetProperty(ref _locatePerson, value);
        }

        public ColorCardLocationViewModel(
            IScanService scanService, 
            IColorCardService colorCardService,
            IDialogService dialogService) : base(scanService,dialogService)
        {
            _colorCardService = colorCardService;
            _dialogService = dialogService;
            
            this.PropertyChanged += (s, e) => 
            {
                if (e.PropertyName == nameof(CurrentBatchNo))
                {
                    if (!string.IsNullOrEmpty(CurrentBatchNo))
                    {
                        CurrentBatchNo = CurrentBatchNo.ToUpper();
                    }
                    UpdateCanAdd();
                }
            };
        }

        private void UpdateCanAdd()
        {
            // 检查是否可以添加：
            // 1. 输入不为空
            // 2. 不在现有列表中（大小写不敏感）
            CanAdd = !string.IsNullOrWhiteSpace(CurrentBatchNo) 
                    && !BatchNos.Any(x => x.Equals(CurrentBatchNo, StringComparison.OrdinalIgnoreCase));
        }

        protected override void ProcessScanResult(string code, string type, byte[] codeSource)
        {
            if (!string.IsNullOrEmpty(code))
            {
                MainThread.BeginInvokeOnMainThread(async () =>
                {
                    var upperCode = code.ToUpper();
                    CurrentBatchNo = upperCode;
                    
                    if (!BatchNos.Any(x => x.Equals(upperCode, StringComparison.OrdinalIgnoreCase)))
                    {
                        BatchNos.Add(upperCode);
                    }
                    else
                    {
                        await _dialogService.ShowToastAsync("该缸号已扫描");
                    }
                });
            }
        }

        [RelayCommand]
        private void RemoveBatchNo(string batchNo)
        {
            // 大小写不敏感的删除
            var itemToRemove = BatchNos.FirstOrDefault(x => 
                x.Equals(batchNo, StringComparison.OrdinalIgnoreCase));
            if (itemToRemove != null)
            {
                BatchNos.Remove(itemToRemove);
            }
        }

        [RelayCommand]
        private async Task AddBatchNo()
        {
            if (string.IsNullOrWhiteSpace(CurrentBatchNo))
                return;

            var upperBatchNo = CurrentBatchNo.ToUpper();
            if (!BatchNos.Any(x => x.Equals(upperBatchNo, StringComparison.OrdinalIgnoreCase)))
            {
                BatchNos.Add(upperBatchNo);
                CurrentBatchNo = string.Empty;
            }
            else
            {
                await _dialogService.ShowToastAsync("该缸号已存在");
            }
        }

        [RelayCommand]
        private async Task SaveAsync()
        {
            if (!ValidateInput())
            {
                return;
            }

            try
            {
                var model = new ColorCardLocation
                {
                    BatchNos = BatchNos.ToList(),
                    Location = SelectedLocation,
                    LocatePerson = LocatePerson
                };

                var result = await _colorCardService.UpdateLocationAsync(model);
                if (result.IsSuccess)
                {
                    await _dialogService.ShowSuccessAsync("保存成功");
                    BatchNos.Clear();
                    CurrentBatchNo = string.Empty;
                    SelectedLocation = null;
                    LocatePerson = string.Empty;
                }
                else
                {
                    await _dialogService.ShowErrorAsync(result.ErrorMessage);
                }
            }
            catch (Exception ex)
            {
                await _dialogService.ShowErrorAsync($"保存失败: {ex.Message}");
            }
        }

        private bool ValidateInput()
        {
            if (BatchNos.Count == 0)
            {
                MainThread.BeginInvokeOnMainThread(async () =>
                    await _dialogService.ShowInfoAsync("请先扫描缸号"));
                return false;
            }

            if (string.IsNullOrEmpty(SelectedLocation))
            {
                MainThread.BeginInvokeOnMainThread(async () =>
                    await _dialogService.ShowInfoAsync("请选择位置"));
                return false;
            }

            if (string.IsNullOrEmpty(LocatePerson))
            {
                MainThread.BeginInvokeOnMainThread(async () =>
                    await _dialogService.ShowInfoAsync("请输入定位人"));
                return false;
            }

            return true;
        }
    }
}
