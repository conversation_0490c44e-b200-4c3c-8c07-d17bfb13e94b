using Android.Media;
using MauiScanManager.Services;

namespace MauiScanManager.Platforms.Android.Services;

public class AndroidAudioService : IAudioService
{
    private readonly MediaPlayer _mediaPlayer;
    private readonly Dictionary<string, int> _soundResources;

    public AndroidAudioService()
    {
        _mediaPlayer = new MediaPlayer();
        _soundResources = new Dictionary<string, int>
        {
            { "check_prompt", Resource.Raw.check_prompt },
            { "check_success", Resource.Raw.check_success },
            { "check_failure", Resource.Raw.check_failure },
            { "Remain1Box", Resource.Raw.Remain1Box },
            { "Remain2Box", Resource.Raw.Remain2Box },
            { "Remain3Box", Resource.Raw.Remain3Box },
            { "Remain4Box", Resource.Raw.Remain4Box },
            { "CheckChemicalBoxPrompt", Resource.Raw.CheckChemicalBoxPrompt },
            { "BoxNoRepeatScan", Resource.Raw.BoxNoRepeatScan }
        };
    }

    public async Task PlayCheckPrompt()
    {
        await PlaySound("check_prompt");
    }

    public async Task PlaySuccessSound()
    {
        await PlaySound("check_success");
    }

    public async Task PlayFailureSound()
    {
        await PlaySound("check_failure");
    }

    public async Task PlayAudioByName(string audioName)
    {
        await PlaySound(audioName);
    }

    private async Task PlaySound(string soundName)
    {
        try
        {
            if (_soundResources.TryGetValue(soundName, out int resourceId))
            {
                var context = Platform.CurrentActivity;
                var afd = context?.Resources?.OpenRawResourceFd(resourceId);
                
                if (afd != null)
                {
                    _mediaPlayer.Reset();
                    await _mediaPlayer.SetDataSourceAsync(afd.FileDescriptor, afd.StartOffset, afd.Length);
                    _mediaPlayer.Prepare();
                    _mediaPlayer.Start();
                    afd.Close();
                }
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error playing sound {soundName}: {ex.Message}");
        }
    }

    ~AndroidAudioService()
    {
        _mediaPlayer?.Release();
    }
} 