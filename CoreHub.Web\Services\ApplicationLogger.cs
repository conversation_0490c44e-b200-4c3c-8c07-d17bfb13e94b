using Serilog;
using Serilog.Context;
using CoreHub.Shared.Services;

namespace CoreHub.Web.Services
{
    /// <summary>
    /// 应用程序日志服务实现
    /// </summary>
    public class ApplicationLogger : IApplicationLogger
    {
        private readonly Serilog.ILogger _logger;

        public ApplicationLogger()
        {
            _logger = Log.ForContext<ApplicationLogger>();
        }

        /// <summary>
        /// 记录调试信息
        /// </summary>
        /// <param name="message">消息</param>
        /// <param name="args">参数</param>
        public void LogDebug(string message, params object[] args)
        {
            _logger.Debug(message, args);
        }

        /// <summary>
        /// 记录信息
        /// </summary>
        /// <param name="message">消息</param>
        /// <param name="args">参数</param>
        public void LogInformation(string message, params object[] args)
        {
            _logger.Information(message, args);
        }

        /// <summary>
        /// 记录警告
        /// </summary>
        /// <param name="message">消息</param>
        /// <param name="args">参数</param>
        public void LogWarning(string message, params object[] args)
        {
            _logger.Warning(message, args);
        }

        /// <summary>
        /// 记录错误
        /// </summary>
        /// <param name="exception">异常</param>
        /// <param name="message">消息</param>
        /// <param name="args">参数</param>
        public void LogError(Exception exception, string message, params object[] args)
        {
            _logger.Error(exception, message, args);
        }

        /// <summary>
        /// 记录严重错误
        /// </summary>
        /// <param name="exception">异常</param>
        /// <param name="message">消息</param>
        /// <param name="args">参数</param>
        public void LogCritical(Exception exception, string message, params object[] args)
        {
            _logger.Fatal(exception, message, args);
        }

        /// <summary>
        /// 记录用户操作
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="action">操作</param>
        /// <param name="details">详细信息</param>
        public void LogUserAction(string userId, string action, object? details = null)
        {
            using (LogContext.PushProperty("EventType", "UserAction"))
            using (LogContext.PushProperty("UserId", userId))
            using (LogContext.PushProperty("Action", action))
            {
                if (details != null)
                {
                    using (LogContext.PushProperty("Details", details, true))
                    {
                        _logger.Information("用户操作: {Action} by {UserId}", action, userId);
                    }
                }
                else
                {
                    _logger.Information("用户操作: {Action} by {UserId}", action, userId);
                }
            }
        }

        /// <summary>
        /// 记录性能指标
        /// </summary>
        /// <param name="operation">操作名称</param>
        /// <param name="duration">持续时间（毫秒）</param>
        /// <param name="additionalData">附加数据</param>
        public void LogPerformance(string operation, long duration, object? additionalData = null)
        {
            using (LogContext.PushProperty("EventType", "Performance"))
            using (LogContext.PushProperty("Operation", operation))
            using (LogContext.PushProperty("Duration", duration))
            {
                if (additionalData != null)
                {
                    using (LogContext.PushProperty("AdditionalData", additionalData, true))
                    {
                        if (duration > 5000)
                        {
                            _logger.Warning("性能警告: {Operation} 耗时 {Duration}ms", operation, duration);
                        }
                        else if (duration > 1000)
                        {
                            _logger.Information("性能监控: {Operation} 耗时 {Duration}ms", operation, duration);
                        }
                        else
                        {
                            _logger.Debug("性能监控: {Operation} 耗时 {Duration}ms", operation, duration);
                        }
                    }
                }
                else
                {
                    if (duration > 5000)
                    {
                        _logger.Warning("性能警告: {Operation} 耗时 {Duration}ms", operation, duration);
                    }
                    else if (duration > 1000)
                    {
                        _logger.Information("性能监控: {Operation} 耗时 {Duration}ms", operation, duration);
                    }
                    else
                    {
                        _logger.Debug("性能监控: {Operation} 耗时 {Duration}ms", operation, duration);
                    }
                }
            }
        }

        /// <summary>
        /// 记录安全事件
        /// </summary>
        /// <param name="eventType">事件类型</param>
        /// <param name="userId">用户ID</param>
        /// <param name="details">详细信息</param>
        public void LogSecurityEvent(string eventType, string? userId, object? details = null)
        {
            using (LogContext.PushProperty("EventType", "Security"))
            using (LogContext.PushProperty("SecurityEventType", eventType))
            using (LogContext.PushProperty("UserId", userId))
            {
                if (details != null)
                {
                    using (LogContext.PushProperty("Details", details, true))
                    {
                        _logger.Warning("安全事件: {SecurityEventType} - 用户: {UserId}", eventType, userId ?? "Anonymous");
                    }
                }
                else
                {
                    _logger.Warning("安全事件: {SecurityEventType} - 用户: {UserId}", eventType, userId ?? "Anonymous");
                }
            }
        }
    }
}
