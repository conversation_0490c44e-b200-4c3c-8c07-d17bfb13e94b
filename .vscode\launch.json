{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "CoreHub Web",
            "type": "coreclr",
            "request": "launch",
            "preLaunchTask": "build-web",
            "program": "${workspaceFolder}/CoreHub.Web/bin/Debug/net8.0/CoreHub.Web.dll",
            "args": [],
            "cwd": "${workspaceFolder}/CoreHub.Web",
            "stopAtEntry": false,
            "serverReadyAction": {
                "action": "openExternally",
                "pattern": "\\bNow listening on:\\s+(https?://\\S+)"
            },
            "env": {
                "ASPNETCORE_ENVIRONMENT": "Development"
            },
            "sourceFileMap": {
                "/Views": "${workspaceFolder}/Views"
            }
        },
        {
            "name": "CoreHub MAUI (Windows)",
            "type": "coreclr",
            "request": "launch",
            "preLaunchTask": "build-maui",
            "program": "${workspaceFolder}/CoreHub.Maui/bin/Debug/net8.0-windows10.0.19041.0/win10-x64/CoreHub.exe",
            "args": [],
            "cwd": "${workspaceFolder}/CoreHub.Maui",
            "stopAtEntry": false,
            "console": "internalConsole"
        },
        {
            "name": ".NET Core Attach",
            "type": "coreclr",
            "request": "attach"
        }
    ]
}