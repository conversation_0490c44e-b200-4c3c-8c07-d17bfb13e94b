-- =============================================
-- SQL Server 2012 兼容性检查脚本
-- =============================================
-- 此脚本用于检查 SQL Server 2012 环境的兼容性
-- 包含版本检查、功能验证和性能测试

PRINT '========================================';
PRINT 'SQL Server 2012 兼容性检查开始';
PRINT '========================================';

-- =============================================
-- 第一部分：环境信息检查
-- =============================================
PRINT '';
PRINT '第一部分：环境信息检查';
PRINT '----------------------------------------';

-- 检查 SQL Server 版本信息
DECLARE @Version NVARCHAR(128) = @@VERSION;
DECLARE @MajorVersion INT = CAST(SERVERPROPERTY('ProductMajorVersion') AS INT);
DECLARE @MinorVersion INT = CAST(SERVERPROPERTY('ProductMinorVersion') AS INT);
DECLARE @BuildVersion INT = CAST(SERVERPROPERTY('ProductBuild') AS INT);
DECLARE @ProductLevel NVARCHAR(128) = CAST(SERVERPROPERTY('ProductLevel') AS NVARCHAR(128));
DECLARE @Edition NVARCHAR(128) = CAST(SERVERPROPERTY('Edition') AS NVARCHAR(128));

PRINT '✓ SQL Server 版本信息:';
PRINT '  完整版本: ' + @Version;
PRINT '  主版本号: ' + CAST(@MajorVersion AS NVARCHAR(10));
PRINT '  次版本号: ' + CAST(@MinorVersion AS NVARCHAR(10));
PRINT '  构建版本: ' + CAST(@BuildVersion AS NVARCHAR(10));
PRINT '  产品级别: ' + @ProductLevel;
PRINT '  版本类型: ' + @Edition;

-- 检查版本兼容性
IF @MajorVersion < 11
BEGIN
    PRINT '✗ 错误: 需要 SQL Server 2012 或更高版本 (当前版本过低)';
    RETURN;
END
ELSE IF @MajorVersion = 11
BEGIN
    PRINT '✓ 版本兼容: SQL Server 2012 (推荐升级到最新 SP)';
END
ELSE
BEGIN
    PRINT '✓ 版本兼容: SQL Server ' + CAST(@MajorVersion AS NVARCHAR(10)) + ' (高于 2012)';
END

-- 检查数据库兼容级别
DECLARE @DbCompatibilityLevel INT = (SELECT compatibility_level FROM sys.databases WHERE name = DB_NAME());
PRINT '✓ 数据库兼容级别: ' + CAST(@DbCompatibilityLevel AS NVARCHAR(10));

IF @DbCompatibilityLevel < 110
BEGIN
    PRINT '⚠ 警告: 建议将兼容级别设置为 110 (SQL Server 2012) 或更高';
    PRINT '  执行: ALTER DATABASE [' + DB_NAME() + '] SET COMPATIBILITY_LEVEL = 110;';
END

-- =============================================
-- 第二部分：功能特性检查
-- =============================================
PRINT '';
PRINT '第二部分：功能特性检查';
PRINT '----------------------------------------';

-- 检查是否支持窗口函数
BEGIN TRY
    DECLARE @WindowFunctionTest INT;
    SELECT @WindowFunctionTest = COUNT(*) OVER() FROM sys.objects WHERE rownum = 1;
    PRINT '✓ 窗口函数: 支持';
END TRY
BEGIN CATCH
    PRINT '✗ 窗口函数: 不支持或有限制';
END CATCH

-- 检查是否支持 TRY_CAST (SQL Server 2012 新功能)
BEGIN TRY
    DECLARE @TryCastTest INT = TRY_CAST('123' AS INT);
    PRINT '✓ TRY_CAST 函数: 支持';
END TRY
BEGIN CATCH
    PRINT '✗ TRY_CAST 函数: 不支持';
END CATCH

-- 检查是否支持 SEQUENCE (SQL Server 2012 新功能)
BEGIN TRY
    IF EXISTS (SELECT * FROM sys.sequences WHERE name = 'TestSequence')
        DROP SEQUENCE TestSequence;
    
    CREATE SEQUENCE TestSequence START WITH 1 INCREMENT BY 1;
    DECLARE @SequenceTest INT = NEXT VALUE FOR TestSequence;
    DROP SEQUENCE TestSequence;
    PRINT '✓ SEQUENCE 对象: 支持';
END TRY
BEGIN CATCH
    PRINT '✗ SEQUENCE 对象: 不支持';
END CATCH

-- =============================================
-- 第三部分：权限管理系统表检查
-- =============================================
PRINT '';
PRINT '第三部分：权限管理系统表检查';
PRINT '----------------------------------------';

-- 检查核心表是否存在
DECLARE @TablesExist TABLE (TableName NVARCHAR(50), Exists BIT);

INSERT INTO @TablesExist (TableName, Exists)
VALUES 
    ('Users', CASE WHEN EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'Users') THEN 1 ELSE 0 END),
    ('Roles', CASE WHEN EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'Roles') THEN 1 ELSE 0 END),
    ('Permissions', CASE WHEN EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'Permissions') THEN 1 ELSE 0 END),
    ('UserRoles', CASE WHEN EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'UserRoles') THEN 1 ELSE 0 END),
    ('RolePermissions', CASE WHEN EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'RolePermissions') THEN 1 ELSE 0 END),
    ('UserPermissions', CASE WHEN EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'UserPermissions') THEN 1 ELSE 0 END),
    ('LoginLogs', CASE WHEN EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'LoginLogs') THEN 1 ELSE 0 END);

SELECT 
    CASE WHEN Exists = 1 THEN '✓' ELSE '✗' END + ' 表 ' + TableName + ': ' + 
    CASE WHEN Exists = 1 THEN '存在' ELSE '不存在' END AS CheckResult
FROM @TablesExist
ORDER BY TableName;

-- 统计表的存在情况
DECLARE @ExistingTablesCount INT = (SELECT COUNT(*) FROM @TablesExist WHERE Exists = 1);
DECLARE @TotalTablesCount INT = (SELECT COUNT(*) FROM @TablesExist);

PRINT '✓ 表检查结果: ' + CAST(@ExistingTablesCount AS NVARCHAR(10)) + '/' + CAST(@TotalTablesCount AS NVARCHAR(10)) + ' 个表存在';

-- =============================================
-- 第四部分：存储过程检查
-- =============================================
PRINT '';
PRINT '第四部分：存储过程检查';
PRINT '----------------------------------------';

-- 检查存储过程是否存在
DECLARE @StoredProceduresExist TABLE (ProcedureName NVARCHAR(100), Exists BIT);

INSERT INTO @StoredProceduresExist (ProcedureName, Exists)
VALUES 
    ('sp_ValidateUser', CASE WHEN EXISTS (SELECT * FROM INFORMATION_SCHEMA.ROUTINES WHERE ROUTINE_NAME = 'sp_ValidateUser' AND ROUTINE_TYPE = 'PROCEDURE') THEN 1 ELSE 0 END),
    ('sp_ValidateUserEnhanced', CASE WHEN EXISTS (SELECT * FROM INFORMATION_SCHEMA.ROUTINES WHERE ROUTINE_NAME = 'sp_ValidateUserEnhanced' AND ROUTINE_TYPE = 'PROCEDURE') THEN 1 ELSE 0 END),
    ('sp_GetUserInfo', CASE WHEN EXISTS (SELECT * FROM INFORMATION_SCHEMA.ROUTINES WHERE ROUTINE_NAME = 'sp_GetUserInfo' AND ROUTINE_TYPE = 'PROCEDURE') THEN 1 ELSE 0 END),
    ('sp_GetAllUsers', CASE WHEN EXISTS (SELECT * FROM INFORMATION_SCHEMA.ROUTINES WHERE ROUTINE_NAME = 'sp_GetAllUsers' AND ROUTINE_TYPE = 'PROCEDURE') THEN 1 ELSE 0 END),
    ('sp_GetUserPermissions', CASE WHEN EXISTS (SELECT * FROM INFORMATION_SCHEMA.ROUTINES WHERE ROUTINE_NAME = 'sp_GetUserPermissions' AND ROUTINE_TYPE = 'PROCEDURE') THEN 1 ELSE 0 END),
    ('sp_CreateUser', CASE WHEN EXISTS (SELECT * FROM INFORMATION_SCHEMA.ROUTINES WHERE ROUTINE_NAME = 'sp_CreateUser' AND ROUTINE_TYPE = 'PROCEDURE') THEN 1 ELSE 0 END),
    ('sp_AssignUserRole', CASE WHEN EXISTS (SELECT * FROM INFORMATION_SCHEMA.ROUTINES WHERE ROUTINE_NAME = 'sp_AssignUserRole' AND ROUTINE_TYPE = 'PROCEDURE') THEN 1 ELSE 0 END),
    ('sp_PermissionSystemStats', CASE WHEN EXISTS (SELECT * FROM INFORMATION_SCHEMA.ROUTINES WHERE ROUTINE_NAME = 'sp_PermissionSystemStats' AND ROUTINE_TYPE = 'PROCEDURE') THEN 1 ELSE 0 END),
    ('sp_MaintenancePermissionSystem', CASE WHEN EXISTS (SELECT * FROM INFORMATION_SCHEMA.ROUTINES WHERE ROUTINE_NAME = 'sp_MaintenancePermissionSystem' AND ROUTINE_TYPE = 'PROCEDURE') THEN 1 ELSE 0 END);

SELECT 
    CASE WHEN Exists = 1 THEN '✓' ELSE '✗' END + ' 存储过程 ' + ProcedureName + ': ' + 
    CASE WHEN Exists = 1 THEN '存在' ELSE '不存在' END AS CheckResult
FROM @StoredProceduresExist
ORDER BY ProcedureName;

-- 统计存储过程的存在情况
DECLARE @ExistingProceduresCount INT = (SELECT COUNT(*) FROM @StoredProceduresExist WHERE Exists = 1);
DECLARE @TotalProceduresCount INT = (SELECT COUNT(*) FROM @StoredProceduresExist);

PRINT '✓ 存储过程检查结果: ' + CAST(@ExistingProceduresCount AS NVARCHAR(10)) + '/' + CAST(@TotalProceduresCount AS NVARCHAR(10)) + ' 个存储过程存在';

-- =============================================
-- 第五部分：索引检查
-- =============================================
PRINT '';
PRINT '第五部分：索引检查';
PRINT '----------------------------------------';

-- 检查关键索引是否存在
SELECT 
    '✓ 索引 ' + i.name + ' 在表 ' + OBJECT_NAME(i.object_id) + ': 存在' AS CheckResult
FROM sys.indexes i
WHERE i.name IS NOT NULL 
  AND i.is_primary_key = 0 
  AND OBJECT_NAME(i.object_id) IN ('Users', 'Roles', 'Permissions', 'UserRoles', 'RolePermissions', 'UserPermissions', 'LoginLogs')
ORDER BY OBJECT_NAME(i.object_id), i.name;

-- =============================================
-- 第六部分：数据完整性检查
-- =============================================
PRINT '';
PRINT '第六部分：数据完整性检查';
PRINT '----------------------------------------';

-- 检查是否有初始数据
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'Users')
BEGIN
    DECLARE @UserCount INT = (SELECT COUNT(*) FROM Users);
    PRINT '✓ 用户数据: ' + CAST(@UserCount AS NVARCHAR(10)) + ' 个用户';
    
    IF @UserCount = 0
        PRINT '⚠ 警告: 没有用户数据，需要执行数据初始化';
END

IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'Roles')
BEGIN
    DECLARE @RoleCount INT = (SELECT COUNT(*) FROM Roles);
    PRINT '✓ 角色数据: ' + CAST(@RoleCount AS NVARCHAR(10)) + ' 个角色';
END

IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'Permissions')
BEGIN
    DECLARE @PermissionCount INT = (SELECT COUNT(*) FROM Permissions);
    PRINT '✓ 权限数据: ' + CAST(@PermissionCount AS NVARCHAR(10)) + ' 个权限';
END

-- =============================================
-- 第七部分：性能测试
-- =============================================
PRINT '';
PRINT '第七部分：性能测试';
PRINT '----------------------------------------';

-- 简单的查询性能测试
DECLARE @StartTime DATETIME = GETDATE();

-- 模拟一些查询操作
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'Users')
BEGIN
    DECLARE @TestResult INT;
    SELECT @TestResult = COUNT(*) FROM Users;
    SELECT @TestResult = COUNT(*) FROM Roles;
    SELECT @TestResult = COUNT(*) FROM Permissions;
END

DECLARE @EndTime DATETIME = GETDATE();
DECLARE @Duration INT = DATEDIFF(millisecond, @StartTime, @EndTime);

PRINT '✓ 基础查询性能: ' + CAST(@Duration AS NVARCHAR(10)) + ' 毫秒';

IF @Duration > 1000
    PRINT '⚠ 警告: 查询响应时间较长，建议检查索引和统计信息';

-- =============================================
-- 第八部分：功能测试
-- =============================================
PRINT '';
PRINT '第八部分：功能测试';
PRINT '----------------------------------------';

-- 测试存储过程是否可以正常调用
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.ROUTINES WHERE ROUTINE_NAME = 'sp_PermissionSystemStats' AND ROUTINE_TYPE = 'PROCEDURE')
BEGIN
    BEGIN TRY
        EXEC sp_PermissionSystemStats;
        PRINT '✓ 系统统计存储过程: 可正常执行';
    END TRY
    BEGIN CATCH
        PRINT '✗ 系统统计存储过程: 执行失败 - ' + ERROR_MESSAGE();
    END CATCH
END
ELSE
BEGIN
    PRINT '✗ 系统统计存储过程: 不存在';
END

-- =============================================
-- 第九部分：安全检查
-- =============================================
PRINT '';
PRINT '第九部分：安全检查';
PRINT '----------------------------------------';

-- 检查数据库认证模式
DECLARE @AuthMode INT = (SELECT CAST(SERVERPROPERTY('IsIntegratedSecurityOnly') AS INT));
IF @AuthMode = 1
    PRINT '✓ 认证模式: Windows 身份验证 (推荐)';
ELSE
    PRINT '⚠ 认证模式: 混合模式 (请确保 SQL 账户安全)';

-- 检查数据库加密状态
IF EXISTS (SELECT * FROM sys.dm_database_encryption_keys WHERE database_id = DB_ID())
    PRINT '✓ 数据库加密: 已启用 TDE';
ELSE
    PRINT '⚠ 数据库加密: 未启用 TDE (生产环境建议启用)';

-- =============================================
-- 检查结果总结
-- =============================================
PRINT '';
PRINT '========================================';
PRINT '兼容性检查完成';
PRINT '========================================';

-- 计算总体兼容性得分
DECLARE @TotalScore INT = 0;
DECLARE @MaxScore INT = 10;

-- 版本检查 (2分)
IF @MajorVersion >= 11
    SET @TotalScore = @TotalScore + 2;

-- 表检查 (2分)
IF @ExistingTablesCount = @TotalTablesCount
    SET @TotalScore = @TotalScore + 2;
ELSE IF @ExistingTablesCount > 0
    SET @TotalScore = @TotalScore + 1;

-- 存储过程检查 (2分)
IF @ExistingProceduresCount = @TotalProceduresCount
    SET @TotalScore = @TotalScore + 2;
ELSE IF @ExistingProceduresCount > 0
    SET @TotalScore = @TotalScore + 1;

-- 数据检查 (2分)
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'Users') AND 
   (SELECT COUNT(*) FROM Users) > 0
    SET @TotalScore = @TotalScore + 2;

-- 性能检查 (1分)
IF @Duration <= 1000
    SET @TotalScore = @TotalScore + 1;

-- 功能检查 (1分)
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.ROUTINES WHERE ROUTINE_NAME = 'sp_PermissionSystemStats')
    SET @TotalScore = @TotalScore + 1;

PRINT '总体兼容性得分: ' + CAST(@TotalScore AS NVARCHAR(10)) + '/' + CAST(@MaxScore AS NVARCHAR(10));

IF @TotalScore >= 9
    PRINT '✓ 系统状态: 优秀，可以正常使用';
ELSE IF @TotalScore >= 7
    PRINT '⚠ 系统状态: 良好，建议完善缺失组件';
ELSE IF @TotalScore >= 5
    PRINT '⚠ 系统状态: 一般，需要解决一些问题';
ELSE
    PRINT '✗ 系统状态: 不佳，需要重新配置和安装';

PRINT '========================================';

-- 提供改进建议
IF @TotalScore < @MaxScore
BEGIN
    PRINT '';
    PRINT '改进建议:';
    
    IF @MajorVersion < 11
        PRINT '1. 升级到 SQL Server 2012 或更高版本';
    
    IF @ExistingTablesCount < @TotalTablesCount
        PRINT '2. 运行数据库初始化脚本创建缺失的表';
    
    IF @ExistingProceduresCount < @TotalProceduresCount
        PRINT '3. 运行存储过程脚本创建缺失的存储过程';
    
    IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'Users') OR 
       (SELECT COUNT(*) FROM Users) = 0
        PRINT '4. 运行数据初始化脚本导入基础数据';
    
    IF @Duration > 1000
        PRINT '5. 更新统计信息和重建索引以提高性能';
    
    PRINT '6. 定期执行维护操作以保持系统最佳状态';
END

PRINT '========================================';

-- 输出下一步操作指南
PRINT '';
PRINT '下一步操作指南:';
PRINT '1. 如果是首次安装，请按顺序执行:';
PRINT '   - SQL2012_Setup.sql';
PRINT '   - StoredProcedures.sql';
PRINT '   - 运行应用程序进行数据初始化';
PRINT '';
PRINT '2. 如果是日常检查，请定期执行:';
PRINT '   - EXEC sp_MaintenancePermissionSystem ''ALL'';';
PRINT '';
PRINT '3. 如果发现问题，请参考部署指南进行故障排除';
PRINT '========================================'; 