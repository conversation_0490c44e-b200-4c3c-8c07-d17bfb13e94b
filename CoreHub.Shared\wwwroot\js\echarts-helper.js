// ECharts 图表助手
window.echartsHelper = {
    charts: {},

    // 初始化设备状态甜甜圈图表
    initEquipmentChart: function(containerId, data) {
        const container = document.getElementById(containerId);
        if (!container) {
            console.error('找不到容器:', containerId);
            return;
        }

        // 销毁已存在的图表
        if (this.charts[containerId]) {
            this.charts[containerId].dispose();
        }

        // 创建新图表
        const chart = echarts.init(container);
        this.charts[containerId] = chart;

        const option = {
            title: {
                text: '设备总数',
                subtext: data.total.toString(),
                left: 'center',
                top: 'center',
                textStyle: {
                    fontSize: 14,
                    color: '#666'
                },
                subtextStyle: {
                    fontSize: 28,
                    fontWeight: 'bold',
                    color: '#333'
                }
            },
            tooltip: {
                trigger: 'item',
                formatter: function(params) {
                    const percent = params.percent.toFixed(1);
                    return `${params.name}: ${params.value} (${percent}%)`;
                }
            },
            legend: {
                show: false
            },
            series: [{
                type: 'pie',
                radius: ['50%', '80%'],
                center: ['50%', '50%'],
                avoidLabelOverlap: false,
                label: {
                    show: false
                },
                emphasis: {
                    label: {
                        show: false
                    },
                    scale: true,
                    scaleSize: 5
                },
                labelLine: {
                    show: false
                },
                data: [
                    { value: data.normal, name: '正常', itemStyle: { color: '#4caf50' } },
                    { value: data.maintenance, name: '维修中', itemStyle: { color: '#ff9800' } },
                    { value: data.disabled, name: '停用', itemStyle: { color: '#9e9e9e' } },
                    { value: data.scrap, name: '报废', itemStyle: { color: '#f44336' } }
                ]
            }]
        };

        chart.setOption(option);
        
        // 响应式调整
        window.addEventListener('resize', function() {
            chart.resize();
        });

        return chart;
    },

    // 初始化报修单状态甜甜圈图表
    initRepairOrderChart: function(containerId, data) {
        const container = document.getElementById(containerId);
        if (!container) {
            console.error('找不到容器:', containerId);
            return;
        }

        // 销毁已存在的图表
        if (this.charts[containerId]) {
            this.charts[containerId].dispose();
        }

        // 创建新图表
        const chart = echarts.init(container);
        this.charts[containerId] = chart;

        const option = {
            title: {
                text: '报修总数',
                subtext: data.total.toString(),
                left: 'center',
                top: 'center',
                textStyle: {
                    fontSize: 14,
                    color: '#666'
                },
                subtextStyle: {
                    fontSize: 28,
                    fontWeight: 'bold',
                    color: '#333'
                }
            },
            tooltip: {
                trigger: 'item',
                formatter: function(params) {
                    const percent = params.percent.toFixed(1);
                    return `${params.name}: ${params.value} (${percent}%)`;
                }
            },
            legend: {
                show: false
            },
            series: [{
                type: 'pie',
                radius: ['50%', '80%'],
                center: ['50%', '50%'],
                avoidLabelOverlap: false,
                label: {
                    show: false
                },
                emphasis: {
                    label: {
                        show: false
                    },
                    scale: true,
                    scaleSize: 5
                },
                labelLine: {
                    show: false
                },
                data: [
                    { value: data.pending, name: '待处理', itemStyle: { color: '#ff9800' } },
                    { value: data.inProgress, name: '处理中', itemStyle: { color: '#2196f3' } },
                    { value: data.completed, name: '已完成', itemStyle: { color: '#4caf50' } },
                    { value: data.cancelled, name: '已作废', itemStyle: { color: '#9e9e9e' } },
                    { value: data.closed, name: '已关闭', itemStyle: { color: '#607d8b' } },
                    { value: data.pendingConfirmation, name: '待确认', itemStyle: { color: '#00bcd4' } }
                ]
            }]
        };

        chart.setOption(option);
        
        // 响应式调整
        window.addEventListener('resize', function() {
            chart.resize();
        });

        return chart;
    },

    // 销毁所有图表
    disposeAll: function() {
        for (const chartId in this.charts) {
            if (this.charts[chartId]) {
                this.charts[chartId].dispose();
                delete this.charts[chartId];
            }
        }
    },

    // 销毁指定图表
    dispose: function(containerId) {
        if (this.charts[containerId]) {
            this.charts[containerId].dispose();
            delete this.charts[containerId];
        }
    }
};
