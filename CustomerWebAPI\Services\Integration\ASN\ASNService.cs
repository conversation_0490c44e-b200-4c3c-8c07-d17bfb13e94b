﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Net.Http.Json;
using System.Text;
using System.Threading.Tasks;
using System.Transactions;
using CustomerWebAPI.Helpers;
using CustomerWebAPI.Models;
using Dapper;
using Dapper.Contrib.Extensions;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using Microsoft.VisualBasic;
using Newtonsoft.Json;

namespace CustomerWebAPI.Services
{
    public class ASNService : IASNService
    {
        private readonly HttpClient _asnClient;
        private readonly IConfiguration _configuration;
        private readonly string _salesDBConnString;

        public ASNService(HttpClient client, IConfiguration configuration)
        {
            _asnClient = client;
            _configuration = configuration;
            _salesDBConnString = _configuration.GetConnectionString("SalesDatabase");
        }


        public async Task<TrackingIdModel> SendASN(string seriAsnJson,string receiveURI)
        {
            //var asnUritest = _configuration.GetValue<string>("TalAsnUriProduction");
            var asnUri = receiveURI;
            var asnUsername = _configuration.GetValue<string>("TalAsnUsername");
            var asnPassword = _configuration.GetValue<string>("TalAsnPassword");

            _asnClient.DefaultRequestHeaders.Authorization = new BasicAuthenticationHeaderValue(asnUsername, asnPassword);

            var content = new StringContent(seriAsnJson);
            content.Headers.ContentType = new MediaTypeHeaderValue("application/json");

            var response = await _asnClient.PostAsync(asnUri, content);
            response.EnsureSuccessStatusCode();
            var trackingIdModel = await response.Content.ReadFromJsonAsync<TrackingIdModel>();
            return trackingIdModel;
        }

        public (bool,string,string) GetNewAsnRootID(string deliveryNotes)
        {
            using SqlConnection connection = new(_salesDBConnString);
            connection.Open();
            using SqlTransaction transaction = connection.BeginTransaction();
            try
            {
                var dynamicParams = new DynamicParameters();
                dynamicParams.Add("DeliveryNoteNoList", deliveryNotes, DbType.String, direction: ParameterDirection.Input);
                dynamicParams.Add("AsnRootId", dbType: DbType.Guid, direction: ParameterDirection.Output);
                var data = connection.Query<Guid>("usp_talGenerateAsn", dynamicParams, transaction, commandType: CommandType.StoredProcedure);
                var result = dynamicParams.Get<Guid>("AsnRootId");
                transaction.Commit();
                return (true, string.Empty, result.ToString());
            }
            catch (Exception ex)
            {
                transaction.Rollback();
                var errorMsg = "GenerateASN failed!" + Environment.NewLine + ex.Message;
                return (false, errorMsg, string.Empty);
            }

        }

    

        public async Task<(bool, string, TrackingIdModel)> SendAsnToTalAsync(DeliveryNoteModel deliveryNote)
        {
            try
            {
                if (string.IsNullOrEmpty(deliveryNote.DeliveryNoteNo)) 
                { 
                    return (false, "单号为空，请检查", null); 
                }

                (bool IsSuccess, string ErrorMsg, string NewAsnRootID) GenResult = GetNewAsnRootID(deliveryNote.DeliveryNoteNo);
                if (!GenResult.IsSuccess) 
                { 
                    return (false, GenResult.ErrorMsg, null); 
                }

                (string asnURI, string asnSeriJSON) asnTuple = GetAsnJsonFromDb(GenResult.NewAsnRootID);
                var trackingIdModel = await SendASN(asnTuple.asnSeriJSON, asnTuple.asnURI);
                return (true, string.Empty, trackingIdModel);
            }
            catch (Exception e)
            {
                var errorMsg = "SendASN failed!" + Environment.NewLine + e.Message;
                return (false, errorMsg, null);
            }
        }


        public (string,string) GetAsnJsonFromDb(string rootId)
        {
            //接收ASN的URI
            var ReceiveURL = string.Empty;

            List<ASNRoot> asnRootsList;
            List<ASNItems> asnItemsList;
            List<ASNHeader> asnHeaderList;
            List<ASNDetail> asnDetailList;
            List<ASNDetailParties> asnDetailPartiesList;
            List<ASNDetailOrders> asnDetailOrdersList;
            List<ASNDetailOrdersParties> asnDetailOrdersPartiesList;
            List<ASNDetailOrdersItems> asnDetailOrdersItemsList;
            List<ASNDetailOrdersItemsPackages> asnDetailOrdersItemsPackagesList;
            List<ASNDetailOrdersItemsPackagesWeights> asnDetailOrdersItemsPackagesWeightsList;
            List<ASNDetailOrdersItemsPackagesMeasurements> asnDetailOrdersItemsPackagesMeasurementsList;

            using (IDbConnection connection = new SqlConnection(_salesDBConnString))
            {
                connection.Open();

                //var rootId = "DEAC1787-8D06-4CDC-8F1C-6F69A97F3483";

                const string sql = "usp_GetASNInfoByTableName";
                asnRootsList = connection.Query<ASNRoot>(sql, new
                {
                    AsnRootID = rootId,
                    TableName = "ASNRoot"
                }, commandType: CommandType.StoredProcedure).ToList();
                asnItemsList = connection.Query<ASNItems>(sql, new
                {
                    AsnRootID = rootId,
                    TableName = "ASNItems"
                }, commandType: CommandType.StoredProcedure).ToList();
                asnHeaderList = connection.Query<ASNHeader>(sql, new
                {
                    AsnRootID = rootId,
                    TableName = "ASNHeader"
                }, commandType: CommandType.StoredProcedure).ToList();
                asnDetailList = connection.Query<ASNDetail>(sql, new
                {
                    AsnRootID = rootId,
                    TableName = "ASNDetail"
                }, commandType: CommandType.StoredProcedure).ToList();
                asnDetailPartiesList = connection.Query<ASNDetailParties>(sql, new
                {
                    AsnRootID = rootId,
                    TableName = "ASNDetailParties"
                }, commandType: CommandType.StoredProcedure).ToList();
                asnDetailOrdersList = connection.Query<ASNDetailOrders>(sql, new
                {
                    AsnRootID = rootId,
                    TableName = "ASNDetailOrders"
                }, commandType: CommandType.StoredProcedure).ToList();
                asnDetailOrdersPartiesList = connection.Query<ASNDetailOrdersParties>(sql, new
                {
                    AsnRootID = rootId,
                    TableName = "ASNDetailOrdersParties"
                }, commandType: CommandType.StoredProcedure).ToList();
                asnDetailOrdersItemsList = connection.Query<ASNDetailOrdersItems>(sql, new
                {
                    AsnRootID = rootId,
                    TableName = "ASNDetailOrdersItems"
                }, commandType: CommandType.StoredProcedure).ToList();
                asnDetailOrdersItemsPackagesList = connection.Query<ASNDetailOrdersItemsPackages>(sql, new
                {
                    AsnRootID = rootId,
                    TableName = "ASNDetailOrdersItemsPackages"
                }, commandType: CommandType.StoredProcedure).ToList();
                asnDetailOrdersItemsPackagesWeightsList = connection.Query<ASNDetailOrdersItemsPackagesWeights>(sql, new
                {
                    AsnRootID = rootId,
                    TableName = "ASNDetailOrdersItemsPackagesWeights"
                }, commandType: CommandType.StoredProcedure).ToList();
                asnDetailOrdersItemsPackagesMeasurementsList = connection.Query<ASNDetailOrdersItemsPackagesMeasurements>(sql, new
                {
                    AsnRootID = rootId,
                    TableName = "ASNDetailOrdersItemsPackagesMeasurements"
                }, commandType: CommandType.StoredProcedure).ToList();
            }
            
            
            

            var rootASNs = new List<RootASN>();
            asnRootsList.ForEach(rt =>
            {
                var asnrtForAdd = new RootASN
                {
                    SenderId = rt.SenderId,
                    SenderIdentifierId = rt.SenderIdentifierId,
                    ReceiverId = rt.ReceiverId,
                    ReceiverIdentifierId = rt.ReceiverIdentifierId,
                    TrackNumber = Convert.ToInt32(rt.TrackNumber),
                    ProductionIndicator = rt.ProductionIndicator
                    //AdvancedShipNotes = advancedShipNotesItemsList
                };

                
                switch (asnrtForAdd.ReceiverId)
                {
                    case "TAP":/*TAP*/
                        ReceiveURL = "https://api.talgroup.com/b2b/api/1.0/suppliers/asn";
                        break;
                    case "8687738280":/*TOA*/
                        ReceiveURL = "https://api.theorient.cn/b2b/api/1.0/suppliers/asn";
                        break;
                }

                var advancedShipNotesItemsList = new List<AdvancedShipNotesItem>();


                //AdvancedShipNotesItems
                var filteredItemsList = asnItemsList.Where(x => x.ASNRootId == rt.Id).ToList();
                asnItemsList.ForEach(ai =>
                {
                    var asniForAdd = new AdvancedShipNotesItem();

                    //header
                    var filteredHeaderList = asnHeaderList.Where(x => x.ASNItemsId == ai.Id).ToList();
                    filteredHeaderList.ForEach(hd =>
                    {
                        var hdForAdd = new Models.Header
                        {
                            PurposeCode = hd.PurposeCode,
                            AdvancedShipNoteNumber = hd.AdvancedShipNoteNumber,
                            HierarchicalStructureCode = hd.HierarchicalStructureCode,
                            DocumentIndex = hd.DocumentIndex,
                            TransactionTypeCode = hd.TransactionTypeCode,
                            SentDate = hd.SentDate,
                            SentTime = hd.SentTime
                        };

                        asniForAdd.Header = hdForAdd;
                    });

                    //detail
                    var dtlForAdd = new Detail();
                    var filteredDetailList = asnDetailList.Where(x => x.ASNItemsId == ai.Id).ToList();
                    filteredDetailList.ForEach(dtl =>
                    {
                        var ordersItemsList = new List<OrdersItem>();

                        dtlForAdd.CarrierDetails.PackagingCode = dtl.PackagingCode;
                        dtlForAdd.CarrierDetails.LadingQuantity = dtl.LadingQuantity;
                        dtlForAdd.CarrierDetails.Weights.Add(new WeightsItem
                        {
                            Qualifier = dtl.WeightsQualifier,
                            Unit = dtl.WeightsUnit,
                            Value = Convert.ToDouble(dtl.WeightsValue)
                        });
                        dtlForAdd.CarrierDetails.TransportationTypeCode = dtl.TransportationTypeCode;
                        dtlForAdd.CarrierDetails.ContainerNumber = dtl.ContainerNumber;
                        dtlForAdd.DivisionCode = dtl.DivisionCode;
                        dtlForAdd.Transportation.BillNumber = dtl.BillNumber;
                        dtlForAdd.Transportation.VoyageNumber = dtl.VoyageNumber;
                        dtlForAdd.Transportation.VesselNumber = dtl.VesselNumber;
                        dtlForAdd.Transportation.ShipMode = dtl.ShipMode;
                        dtlForAdd.Dates.SailDate = dtl.SailDate;
                        dtlForAdd.Dates.DeliveryDate = dtl.DeliveryDate;
                        dtlForAdd.Dates.EstimatedArrivalDate = dtl.EstimatedArrivalDate;


                        //asnDetailPartiesList
                        var detailPartiesItemsList = new List<PartiesItem>();
                        var filteredDetailPartiesList = asnDetailPartiesList.Where(ord => ord.ASNDetailId == dtl.Id).ToList();
                        filteredDetailPartiesList.ForEach(dp =>
                        {
                            var dpForAdd = new PartiesItem();
                            dpForAdd.Identifier = dp.Identifier;
                            dpForAdd.Name = dp.Name;
                            dpForAdd.Code = dp.Code;
                            if (string.IsNullOrWhiteSpace(dp.AddressLine1) && string.IsNullOrWhiteSpace(dp.AddressLine2))
                            {
                                dpForAdd.Address = null;
                            }
                            else
                            {
                                dpForAdd.Address.AddressLine1 = dp.AddressLine1;
                                dpForAdd.Address.AddressLine2 = string.IsNullOrWhiteSpace(dp.AddressLine2) ? null : dp.AddressLine2;
                            }


                            detailPartiesItemsList.Add(dpForAdd);
                        });

                        //asnDetailOrdersList
                        var filteredOrdersList = asnDetailOrdersList.Where(ord => ord.ASNDetailId == dtl.Id).ToList();
                        filteredOrdersList.ForEach(od =>
                        {
                            var odForAdd = new OrdersItem
                            {
                                PurchaseOrderNumber = od.PurchaseOrderNumber,
                                PurchaseOrderReleaseNumber = od.PurchaseOrderReleaseNumber
                            };


                            ordersItemsList.Add(odForAdd);

                            //asnDetailOrdersPartiesList
                            var orderPartiesItemsList = new List<PartiesItem>();
                            var filteredOrdersPartiesList = asnDetailOrdersPartiesList.Where(op => op.ASNDetailOrdersId == od.Id).ToList();
                            filteredOrdersPartiesList.ForEach(op =>
                            {
                                var opForAdd = new PartiesItem();
                                opForAdd.Identifier = op.Identifier;
                                opForAdd.Name = op.Name;
                                opForAdd.Code = op.Code;
                                if (string.IsNullOrWhiteSpace(op.AddressLine1) && string.IsNullOrWhiteSpace(op.AddressLine2))
                                {
                                    opForAdd.Address = null;
                                }
                                else
                                {
                                    opForAdd.Address.AddressLine1 = op.AddressLine1;
                                    opForAdd.Address.AddressLine2 = string.IsNullOrWhiteSpace(op.AddressLine2) ? null : op.AddressLine2;
                                }

                                orderPartiesItemsList.Add(opForAdd);
                            });

                            //asnDetailOrdersItemsList
                            var ordersItemsItemList = new List<ItemsItem>();
                            var filteredOrdersItemsList = asnDetailOrdersItemsList.Where(oi => oi.ASNDetailOrdersId == od.Id).ToList();
                            filteredOrdersItemsList.ForEach(oi =>
                            {
                                var oiForAdd = new ItemsItem
                                {
                                    AssignedId = oi.AssignedId,
                                    PoLineNumber = oi.PoLineNumber,
                                    ItemNumber = oi.ItemNumber,
                                    ContractNumber = oi.ContractNumber,
                                    DyeLotSeries = oi.DyeLotSeries,
                                    SubLineNumber = oi.SubLineNumber,
                                    PackQuantity = oi.PackQuantity,
                                    PackCode = oi.PackCode
                                };
                                oiForAdd.Weights.Add(new WeightsItem
                                {
                                    Qualifier = oi.WeightsQualifier,
                                    Unit = oi.WeightsUnit,
                                    Value = Convert.ToDouble(oi.WeightsValue)
                                });
                                oiForAdd.DyeMatch = oi.DyeMatch;
                                oiForAdd.ColorDescription = oi.ColorDescription;
                                oiForAdd.FabricDescription = oi.FabricDescription;
                                oiForAdd.FreeOfChargeQuantity = Convert.ToDouble(oi.FreeOfChargeQuantity);


                                ordersItemsItemList.Add(oiForAdd);


                                //asnDetailOrdersItemsPackagesList
                                var packagesItemsList = new List<PackagesItem>();
                                var oipList = asnDetailOrdersItemsPackagesList.Where(pki => pki.ASNDetailOrdersItemsId == oi.Id).ToList();
                                oipList.ForEach(pk =>
                                {
                                    var weightsItemList = new List<WeightsItem>();
                                    var measurementsItemsList = new List<MeasurementsItem>();

                                    var pkitem = new PackagesItem
                                    {
                                        AssignedId = pk.AssignedId,
                                        Quantity = Convert.ToDouble(pk.Quantity),
                                        QuantityUnit = pk.QuantityUnit,
                                        FabricWidth = Convert.ToDouble(pk.FabricWidth),
                                        WidthUnit = pk.WidthUnit,
                                        QaStatus = pk.QaStatus,
                                        FabricLengthString = pk.FabricLengthString,
                                        UCC = pk.UCC,
                                        RollNumber = pk.RollNumber
                                    };


                                    //asnDetailOrdersItemsPackagesWeightsList                            
                                    var pkwTempList = asnDetailOrdersItemsPackagesWeightsList.Where(pkw => pkw.ASNDetailOrdersItemsPackagesId == pk.Id).ToList();
                                    pkwTempList.ForEach(wi =>
                                    {
                                        weightsItemList.Add(new WeightsItem
                                        {
                                            Qualifier = wi.Qualifier,
                                            Unit = wi.Unit,
                                            Value = Convert.ToDouble(wi.Value)
                                        });
                                    });

                                    //asnDetailOrdersItemsPackagesMeasurementsList
                                    var pkmsTempList = asnDetailOrdersItemsPackagesMeasurementsList.Where(pkms => pkms.ASNDetailOrdersItemsPackagesId == pk.Id).ToList();
                                    pkmsTempList.ForEach(pm =>
                                    {
                                        measurementsItemsList.Add(new MeasurementsItem
                                        {
                                            MeasurementUnit = pm.MeasurementUnit,
                                            Name = pm.Name,
                                            Value = Convert.ToDouble(pm.Value)
                                        });
                                    });

                                    pkitem.Weights = weightsItemList;
                                    pkitem.Measurements = measurementsItemsList;


                                    packagesItemsList.Add(pkitem);
                                });


                                oiForAdd.Packages.AddRange(packagesItemsList);
                            });


                            odForAdd.Parties.AddRange(orderPartiesItemsList);
                            odForAdd.Items.AddRange(ordersItemsItemList);
                        });
                        dtlForAdd.Parties.AddRange(detailPartiesItemsList);
                        dtlForAdd.Orders.AddRange(ordersItemsList);

                        asniForAdd.Detail = dtlForAdd;
                    });

                    advancedShipNotesItemsList.Add(asniForAdd);
                });

                asnrtForAdd.AdvancedShipNotes.AddRange(advancedShipNotesItemsList);

                rootASNs.Add(asnrtForAdd);
            });

          

            var jsonSetting = new JsonSerializerSettings
            {
                NullValueHandling = NullValueHandling.Ignore
            };
            var result = JsonConvert.SerializeObject(rootASNs.FirstOrDefault(), jsonSetting);

            if (!JsonHelper.ValidateAsnJson(result)) throw new Exception("Invalid JSON for ASN JSON Schema! 请使用JSON工具验证：https://jsonschemalint.com/#!/version/draft-07/markup/json");

            return new (ReceiveURL,result);
        }
    }
}