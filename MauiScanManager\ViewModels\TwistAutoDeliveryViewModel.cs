﻿using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using MauiScanManager.Models;
using MauiScanManager.Services;
using System.Collections.ObjectModel;


namespace MauiScanManager.ViewModels
{
    public partial class TwistAutoDeliveryViewModel : BaseOperationViewModel
    {
        private readonly IAudioService _audioService;
        private readonly ITwistService _twistService;
        private readonly IPlatformLoadingService _loadingService;


        [ObservableProperty]
        private string boxNo = string.Empty;
        [ObservableProperty]
        private string boxNoList = string.Empty;
        [ObservableProperty]
        private int scanNum = 0;

        public TwistAutoDeliveryViewModel(
            IScanService scanService,
            IDialogService dialogService,
            IAudioService audioService,
            ITwistService twistService,
            IPlatformLoadingService loadingService)
            : base(scanService, dialogService)
        {
            _audioService = audioService;
            _twistService = twistService;
            _loadingService = loadingService;
        }


        public override async void Initialize(Operation operation)
        {
            base.Initialize(operation);
            ResetScanState();
        }

        protected override void ProcessScanResult(string code, string type, byte[] codeSource)
        {
            if (string.IsNullOrEmpty(code)) return;

            MainThread.BeginInvokeOnMainThread(async () =>
            {
                try
                {
                    await ProcessScanStateAsync(code);
                }
                catch (Exception ex)
                {
                    await _dialogService.ShowErrorAsync($"处理扫描结果失败：{ex.Message}");
                }
            });
        }



        private async Task ProcessScanStateAsync(string code)
        {
            if (string.IsNullOrWhiteSpace(code))
            {
                await _dialogService.ShowWarningAsync("无效的箱号");
                return;
            }

            if (boxNoList.Contains(code)) { return; }
            else
            {
                BoxNo = code;
                boxNoList = code + ',' + boxNoList;
                ScanNum = ScanNum + 1;
            }
        }

        private string GetFriendlyErrorMessage(Exception ex)
        {
            if (ex.Message.Contains("HttpClient.Timeout") ||
                ex.Message.Contains("The request was canceled"))
            {
                return "网络请求超时，请检查网络连接后重试";
            }
            return ex.Message;
        }


        [RelayCommand]
        private async Task TwAutoDelivery()
        {
            if (string.IsNullOrEmpty(boxNoList) )
            {
                await _dialogService.ShowWarningAsync("请输入完整信息");
                return;
            }

            try
            {
                _loadingService?.ShowNativeLoading("正在保存...");
                var model = new TwistAutoDelivery
                {
                    BoxNoList = boxNoList
                };

                var result = await _twistService.AutoDeliveryByScanAsync(model);
                if (!result.IsSuccess)
                {
                    await _dialogService.ShowErrorAsync(result.ErrorMessage);
                    return;
                }
                await _dialogService.ShowSuccessAsync("出货成功！");
            }
            catch (Exception ex)
            {
                await _dialogService.ShowErrorAsync(GetFriendlyErrorMessage(ex));
            }
            finally
            {
                _loadingService?.HideNativeLoading();
                ResetScanState();
            }
        }

        [RelayCommand]

        private void ResetScanState()
        {
            BoxNo = string.Empty;
            boxNoList = string.Empty;
            ScanNum = 0;
        }

    }
}
