using System;
using System.Threading.Tasks;

namespace MauiScanManager.Services
{
    public interface IPrintService
    {
        event EventHandler<PrintEventArgs> OnPrintResult;
        void Initialize();
        void Dispose();
        
        // 原有的异步方法（返回bool，通过事件回调）
        Task<bool> PrintTextAsync(string text);
        Task<bool> PrintTextAsync(string text, PrintConfig config);
        Task<bool> PrintTextAsync(string text, PrintConfig config, bool enableBlackMark);
        Task<bool> PrintTextAsync(string text, PrintConfig config, bool enableBlackMark, int feedPaperSpace, int unwindPaperLen);
        Task<bool> PrintBarcodeAsync(string content, BarcodeType barcodeType = BarcodeType.Code128, bool enableBlackMark = false);
        Task<bool> PrintBarcodeAsync(string content, BarcodeType barcodeType, bool enableBlackMark, int feedPaperSpace, int unwindPaperLen);
        Task<bool> PrintBarcodeAsync(string content, BarcodeType barcodeType, HRIPosition hriPosition, bool enableBlackMark = false);
        Task<bool> PrintBarcodeAsync(string content, BarcodeType barcodeType, HRIPosition hriPosition, bool enableBlackMark, int feedPaperSpace, int unwindPaperLen);
        Task<bool> PrintQRCodeAsync(string content, bool enableBlackMark = false);
        Task<bool> PrintQRCodeAsync(string content, int height, bool enableBlackMark = false);
        Task<bool> PrintQRCodeAsync(string content, int height, bool enableBlackMark, int feedPaperSpace, int unwindPaperLen);
        Task<bool> PrintQRCodeAsync(string content, PrintConfig.TextAlign align, int height = 184, bool enableBlackMark = false);
        Task<bool> PrintQRCodeAsync(string content, PrintConfig.TextAlign align, int height, bool enableBlackMark, int feedPaperSpace, int unwindPaperLen);
        Task<bool> PrintBitmapAsync(byte[] bitmapData);
        Task<bool> PrintBitmapAsync(byte[] bitmapData, bool enableBlackMark);
        Task<bool> PrintBitmapAsync(byte[] bitmapData, bool enableBlackMark, int feedPaperSpace, int unwindPaperLen);
        Task<bool> PrintTemplateAsync(PrintTemplate template);
        Task<bool> PrintTemplateAsync(PrintTemplate template, bool enableBlackMark);
        Task<bool> PrintTemplateAsync(PrintTemplate template, bool enableBlackMark, int feedPaperSpace, int unwindPaperLen);
        
        // 新增的等待结果方法（直接返回打印结果）
        Task<PrintEventArgs> PrintTextAndWaitAsync(string text, PrintConfig config = null, bool enableBlackMark = false);
        Task<PrintEventArgs> PrintBarcodeAndWaitAsync(string content, BarcodeType barcodeType = BarcodeType.Code128, HRIPosition hriPosition = HRIPosition.Below, bool enableBlackMark = false);
        Task<PrintEventArgs> PrintQRCodeAndWaitAsync(string content, PrintConfig.TextAlign align = PrintConfig.TextAlign.Center, int height = 184, bool enableBlackMark = false);
        Task<PrintEventArgs> PrintBitmapAndWaitAsync(byte[] bitmapData, bool enableBlackMark = false);
        Task<PrintEventArgs> PrintTemplateAndWaitAsync(PrintTemplate template, bool enableBlackMark = false);
        
        bool IsReady { get; }
    }

    public class PrintEventArgs : System.EventArgs
    {
        public PrintResult Result { get; set; }
        public string Message { get; set; }
        public int ErrorCode { get; set; }
    }

    public enum PrintResult
    {
        Success,
        NoPaper,
        DeviceBusy,
        DeviceNotOpen,
        DataError,
        CommandError,
        UnknownError
    }

    public enum BarcodeType
    {
        UpcA,       // UPC-A
        Ean8,       // EAN-8
        Ean13,      // EAN-13
        Code39,     // CODE 39
        Itf,        // ITF
        Code128     // CODE 128
    }

    public enum HRIPosition
    {
        None,       // 不显示文本
        Below,      // 文本在条码下方
        Above       // 文本在条码上方
    }

    public class PrintConfig
    {
        public TextAlign Align { get; set; } = TextAlign.Left;
        public FontSize Size { get; set; } = FontSize.Normal;
        public bool Bold { get; set; } = false;
        public bool Underline { get; set; } = false;
        
        public enum TextAlign
        {
            Left,
            Center,
            Right
        }
        
        public enum FontSize
        {
            Small,
            Normal,
            Large
        }
    }

    public class PrintTemplate
    {
        public string Title { get; set; }
        public List<PrintItem> Items { get; set; } = new List<PrintItem>();
        
        public class PrintItem
        {
            public PrintItemType Type { get; set; }
            public string Content { get; set; }
            public PrintConfig Config { get; set; }
            public byte[] ImageData { get; set; }
            public BarcodeType BarcodeType { get; set; } = BarcodeType.Code128;
            public HRIPosition HRIPosition { get; set; } = HRIPosition.Below;
            public PrintConfig.TextAlign Align { get; set; } = PrintConfig.TextAlign.Center; // 用于二维码和条码的对齐
        }
        
        public enum PrintItemType
        {
            Text,
            Line,
            Image,
            QRCode,
            Barcode
        }
    }
} 