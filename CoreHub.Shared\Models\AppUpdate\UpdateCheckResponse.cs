namespace CoreHub.Shared.Models.AppUpdate
{
    /// <summary>
    /// 更新检查响应
    /// </summary>
    public class UpdateCheckResponse
    {
        /// <summary>
        /// 是否有可用更新
        /// </summary>
        public bool HasUpdate { get; set; }

        /// <summary>
        /// 是否强制更新
        /// </summary>
        public bool IsForceUpdate { get; set; }

        /// <summary>
        /// 最新版本信息
        /// </summary>
        public VersionInfo? LatestVersion { get; set; }

        /// <summary>
        /// 更新消息
        /// </summary>
        public string? Message { get; set; }

        /// <summary>
        /// 更新策略
        /// </summary>
        public UpdateStrategy Strategy { get; set; } = UpdateStrategy.Optional;
    }

    /// <summary>
    /// 版本信息
    /// </summary>
    public class VersionInfo
    {
        /// <summary>
        /// 版本号
        /// </summary>
        public string VersionNumber { get; set; } = string.Empty;

        /// <summary>
        /// 版本代码
        /// </summary>
        public int VersionCode { get; set; }

        /// <summary>
        /// 更新标题
        /// </summary>
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// 更新描述
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// 更新类型
        /// </summary>
        public string UpdateType { get; set; } = string.Empty;

        /// <summary>
        /// 下载URL
        /// </summary>
        public string DownloadUrl { get; set; } = string.Empty;

        /// <summary>
        /// 文件大小
        /// </summary>
        public long FileSize { get; set; }

        /// <summary>
        /// 文件MD5
        /// </summary>
        public string? FileMd5 { get; set; }

        /// <summary>
        /// 发布时间
        /// </summary>
        public DateTime? ReleaseTime { get; set; }

        /// <summary>
        /// 格式化的文件大小
        /// </summary>
        public string FormattedFileSize => FormatFileSize(FileSize);

        /// <summary>
        /// 格式化文件大小
        /// </summary>
        private static string FormatFileSize(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB", "TB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }
    }

    /// <summary>
    /// 更新策略
    /// </summary>
    public enum UpdateStrategy
    {
        /// <summary>
        /// 可选更新
        /// </summary>
        Optional = 0,

        /// <summary>
        /// 推荐更新
        /// </summary>
        Recommended = 1,

        /// <summary>
        /// 强制更新
        /// </summary>
        Force = 2,

        /// <summary>
        /// 静默更新
        /// </summary>
        Silent = 3
    }
}
