<template>
	<view>
		<view class="flexContainer">
			<!-- <button>test</button> -->
			<button class="flexItem" type="primary" @click="startScan">开始扫描校验</button>
		</view>

	</view>
</template>



<script>
	//test
	import { soundHelper } from '@/utils/soundHelper.js';
	var _self;

	//扫描	
	function utf8ByteToUnicodeStr(utf8Bytes) {
		var unicodeStr = "";
		for (var pos = 0; pos < utf8Bytes.length;) {
			var flag = utf8Bytes[pos];
			var unicode = 0;
			if ((flag >>> 7) === 0) {
				unicodeStr += String.fromCharCode(utf8Bytes[pos]);
				pos += 1;

			} else if ((flag & 0xFC) === 0xFC) {
				unicode = (utf8Bytes[pos] & 0x3) << 30;
				unicode |= (utf8Bytes[pos + 1] & 0x3F) << 24;
				unicode |= (utf8Bytes[pos + 2] & 0x3F) << 18;
				unicode |= (utf8Bytes[pos + 3] & 0x3F) << 12;
				unicode |= (utf8Bytes[pos + 4] & 0x3F) << 6;
				unicode |= (utf8Bytes[pos + 5] & 0x3F);
				unicodeStr += String.fromCodePoint(unicode);
				pos += 6;

			} else if ((flag & 0xF8) === 0xF8) {
				unicode = (utf8Bytes[pos] & 0x7) << 24;
				unicode |= (utf8Bytes[pos + 1] & 0x3F) << 18;
				unicode |= (utf8Bytes[pos + 2] & 0x3F) << 12;
				unicode |= (utf8Bytes[pos + 3] & 0x3F) << 6;
				unicode |= (utf8Bytes[pos + 4] & 0x3F);
				unicodeStr += String.fromCodePoint(unicode);
				pos += 5;

			} else if ((flag & 0xF0) === 0xF0) {
				unicode = (utf8Bytes[pos] & 0xF) << 18;
				unicode |= (utf8Bytes[pos + 1] & 0x3F) << 12;
				unicode |= (utf8Bytes[pos + 2] & 0x3F) << 6;
				unicode |= (utf8Bytes[pos + 3] & 0x3F);
				unicodeStr += String.fromCodePoint(unicode);
				pos += 4;

			} else if ((flag & 0xE0) === 0xE0) {
				unicode = (utf8Bytes[pos] & 0x1F) << 12;;
				unicode |= (utf8Bytes[pos + 1] & 0x3F) << 6;
				unicode |= (utf8Bytes[pos + 2] & 0x3F);
				unicodeStr += String.fromCharCode(unicode);
				pos += 3;

			} else if ((flag & 0xC0) === 0xC0) { //110
				unicode = (utf8Bytes[pos] & 0x3F) << 6;
				unicode |= (utf8Bytes[pos + 1] & 0x3F);
				unicodeStr += String.fromCharCode(unicode);
				pos += 2;

			} else {
				unicodeStr += String.fromCharCode(utf8Bytes[pos]);
				pos += 1;
			}
		}
		return unicodeStr;
	}

	var main, receiver, filter;
	var ScanDeviceClass = plus.android.importClass("android.device.ScanDevice");
	var scanDevice;

	var FirstScanValue = '';
	var SecondScanValue = '';

	export default {
		data() {
			return {
				checkMessage: ''
			}
		},
		created: function(option) {
			_self = this;

			scanDevice = new ScanDeviceClass();
			scanDevice.openScan(); // 打开扫描
			scanDevice.setOutScanMode(0); // 扫描模式

			this.initScan();
			this.registerScan();
		},

		onUnload: function(cw) {
			this.unregisterScan();
		},
		onLoad() {
			soundHelper.playSound('checkPrompt');
		},
		methods: {
			initScan() {
				let _this = this;
				main = plus.android.runtimeMainActivity(); //获取activity  
				var IntentFilter = plus.android.importClass('android.content.IntentFilter');
				filter = new IntentFilter();

				filter.addAction("scan.rcv.message"); // 换你的广播动作  
				receiver = plus.android.implements('io.dcloud.feature.internal.reflect.BroadcastReceiver', {
					onReceive: function(context, intent) {
						plus.android.importClass(intent);
						let code = intent.getByteArrayExtra('barocode');
						//	let codeStr = String.fromCharCode(...code);

						let codeStr = utf8ByteToUnicodeStr(code);
						console.log('codeStr:', codeStr);
						scanDevice.stopScan(); // 停止扫描		

						//扫到后的处理逻辑
						if (_self.FirstScanValue == undefined || _self.FirstScanValue == '') {
							_self.FirstScanValue = codeStr;
							uni.showToast({
								title: '请再扫描染料箱号',
								duration: 2000
							});
						} else if (_self.SecondScanValue == undefined || _self.SecondScanValue == '') {
							_self.SecondScanValue = codeStr;
							_self.CheckChemical(_self.FirstScanValue, _self.SecondScanValue);

						};

					}
				});
			},
			registerScan() {
				main.registerReceiver(receiver, filter);
			},
			unregisterScan() {
				main.unregisterReceiver(receiver);
			},
			startScan() {
				// console.log(_self.isFirstScan);
				// if (_self.isFirstScan==true) {
				// 	uni.showToast({
				// 		title: '请先扫描染料代号',
				// 		duration: 2000
				// 	})
				// };
				scanDevice.stopScan(); // 停止扫描
				scanDevice.startScan(); // 开始扫描
			},
			isFirstScan() {
				return (_self.FirstScanValue == undefined || _self.FirstScanValue == '') & (_self.SecondScanValue ==
					undefined || _self.SecondScanValue == '')
			},
			CheckChemical(chemicalCode, chemicalBoxNo) {
				uni.request({
					url: 'http://172.16.1.12:8898/api/chemical/ChemicalChange',
					method: 'POST',
					data: {
						chemicalCode: chemicalCode, //DF007
						chemicalBoxNo: chemicalBoxNo //"M2407260011"

					},
					header: {
						'Content-Type': 'application/json',
						'Authorization': 'Basic VEFMOlNhaW50eWVhcnRleA=='
					},
					success: (res) => {
						// console.log(res.data);
						// let checkResult = res.data == '相同' ? '染料相同' : '染料不同，请核对！';
						// uni.showModal({
						// 	title: '校验结果',
						// 	content: checkResult,
						// 	showCancel: false,
						// 	cancelText: '',
						// 	confirmText: '确定',
						// 	success: res => {},
						// 	fail: () => {},
						// 	complete: () => {}
						// });
						if(res.data == '相同'){
							uni.showToast({
													title: '校验OK',
												icon: 'success',
												duration: 2000
												});
							soundHelper.playSound('success');	
						}
						else{
							uni.showToast({
													title: '校验失败',
												icon: 'error',
												duration: 2000
												});
							soundHelper.playSound('failure');	
						}
						
					},
					fail: (res) => {
						console.log(res.errMsg);
						uni.showToast({
							title: "请求失败！",
							duration: 2000
						});

					},
					complete: (res) => {
						console.log('清空变量');
						_self.FirstScanValue = '';
						_self.SecondScanValue = ''
					}
				});
			},



		}
	}
</script>

<style>
	.flexContainer {
		display: flex;
		flex-direction: column-reverse;
		/* align-items: center; 
		justify-content: center; */
		height: 95vh;
		flex-wrap: wrap;
		margin: 8rpx 20rpx 8rpx 8rpx;
		gap: 8rpx;
	}

	.flexItem {
		margin-left: 10rpx;
		margin-right: 10rpx;
		padding: 80rpx;
		font-size: 2rem;
	}
</style>