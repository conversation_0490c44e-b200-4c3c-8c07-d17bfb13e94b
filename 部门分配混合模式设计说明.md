# 部门分配混合模式设计说明

## 概述

CoreHub 系统采用混合模式的部门分配设计，明确区分了**用户所属部门**和**角色权限部门**两个概念，以满足不同的业务需求。

## 设计理念

### 核心原则
- **用户所属部门**：表示用户在组织架构中的归属，回答"我在哪个部门工作"
- **角色权限部门**：表示角色可以访问哪些部门的数据，回答"我可以访问哪些部门的数据"

### 业务场景
这种设计符合实际企业管理需求：
- 维修人员有明确的部门归属（如工程部、动力部）
- 维修工作可能涉及多个部门的设备
- 管理人员需要跨部门的数据访问权限

## 技术实现

### 1. 用户所属部门 (`User.DepartmentId`)

#### 数据结构
```sql
Users 表:
- Id: 用户ID
- Username: 用户名
- DepartmentId: 所属部门ID（外键关联 Departments.Id）
```

#### 主要用途
1. **维修人员筛选**
   - 报修设备时，根据维修部门筛选该部门的维修人员
   - 例如：选择工程部维修时，显示工程部的维修工

2. **组织架构管理**
   - 员工列表中显示每个人的所属部门
   - 部门人员统计和管理

3. **业务流程**
   - 维修任务分配时明确维修人员来源
   - 工作量统计按部门归属

#### 代码示例
```csharp
// 获取工程部的维修人员
public async Task<List<User>> GetMaintenanceUsersByDepartmentAsync(int departmentId)
{
    // 根据用户所属部门筛选维修人员
    var users = await _context.Db.Queryable<User>()
        .Where(u => maintenanceUserIds.Contains(u.Id) &&
                   u.DepartmentId == departmentId &&  // 用户所属部门
                   u.IsEnabled)
        .ToListAsync();
    return users;
}
```

### 2. 角色权限部门 (`RoleDepartmentAssignments`)

#### 数据结构
```sql
RoleDepartmentAssignments 表:
- Id: 记录ID
- RoleId: 角色ID
- DepartmentId: 可访问的部门ID
- IsEnabled: 是否启用
```

#### 主要用途
1. **数据访问控制**
   - 控制用户可以查看哪些部门的设备和报修单
   - 例如：维修主管可以查看多个部门的维修数据

2. **权限管理**
   - 灵活配置角色的数据访问范围
   - 支持跨部门的权限分配

3. **业务权限**
   - 报修单查看权限
   - 设备管理权限
   - 统计报表权限

#### 代码示例
```csharp
// 获取用户可访问的部门
public async Task<List<Department>> GetUserAccessibleDepartments(int userId)
{
    var departments = await _context.Db.Queryable<Department>()
        .InnerJoin<RoleDepartmentAssignments>((d, rda) => d.Id == rda.DepartmentId)
        .InnerJoin<UserRoles>((d, rda, ur) => rda.RoleId == ur.RoleId)
        .Where((d, rda, ur) => ur.UserId == userId && rda.IsEnabled)
        .Select(d => d)
        .ToListAsync();
    return departments;
}
```

## 业务场景示例

### 场景1：设备报修流程

1. **张三**（整理部操作员）发现设备故障
2. 系统根据**角色权限部门**判断张三只能报修整理部设备
3. 张三选择维修部门（工程部）
4. 系统根据**用户所属部门**显示工程部的维修人员
5. 张三选择李四（工程部维修工）作为维修人员

### 场景2：维修主管管理

1. **赵六**（动力部维修主管）登录系统
2. 系统根据**角色权限部门**显示赵六可管理的部门数据
3. 赵六可以看到整理部、织造部的维修任务
4. 赵六将任务分配给动力部的维修人员（根据**用户所属部门**）

### 场景3：权限控制

1. **李四**（工程部维修工）查看维修任务
2. 系统根据**角色权限部门**只显示工程部负责的维修任务
3. 李四不能看到动力部负责的维修任务
4. 但在人员信息中，李四的**用户所属部门**显示为工程部

## 界面设计

### 1. 用户所属部门管理
- **页面路径**：`/user-department-assignment-management`
- **菜单名称**：用户所属部门
- **功能**：设置用户在组织架构中的归属
- **说明**：用于维修人员筛选和组织管理

### 2. 角色权限部门管理
- **页面路径**：`/role-department-assignment-management`
- **菜单名称**：角色权限部门
- **功能**：设置角色可访问的部门数据
- **说明**：用于数据访问权限控制

### 3. 用户管理页面
- **部门字段**：所属部门
- **说明**：用户在组织架构中的归属部门
- **用途**：组织管理和维修人员筛选

## 数据一致性

### 验证规则
1. 用户所属部门必须是有效的部门
2. 角色权限部门必须是启用的部门
3. 用户至少要有一个角色的权限部门分配

### 业务逻辑
1. 维修人员筛选优先使用用户所属部门
2. 数据访问控制优先使用角色权限部门
3. 两种部门分配相互独立，互不冲突

## 优势总结

### 1. 逻辑清晰
- 明确区分组织归属和权限范围
- 避免概念混淆和权限冲突

### 2. 灵活性高
- 支持跨部门的权限分配
- 支持复杂的组织架构

### 3. 易于管理
- 人事变动只需修改用户所属部门
- 权限调整只需修改角色权限部门

### 4. 符合实际
- 反映真实的企业组织结构
- 满足复杂的业务权限需求

## 注意事项

### 1. 数据维护
- 定期检查部门分配的一致性
- 及时更新离职人员的部门信息

### 2. 权限设计
- 确保每个角色都有合适的权限部门
- 避免权限过大或过小的问题

### 3. 用户培训
- 向管理员说明两种部门分配的区别
- 提供清晰的操作指南

## 未来扩展

### 1. 多部门归属
- 支持用户同时属于多个部门
- 适用于兼职或跨部门工作的场景

### 2. 临时权限
- 支持临时的权限部门分配
- 适用于项目或临时任务

### 3. 权限继承
- 支持部门层级的权限继承
- 简化复杂组织架构的权限管理

这种混合模式设计既保持了组织架构的清晰性，又提供了灵活的权限控制，是企业级应用的最佳实践。
