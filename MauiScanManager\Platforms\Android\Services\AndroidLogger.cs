#if ANDROID
using Android.Util;

namespace MauiScanManager.Platforms.Android.Services
{
    public static class AndroidLogger
    {
        private const string TAG = "MauiScanManager";

        public static void LogDebug(string message)
        {
            Log.Debug(TAG, message);
        }

        public static void LogError(string message, Exception ex = null)
        {
            Log.Error(TAG, $"{message}, Exception: {ex?.Message}");
        }
    }
}
#endif 