-- 插入测试版本数据
USE CoreHub;
GO

-- 删除已存在的测试版本（如果有）
DELETE FROM [dbo].[AppVersions] WHERE [Platform] = 'Android' AND [VersionNumber] = '1.0.1';
GO

-- 插入新的测试版本
INSERT INTO [dbo].[AppVersions] 
([VersionNumber], [VersionCode], [Platform], [Title], [Description], [UpdateType], [IsForceUpdate], 
 [DownloadUrl], [FileSize], [FileMd5], [Status], [TargetAudience], [ReleaseTime], [CreatedBy])
VALUES 
('1.0.1', 2, 'Android', 'CoreHub 更新版本 1.0.1', 
 '这是 CoreHub 移动应用的更新版本，包含以下改进：\n• 修复了更新下载功能\n• 优化了用户界面\n• 提升了性能', 
 'Minor', 0, 'https://************:8081/api/AppUpdate/download/android/1.0.1', 53960055, 
 NULL, 'Released', 'All', GETDATE(), 1);

-- 验证插入结果
SELECT * FROM [dbo].[AppVersions] WHERE [Platform] = 'Android' ORDER BY [VersionCode] DESC;

PRINT '✓ 测试版本数据插入成功';
