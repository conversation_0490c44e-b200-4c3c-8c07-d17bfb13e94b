<?xml version="1.0" encoding="utf-8" ?>

<views:BaseOperationPage
    x:Class="MauiScanManager.Views.RTcpDeliveryByScanPage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:toolkit="http://schemas.microsoft.com/dotnet/2022/maui/toolkit"
    xmlns:viewmodels="clr-namespace:MauiScanManager.ViewModels"
    xmlns:views="clr-namespace:MauiScanManager.Views"
    Title="{Binding Operation.Description}"
    x:DataType="viewmodels:RTcpDeliveryByScanViewModel">


    <Grid RowDefinitions="Auto,*,Auto">
        <ScrollView Grid.Row="0">
            <VerticalStackLayout Padding="10" Spacing="5">
                <Frame
                    Margin="0,0,0,0"
                    Padding="8"
                    BackgroundColor="#FFF8E1"
                    CornerRadius="8"
                    HasShadow="False">
                    <Label
                        FontAttributes="Bold"
                        FontSize="18"
                        HorizontalOptions="Center"
                        Text="{Binding ScanPrompt}"
                        TextColor="#FF9800" />
                </Frame>
                <!--  工号号输入  -->
                <HorizontalStackLayout Spacing="8" VerticalOptions="Center">
                    <Label
                        Margin="0,11,0,0"
                        FontSize="20"
                        Text="工号:" />

                    <Entry
                        FontSize="20"
                        Placeholder="输入工号"
                        ReturnCommand="{Binding AddWorkerNameCommand}"
                        ReturnType="Done"
                        Text="{Binding WorkerName, Mode=TwoWay}"
                        WidthRequest="240" />
                </HorizontalStackLayout>


                <!--  选择交地  -->
                <HorizontalStackLayout Spacing="8" VerticalOptions="Center">
                    <Label
                        Margin="0,12,0,0"
                        FontSize="20"
                        Text="交地:" />
                    <Picker
                        Title="选择交地"
                        FontSize="20"
                        HeightRequest="45"
                        IsEnabled="{Binding IsPickerEnabled}"
                        ItemDisplayBinding="{Binding CurDept}"
                        ItemsSource="{Binding cpCurDeptList}"
                        SelectedItem="{Binding SelectedCurDept}"
                        WidthRequest="240" />
                </HorizontalStackLayout>

                <!--  车号输入框和多车按钮  -->
                <Grid
                    Margin="0,0,0,0"
                    ColumnDefinitions="Auto,*,Auto"
                    HorizontalOptions="FillAndExpand"
                    VerticalOptions="Center">
                    <Label
                        Margin="0,11,0,0"
                        FontSize="20"
                        Text="车号: " />
                    <Entry
                        x:Name="EntryCarNO"
                        Grid.Column="1"
                        FontSize="20"
                        IsEnabled="{Binding IsCarNOEnabled}"
                        Placeholder="扫描或输入车号"
                        ReturnCommand="{Binding AddCarNoCommand}"
                        ReturnType="Done"
                        Text="{Binding CarNO}" />
                    <Switch
                        x:Name="CarNoIsMultiple"
                        Grid.Column="2"
                        HeightRequest="20"
                        IsToggled="{Binding IsSwitchToggled, Mode=TwoWay}">
                        <Switch.Behaviors>
                            <toolkit:EventToCommandBehavior Command="{Binding SwitchToggledCommand}" EventName="Toggled" />
                        </Switch.Behaviors>
                    </Switch>
                </Grid>

                <!--  缸号/车号数量统计（更明显）  -->
                <HorizontalStackLayout Margin="0,3,0,0" VerticalOptions="Center">
                    <Label FontSize="20" Text="缸号:" />
                    <Label
                        x:Name="EntryBatchNo"
                        FontAttributes="Bold"
                        FontSize="20"
                        Text="{Binding CarNOs.Count, StringFormat='（已添加车号：{0}）'}"
                        TextColor="#1976D2" />
                </HorizontalStackLayout>
                <!--  缸号输入  -->
                <Entry
                    FontSize="20"
                    Placeholder="扫描或输入缸号"
                    ReturnCommand="{Binding AddBatchNoCommand}"
                    ReturnType="Done"
                    Text="{Binding BatchNO}">
                    <Entry.Behaviors>
                        <toolkit:EventToCommandBehavior Command="{Binding BatchNoFocusedCommand}" EventName="Focused" />
                    </Entry.Behaviors>
                </Entry>
            </VerticalStackLayout>
        </ScrollView>


        <!--  显示五层样和挑筒信息  -->

        <Grid
            Grid.Row="1"
            Padding="2"
            RowDefinitions="*">

            <Frame
                Padding="5,8"
                BackgroundColor="{AppThemeBinding Light={StaticResource Gray100},
                                                  Dark={StaticResource Gray800}}"
                BorderColor="{AppThemeBinding Light={StaticResource Gray300},
                                              Dark={StaticResource Gray600}}"
                CornerRadius="8"
                HasShadow="False">
                <Label
                    FontSize="16"
                    Text="{Binding QcSuggestion}"
                    TextColor="{AppThemeBinding Light={StaticResource Black},
                                                Dark={StaticResource White}}" />
            </Frame>
        </Grid>


        <!--  底部操作按钮区，横向排列  -->
        <Grid
            Grid.Row="2"
            Padding="10,8"
            BackgroundColor="White">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="10" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>
            <Button
                Grid.Column="0"
                BackgroundColor="{StaticResource Primary}"
                Command="{Binding SaveCPAutoDeliveryByScanCommand}"
                FontAttributes="Bold"
                FontSize="20"
                HeightRequest="48"
                HorizontalOptions="Fill"
                Text="保存" />
            <Button
                Grid.Column="2"
                BackgroundColor="{StaticResource Primary}"
                Command="{Binding ClearStateCommand}"
                FontAttributes="Bold"
                FontSize="20"
                HeightRequest="48"
                HorizontalOptions="Fill"
                IsEnabled="{Binding IsBusy, Converter={StaticResource InverseBoolConverter}}"
                Text="清空" />
        </Grid>

        <!--<Grid Grid.Row="2" ColumnDefinitions="*,*" ColumnSpacing="10" BackgroundColor="White">
            <StackLayout Orientation="Horizontal" HorizontalOptions="Fill">
            <Button Grid.Column="0" Text="保存" Command="{Binding SaveCPAutoDeliveryByScanCommand}" BackgroundColor="{StaticResource Primary}" HeightRequest="48" FontSize="20" FontAttributes="Bold" HorizontalOptions="Fill"/>
            <Button Grid.Column="1" Text="清空" Command="{Binding ClearStateCommand}" IsEnabled="{Binding IsBusy, Converter={StaticResource InverseBoolConverter}}" BackgroundColor="{StaticResource Primary}" HeightRequest="48" FontSize="20" FontAttributes="Bold" HorizontalOptions="Fill"/>

        </StackLayout>
        </Grid>-->
        <ActivityIndicator
            HorizontalOptions="Center"
            IsRunning="{Binding IsBusy}"
            IsVisible="{Binding IsBusy}"
            VerticalOptions="Center"
            Color="{StaticResource Primary}" />
    </Grid>
    <!--<Grid RowDefinitions="Auto,Auto,Auto,Auto,Auto,Auto,*,Auto" >
        <ScrollView Grid.Row="0">
            <VerticalStackLayout Spacing="5" Padding="10">
                <Frame BackgroundColor="#FFF8E1" Padding="8" Margin="0,0,0,0" CornerRadius="8" HasShadow="False">
                    <Label Text="{Binding ScanPrompt}" FontSize="18" TextColor="#FF9800" FontAttributes="Bold" HorizontalOptions="Center"/>
                </Frame>
            </VerticalStackLayout>




        </ScrollView>


    -->
    <!--  工号  -->
    <!--
        <Grid Grid.Row="1" ColumnDefinitions="Auto,*" Padding="3" VerticalOptions="Center">
            <Label Grid.Column="0" Text="工号:" FontSize="16"  Margin="8,0,0,0"/>

            <Entry Grid.Column="1"  Placeholder="输入工号"
                   Text="{Binding WorkerName , Mode=TwoWay}"
                   FontSize="16"
                   ReturnCommand="{Binding AddWorkerNameCommand}"
                   ReturnType="Done"/>
        </Grid>

    -->
    <!--  交地  -->
    <!--
        <Grid Grid.Row="2" ColumnDefinitions="Auto,*"  Padding="3" VerticalOptions="Center">
            <Label Grid.Column="0"  Text="交地:" FontSize="16"  Margin="8,0,0,0"/>
            <Picker Grid.Column="1" ItemsSource="{Binding cpCurDeptList}"
         SelectedItem="{Binding CurDept}"
                     Title="选择交地"
         FontSize="16"
         HeightRequest="45">
                <Picker.Behaviors>
                    <toolkit:EventToCommandBehavior
           EventName="SelectedIndexChanged"
           Command="{Binding CurDeptChangedCommand}"/>
                </Picker.Behaviors>
            </Picker>
        </Grid>

    -->
    <!--  车号  -->
    <!--
        <Grid Grid.Row="3" ColumnDefinitions="Auto,*,Auto"  Padding="3" VerticalOptions="Center">
            <Label Grid.Column="0"  Text="车号:" FontSize="16" Margin="8,0,0,0" />
            <Entry Grid.Column="1"  Placeholder="扫描或输入车号"
         Text="{Binding CarNO}"
         FontSize="16"
         ReturnCommand="{Binding AddCarNoCommand}"
         ReturnType="Done"/>
            <Switch Grid.Column="3"  x:Name="CarNoIsMultiple"
                    IsToggled="{Binding IsSwitchToggled}"
                    HeightRequest="20"/>
        </Grid>
    -->
    <!--  缸号统计  -->
    <!--
        <Grid Grid.Row="4" ColumnDefinitions="Auto,*" Padding="3" VerticalOptions="Center" >
            <Label Grid.Column="0"  Text="缸号:" FontSize="16" />
            <Label Grid.Column="1"  Text="{Binding BatchNoCount.Count, StringFormat='（已添加：{0}）'}" FontSize="16" FontAttributes="Bold" TextColor="#1976D2"/>
        </Grid>

    -->
    <!--  缸号  -->
    <!--
        <Grid Grid.Row="5" ColumnDefinitions="*" Padding="3"  >
            <Entry Placeholder="扫描或输入缸号"
Text="{Binding BatchNO}"
FontSize="16"
ReturnCommand="{Binding AddBatchNoCommand}"
ReturnType="Done"/>
        </Grid>

    -->
    <!--  显示五层样和挑筒信息  -->
    <!--
        <Grid Grid.Row="6" RowDefinitions="*" Padding="3">

            <Frame
     Padding="5,8"
     BorderColor="{AppThemeBinding Light={StaticResource Gray300}, Dark={StaticResource Gray600}}"
     BackgroundColor="{AppThemeBinding Light={StaticResource Gray100}, Dark={StaticResource Gray800}}"
     CornerRadius="8"
     HasShadow="False">
                <Label
         Text="{Binding QcSuggestion}"
         FontSize="18"
         TextColor="{AppThemeBinding Light={StaticResource Black}, Dark={StaticResource White}}"/>
            </Frame>
        </Grid>

    -->
    <!--  底部操作按钮区，横向排列  -->
    <!--
        <Grid Grid.Row="7" ColumnDefinitions="*,*" ColumnSpacing="10" BackgroundColor="White">

    -->
    <!--<StackLayout Orientation="Horizontal" HorizontalOptions="Fill">-->
    <!--

            <Button Grid.Column="0" Text="保存" Command="{Binding SaveCPAutoDeliveryByScanCommand}" BackgroundColor="{StaticResource Primary}" HeightRequest="48" FontSize="20" FontAttributes="Bold" HorizontalOptions="Fill"/>
            <Button Grid.Column="1" Text="清空" Command="{Binding ClearStateCommand}" IsEnabled="{Binding IsBusy, Converter={StaticResource InverseBoolConverter}}" BackgroundColor="{StaticResource Primary}" HeightRequest="48" FontSize="20" FontAttributes="Bold" HorizontalOptions="Fill"/>
    -->
    <!--</StackLayout>-->
    <!--
        </Grid>
        <ActivityIndicator
         IsRunning="{Binding IsBusy}"
         IsVisible="{Binding IsBusy}"
         HorizontalOptions="Center"
         VerticalOptions="Center"
         Color="{StaticResource Primary}"/>
    </Grid>-->

</views:BaseOperationPage>
