﻿using MauiScanManager.Services;
using MauiScanManager.Pages;
using MauiScanManager.ViewModels;
using MauiScanManager.Views;
using MauiScanManager.Extensions;
using CommunityToolkit.Maui;
using Microsoft.Extensions.Logging;
#if ANDROID
using MauiScanManager.Platforms.Android.Services;
#endif

namespace MauiScanManager
{
    public static class MauiProgram
    {
        public static MauiApp CreateMauiApp()
        {
            var builder = MauiApp.CreateBuilder();
            builder
                .UseMauiApp<App>()
                .UseMauiCommunityToolkit()
                .ConfigureFonts(fonts =>
                {
                    fonts.AddFont("OpenSans-Regular.ttf", "OpenSansRegular");
                    fonts.AddFont("OpenSans-Semibold.ttf", "OpenSansSemibold");
                });

            // 添加日志配置
#if DEBUG
            builder.Logging.AddDebug();
            builder.Logging.SetMinimumLevel(LogLevel.Debug);  // 设置最小日志级别为 Debug
#endif

            var appSettings = new AppSettings 
            {
                BaseUrl = "http://***********:6898",
             //   BaseUrl = "http://************:6898",
                RequestTimeout = 30,
                UseHttps = false
            };

            builder.Services.AddApplicationServices(appSettings);
            
            

            return builder.Build();
        }
    }
}
