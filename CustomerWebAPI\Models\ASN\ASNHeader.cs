using System;
using Dapper.Contrib.Extensions;

namespace CustomerWebAPI.Models
{
    /// <summary>
    /// 
    /// </summary>
    /// 
    [Table("ASNHeader")]
    public class ASNHeader
    {
        /// <summary>
        /// 
        /// </summary>
        /// 
        [Explicit<PERSON>ey]
        public Guid Id { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public Guid? ASNItemsId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public int DocumentIndex { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string PurposeCode { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string AdvancedShipNoteNumber { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string SentDate { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string SentTime { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string HierarchicalStructureCode { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string TransactionTypeCode { get; set; }
    }
}