using FluentValidation;
using CustomerWebAPI.Models;
using System.Linq;

namespace CustomerWebAPI.Validators
{
    public class ChemicalValidator : AbstractValidator<Chemical>
    {
        public ChemicalValidator()
        {
            RuleFor(x => x.ChemicalCode)
                .NotEmpty().WithMessage("染料代码不能为空")
                .MaximumLength(20).WithMessage("染料代码长度不能超过20个字符")
                .Must(x => x.All(char.IsLetterOrDigit)).WithMessage("染料代码只能包含字母和数字");
        }
    }
} 