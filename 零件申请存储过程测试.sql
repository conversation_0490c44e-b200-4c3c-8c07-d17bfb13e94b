-- =============================================
-- 零件申请存储过程测试脚本
-- 用于验证存储过程的功能和错误处理
-- =============================================

USE [CoreHub]
GO

PRINT '开始测试零件申请存储过程...'
PRINT '========================================'

-- =============================================
-- 测试1：sp_GetPendingPartRequests 基本功能测试
-- =============================================

PRINT ''
PRINT '测试1：获取待领用零件信息'
PRINT '----------------------------------------'

-- 1.1 基本查询测试
PRINT '1.1 基本查询（获取所有待领用零件）：'
EXEC sp_GetPendingPartRequests;

-- 1.2 按紧急程度过滤测试
PRINT ''
PRINT '1.2 按紧急程度过滤（紧急级别）：'
EXEC sp_GetPendingPartRequests @UrgencyLevel = 1;

-- 1.3 分页查询测试
PRINT ''
PRINT '1.3 分页查询测试（第1页，每页5条）：'
EXEC sp_GetPendingPartRequests @PageIndex = 1, @PageSize = 5;

-- 1.4 按时间范围过滤测试
PRINT ''
PRINT '1.4 按时间范围过滤（最近7天）：'
DECLARE @StartDate DATETIME2 = DATEADD(DAY, -7, GETDATE());
EXEC sp_GetPendingPartRequests @StartDate = @StartDate;

-- =============================================
-- 测试2：sp_UpdatePartIssueInfo 功能测试
-- =============================================

PRINT ''
PRINT '测试2：更新零件发放信息'
PRINT '----------------------------------------'

-- 首先检查是否有待处理的零件申请
DECLARE @TestPartRequestId INT;
SELECT TOP 1 @TestPartRequestId = Id 
FROM RepairOrderPartRequests 
WHERE Status = 1;

IF @TestPartRequestId IS NOT NULL
BEGIN
    PRINT '找到测试用的零件申请ID: ' + CAST(@TestPartRequestId AS VARCHAR(10));
    
    -- 2.1 参数验证测试
    PRINT ''
    PRINT '2.1 参数验证测试：'
    
    -- 测试无效的零件申请ID
    PRINT '测试无效的零件申请ID：'
    EXEC sp_UpdatePartIssueInfo
        @PartRequestId = -1,
        @ActualQuantity = 1,
        @WarehouseOrderNumber = 'TEST001',
        @IssuerId = 1;
    
    -- 测试无效的数量
    PRINT ''
    PRINT '测试无效的数量：'
    EXEC sp_UpdatePartIssueInfo
        @PartRequestId = @TestPartRequestId,
        @ActualQuantity = 0,
        @WarehouseOrderNumber = 'TEST001',
        @IssuerId = 1;
    
    -- 测试空的仓库单号
    PRINT ''
    PRINT '测试空的仓库单号：'
    EXEC sp_UpdatePartIssueInfo
        @PartRequestId = @TestPartRequestId,
        @ActualQuantity = 1,
        @WarehouseOrderNumber = '',
        @IssuerId = 1;
    
    -- 2.2 正常更新测试
    PRINT ''
    PRINT '2.2 正常更新测试：'
    
    -- 检查更新前的状态
    PRINT '更新前的零件申请状态：'
    SELECT 
        Id, PartName, RequestedQuantity, Status, 
        CASE Status
            WHEN 1 THEN '申请中'
            WHEN 2 THEN '已领用'
            WHEN 3 THEN '已安装'
            WHEN 4 THEN '已取消'
        END AS StatusName,
        IssuedBy, IssuedAt, WarehouseOrderNumber
    FROM RepairOrderPartRequests 
    WHERE Id = @TestPartRequestId;
    
    -- 执行正常更新
    PRINT ''
    PRINT '执行零件发放更新：'
    EXEC sp_UpdatePartIssueInfo
        @PartRequestId = @TestPartRequestId,
        @ActualQuantity = 2,
        @WarehouseOrderNumber = 'WH20250110001',
        @UnitPrice = 150.00,
        @TotalCost = 300.00,
        @IssuerId = 1,
        @ExternalSystemId = 'EXT_TEST_001',
        @ActualPartName = '测试实际发放零件',
        @ActualSpecification = '测试规格',
        @ExternalPartNumber = 'P_TEST_001';
    
    -- 检查更新后的状态
    PRINT ''
    PRINT '更新后的零件申请状态：'
    SELECT 
        Id, PartName, RequestedQuantity, ActualQuantity, Status,
        CASE Status
            WHEN 1 THEN '申请中'
            WHEN 2 THEN '已领用'
            WHEN 3 THEN '已安装'
            WHEN 4 THEN '已取消'
        END AS StatusName,
        IssuedBy, IssuedAt, WarehouseOrderNumber, UnitPrice, TotalCost,
        ExternalRequisitionDetailId, ActualPartName, ExternalPartNumber
    FROM RepairOrderPartRequests 
    WHERE Id = @TestPartRequestId;
    
    -- 2.3 重复更新测试（应该失败）
    PRINT ''
    PRINT '2.3 重复更新测试（应该失败）：'
    EXEC sp_UpdatePartIssueInfo
        @PartRequestId = @TestPartRequestId,
        @ActualQuantity = 1,
        @WarehouseOrderNumber = 'WH20250110002',
        @IssuerId = 1;
        
END
ELSE
BEGIN
    PRINT '没有找到状态为"申请中"的零件申请记录，跳过更新测试';
    PRINT '请先创建一些测试数据或检查数据库中的零件申请记录';
END

-- =============================================
-- 测试3：综合查询测试
-- =============================================

PRINT ''
PRINT '测试3：综合查询测试'
PRINT '----------------------------------------'

-- 3.1 查看所有零件申请的状态分布
PRINT '3.1 零件申请状态分布：'
SELECT 
    Status,
    CASE Status
        WHEN 1 THEN '申请中'
        WHEN 2 THEN '已领用'
        WHEN 3 THEN '已安装'
        WHEN 4 THEN '已取消'
        ELSE '未知'
    END AS StatusName,
    COUNT(*) AS Count
FROM RepairOrderPartRequests
GROUP BY Status
ORDER BY Status;

-- 3.2 查看最近的零件申请记录
PRINT ''
PRINT '3.2 最近的零件申请记录（前5条）：'
SELECT TOP 5
    pr.Id,
    pr.PartName,
    pr.RequestedQuantity,
    pr.Status,
    CASE pr.Status
        WHEN 1 THEN '申请中'
        WHEN 2 THEN '已领用'
        WHEN 3 THEN '已安装'
        WHEN 4 THEN '已取消'
        ELSE '未知'
    END AS StatusName,
    pr.RequestedAt,
    ru.DisplayName AS RequesterName,
    ro.OrderNumber AS RepairOrderNumber
FROM RepairOrderPartRequests pr
    INNER JOIN RepairOrders ro ON pr.RepairOrderId = ro.Id
    LEFT JOIN Users ru ON pr.RequestedBy = ru.Id
ORDER BY pr.RequestedAt DESC;

-- 3.3 查看有零件申请的报修单统计
PRINT ''
PRINT '3.3 有零件申请的报修单统计：'
SELECT 
    ro.OrderNumber,
    ro.FaultDescription,
    COUNT(pr.Id) AS PartRequestCount,
    SUM(CASE WHEN pr.Status = 1 THEN 1 ELSE 0 END) AS PendingCount,
    SUM(CASE WHEN pr.Status = 2 THEN 1 ELSE 0 END) AS IssuedCount,
    SUM(CASE WHEN pr.Status = 3 THEN 1 ELSE 0 END) AS InstalledCount
FROM RepairOrders ro
    INNER JOIN RepairOrderPartRequests pr ON ro.Id = pr.RepairOrderId
GROUP BY ro.Id, ro.OrderNumber, ro.FaultDescription
ORDER BY ro.OrderNumber;

-- =============================================
-- 测试完成
-- =============================================

PRINT ''
PRINT '========================================'
PRINT '零件申请存储过程测试完成！'
PRINT ''
PRINT '测试总结：'
PRINT '1. sp_GetPendingPartRequests - 查询待领用零件信息'
PRINT '   ✓ 基本查询功能'
PRINT '   ✓ 条件过滤功能'
PRINT '   ✓ 分页查询功能'
PRINT ''
PRINT '2. sp_UpdatePartIssueInfo - 更新零件发放信息'
PRINT '   ✓ 参数验证功能'
PRINT '   ✓ 状态检查功能'
PRINT '   ✓ 数据更新功能'
PRINT '   ✓ 错误处理功能'
PRINT ''
PRINT '注意：如果某些测试失败，请检查：'
PRINT '- 数据库中是否有足够的测试数据'
PRINT '- 用户表中是否有ID为1的用户记录'
PRINT '- 零件申请表中是否有状态为1的记录'

GO
