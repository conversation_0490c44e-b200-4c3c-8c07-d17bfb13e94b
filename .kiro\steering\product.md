# 产品概述

CoreHub 是一个基于 .NET 8 和 Blazor 构建的跨平台企业设备管理系统。它提供全面的设备维护工作流程、基于角色的权限管理，并支持 Web 和移动平台。

## 核心功能

- **设备管理**: 设备信息管理、型号管理、二维码扫描
- **维修系统**: 设备维修申请、维护工单管理
- **工作流管理**: 维修流程控制和状态跟踪
- **权限管理**: 基于角色的访问控制，支持细粒度权限
- **部门管理**: 部门类型和工种分类管理
- **用户管理**: 用户账户和角色分配
- **通知系统**: 跨平台消息通知
- **动态菜单系统**: 基于权限的菜单渲染

## 目标平台

- **Web**: 面向桌面浏览器的 ASP.NET Core Blazor Server 应用程序
- **移动端**: 支持 Android、iOS、Windows 和 macOS 的 .NET MAUI 混合应用程序
- **数据库**: SQL Server 配合 SqlSugar ORM

## 业务领域

该系统专为工业/制造业环境设计，在这些环境中，设备维护、维修工作流程和部门权限控制是关键的业务需求。