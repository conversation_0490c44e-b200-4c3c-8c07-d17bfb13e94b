-- =============================================
-- 设备管理系统基础数据插入脚本
-- 创建日期: 2024-12-19
-- 描述: 根据设备列表.csv文件插入基础数据
-- =============================================

USE [EquipmentManagement]
GO

-- =============================================
-- 1. 插入部门数据
-- =============================================
SET IDENTITY_INSERT [dbo].[Departments] ON
GO

INSERT INTO [dbo].[Departments] ([Id], [Code], [Name], [Description], [ParentId], [Level], [SortOrder], [IsEnabled], [CreatedAt])
VALUES 
    (1, 'ZLB', N'整理部', N'负责织物整理加工的部门', NULL, 1, 1, 1, GETDATE()),
    (2, 'WXB', N'维修部', N'负责设备维修保养的部门', NULL, 1, 2, 1, GETDATE()),
    (3, 'DLB', N'动力部', N'负责动力设备管理的部门', NULL, 1, 3, 1, GETDATE()),
    (4, 'AQB', N'安全部', N'负责安全管理的部门', NULL, 1, 4, 1, GETDATE())
GO

SET IDENTITY_INSERT [dbo].[Departments] OFF
GO

-- =============================================
-- 2. 插入设备型号数据
-- =============================================
SET IDENTITY_INSERT [dbo].[EquipmentModels] ON
GO

INSERT INTO [dbo].[EquipmentModels] ([Id], [Code], [Name], [Category], [Brand], [Description], [IsEnabled], [CreatedAt])
VALUES 
    (1, 'DXJ', N'定型机', N'定型机', NULL, N'用于织物定型的设备', 1, GETDATE()),
    (2, 'SMJ', N'烧毛机', N'烧毛机', NULL, N'用于织物烧毛处理的设备', 1, GETDATE()),
    (3, 'TJJ', N'退浆机', N'退浆机', NULL, N'用于织物退浆处理的设备', 1, GETDATE()),
    (4, 'SGJ', N'丝光机', N'丝光机', NULL, N'用于织物丝光处理的设备', 1, GETDATE()),
    (5, 'QLJ', N'气流机', N'气流机', NULL, N'用于织物气流处理的设备', 1, GETDATE()),
    (6, 'TSJ', N'脱水机', N'脱水机', NULL, N'用于织物脱水的设备', 1, GETDATE()),
    (7, 'SXJ', N'水洗机', N'水洗机', NULL, N'用于织物水洗的设备', 1, GETDATE()),
    (8, 'PHJ', N'焙烘机', N'焙烘机', NULL, N'用于织物焙烘的设备', 1, GETDATE()),
    (9, 'ZHJ', N'单烘桶轧烘机', N'轧烘机', NULL, N'用于织物轧烘的设备', 1, GETDATE()),
    (10, 'QMJ', N'起毛机', N'起毛机', NULL, N'用于织物起毛的设备', 1, GETDATE()),
    (11, 'ZGJ', N'轧光机', N'轧光机', NULL, N'用于织物轧光的设备', 1, GETDATE()),
    (12, 'YSJ', N'预缩机', N'预缩机', NULL, N'用于织物预缩的设备', 1, GETDATE()),
    (13, 'MMJ', N'磨毛机', N'磨毛机', NULL, N'用于织物磨毛的设备', 1, GETDATE()),
    (14, 'GZJ', N'罐蒸机', N'罐蒸机', NULL, N'用于织物罐蒸的设备', 1, GETDATE()),
    (15, 'XYJ', N'洗衣机', N'洗衣机', NULL, N'用于织物洗涤的设备', 1, GETDATE()),
    (16, 'KFJ', N'开幅机', N'开幅机', NULL, N'用于织物开幅的设备', 1, GETDATE()),
    (17, 'CCJ', N'除尘机组', N'除尘机组', NULL, N'用于除尘的设备', 1, GETDATE()),
    (18, 'JMJ', N'剪毛机', N'剪毛机', NULL, N'用于织物剪毛的设备', 1, GETDATE()),
    (19, 'ZNJ', N'蒸呢机', N'蒸呢机', NULL, N'用于织物蒸呢的设备', 1, GETDATE()),
    (20, 'TJJ2', N'退卷机', N'退卷机', NULL, N'用于织物退卷的设备', 1, GETDATE()),
    (21, 'FCJ', N'静电式油烟废气处理机', N'废气处理机', NULL, N'用于废气处理的设备', 1, GETDATE()),
    (22, 'DDJ', N'打样定型', N'定型机', NULL, N'用于打样定型的设备', 1, GETDATE()),
    (23, 'ZJJ', N'助剂自动配送机', N'配送机', NULL, N'用于助剂自动配送的设备', 1, GETDATE()),
    (24, 'HXJ', N'烘箱', N'烘箱', NULL, N'用于烘干的设备', 1, GETDATE()),
    (25, 'YCJ', N'小轧车', N'轧车', NULL, N'用于小批量轧制的设备', 1, GETDATE()),
    (26, 'KSQ', N'开水器', N'开水器', NULL, N'用于提供开水的设备', 1, GETDATE())
GO

SET IDENTITY_INSERT [dbo].[EquipmentModels] OFF
GO

-- =============================================
-- 3. 插入位置数据
-- =============================================
SET IDENTITY_INSERT [dbo].[Locations] ON
GO

INSERT INTO [dbo].[Locations] ([Id], [Code], [Name], [DepartmentId], [ParentId], [Level], [Address], [Description], [IsEnabled], [CreatedAt])
VALUES 
    (1, 'HZCJ', N'后整车间', 1, NULL, 1, NULL, N'后整理主车间', 1, GETDATE()),
    (2, 'QMCJ', N'起毛车间', 1, NULL, 1, NULL, N'起毛专用车间', 1, GETDATE()),
    (3, 'HZL', N'后整理', 1, NULL, 1, NULL, N'后整理区域', 1, GETDATE()),
    (4, 'HZTHCJ', N'后整天虹车间', 1, NULL, 1, NULL, N'后整天虹车间', 1, GETDATE()),
    (5, 'HZHYS', N'后整化验室', 1, NULL, 1, NULL, N'后整化验室', 1, GETDATE()),
    (6, 'HZHLF', N'后整化料房', 1, NULL, 1, NULL, N'后整化料房', 1, GETDATE()),
    (7, 'HYS', N'化验室', 1, NULL, 1, NULL, N'化验室', 1, GETDATE()),
    (8, 'QM1HJ', N'起毛1号机', 1, 2, 2, NULL, N'起毛1号机位置', 1, GETDATE()),
    (9, 'QM2HJ', N'起毛2号机', 1, 2, 2, NULL, N'起毛2号机位置', 1, GETDATE())
GO

SET IDENTITY_INSERT [dbo].[Locations] OFF
GO

-- =============================================
-- 4. 插入设备数据
-- =============================================
SET IDENTITY_INSERT [dbo].[Equipment] ON
GO

-- 定型机设备 (1-6号)
INSERT INTO [dbo].[Equipment] ([Id], [Code], [Name], [DepartmentId], [ModelId], [LocationId], [Status], [IsEnabled], [CreatedAt])
VALUES
    (1, 'FN-DX-001', N'定型1号机', 1, 1, 1, 1, 1, GETDATE()),
    (2, 'FN-DX-002', N'定型2号机', 1, 1, 1, 1, 1, GETDATE()),
    (3, 'FN-DX-003', N'定型3号机', 1, 1, 1, 1, 1, GETDATE()),
    (4, 'FN-DX-004', N'定型4号机', 1, 1, 1, 1, 1, GETDATE()),
    (5, 'FN-DX-005', N'定型5号机', 1, 1, 1, 1, 1, GETDATE()),
    (6, 'FN-DX-006', N'定型6号机', 1, 1, 1, 1, 1, GETDATE()),

-- 烧毛机设备 (1-2号)
    (7, 'FN-SM-001', N'烧毛1号机', 1, 2, 1, 1, 1, GETDATE()),
    (8, 'FN-SM-002', N'烧毛2号机', 1, 2, 1, 1, 1, GETDATE()),

-- 退浆机设备 (1-2号)
    (9, 'FN-TJ-001', N'退浆1号机', 1, 3, 1, 1, 1, GETDATE()),
    (10, 'FN-TJ-002', N'退浆2号机', 1, 3, 1, 1, 1, GETDATE()),

-- 丝光机设备 (1-2号)
    (11, 'FN-SG-001', N'丝光1号机', 1, 4, 1, 1, 1, GETDATE()),
    (12, 'FN-SG-002', N'丝光2号机', 1, 4, 1, 1, 1, GETDATE()),

-- 气流机设备 (1-3号)
    (13, 'FN-QL-001', N'气流1号机', 1, 5, 1, 1, 1, GETDATE()),
    (14, 'FN-QL-002', N'气流2号机', 1, 5, 1, 1, 1, GETDATE()),
    (15, 'FN-QL-003', N'气流3号机', 1, 5, 1, 1, 1, GETDATE()),

-- 脱水机设备
    (16, 'FN-TS-001', N'脱水1号机', 1, 6, 1, 1, 1, GETDATE()),

-- 水洗机设备 (1-3号)
    (17, 'FN-SX-001', N'水洗机', 1, 7, 1, 1, 1, GETDATE()),
    (18, 'FN-SX-002', N'松式水洗1号机', 1, 7, 1, 1, 1, GETDATE()),
    (19, 'FN-SX-003', N'松式水洗2号机', 1, 7, 1, 1, 1, GETDATE()),

-- 焙烘机设备
    (20, 'FN-PH-001', N'焙烘1号机', 1, 8, 1, 1, 1, GETDATE()),

-- 单烘桶轧烘机设备 (1-2号)
    (21, 'FN-ZH-001', N'单烘桶轧烘1号机', 1, 9, 1, 1, 1, GETDATE()),
    (22, 'FN-ZH-002', N'单烘桶轧烘2号机', 1, 9, 1, 1, 1, GETDATE()),

-- 起毛机设备 (3-18号)
    (23, 'FN-QM-003', N'高效起毛3号机', 1, 10, 1, 1, 1, GETDATE()),
    (24, 'FN-QM-004', N'高效起毛4号机', 1, 10, 1, 1, 1, GETDATE()),
    (25, 'FN-QM-005', N'高效起毛5号机', 1, 10, 1, 1, 1, GETDATE()),
    (26, 'FN-QM-006', N'高效起毛6号机', 1, 10, 1, 1, 1, GETDATE()),
    (27, 'FN-QM-007', N'高效起毛7号机', 1, 10, 1, 1, 1, GETDATE()),
    (28, 'FN-QM-008', N'高效起毛8号机', 1, 10, 1, 1, 1, GETDATE()),
    (29, 'FN-QM-009', N'汉斯起毛机', 1, 10, 1, 1, 1, GETDATE()),
    (30, 'FN-QM-010', N'强力起毛9号机', 1, 10, 1, 1, 1, GETDATE()),
    (31, 'FN-QM-011', N'强力起毛10号机', 1, 10, 1, 1, 1, GETDATE()),
    (32, 'FN-QM-012', N'强力起毛11号机', 1, 10, 1, 1, 1, GETDATE()),
    (33, 'FN-QM-013', N'强力起毛12号机', 1, 10, 1, 1, 1, GETDATE()),
    (34, 'FN-QM-014', N'强力起毛13号机', 1, 10, 1, 1, 1, GETDATE()),
    (35, 'FN-QM-015', N'强力起毛14号机', 1, 10, 1, 1, 1, GETDATE()),
    (36, 'FN-QM-016', N'强力起毛15号机', 1, 10, 1, 1, 1, GETDATE()),
    (37, 'FN-QM-017', N'强力起毛16号机', 1, 10, 1, 1, 1, GETDATE()),
    (38, 'FN-QM-018', N'强力起毛17号机', 1, 10, 1, 1, 1, GETDATE()),
    (39, 'FN-QM-019', N'强力起毛18号机', 1, 10, 1, 1, 1, GETDATE()),

-- 其他设备
    (40, 'FN-ZG-001', N'轧光1号机', 1, 11, 1, 1, 1, GETDATE()),
    (41, 'FN-YS-001', N'预缩1号机', 1, 12, 1, 1, 1, GETDATE()),
    (42, 'FN-YS-002', N'预缩2号机', 1, 12, 1, 1, 1, GETDATE()),
    (43, 'FN-YS-003', N'预缩3号机', 1, 12, 1, 1, 1, GETDATE()),
    (44, 'FN-YS-004', N'预缩4号机', 1, 12, 1, 1, 1, GETDATE()),
    (45, 'FN-MM-001', N'磨毛1号机', 1, 13, 1, 1, 1, GETDATE()),
    (46, 'FN-MM-002', N'磨毛2号机', 1, 13, 1, 1, 1, GETDATE()),
    (47, 'FN-GZ-001', N'罐蒸1号机', 1, 14, 1, 1, 1, GETDATE()),
    (48, 'FN-XY-001', N'1#洗衣机', 1, 15, 1, 1, 1, GETDATE()),
    (49, 'FN-KF-001', N'退捻开幅机', 1, 16, 1, 1, 1, GETDATE()),
    (50, 'FN-CC-001', N'除尘机组1号', 1, 17, 8, 1, 1, GETDATE()),
    (51, 'FN-CC-002', N'除尘机组2号', 1, 17, 9, 1, 1, GETDATE()),
    (52, 'FN-JM-001', N'汉斯剪毛机', 1, 18, 1, 1, 1, GETDATE()),
    (53, 'FN-JM-002', N'拉法剪毛机', 1, 18, 2, 1, 1, GETDATE()),
    (54, 'FN-ZN-001', N'1#蒸呢机', 1, 19, 3, 1, 1, GETDATE()),
    (55, 'FN-TJ-001', N'框架式退卷1号机', 1, 20, 4, 1, 1, GETDATE()),
    (56, 'FN-TJ-002', N'框架式退卷2号机', 1, 20, 4, 1, 1, GETDATE()),
    (57, 'FN-FC-001', N'静电式油烟废气处理机', 1, 21, 3, 1, 1, GETDATE()),
    (58, 'FN-DD-001', N'1#打样定型机', 1, 22, 5, 1, 1, GETDATE()),
    (59, 'FN-ZJ-001', N'1号助剂自动配送机', 1, 23, 6, 1, 1, GETDATE()),
    (60, 'FN-ZJ-002', N'2号助剂自动配送机', 1, 23, 6, 1, 1, GETDATE()),
    (61, 'FN-ZJ-003', N'3号助剂自动配送机', 1, 23, 6, 1, 1, GETDATE()),
    (62, 'FN-HX-001', N'烘箱1号', 1, 24, 5, 1, 1, GETDATE()),
    (63, 'FN-HX-002', N'恒温干烘箱1号', 1, 24, 5, 1, 1, GETDATE()),
    (64, 'FN-YC-001', N'小轧车1号', 1, 25, 5, 1, 1, GETDATE()),
    (65, 'FN-KS-001', N'全自动电热开水器', 1, 26, 5, 1, 1, GETDATE()),
    (66, 'FN-KS-002', N'商用开水器', 1, 26, 7, 1, 1, GETDATE())
GO

SET IDENTITY_INSERT [dbo].[Equipment] OFF
GO

PRINT N'基础数据插入完成！'
PRINT N'- 部门数据: 4条'
PRINT N'- 设备型号数据: 26条'
PRINT N'- 位置数据: 9条'
PRINT N'- 设备数据: 66条'
GO
