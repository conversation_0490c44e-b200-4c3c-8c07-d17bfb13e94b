using System.Security.Cryptography.X509Certificates;
using CoreHub.Web.Services;
using CoreHub.Shared.Services;

namespace CoreHub.Web.Services
{
    /// <summary>
    /// SSL 证书管理服务
    /// </summary>
    public interface ISslCertificateService
    {
        /// <summary>
        /// 验证证书有效性
        /// </summary>
        /// <param name="certificatePath">证书路径</param>
        /// <param name="password">证书密码</param>
        /// <returns>验证结果</returns>
        Task<CertificateValidationResult> ValidateCertificateAsync(string certificatePath, string password);

        /// <summary>
        /// 获取证书信息
        /// </summary>
        /// <param name="certificatePath">证书路径</param>
        /// <param name="password">证书密码</param>
        /// <returns>证书信息</returns>
        Task<CertificateInfo?> GetCertificateInfoAsync(string certificatePath, string password);

        /// <summary>
        /// 检查证书是否即将过期
        /// </summary>
        /// <param name="certificatePath">证书路径</param>
        /// <param name="password">证书密码</param>
        /// <param name="warningDays">提前警告天数</param>
        /// <returns>是否即将过期</returns>
        Task<bool> IsCertificateExpiringAsync(string certificatePath, string password, int warningDays = 30);
    }

    /// <summary>
    /// SSL 证书管理服务实现
    /// </summary>
    public class SslCertificateService : ISslCertificateService
    {
        private readonly IApplicationLogger _logger;
        private readonly IConfiguration _configuration;

        public SslCertificateService(IApplicationLogger logger, IConfiguration configuration)
        {
            _logger = logger;
            _configuration = configuration;
        }

        /// <summary>
        /// 验证证书有效性
        /// </summary>
        public async Task<CertificateValidationResult> ValidateCertificateAsync(string certificatePath, string password)
        {
            try
            {
                if (!File.Exists(certificatePath))
                {
                    _logger.LogWarning("SSL证书文件不存在: {CertificatePath}", certificatePath);
                    return new CertificateValidationResult
                    {
                        IsValid = false,
                        ErrorMessage = "证书文件不存在"
                    };
                }

                var certificate = new X509Certificate2(certificatePath, password);

                var result = new CertificateValidationResult
                {
                    IsValid = true,
                    Certificate = certificate,
                    Subject = certificate.Subject,
                    Issuer = certificate.Issuer,
                    NotBefore = certificate.NotBefore,
                    NotAfter = certificate.NotAfter,
                    Thumbprint = certificate.Thumbprint
                };

                // 检查证书是否已过期
                if (certificate.NotAfter <= DateTime.Now)
                {
                    result.IsValid = false;
                    result.ErrorMessage = $"证书已过期，过期时间: {certificate.NotAfter}";
                    _logger.LogError(new InvalidOperationException("SSL证书已过期"), "SSL证书已过期: {ExpiryDate}", certificate.NotAfter);
                }
                // 检查证书是否尚未生效
                else if (certificate.NotBefore > DateTime.Now)
                {
                    result.IsValid = false;
                    result.ErrorMessage = $"证书尚未生效，生效时间: {certificate.NotBefore}";
                    _logger.LogError(new InvalidOperationException("SSL证书尚未生效"), "SSL证书尚未生效: {ValidFrom}", certificate.NotBefore);
                }
                // 检查证书是否即将过期（30天内）
                else if (certificate.NotAfter <= DateTime.Now.AddDays(30))
                {
                    result.IsExpiringSoon = true;
                    result.DaysUntilExpiry = (int)(certificate.NotAfter - DateTime.Now).TotalDays;
                    _logger.LogWarning("SSL证书即将过期: {ExpiryDate}, 剩余天数: {DaysRemaining}", 
                        certificate.NotAfter, result.DaysUntilExpiry);
                }
                else
                {
                    _logger.LogInformation("SSL证书验证成功，有效期至: {ExpiryDate}", certificate.NotAfter);
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "SSL证书验证失败: {CertificatePath}", certificatePath);
                return new CertificateValidationResult
                {
                    IsValid = false,
                    ErrorMessage = $"证书验证失败: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 获取证书信息
        /// </summary>
        public async Task<CertificateInfo?> GetCertificateInfoAsync(string certificatePath, string password)
        {
            try
            {
                var validationResult = await ValidateCertificateAsync(certificatePath, password);
                if (!validationResult.IsValid || validationResult.Certificate == null)
                {
                    return null;
                }

                var cert = validationResult.Certificate;
                return new CertificateInfo
                {
                    Subject = cert.Subject,
                    Issuer = cert.Issuer,
                    SerialNumber = cert.SerialNumber,
                    Thumbprint = cert.Thumbprint,
                    NotBefore = cert.NotBefore,
                    NotAfter = cert.NotAfter,
                    HasPrivateKey = cert.HasPrivateKey,
                    KeyAlgorithm = cert.PublicKey.Oid.FriendlyName ?? "Unknown",
                    SignatureAlgorithm = cert.SignatureAlgorithm.FriendlyName ?? "Unknown",
                    Version = cert.Version,
                    DaysUntilExpiry = (int)(cert.NotAfter - DateTime.Now).TotalDays,
                    IsExpired = cert.NotAfter <= DateTime.Now,
                    IsExpiringWithin30Days = cert.NotAfter <= DateTime.Now.AddDays(30)
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取证书信息失败: {CertificatePath}", certificatePath);
                return null;
            }
        }

        /// <summary>
        /// 检查证书是否即将过期
        /// </summary>
        public async Task<bool> IsCertificateExpiringAsync(string certificatePath, string password, int warningDays = 30)
        {
            try
            {
                var validationResult = await ValidateCertificateAsync(certificatePath, password);
                if (!validationResult.IsValid || validationResult.Certificate == null)
                {
                    return true; // 无效证书视为需要关注
                }

                return validationResult.Certificate.NotAfter <= DateTime.Now.AddDays(warningDays);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查证书过期状态失败: {CertificatePath}", certificatePath);
                return true;
            }
        }
    }

    /// <summary>
    /// 证书验证结果
    /// </summary>
    public class CertificateValidationResult
    {
        public bool IsValid { get; set; }
        public string? ErrorMessage { get; set; }
        public X509Certificate2? Certificate { get; set; }
        public string? Subject { get; set; }
        public string? Issuer { get; set; }
        public DateTime NotBefore { get; set; }
        public DateTime NotAfter { get; set; }
        public string? Thumbprint { get; set; }
        public bool IsExpiringSoon { get; set; }
        public int DaysUntilExpiry { get; set; }
    }

    /// <summary>
    /// 证书信息
    /// </summary>
    public class CertificateInfo
    {
        public string Subject { get; set; } = string.Empty;
        public string Issuer { get; set; } = string.Empty;
        public string SerialNumber { get; set; } = string.Empty;
        public string Thumbprint { get; set; } = string.Empty;
        public DateTime NotBefore { get; set; }
        public DateTime NotAfter { get; set; }
        public bool HasPrivateKey { get; set; }
        public string KeyAlgorithm { get; set; } = string.Empty;
        public string SignatureAlgorithm { get; set; } = string.Empty;
        public int Version { get; set; }
        public int DaysUntilExpiry { get; set; }
        public bool IsExpired { get; set; }
        public bool IsExpiringWithin30Days { get; set; }
    }
}
