using System;
using Microsoft.Extensions.Logging;
using Serilog;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;
using CustomerWebAPI.Services;

public static class ApplicationBuilderExtensions
{
    public static IApplicationBuilder InitializeDirectories(this IApplicationBuilder app)
    {
        try
        {
            var dirService = app.ApplicationServices.GetRequiredService<IDirectoryInitializationService>();
            dirService.EnsureDirectoriesExist();
        }
        catch (Exception ex)
        {
            Log.Fatal(ex, "Failed to initialize required directories");
            throw;
        }
        
        return app;
    }
} 