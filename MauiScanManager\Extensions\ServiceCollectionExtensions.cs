using MauiScanManager.Attributes;
using MauiScanManager.Pages;
using MauiScanManager.Services;
using MauiScanManager.Services;
using MauiScanManager.ViewModels;
using MauiScanManager.Views;
using System.Net;
using System.Net.Http.Headers;
using System.Reflection;
#if ANDROID
using MauiScanManager.Platforms.Android.Services;
#endif
namespace MauiScanManager.Extensions
{
    public static class ServiceCollectionExtensions
    {
        /// <summary>
        /// 注册应用服务
        /// </summary>
        /// <param name="services"></param>
        /// <param name="settings"></param>
        /// <returns></returns>
        public static IServiceCollection AddApplicationServices(this IServiceCollection services, AppSettings settings)
        {
            services.AddSingleton(settings);
            services.AddHttpClients(settings);
            services.AddCoreServices();
            services.AddPlatformServices();
            services.AddNavigationServices();
            services.AddBusinessServices();
            services.AddDialogServices();

            return services;
        }

        /// <summary>
        /// 注册 HttpClient
        /// </summary>
        /// <param name="services"></param>
        /// <param name="settings"></param>
        /// <returns></returns>
        private static IServiceCollection AddHttpClients(this IServiceCollection services, AppSettings settings)
        {
            services.AddHttpClient("API", client =>
            {
                client.BaseAddress = new Uri(settings.BaseUrl);
                client.Timeout = TimeSpan.FromSeconds(settings.RequestTimeout);
                client.DefaultRequestHeaders.ConnectionClose = false;
                client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
            })
            .ConfigurePrimaryHttpMessageHandler(() =>
            {
                ServicePointManager.SecurityProtocol = 
                    SecurityProtocolType.Tls12 | 
                    SecurityProtocolType.Tls13;

                return new HttpClientHandler
                {
                    ServerCertificateCustomValidationCallback = (message, cert, chain, errors) => true,
                    AutomaticDecompression = DecompressionMethods.GZip | DecompressionMethods.Deflate
                };
            });

            return services;
        }

        /// <summary>
        /// 注册核心服务
        /// </summary>
        /// <param name="services"></param>
        /// <returns></returns>
        private static IServiceCollection AddCoreServices(this IServiceCollection services)
        {
            services.AddSingleton<App>();
            services.AddSingleton<IApiService, ApiService>();
            services.AddSingleton<INetworkService, NetworkService>();
            services.AddSingleton<IAppUpdateService, AppUpdateService>();
            
            return services;
        }

        /// <summary>
        /// 注册平台服务
        /// </summary>
        /// <param name="services"></param>
        /// <returns></returns>
        private static IServiceCollection AddPlatformServices(this IServiceCollection services)
        {
#if ANDROID
            services.AddSingleton<IPlatformDialogService, AndroidDialogService>();
            services.AddSingleton<IScanService, ScanService>();
            services.AddSingleton<IPrintService, PrintService>();
            services.AddSingleton<IPlatformInstaller, AndroidInstaller>();
            services.AddSingleton<IAudioService, AndroidAudioService>();
            services.AddSingleton<IPlatformLoadingService, AndroidLoadingService>();
            services.AddSingleton<IResourceProvider, AndroidResourceProvider>();
#else
            services.AddSingleton<IPlatformDialogService, DefaultDialogService>();
            services.AddSingleton<IScanService, DefaultScanService>();
            services.AddSingleton<IPrintService, DefaultPrintService>();
            services.AddSingleton<IPlatformInstaller, DefaultInstaller>();
            services.AddSingleton<IAudioService, DefaultAudioService>();
#endif


            return services;
        }

        /// <summary>
        /// 注册导航服务
        /// </summary>
        /// <param name="services"></param>
        /// <returns></returns>
        private static IServiceCollection AddNavigationServices(this IServiceCollection services)
        {
            var navigationService = new OperationNavigationService();
            services.AddSingleton<IOperationNavigationService>(navigationService);

            services.AddSingleton<DepartmentSelectionPage>()
                  .AddTransient<DepartmentSelectionViewModel>();

            services.AutoRegisterOperationPages()
                   .RegisterOperationRoutes(navigationService);

            services.AddTransient<UpdatePage>();
#if DEBUG
            services.AddTransient<PrintTestPage>();
#endif

            return services;
        }

        /// <summary>
        /// 注册业务服务
        /// </summary>
        /// <param name="services"></param>
        /// <returns></returns>
        private static IServiceCollection AddBusinessServices(this IServiceCollection services)
        {
            services.AddSingleton<IDepartmentService, DepartmentService>();
            services.AddSingleton<IColorCardService, ColorCardService>();
            services.AddSingleton<IScanOrderService, ScanOrderService>();
            services.AddSingleton<IChemicalService, ChemicalService>();
            services.AddSingleton<ISizingService, SizingService>();
            services.AddSingleton<IYarnCarLocationService, YarnCarLocationService>();
            services.AddSingleton<ITwistService, TwistService>();
            services.AddSingleton<ICPDeliveryService, CPDeliveryService>();
            return services;
        }
        /// <summary>
        /// 注册对话服务
        /// </summary>
        /// <param name="services"></param>
        /// <returns></returns>
        private static IServiceCollection AddDialogServices(this IServiceCollection services)
        {
            services.AddSingleton<IDialogService, DialogService>();
            return services;
        }

        /// <summary>
        /// 自动注册操作页面
        /// </summary>
        /// <param name="services"></param>
        /// <returns></returns>
        public static IServiceCollection AutoRegisterOperationPages(this IServiceCollection services)
        {
            // 获取所有带有 OperationPage 特性的页面
            var pageTypes = Assembly.GetExecutingAssembly()
                .GetTypes()
                .Where(t => t.IsClass 
                    && !t.IsAbstract 
                    && t.IsSubclassOf(typeof(Page))
                    && t.GetCustomAttributes<OperationPageAttribute>().Any());

            foreach (var pageType in pageTypes)
            {
                // 注册页面为 Singleton
                services.AddSingleton(pageType);

                // 尝试注册对应的 ViewModel
                string viewModelName = pageType.Name.Replace("Page", "ViewModel");
                var viewModelType = Assembly.GetExecutingAssembly()
                    .GetTypes()
                    .FirstOrDefault(t => t.Name == viewModelName);

                if (viewModelType != null)
                {
                    services.AddTransient(viewModelType);
                }
            }

            return services;
        }
        /// <summary>
        /// 注册操作页面路由
        /// </summary>
        /// <param name="services"></param>
        /// <param name="navigationService"></param>
        /// <returns></returns>
        public static IServiceCollection RegisterOperationRoutes(this IServiceCollection services, IOperationNavigationService navigationService)
        {
            var operationPages = Assembly.GetExecutingAssembly()
                .GetTypes()
                .Where(t => t.IsClass 
                    && !t.IsAbstract 
                    && t.IsSubclassOf(typeof(Page)));

            foreach (var pageType in operationPages)
            {
                var attributes = pageType.GetCustomAttributes<OperationPageAttribute>();
                foreach (var attribute in attributes)
                {
                    navigationService.RegisterOperation(
                        attribute.OperationCode,
                        pageType.Name
                    );

                    Routing.RegisterRoute(pageType.Name, pageType);
                }
            }

            return services;
        }

    }
}
