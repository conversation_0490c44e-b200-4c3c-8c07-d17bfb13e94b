using System.Security.Cryptography.X509Certificates;
using CoreHub.Web.Services;
using CoreHub.Shared.Services;

namespace CoreHub.Web.Services
{
    /// <summary>
    /// 证书更换服务接口
    /// </summary>
    public interface ICertificateReplacementService
    {
        /// <summary>
        /// 验证新证书
        /// </summary>
        Task<CertificateValidationResult> ValidateNewCertificateAsync(string newCertPath, string newCertPassword);
        
        /// <summary>
        /// 备份当前证书
        /// </summary>
        Task<string> BackupCurrentCertificateAsync();
        
        /// <summary>
        /// 更换证书
        /// </summary>
        Task<CertificateReplacementResult> ReplaceCertificateAsync(string newCertPath, string newCertPassword, bool createBackup = true);
        
        /// <summary>
        /// 回滚证书
        /// </summary>
        Task<CertificateReplacementResult> RollbackCertificateAsync(string backupPath);
        
        /// <summary>
        /// 获取证书更换历史
        /// </summary>
        Task<List<CertificateReplacementRecord>> GetReplacementHistoryAsync();
    }

    /// <summary>
    /// 证书更换服务实现
    /// </summary>
    public class CertificateReplacementService : ICertificateReplacementService
    {
        private readonly ISslCertificateService _sslService;
        private readonly IApplicationLogger _logger;
        private readonly IConfiguration _configuration;
        private readonly string _backupDirectory;
        private readonly string _historyFile;

        public CertificateReplacementService(
            ISslCertificateService sslService,
            IApplicationLogger logger,
            IConfiguration configuration)
        {
            _sslService = sslService;
            _logger = logger;
            _configuration = configuration;
            _backupDirectory = Path.Combine(Directory.GetCurrentDirectory(), "Certificates", "Backups");
            _historyFile = Path.Combine(_backupDirectory, "replacement_history.json");
            
            // 确保备份目录存在
            Directory.CreateDirectory(_backupDirectory);
        }

        /// <summary>
        /// 验证新证书
        /// </summary>
        public async Task<CertificateValidationResult> ValidateNewCertificateAsync(string newCertPath, string newCertPassword)
        {
            try
            {
                _logger.LogInformation("开始验证新证书: {CertPath}", newCertPath);
                
                if (!File.Exists(newCertPath))
                {
                    return new CertificateValidationResult
                    {
                        IsValid = false,
                        ErrorMessage = "新证书文件不存在"
                    };
                }

                var validationResult = await _sslService.ValidateCertificateAsync(newCertPath, newCertPassword);
                
                if (validationResult.IsValid)
                {
                    _logger.LogInformation("新证书验证成功: {Subject}, 有效期至: {ExpiryDate}", 
                        validationResult.Subject, validationResult.NotAfter);
                }
                else
                {
                    _logger.LogError(new InvalidOperationException("新证书验证失败"), "新证书验证失败: {Error}", validationResult.ErrorMessage);
                }

                return validationResult;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证新证书时发生错误: {Error}", ex.Message);
                return new CertificateValidationResult
                {
                    IsValid = false,
                    ErrorMessage = $"验证过程中发生错误: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 备份当前证书
        /// </summary>
        public async Task<string> BackupCurrentCertificateAsync()
        {
            try
            {
                var currentCertPath = _configuration["Kestrel:EndPoints:Https:Certificate:Path"];
                if (string.IsNullOrEmpty(currentCertPath) || !File.Exists(currentCertPath))
                {
                    throw new InvalidOperationException("当前证书文件不存在");
                }

                var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                var fileName = Path.GetFileNameWithoutExtension(currentCertPath);
                var extension = Path.GetExtension(currentCertPath);
                var backupFileName = $"{fileName}_backup_{timestamp}{extension}";
                var backupPath = Path.Combine(_backupDirectory, backupFileName);

                File.Copy(currentCertPath, backupPath, true);
                
                _logger.LogInformation("证书备份成功: {BackupPath}", backupPath);
                return backupPath;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "备份当前证书失败: {Error}", ex.Message);
                throw;
            }
        }

        /// <summary>
        /// 更换证书
        /// </summary>
        public async Task<CertificateReplacementResult> ReplaceCertificateAsync(string newCertPath, string newCertPassword, bool createBackup = true)
        {
            var result = new CertificateReplacementResult
            {
                StartTime = DateTime.Now,
                NewCertificatePath = newCertPath
            };

            try
            {
                _logger.LogInformation("开始证书更换流程: {NewCertPath}", newCertPath);

                // 1. 验证新证书
                var validationResult = await ValidateNewCertificateAsync(newCertPath, newCertPassword);
                if (!validationResult.IsValid)
                {
                    result.IsSuccess = false;
                    result.ErrorMessage = $"新证书验证失败: {validationResult.ErrorMessage}";
                    return result;
                }

                result.NewCertificateInfo = validationResult;

                // 2. 备份当前证书
                if (createBackup)
                {
                    try
                    {
                        result.BackupPath = await BackupCurrentCertificateAsync();
                        _logger.LogInformation("当前证书已备份到: {BackupPath}", result.BackupPath);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning("备份当前证书失败，继续执行更换流程: {Error}", ex.Message);
                    }
                }

                // 3. 获取当前证书信息（用于记录）
                var currentCertPath = _configuration["Kestrel:EndPoints:Https:Certificate:Path"];
                var currentCertPassword = _configuration["Kestrel:EndPoints:Https:Certificate:Password"];
                if (!string.IsNullOrEmpty(currentCertPath) && !string.IsNullOrEmpty(currentCertPassword))
                {
                    result.OldCertificateInfo = await _sslService.ValidateCertificateAsync(currentCertPath, currentCertPassword);
                }

                // 4. 复制新证书到目标位置
                if (!string.IsNullOrEmpty(currentCertPath))
                {
                    File.Copy(newCertPath, currentCertPath, true);
                    _logger.LogInformation("新证书已复制到: {TargetPath}", currentCertPath);
                }

                // 5. 更新配置文件中的密码（如果不同）
                // 注意：这里只是示例，实际生产环境中可能需要更复杂的配置管理
                if (newCertPassword != currentCertPassword)
                {
                    _logger.LogInformation("证书密码已更新（需要重启应用程序生效）");
                    // TODO: 实现配置文件更新逻辑
                }

                result.IsSuccess = true;
                result.Message = "证书更换成功，请重启应用程序使新证书生效";
                result.EndTime = DateTime.Now;

                // 6. 记录更换历史
                await RecordReplacementHistoryAsync(result);

                _logger.LogInformation("证书更换完成: {Message}", result.Message);
                return result;
            }
            catch (Exception ex)
            {
                result.IsSuccess = false;
                result.ErrorMessage = $"证书更换失败: {ex.Message}";
                result.EndTime = DateTime.Now;
                
                _logger.LogError(ex, "证书更换过程中发生错误");
                return result;
            }
        }

        /// <summary>
        /// 回滚证书
        /// </summary>
        public async Task<CertificateReplacementResult> RollbackCertificateAsync(string backupPath)
        {
            var result = new CertificateReplacementResult
            {
                StartTime = DateTime.Now,
                NewCertificatePath = backupPath
            };

            try
            {
                _logger.LogInformation("开始证书回滚流程: {BackupPath}", backupPath);

                if (!File.Exists(backupPath))
                {
                    result.IsSuccess = false;
                    result.ErrorMessage = "备份证书文件不存在";
                    return result;
                }

                var currentCertPath = _configuration["Kestrel:EndPoints:Https:Certificate:Path"];
                if (string.IsNullOrEmpty(currentCertPath))
                {
                    result.IsSuccess = false;
                    result.ErrorMessage = "当前证书路径配置不存在";
                    return result;
                }

                // 复制备份证书到当前位置
                File.Copy(backupPath, currentCertPath, true);

                result.IsSuccess = true;
                result.Message = "证书回滚成功，请重启应用程序使证书生效";
                result.EndTime = DateTime.Now;

                _logger.LogInformation("证书回滚完成: {Message}", result.Message);
                return result;
            }
            catch (Exception ex)
            {
                result.IsSuccess = false;
                result.ErrorMessage = $"证书回滚失败: {ex.Message}";
                result.EndTime = DateTime.Now;
                
                _logger.LogError(ex, "证书回滚过程中发生错误");
                return result;
            }
        }

        /// <summary>
        /// 获取证书更换历史
        /// </summary>
        public async Task<List<CertificateReplacementRecord>> GetReplacementHistoryAsync()
        {
            try
            {
                if (!File.Exists(_historyFile))
                {
                    return new List<CertificateReplacementRecord>();
                }

                var json = await File.ReadAllTextAsync(_historyFile);
                var history = System.Text.Json.JsonSerializer.Deserialize<List<CertificateReplacementRecord>>(json) 
                    ?? new List<CertificateReplacementRecord>();
                
                return history.OrderByDescending(h => h.ReplacementTime).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "读取证书更换历史失败");
                return new List<CertificateReplacementRecord>();
            }
        }

        /// <summary>
        /// 记录更换历史
        /// </summary>
        private async Task RecordReplacementHistoryAsync(CertificateReplacementResult result)
        {
            try
            {
                var history = await GetReplacementHistoryAsync();
                
                var record = new CertificateReplacementRecord
                {
                    Id = Guid.NewGuid().ToString(),
                    ReplacementTime = result.StartTime,
                    IsSuccess = result.IsSuccess,
                    OldCertificateSubject = result.OldCertificateInfo?.Subject,
                    OldCertificateExpiry = result.OldCertificateInfo?.NotAfter,
                    NewCertificateSubject = result.NewCertificateInfo?.Subject,
                    NewCertificateExpiry = result.NewCertificateInfo?.NotAfter,
                    BackupPath = result.BackupPath,
                    Message = result.Message ?? result.ErrorMessage,
                    Duration = result.EndTime - result.StartTime
                };

                history.Insert(0, record);
                
                // 只保留最近100条记录
                if (history.Count > 100)
                {
                    history = history.Take(100).ToList();
                }

                var json = System.Text.Json.JsonSerializer.Serialize(history, new System.Text.Json.JsonSerializerOptions 
                { 
                    WriteIndented = true 
                });
                
                await File.WriteAllTextAsync(_historyFile, json);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "记录证书更换历史失败");
            }
        }
    }

    /// <summary>
    /// 证书更换结果
    /// </summary>
    public class CertificateReplacementResult
    {
        public bool IsSuccess { get; set; }
        public string? Message { get; set; }
        public string? ErrorMessage { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public string? NewCertificatePath { get; set; }
        public string? BackupPath { get; set; }
        public CertificateValidationResult? OldCertificateInfo { get; set; }
        public CertificateValidationResult? NewCertificateInfo { get; set; }
    }

    /// <summary>
    /// 证书更换记录
    /// </summary>
    public class CertificateReplacementRecord
    {
        public string Id { get; set; } = string.Empty;
        public DateTime ReplacementTime { get; set; }
        public bool IsSuccess { get; set; }
        public string? OldCertificateSubject { get; set; }
        public DateTime? OldCertificateExpiry { get; set; }
        public string? NewCertificateSubject { get; set; }
        public DateTime? NewCertificateExpiry { get; set; }
        public string? BackupPath { get; set; }
        public string? Message { get; set; }
        public TimeSpan Duration { get; set; }
    }
}
