using System;
using System.Threading.Tasks;
using MauiScanManager.Services.EventArgs;

namespace MauiScanManager.Services
{
    public interface IAppUpdateService
    {
        Task<bool> CheckForUpdate();
        Task<bool> DownloadAndInstallUpdate();
        event EventHandler<UpdateProgressEventArgs> UpdateProgress;
        bool IsForceUpdate { get; }
        bool IsUpdating { get; }
        bool HasChecked { get; }
    }
}
