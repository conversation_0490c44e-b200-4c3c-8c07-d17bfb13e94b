<?xml version="1.0" encoding="utf-8" ?>
<views:BaseOperationPage 
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:views="clr-namespace:MauiScanManager.Views"
             xmlns:viewmodels="clr-namespace:MauiScanManager.ViewModels"
             x:Class="MauiScanManager.Views.ScanOrderPage"
             Title="{Binding Operation.Description}">

    <Grid RowDefinitions="Auto,Auto,*,Auto" 
          Padding="10" 
          RowSpacing="10">
        
        
        <!-- 标题和数量 -->
        <Grid Grid.Row="1" ColumnDefinitions="*,Auto" Margin="5,0,5,2">
            <Label Text="已扫描订单"
                   FontSize="18"
                   FontAttributes="Bold"
                   VerticalOptions="Center"/>
            <StackLayout Grid.Column="1" 
                         Orientation="Horizontal" 
                         Spacing="5"
                         VerticalOptions="Center">
                <Label Text="{Binding ScannedOrders.Count}"
                       FontSize="32"
                       FontAttributes="Bold"
                       TextColor="{StaticResource Primary}"/>
                <Label Text="条"
                       FontSize="18"
                       VerticalOptions="Center"/>
            </StackLayout>
        </Grid>

        <!-- 订单列表 -->
        <CollectionView Grid.Row="2" 
                       ItemsSource="{Binding ScannedOrders}">
            <CollectionView.Header>
                <Grid BackgroundColor="#f0f0f0" 
                      Padding="15,5">
                    <Label Text="订单列表" 
                           FontSize="14" 
                           TextColor="Gray"/>
                </Grid>
            </CollectionView.Header>
            <CollectionView.ItemTemplate>
                <DataTemplate>
                    <Grid Padding="15,2">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <Label Text="{Binding}"
                               FontSize="16"
                               VerticalOptions="Center"/>
                        <Button Grid.Column="1"
                                Text="删除"
                                TextColor="White"
                                BackgroundColor="Red"
                                Command="{Binding Source={RelativeSource AncestorType={x:Type viewmodels:ScanOrderViewModel}}, Path=DeleteOrderCommand}"
                                CommandParameter="{Binding}"
                                HeightRequest="28"
                                Padding="8,0"/>
                    </Grid>
                </DataTemplate>
            </CollectionView.ItemTemplate>
            <CollectionView.EmptyView>
                <Label Text="请扫描订单"
                       HorizontalOptions="Center"
                       VerticalOptions="Center"
                       TextColor="Gray"
                       FontSize="16"/>
            </CollectionView.EmptyView>
        </CollectionView>

        <!-- 保存按钮 -->
        <Button Grid.Row="3"
                Text="保存"
                Command="{Binding SaveCommand}"
                IsEnabled="{Binding CanSave}"
                HeightRequest="50"
                FontSize="18"
                BackgroundColor="{StaticResource Primary}"
                Margin="0,10,0,0"/>
    </Grid>

</views:BaseOperationPage> 