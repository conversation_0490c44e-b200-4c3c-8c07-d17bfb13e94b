using System;
using System.IO;
using System.Net.Http;
using System.Text.Json;
using System.Threading.Tasks;
using System.Diagnostics;
using Microsoft.Maui.Storage;
using MauiScanManager.Services.EventArgs;
using MauiScanManager.Models;
using MauiScanManager.Services;

#if ANDROID
using MauiScanManager.Platforms.Android.Services;
#endif

namespace MauiScanManager.Services
{
    public class AppUpdateService : IAppUpdateService
    {
        private readonly HttpClient _httpClient;
        private readonly AppSettings _appSettings;
        private UpdateInfo _latestUpdateInfo;
        private readonly IPlatformInstaller _platformInstaller;
        private static bool _isUpdating = false;
        private static bool _hasChecked = false;
        private readonly IPlatformLoadingService _loadingService;
        
        public event EventHandler<UpdateProgressEventArgs> UpdateProgress;

        public AppUpdateService(
            HttpClient httpClient, 
            AppSettings appSettings, 
            IPlatformInstaller platformInstaller,
            IPlatformLoadingService loadingService)
        {
            _httpClient = httpClient;
            _appSettings = appSettings;
            _platformInstaller = platformInstaller;
            _loadingService = loadingService;
        }

        private void Log(string message)
        {
            Debug.WriteLine(message);
            LogService.Log(message);
        }

        public bool IsUpdating => _isUpdating;
        public bool HasChecked => _hasChecked;

        public async Task<bool> CheckForUpdate()
        {
            try
            {
                _isUpdating = true;
                var platform = DeviceInfo.Platform == DevicePlatform.Android ? "android" : "ios";
                
                _loadingService?.ShowNativeLoading("正在获取版本信息...");
                Log($"正在检查更新，URL: {_appSettings.BaseUrl}/api/update/check/{platform}");
                Log($"网络状态: {Connectivity.NetworkAccess}");

                if (Connectivity.NetworkAccess != NetworkAccess.Internet)
                {
                    Log("无网络连接");
                    return false;
                }

                using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(30));
                var response = await _httpClient.GetAsync(
                    $"{_appSettings.BaseUrl}/api/update/check/{platform}", 
                    cts.Token);
                
                Log($"服务器响应状态码: {response.StatusCode}");
                if (!response.IsSuccessStatusCode) 
                {
                    Log($"服务器返回错误: {response.StatusCode}");
                    return false;
                }

                var jsonString = await response.Content.ReadAsStringAsync();
                Log($"服务器响应内容: {jsonString}");

                var options = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                };
                
                _latestUpdateInfo = JsonSerializer.Deserialize<UpdateInfo>(jsonString, options);
                Log($"反序列化后的版本号: {_latestUpdateInfo?.Version ?? "null"}");
                
                var currentVersion = AppInfo.Current.VersionString;
                Log($"当前版本: {currentVersion}, 最新版本: {_latestUpdateInfo?.Version ?? "null"}");

                _loadingService?.ShowNativeLoading("正在比对版本...");

                if (string.IsNullOrEmpty(_latestUpdateInfo?.Version) || string.IsNullOrEmpty(currentVersion))
                {
                    Log("版本号为空，无法比较");
                    return false;
                }

                try
                {
                    var current = new Version(currentVersion);
                    var latest = new Version(_latestUpdateInfo.Version);
                    var needUpdate = latest > current;
                    Log($"版本比较结果: {current} vs {latest} = {needUpdate}");
                    if (!needUpdate)
                    {
                        _isUpdating = false;
                    }
                    _hasChecked = true;
                    return needUpdate;
                }
                catch (Exception ex)
                {
                    Log($"版本号比较出错: {ex.Message}");
                    _hasChecked = true;
                    throw;
                }
            }
            catch (Exception ex)
            {
                _isUpdating = false;
                Log($"检查更新时出错: {ex.Message}");
                Log($"错误堆栈: {ex.StackTrace}");
                _hasChecked = true;
                throw;
            }
            finally
            {
                _loadingService?.HideNativeLoading();
            }
        }

        public async Task<bool> DownloadAndInstallUpdate()
        {
            const int MaxRetries = 3;  // 最大重试次数
            const int BufferSize = 4096;  // 缓冲区大小
            int retryCount = 0;

            while (retryCount < MaxRetries)
            {
                try
                {
                    if (_latestUpdateInfo == null)
                    {
                        Log("更新信息为空");
                        return false;
                    }

                    var downloadUrl = _latestUpdateInfo.UpdateUrl.StartsWith("http") 
                        ? _latestUpdateInfo.UpdateUrl 
                        : $"{_appSettings.BaseUrl}{_latestUpdateInfo.UpdateUrl}";

                    Log($"开始下载更新，URL: {downloadUrl}，第{retryCount + 1}次尝试");

                    var tempPath = Path.Combine(FileSystem.CacheDirectory, $"temp_{Guid.NewGuid()}.apk");
                    var finalPath = Path.Combine(FileSystem.CacheDirectory, _latestUpdateInfo.FileName);

                    // 确保目录存在
                    Directory.CreateDirectory(FileSystem.CacheDirectory);

                    // 删除可能存在的旧文件
                    if (File.Exists(finalPath)) File.Delete(finalPath);

                    using (var response = await _httpClient.GetAsync(downloadUrl, HttpCompletionOption.ResponseHeadersRead))
                    {
                        Log($"下载响应状态码: {response.StatusCode}");
                        
                        if (!response.IsSuccessStatusCode)
                        {
                            Log($"服务器返回错误: {response.StatusCode}");
                            retryCount++;
                            continue;
                        }

                        var totalBytes = response.Content.Headers.ContentLength ?? -1L;
                        var downloadedBytes = 0L;

                        using (var downloadStream = await response.Content.ReadAsStreamAsync())
                        using (var fileStream = new FileStream(tempPath, FileMode.Create, FileAccess.Write, FileShare.None, BufferSize))
                        {
                            var buffer = new byte[BufferSize];
                            int bytesRead;
                            var lastProgress = 0.0;
                            var lastBytes = 0L;

                            Log($"开始下载，总大小: {totalBytes} bytes");

                            while ((bytesRead = await downloadStream.ReadAsync(buffer, 0, buffer.Length)) > 0)
                            {
                                await fileStream.WriteAsync(buffer.AsMemory(0, bytesRead));
                                downloadedBytes += bytesRead;

                                if (totalBytes > 0)
                                {
                                    var progress = Math.Min((double)downloadedBytes / totalBytes, 1.0);  // 确保不超过100%
                                    if (progress - lastProgress >= 0.01)  // 每1%更新一次
                                    {
                                        lastProgress = progress;
                                        var speed = (downloadedBytes - lastBytes) / 1024.0;  // KB/s
                                        lastBytes = downloadedBytes;
                                        
                                        Log($"下载进度: {progress:P2} ({downloadedBytes}/{totalBytes} bytes), 速度: {speed:F2} KB/s");
                                        UpdateProgress?.Invoke(this, new UpdateProgressEventArgs
                                        {
                                            Progress = progress,
                                            Status = $"正在下载: {progress:P0} ({downloadedBytes / 1024.0:F2}/{totalBytes / 1024.0:F2} KB)"
                                        });
                                    }
                                }
                            }

                            // 确保写入所有数据
                            await fileStream.FlushAsync();
                            fileStream.Close();
                        }

                        // 验证下载是否完整
                        var fileInfo = new FileInfo(tempPath);
                        Log($"下载完成，文件大小: {fileInfo.Length} bytes, 预期大小: {totalBytes} bytes");

                        if (totalBytes > 0 && fileInfo.Length != totalBytes)
                        {
                            Log($"下载不完整: {fileInfo.Length} vs {totalBytes}");
                            if (File.Exists(tempPath)) File.Delete(tempPath);
                            throw new Exception($"下载不完整: 已下载 {fileInfo.Length} bytes, 需要 {totalBytes} bytes");
                        }

                        // 确保文件流完全关闭
                        GC.Collect();
                        GC.WaitForPendingFinalizers();

                        // 等待一段时间确保文件可用
                        await Task.Delay(1000);

                        // 移动文件前再次检查
                        if (!File.Exists(tempPath))
                        {
                            Log("临时文件不存在，下载可能未完成");
                            throw new Exception("临时文件不存在");
                        }

                        // 移动文件到最终位置
                        try
                        {
                            if (File.Exists(finalPath))
                            {
                                File.Delete(finalPath);
                                Log("删除已存在的目标文件");
                            }

                            File.Move(tempPath, finalPath);
                            Log("文件移动成功");

                            // 验证最终文件
                            if (!File.Exists(finalPath))
                            {
                                Log("最终文件不存在");
                                throw new Exception("文件移动后不存在");
                            }

                            var finalFileInfo = new FileInfo(finalPath);
                            Log($"最终文件大小: {finalFileInfo.Length} bytes");

                            if (finalFileInfo.Length != totalBytes)
                            {
                                Log("最终文件大小不匹配");
                                throw new Exception($"文件大小不匹配: {finalFileInfo.Length} vs {totalBytes}");
                            }

                            // 安装APK
                            if (DeviceInfo.Platform == DevicePlatform.Android)
                            {
                                if (_platformInstaller == null)
                                {
                                    Log("平台安装器为空");
                                    return false;
                                }

                                Log("开始安装APK");
                                
                                // 多次尝试安装
                                for (int i = 0; i < 3; i++)
                                {
                                    Log($"尝试安装 APK，第 {i + 1} 次");
                                    var installResult = _platformInstaller.InstallUpdate(finalPath);
                                    Log($"安装结果: {installResult}");
                                    
                                    if (installResult)
                                    {
                                        return true;
                                    }
                                    
                                    // 果安装失败，等待后重试
                                    if (i < 2)
                                    {
                                        await Task.Delay(1000 * (i + 1));
                                    }
                                }

                                Log("所有安装尝试都失败了");
                                return false;
                            }

                            return false;
                        }
                        catch (Exception ex)
                        {
                            Log($"文件处理或安装过程出错: {ex.Message}");
                            Log($"错误堆栈: {ex.StackTrace}");
                            throw;
                        }
                    }
                }
                catch (Exception ex)
                {
                    Log($"下载出错 (尝试 {retryCount + 1}/{MaxRetries}): {ex.Message}");
                    retryCount++;
                    await Task.Delay(1000 * retryCount);  // 递增延迟
                }
            }

            Log($"下载失败，已重试 {MaxRetries} 次");
            return false;
        }

        public bool IsForceUpdate => _latestUpdateInfo?.ForceUpdate ?? false;

        public void ClearUpdateState()
        {
            _isUpdating = false;
        }
    }
}
