﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="MauiScanManager.MainPage">

    <ScrollView>
        <VerticalStackLayout Spacing="25" Padding="30,0" VerticalOptions="Center">

            <Label 
                Text="扫描测试"
                SemanticProperties.HeadingLevel="Level1"
                FontSize="32"
                HorizontalOptions="Center" />

            <Frame BorderColor="Gray" Padding="10" Margin="0,10">
                <VerticalStackLayout>
                    <Label 
                        Text="扫描历史记录"
                        FontSize="20"
                        HorizontalOptions="Center" />
                    <Editor 
                        x:Name="ScanHistoryEditor"
                        Text="{Binding ScanHistory}"
                        HeightRequest="200"
                        IsReadOnly="True"
                        AutoSize="TextChanges"/>
                </VerticalStackLayout>
            </Frame>

            <Label 
                Text="{Binding LastScanResult}"
                SemanticProperties.HeadingLevel="Level2"
                FontSize="18"
                HorizontalOptions="Center" />

            <Button 
                Text="清除历史"
                Command="{Binding ClearHistoryCommand}"
                HorizontalOptions="Center" />

        </VerticalStackLayout>
    </ScrollView>

</ContentPage>
