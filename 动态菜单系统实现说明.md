# 动态菜单系统实现说明

## 概述

本次修改将原本硬编码在 NavMenu.razor 中的菜单项改为从数据库动态获取，实现了基于用户权限的动态菜单显示功能。

## 主要修改内容

### 1. 数据模型

#### MenuItem 实体 (`CoreHub.Shared/Models/Database/MenuItem.cs`)
- 新增菜单项数据模型
- 支持层级菜单结构（父子关系）
- 包含权限控制字段
- 支持菜单分组功能

**主要字段：**
- `Code`: 菜单编码（唯一标识）
- `Name`: 菜单名称
- `RouteUrl`: 路由地址
- `Icon`: 图标类名
- `ParentId`: 父菜单ID（支持层级结构）
- `PermissionCode`: 关联的权限编码
- `IsPublic`: 是否公开菜单（如首页）
- `MenuType`: 菜单类型（1=菜单，2=分组标题）
- `SortOrder`: 排序号

### 2. 服务层

#### IMenuService 接口 (`CoreHub.Shared/Services/IMenuService.cs`)
定义菜单管理的核心方法：
- `GetUserMenusAsync()`: 获取用户可访问的菜单
- `GetAllMenusAsync()`: 获取所有菜单（管理用）
- `CreateMenuAsync()`: 创建菜单
- `UpdateMenuAsync()`: 更新菜单
- `DeleteMenuAsync()`: 删除菜单
- `InitializeDefaultMenusAsync()`: 初始化默认菜单

#### MenuService 实现 (`CoreHub.Shared/Services/MenuService.cs`)
- 实现基于权限的菜单过滤逻辑
- 支持菜单树结构构建
- 集成用户权限检查
- 提供完整的菜单CRUD操作

### 3. 数据库层

#### DatabaseContext 更新 (`CoreHub.Shared/Data/DatabaseContext.cs`)
- 添加 `MenuItems` 实体集合
- 在 `CreateTablesAsync()` 中包含 MenuItem 表创建
- 在 `SeedDataAsync()` 中添加菜单数据初始化
- 新增 `SeedMenuItemsAsync()` 方法初始化默认菜单

### 4. 前端组件

#### NavMenu.razor 重构 (`CoreHub.Shared/Layout/NavMenu.razor`)
- 移除硬编码的菜单项
- 实现动态菜单渲染
- 支持认证状态变化监听
- 实现菜单树结构渲染
- 区分已登录和未登录用户的菜单显示

**主要功能：**
- 动态加载用户菜单
- 基于权限的菜单过滤
- 支持菜单分组显示
- 响应认证状态变化

### 5. 服务注册

#### Web 项目 (`CoreHub.Web/Program.cs`)
```csharp
builder.Services.AddScoped<IMenuService, MenuService>();
```

## 数据库脚本

### 菜单表结构 (`菜单系统数据库脚本.sql`)
- 创建 MenuItems 表
- 创建必要的索引
- 初始化默认菜单数据
- 包含完整的菜单层级结构

## 菜单权限控制逻辑

### 权限检查流程
1. 获取用户ID（从认证状态）
2. 查询所有启用的菜单项
3. 对每个菜单项进行权限检查：
   - 公开菜单：所有用户可见
   - 需要权限的菜单：检查用户是否具有对应权限
   - 无权限要求的菜单：登录用户可见
4. 构建菜单树结构
5. 按排序号排序

### 菜单类型说明
- **MenuType = 1**: 普通菜单项，可点击跳转
- **MenuType = 2**: 分组标题，仅用于分组显示

## 使用方式

### 1. 添加新菜单
```csharp
var newMenu = new MenuItem
{
    Code = "NewFeature",
    Name = "新功能",
    RouteUrl = "new-feature",
    Icon = "bi bi-star-nav-menu",
    PermissionCode = "NewFeature.View",
    SortOrder = 60
};

await menuService.CreateMenuAsync(newMenu);
```

### 2. 创建菜单分组
```csharp
// 1. 创建分组
var group = new MenuItem
{
    Code = "ReportGroup",
    Name = "报表管理",
    Icon = "bi bi-graph-up-nav-menu",
    MenuType = 2, // 分组类型
    SortOrder = 300
};

// 2. 创建子菜单
var subMenu = new MenuItem
{
    Code = "SalesReport",
    Name = "销售报表",
    RouteUrl = "reports/sales",
    ParentId = group.Id, // 设置父菜单ID
    Level = 2,
    PermissionCode = "Reports.Sales.View"
};
```

## 优势

1. **动态性**: 菜单可通过数据库配置，无需重新部署
2. **权限控制**: 基于用户权限动态显示菜单
3. **层级支持**: 支持多级菜单结构
4. **易维护**: 菜单配置与代码分离
5. **扩展性**: 可轻松添加新的菜单项和功能

## 注意事项

1. 确保数据库连接正常
2. 菜单的权限编码需要与权限系统中的权限编码一致
3. 菜单排序号决定显示顺序
4. 分组菜单只有在有子菜单时才会显示
5. 公开菜单对所有用户可见，无需权限检查

## 后续扩展

1. 可添加菜单管理页面，支持在线配置菜单
2. 可添加菜单缓存机制，提高性能
3. 可支持更复杂的菜单权限控制逻辑
4. 可添加菜单国际化支持