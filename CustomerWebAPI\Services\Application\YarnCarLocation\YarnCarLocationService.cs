using System.Threading.Tasks;
using CustomerWebAPI.Database;
using CustomerWebAPI.Models;
using Microsoft.Extensions.Logging;
using SqlSugar;

namespace CustomerWebAPI.Services
{
    public class YarnCarLocationService : IYarnCarLocationService
    {
        private readonly DbContext _dbContext;
        private readonly ILogger<YarnCarLocationService> _logger;

        public YarnCarLocationService(DbContext dbContext, ILogger<YarnCarLocationService> logger)
        {
            _dbContext = dbContext;
            _logger = logger;
        }

        public async Task<ApiResponse<string>> SaveBatchAsync(string locationNo, string carNos)
        {
            try
            {
                await _dbContext.ExecuteAsync(async db =>
                {
                    await db.Ado.UseStoredProcedure().ExecuteCommandAsync(
                        "YDMDB..usp_ydSaveYarnCarLocationBatch",
                        new { LocationNo = locationNo, CarNos = carNos }
                    );
                    return true;
                });
                return ApiResponse<string>.Ok("", "保存成功");
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "批量保存染纱车号定位失败");
                return ApiResponse<string>.Fail($"批量保存染纱车号定位失败 - {ex.Message}");
            }
        }
    }
} 