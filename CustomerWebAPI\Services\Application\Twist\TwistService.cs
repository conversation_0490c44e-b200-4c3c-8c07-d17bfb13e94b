﻿using System;
using System.Threading.Tasks;
using CustomerWebAPI.Models;
using CustomerWebAPI.Common;
using CustomerWebAPI.Database;
using Microsoft.Extensions.Logging;
using CustomerWebAPI.Models.Twist;

namespace CustomerWebAPI.Services
{
    public class TwistService : ITwistService
    {
        private readonly DbContext _dbContext;
        private readonly ILogger<TwistService> _logger;

        public TwistService(DbContext dbContext, ILogger<TwistService> logger)
        {
            _dbContext = dbContext;
            _logger = logger;
        }

        public async Task<ApiResponse<string>> CreateBoxByScan(TwistCreateBox model)
        {
            try
            {
                await _dbContext.ExecuteAsync(async db =>
                {
                    await db.Ado.UseStoredProcedure()
                        .ExecuteCommandAsync("YDMDB.dbo.usp_twCreateBoxByScan", new
                        {
                            TaskNO = model.TaskNO,
                            ConeNum = model.ConeNum,
                            BoxWeight = model.BoxWeight,
                            BoxNum = model.BoxNum
                          
                        });
                    return true;
                });

                return ApiResponse<string>.Ok("装箱保存成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "装箱保存失败");
                return ApiResponse<string>.Fail(
                    $"装箱保存失败 - {ex.Message}",
                    (int)ApiErrorCodes.DatabaseError);
            }
        }

        public async Task<ApiResponse<string>> AutoDeliveryByScan(TwistBoxList model)
        {
            try
            {
                await _dbContext.ExecuteAsync(async db =>
                {
                    await db.Ado.UseStoredProcedure()
                        .ExecuteCommandAsync("YDMDB.dbo.usp_twAutoDeliveryByScan", new
                        {
                            BoxNoList = model.BoxNoList,
                        });
                    return true;
                });

                return ApiResponse<string>.Ok("出货保存成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "出货保存失败");
                return ApiResponse<string>.Fail(
                    $"出货保存失败 - {ex.Message}",
                    (int)ApiErrorCodes.DatabaseError);
            }
        }

    }
}