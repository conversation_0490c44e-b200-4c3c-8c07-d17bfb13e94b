﻿using MauiScanManager.Services;
using MauiScanManager.Pages;

namespace MauiScanManager
{
    public partial class AppShell : Shell
    {
        public AppShell(IOperationNavigationService navigationService)
        {
            InitializeComponent();
            
#if DEBUG
            // 注册打印测试页面路由（仅在DEBUG模式下）
            Routing.RegisterRoute("PrintTestPage", typeof(PrintTestPage));
#endif
        }
    }
}
