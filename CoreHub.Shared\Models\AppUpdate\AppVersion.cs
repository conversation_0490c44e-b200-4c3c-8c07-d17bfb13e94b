using SqlSugar;
using System.ComponentModel.DataAnnotations;

namespace CoreHub.Shared.Models.AppUpdate
{
    /// <summary>
    /// 应用版本信息
    /// </summary>
    [SugarTable("AppVersions")]
    public class AppVersion
    {
        /// <summary>
        /// 版本ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        /// <summary>
        /// 版本号 (如: 1.2.3)
        /// </summary>
        [SugarColumn(Length = 20, IsNullable = false)]
        [Required(ErrorMessage = "版本号不能为空")]
        [StringLength(20, ErrorMessage = "版本号长度不能超过20个字符")]
        public string VersionNumber { get; set; } = string.Empty;

        /// <summary>
        /// 版本代码 (用于比较版本大小)
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int VersionCode { get; set; }

        /// <summary>
        /// 平台类型 (Android, iOS, Windows, MacOS)
        /// </summary>
        [SugarColumn(Length = 20, IsNullable = false)]
        [Required(ErrorMessage = "平台类型不能为空")]
        public string Platform { get; set; } = string.Empty;

        /// <summary>
        /// 更新标题
        /// </summary>
        [SugarColumn(Length = 100, IsNullable = false)]
        [Required(ErrorMessage = "更新标题不能为空")]
        [StringLength(100, ErrorMessage = "更新标题长度不能超过100个字符")]
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// 更新描述
        /// </summary>
        [SugarColumn(ColumnDataType = "TEXT", IsNullable = true)]
        public string? Description { get; set; }

        /// <summary>
        /// 更新类型 (Major=重大更新, Minor=功能更新, Patch=修复更新, Hotfix=紧急修复)
        /// </summary>
        [SugarColumn(Length = 20, IsNullable = false)]
        public string UpdateType { get; set; } = "Minor";

        /// <summary>
        /// 是否强制更新
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public bool IsForceUpdate { get; set; } = false;

        /// <summary>
        /// 下载URL
        /// </summary>
        [SugarColumn(Length = 500, IsNullable = false)]
        [Required(ErrorMessage = "下载URL不能为空")]
        [StringLength(500, ErrorMessage = "下载URL长度不能超过500个字符")]
        public string DownloadUrl { get; set; } = string.Empty;

        /// <summary>
        /// 文件大小 (字节)
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public long FileSize { get; set; }

        /// <summary>
        /// 文件MD5哈希值
        /// </summary>
        [SugarColumn(Length = 32, IsNullable = true)]
        [StringLength(32, ErrorMessage = "MD5哈希值长度必须为32个字符")]
        public string? FileMd5 { get; set; }

        /// <summary>
        /// 最低支持版本代码
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? MinSupportedVersionCode { get; set; }

        /// <summary>
        /// 发布状态 (Draft=草稿, Testing=测试, Released=已发布, Archived=已归档)
        /// </summary>
        [SugarColumn(Length = 20, IsNullable = false)]
        public string Status { get; set; } = "Draft";

        /// <summary>
        /// 目标用户群体 (All=所有用户, Beta=测试用户, Department=特定部门)
        /// </summary>
        [SugarColumn(Length = 20, IsNullable = false)]
        public string TargetAudience { get; set; } = "All";

        /// <summary>
        /// 目标部门ID列表 (JSON格式，当TargetAudience为Department时使用)
        /// </summary>
        [SugarColumn(ColumnDataType = "TEXT", IsNullable = true)]
        public string? TargetDepartmentIds { get; set; }

        /// <summary>
        /// 发布时间
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? ReleaseTime { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 创建者ID
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int CreatedBy { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// 更新者ID
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? UpdatedBy { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 备注
        /// </summary>
        [SugarColumn(Length = 500, IsNullable = true)]
        [StringLength(500, ErrorMessage = "备注长度不能超过500个字符")]
        public string? Remark { get; set; }
    }
}
