<?xml version="1.0" encoding="utf-8" ?>
<views:BaseOperationPage
    x:Class="MauiScanManager.Views.TwistAutoDeliveryPage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:toolkit="http://schemas.microsoft.com/dotnet/2022/maui/toolkit"
    xmlns:viewmodels="clr-namespace:MauiScanManager.ViewModels"
    xmlns:views="clr-namespace:MauiScanManager.Views"
    Title="{Binding Operation.Description}"
    x:DataType="viewmodels:TwistAutoDeliveryViewModel">

    <Grid>
        <ScrollView>
            <VerticalStackLayout Padding="10" Spacing="5">

                <!--  扫描包数  -->
                <Grid
                    Grid.Row="0"
                    Margin="5,0,5,2"
                    ColumnDefinitions="*,Auto">
                    <Label
                        FontAttributes="Bold"
                        FontSize="18"
                        Text="扫描包数"
                        VerticalOptions="Center" />
                    <StackLayout
                        Grid.Column="1"
                        Orientation="Horizontal"
                        VerticalOptions="Center">
                        <Label
                            FontAttributes="Bold"
                            FontSize="32"
                            Text="{Binding ScanNum}"
                            TextColor="{StaticResource Primary}" />
                    </StackLayout>
                </Grid>


                <!--  箱号  -->
                <Label
                    FontAttributes="Bold"
                    FontSize="20"
                    Text="当前箱号" />
                <Entry
                    FontSize="20"
                    HeightRequest="45"
                    IsEnabled="False"
                    Text="{Binding BoxNo}" />


                <!--  保存按钮  -->
                <Button
                    Margin="0,10,0,0"
                    BackgroundColor="{StaticResource Primary}"
                    Command="{Binding TwAutoDeliveryCommand}"
                    FontSize="20"
                    HeightRequest="48"
                    Text="保存" />


            </VerticalStackLayout>
        </ScrollView>
        <ActivityIndicator
            HorizontalOptions="Center"
            VerticalOptions="Center"
            Color="{StaticResource Primary}" />
    </Grid>
</views:BaseOperationPage> 