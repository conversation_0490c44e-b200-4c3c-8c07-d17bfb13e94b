﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFrameworks>net8.0-android;net8.0-ios;net8.0-maccatalyst</TargetFrameworks>
		<TargetFrameworks Condition="$([MSBuild]::IsOSPlatform('windows'))">$(TargetFrameworks);net8.0-windows10.0.19041.0</TargetFrameworks>
		<!-- Uncomment to also build the tizen app. You will need to install tizen by following this: https://github.com/Samsung/Tizen.NET -->
		<!-- <TargetFrameworks>$(TargetFrameworks);net8.0-tizen</TargetFrameworks> -->

		<!-- Note for MacCatalyst:
		The default runtime is maccatalyst-x64, except in Release config, in which case the default is maccatalyst-x64;maccatalyst-arm64.
		When specifying both architectures, use the plural <RuntimeIdentifiers> instead of the singular <RuntimeIdentifier>.
		The Mac App Store will NOT accept apps with ONLY maccatalyst-arm64 indicated;
		either BOTH runtimes must be indicated or ONLY macatalyst-x64. -->
		<!-- For example: <RuntimeIdentifiers>maccatalyst-x64;maccatalyst-arm64</RuntimeIdentifiers> -->

		<OutputType>Exe</OutputType>
		<RootNamespace>MauiScanManager</RootNamespace>
		<UseMaui>true</UseMaui>
		<SingleProject>true</SingleProject>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>

		<!-- Display name -->
		<ApplicationTitle>三元扫描</ApplicationTitle>

		<!-- App Identifier -->
		<ApplicationId>com.saintyeartex.mauiscanmanager</ApplicationId>

		<!-- Versions -->
		<ApplicationDisplayVersion>1.0.28</ApplicationDisplayVersion>
		<ApplicationVersion>1</ApplicationVersion>

		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'ios'">11.0</SupportedOSPlatformVersion>
		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'maccatalyst'">13.1</SupportedOSPlatformVersion>
		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'android'">21.0</SupportedOSPlatformVersion>
		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">10.0.17763.0</SupportedOSPlatformVersion>
		<TargetPlatformMinVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">10.0.17763.0</TargetPlatformMinVersion>
		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'tizen'">6.5</SupportedOSPlatformVersion>
	</PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='Release|net8.0-android|AnyCPU'">
	  <AndroidPackageFormat>apk</AndroidPackageFormat>
	  <AndroidKeyStore>False</AndroidKeyStore>
	</PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='Debug|net8.0-android|AnyCPU'">
	  <AndroidKeyStore>False</AndroidKeyStore>
	</PropertyGroup>

	<ItemGroup>
		<!-- App Icon -->
		<MauiIcon Include="Resources\AppIcon\appicon.svg" ForegroundFile="Resources\AppIcon\appiconfg.svg" Color="#512BD4" />

		<!-- Splash Screen -->
		<MauiSplashScreen Include="Resources\Splash\splash.svg" Color="#512BD4" BaseSize="128,128" />

		<!-- Images -->
		<MauiImage Include="Resources\Images\*" />
		<MauiImage Update="Resources\Images\dotnet_bot.png" Resize="True" BaseSize="300,185" />

		<!-- Custom Fonts -->
		<MauiFont Include="Resources\Fonts\*" />

		<!-- Raw Assets (also remove the "Resources\Raw" prefix) -->
		<MauiAsset Include="Resources\Raw\**" LogicalName="%(RecursiveDir)%(Filename)%(Extension)" />
	</ItemGroup>

	<ItemGroup Condition="'$(TargetFramework)' == 'net8.0-android'">
		<AndroidResource Include="Platforms\Android\Resources\raw\*.mp3" />
		<AndroidLibrary Include="Platforms\Android\libs\lcprintsdk1.1-release.aar" />
	</ItemGroup>


	<ItemGroup>
		<PackageReference Include="Microsoft.Extensions.Http" Version="8.0.1" />
		<PackageReference Include="Microsoft.Maui.Controls" Version="$(MauiVersion)" />
		<PackageReference Include="Microsoft.Maui.Controls.Compatibility" Version="$(MauiVersion)" />
		<PackageReference Include="Microsoft.Extensions.Logging.Debug" Version="8.0.0" />
		<PackageReference Include="CommunityToolkit.Mvvm" Version="8.2.2" />
		<PackageReference Include="CommunityToolkit.Maui" Version="7.0.1" />
	</ItemGroup>

	<ItemGroup>
	  <AndroidResource Update="Platforms\Android\Resources\raw\CheckChemicalBoxPrompt.mp3">
	    <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	  </AndroidResource>
	  <AndroidResource Update="Platforms\Android\Resources\raw\check_failure.mp3">
	    <CopyToOutputDirectory>Never</CopyToOutputDirectory>
	  </AndroidResource>
	  <AndroidResource Update="Platforms\Android\Resources\raw\check_prompt.mp3">
	    <CopyToOutputDirectory>Never</CopyToOutputDirectory>
	  </AndroidResource>
	  <AndroidResource Update="Platforms\Android\Resources\raw\check_success.mp3">
	    <CopyToOutputDirectory>Never</CopyToOutputDirectory>
	  </AndroidResource>
	  <AndroidResource Update="Platforms\Android\Resources\raw\Remain1Box.mp3">
	    <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	  </AndroidResource>
	  <AndroidResource Update="Platforms\Android\Resources\raw\Remain2Box.mp3">
	    <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	  </AndroidResource>
	  <AndroidResource Update="Platforms\Android\Resources\raw\Remain3Box.mp3">
	    <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	  </AndroidResource>
	  <AndroidResource Update="Platforms\Android\Resources\raw\Remain4Box.mp3">
	    <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	  </AndroidResource>
	</ItemGroup>

	<ItemGroup>
	  <Compile Update="Views\RTcpDeliveryByScanPage.xaml.cs">
	    <DependentUpon>RTcpDeliveryByScanPage.xaml</DependentUpon>
	  </Compile>
	</ItemGroup>

	<ItemGroup>
	  <MauiXaml Update="Views\RTcpDeliveryByScanPage.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </MauiXaml>
	  <MauiXaml Update="Views\TwistAutoDeliveryPage.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </MauiXaml>
	  <MauiXaml Update="Views\TwistCreateBoxPage.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </MauiXaml>
	</ItemGroup>

	<ProjectExtensions><VisualStudio><UserProperties XamarinHotReloadDebuggerTimeoutExceptionMauiScanManagerHideInfoBar="True" /></VisualStudio></ProjectExtensions>

</Project>
