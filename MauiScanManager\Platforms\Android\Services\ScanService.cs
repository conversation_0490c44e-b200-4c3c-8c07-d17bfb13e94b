using Android.Content;
using Microsoft.Maui.Platform;
using MauiScanManager.Services;
using System.Diagnostics;

namespace MauiScanManager.Platforms.Android.Services;

public class ScanService : IScanService
{
    private BroadcastReceiver _receiver;
    private readonly IServiceProvider _serviceProvider;
    private bool _isInitialized;

    public event EventHandler<ScanEventArgs> OnScanResult;

    public ScanService(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
    }

    public void Initialize()
    {
        if (_isInitialized) return;

        System.Diagnostics.Debug.WriteLine("扫描服务已初始化");
        
        _receiver = new ScanBroadcastReceiver();
        (_receiver as ScanBroadcastReceiver).OnScanResult += (s, e) => 
        {
            System.Diagnostics.Debug.WriteLine($"收到扫描结果: {e.Code}");
            OnScanResult?.Invoke(this, e);
        };

        var filter = new IntentFilter();
        // 添加多个可能的广播 action
        filter.AddAction("com.kte.scan.result");          // 原始 action
        filter.AddAction("android.intent.ACTION_DECODE_DATA");  // 通用扫描 action
        filter.AddAction("scanner.rcv.message");          // 常见扫描 action
        filter.AddAction("com.android.server.scannerservice.broadcast"); // 另一个常见 action
        filter.AddAction("com.speedata.showdecodedata"); // 另一个常见 action

        Platform.CurrentActivity.RegisterReceiver(_receiver, filter);
        
        System.Diagnostics.Debug.WriteLine("扫描服务初始化完成");
        _isInitialized = true;
    }

    public void Dispose()
    {
        if (_receiver != null)
        {
            Platform.CurrentActivity.UnregisterReceiver(_receiver);
            _receiver = null;
        }
        _isInitialized = false;
    }

    // 添加一个方法用于触发扫描结果
    protected virtual void RaiseScanResult(string code, string type, byte[] source)
    {
        OnScanResult?.Invoke(this, new ScanEventArgs 
        { 
            Code = code, 
            Type = type, 
            CodeSource = source 
        });
    }
}

public class ScanBroadcastReceiver : BroadcastReceiver
{
    public event EventHandler<ScanEventArgs> OnScanResult;

    public override void OnReceive(Context context, Intent intent)
    {
        Debug.WriteLine("收到广播...");
        Debug.WriteLine($"Action: {intent.Action}");
        
        // 尝试不同的键名，因为不同扫描设备可能使用不同的键
        var code = intent.GetStringExtra("code")           // 标准键名
                  ?? intent.GetStringExtra("barcode")      // 常见替代键名
                  ?? intent.GetStringExtra("scannerdata")  // 其他可能的键名
                  ?? intent.GetStringExtra("data")        // 通用键名
                  ?? intent.GetStringExtra("message");        // SC40G

        var type = intent.GetStringExtra("type")
                   ?? intent.GetStringExtra("codetype")
                   ?? intent.GetStringExtra("barcodetype");

        var codeSrc = intent.GetByteArrayExtra("code_src")
                      ?? intent.GetByteArrayExtra("raw_data"); 

        Debug.WriteLine($"扫描内容: {code}");
        Debug.WriteLine($"扫描类型: {type}");
        Debug.WriteLine($"原始数据: {(codeSrc != null ? "有数据" : "无数据")}");

        // 获取所有Extra数据的键，用于调试
        var extras = intent.Extras;
        if (extras != null)
        {
            Debug.WriteLine("所有Extra数据:");
            foreach (var key in extras.KeySet())
            {
                var value = extras.Get(key);
                Debug.WriteLine($"Key: {key}, Value: {value}, Type: {value?.GetType().Name ?? "null"}");
            }
        }

        if (!string.IsNullOrEmpty(code))
        {
            OnScanResult?.Invoke(this, new ScanEventArgs
            {
                Code = code,
                Type = type,
                CodeSource = codeSrc
            });
        }
        else
        {
            Debug.WriteLine("警告: 未能从广播中获取到扫描数据");
        }
    }
}
