using SqlSugar;
using System.ComponentModel.DataAnnotations;

namespace CoreHub.Shared.Models.Database
{
    /// <summary>
    /// 设备型号实体
    /// </summary>
    [SugarTable("EquipmentModels")]
    public class EquipmentModel
    {
        /// <summary>
        /// 设备型号ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        /// <summary>
        /// 型号编码（唯一）
        /// </summary>
        [SugarColumn(Length = 50, IsNullable = false)]
        [Required(ErrorMessage = "型号编码不能为空")]
        [StringLength(50, ErrorMessage = "型号编码长度不能超过50个字符")]
        public string Code { get; set; } = string.Empty;

        /// <summary>
        /// 型号名称
        /// </summary>
        [SugarColumn(Length = 100, IsNullable = false)]
        [Required(ErrorMessage = "型号名称不能为空")]
        [StringLength(100, ErrorMessage = "型号名称长度不能超过100个字符")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 设备类别
        /// </summary>
        [SugarColumn(Length = 50, IsNullable = false)]
        [Required(ErrorMessage = "设备类别不能为空")]
        [StringLength(50, ErrorMessage = "设备类别长度不能超过50个字符")]
        public string Category { get; set; } = string.Empty;

        /// <summary>
        /// 品牌
        /// </summary>
        [SugarColumn(Length = 100, IsNullable = true)]
        [StringLength(100, ErrorMessage = "品牌长度不能超过100个字符")]
        public string? Brand { get; set; }

        /// <summary>
        /// 型号规格
        /// </summary>
        [SugarColumn(Length = 100, IsNullable = true)]
        [StringLength(100, ErrorMessage = "型号规格长度不能超过100个字符")]
        public string? Model { get; set; }

        /// <summary>
        /// 技术规格
        /// </summary>
        [SugarColumn(Length = 1000, IsNullable = true)]
        [StringLength(1000, ErrorMessage = "技术规格长度不能超过1000个字符")]
        public string? Specifications { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        [SugarColumn(Length = 500, IsNullable = true)]
        [StringLength(500, ErrorMessage = "描述长度不能超过500个字符")]
        public string? Description { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 创建时间
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 创建人ID
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? CreatedBy { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// 更新人ID
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? UpdatedBy { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [SugarColumn(Length = 1000, IsNullable = true)]
        [StringLength(1000, ErrorMessage = "备注长度不能超过1000个字符")]
        public string? Remark { get; set; }

        // 导航属性
        /// <summary>
        /// 该型号的设备列表
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public List<Equipment> Equipment { get; set; } = new List<Equipment>();
    }
}
