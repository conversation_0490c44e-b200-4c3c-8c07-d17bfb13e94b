using MauiScanManager.Constants;
using MauiScanManager.Models;
using Microsoft.Extensions.Logging;
using System.Threading.Tasks;

namespace MauiScanManager.Services
{
    public class YarnCarLocationService : IYarnCarLocationService
    {
        private readonly IApiService _apiService;
        private readonly IPlatformLoadingService _loadingService;
        private readonly ILogger<YarnCarLocationService> _logger;

        public YarnCarLocationService(
            IApiService apiService,
            IPlatformLoadingService loadingService,
            ILogger<YarnCarLocationService> logger)
        {
            _apiService = apiService;
            _loadingService = loadingService;
            _logger = logger;
        }

        public async Task<ServiceResult<string>> SaveBatchAsync(string locationNo, string carNos)
        {
            _loadingService.ShowNativeLoading("正在保存...");
            try
            {
                var model = new YarnCarLocation
                {
                    LocationNo = locationNo,
                    CarNos = carNos
                };
                var response = await _apiService.PostAsync<string>(ApiEndpoints.YarnCarLocation.Save, model);
                return response.Success
                    ? ServiceResult<string>.Success("保存成功")
                    : ServiceResult<string>.Failure(response.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量保存染纱车号定位失败");
                return ServiceResult<string>.Failure(ex.Message);
            }
            finally
            {
                _loadingService.HideNativeLoading();
            }
        }
    }
} 