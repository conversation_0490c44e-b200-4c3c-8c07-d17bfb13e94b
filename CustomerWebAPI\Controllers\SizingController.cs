﻿using CustomerWebAPI.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Http;
using CustomerWebAPI.Common;
using CustomerWebAPI.Models;
using System.Threading.Tasks;

namespace CustomerWebAPI.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class SizingController : ControllerBase
    {
        private readonly ISizingService _sizingService;
        private readonly ILogger<SizingController> _logger;

        public SizingController(ISizingService sizingService, ILogger<SizingController> logger)
        {
            _sizingService = sizingService;
            _logger = logger;
        }

        [HttpPost("UpMachine")]
        [ProducesResponseType(typeof(ApiResponse<string>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ApiResponse<string>), StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<ApiResponse<string>>> SaveSizingUpMachine([FromBody] SizingUpMachine model)
        {
            var response = await _sizingService.SaveSizingUpMachine(model);
            return response.Success ? Ok(response) : BadRequest(response);
        }

        [HttpPost("MachineUpInfo")]
        [ProducesResponseType(typeof(ApiResponse<SizingMachineUpInfo>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ApiResponse<SizingMachineUpInfo>), StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<ApiResponse<SizingMachineUpInfo>>> GetSizingMachineUpInfo([FromBody] SizingMachine model)
        {
            var response = await _sizingService.GetSizingMachineUpInfo(model);
            return response.Success ? Ok(response) : BadRequest(response);
        }

        [HttpPost("DownMachine")]
        [ProducesResponseType(typeof(ApiResponse<string>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ApiResponse<string>), StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<ApiResponse<string>>> SaveSizingDownMachine([FromBody] SizingUpMachine model)
        {
            var response = await _sizingService.SaveSizingDownMachine(model);
            return response.Success ? Ok(response) : BadRequest(response);
        }

        [HttpPost("CancelUpMachine")]
        [ProducesResponseType(typeof(ApiResponse<string>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ApiResponse<string>), StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<ApiResponse<string>>> SaveSizingCancelUpMachine([FromBody] SizingCancelUpMachine model)
        {
            var response = await _sizingService.SaveSizingCancelUpMachine(model);
            return response.Success ? Ok(response) : BadRequest(response);
        }
    }
}
