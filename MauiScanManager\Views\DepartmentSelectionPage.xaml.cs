using MauiScanManager.ViewModels;  // 添加这行来引用 ViewModel 命名空间

namespace MauiScanManager.Views
{
    public partial class DepartmentSelectionPage : ContentPage
    {
        private readonly DepartmentSelectionViewModel _viewModel;

        public DepartmentSelectionPage(DepartmentSelectionViewModel viewModel)
        {
            InitializeComponent();
            _viewModel = viewModel;
            BindingContext = viewModel;
        }

        protected override async void OnAppearing()
        {
            base.OnAppearing();
            await _viewModel.InitializeCommand.ExecuteAsync(null);
        }
    }
}
