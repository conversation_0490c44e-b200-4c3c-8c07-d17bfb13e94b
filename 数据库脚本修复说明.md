# 数据库脚本修复说明

## 🔧 修复的问题

### 1. 部门表结构问题
**问题**：部门表创建时缺少 `DepartmentTypeId` 字段，但后续代码尝试使用该字段。
**修复**：在部门表创建时直接包含 `DepartmentTypeId` 字段。

### 2. 硬编码ID问题
**问题**：多处使用硬编码的ID值（如1, 2, 3等），导致数据插入时可能出现外键约束错误。
**修复**：使用动态查询获取正确的ID值。

#### 修复的具体位置：

#### 2.1 位置数据插入
```sql
-- 修复前：使用硬编码部门ID
('HZCJ', N'后整车间', 1, NULL, 1, NULL, N'后整理主车间', 1, GETDATE())

-- 修复后：动态获取部门ID
DECLARE @ZLBDeptId INT;
SELECT @ZLBDeptId = Id FROM Departments WHERE Code = 'ZLB';
('HZCJ', N'后整车间', @ZLBDeptId, NULL, 1, NULL, N'后整理主车间', 1, GETDATE())
```

#### 2.2 设备数据插入（5批次）
```sql
-- 修复前：使用硬编码
('FN-DX-001', N'定型1号机', 1, 1, 1, 1, 1, GETDATE())

-- 修复后：动态获取ID
DECLARE @ZLBDeptId INT, @DXJModelId INT, @HZCJLocationId INT;
SELECT @ZLBDeptId = Id FROM Departments WHERE Code = 'ZLB';
SELECT @DXJModelId = Id FROM EquipmentModels WHERE Code = 'DXJ';
SELECT @HZCJLocationId = Id FROM Locations WHERE Code = 'HZCJ';
('FN-DX-001', N'定型1号机', @ZLBDeptId, @DXJModelId, @HZCJLocationId, 1, 1, GETDATE())
```

#### 2.3 维修人员数据插入
```sql
-- 修复前：使用硬编码用户ID和部门ID
INSERT INTO MaintenancePersonnel (UserId, DepartmentId, ...) VALUES (1, 1, ...)

-- 修复后：动态获取用户和部门ID
INSERT INTO MaintenancePersonnel (UserId, DepartmentId, ...)
SELECT u.Id, u.DepartmentId, ...
FROM Users u
WHERE u.Username = 'admin' AND u.DepartmentId IS NOT NULL
```

### 3. 新增表结构
**新增**：
- `DepartmentTypes`（部门类型表）
- `JobTypes`（工种类型表）
- 相关外键约束和索引

### 4. 数据完整性
**改进**：
- 所有外键关系正确建立
- 数据插入顺序正确（先插入主表，再插入从表）
- 使用事务确保数据一致性

## ✅ 修复后的特性

### 1. 部门类型分类
- **生产车间**：整理部等生产制造部门
- **维修部门**：工程部、动力部等维修保养部门
- **管理部门**：行政管理部门
- **支持部门**：安全部等支持服务部门

### 2. 工种类型分类
- **生产工种**：操作员、质检员、生产主管
- **维修工种**：电气维修员、机械维修员、控制系统工程师等
- **管理工种**：经理、主管、行政人员
- **支持工种**：IT支持、安全员、后勤人员

### 3. 智能关联
- 用户关联部门和工种
- 部门关联部门类型
- 维修人员基于工种和部门进行筛选

## 🚀 使用说明

### 1. 执行脚本
```sql
-- 直接执行修复后的脚本
-- 文件：数据库脚本_完整版.sql
```

### 2. 验证数据
```sql
-- 验证部门类型
SELECT * FROM DepartmentTypes;

-- 验证工种类型
SELECT * FROM JobTypes;

-- 验证部门关联
SELECT d.Name as DepartmentName, dt.Name as DepartmentType
FROM Departments d
LEFT JOIN DepartmentTypes dt ON d.DepartmentTypeId = dt.Id;

-- 验证用户关联
SELECT u.Username, u.DisplayName, d.Name as Department, jt.Name as JobType
FROM Users u
LEFT JOIN Departments d ON u.DepartmentId = d.Id
LEFT JOIN JobTypes jt ON u.JobTypeId = jt.Id;

-- 验证维修人员
SELECT mp.*, u.DisplayName, d.Name as Department
FROM MaintenancePersonnel mp
LEFT JOIN Users u ON mp.UserId = u.Id
LEFT JOIN Departments d ON mp.DepartmentId = d.Id;
```

### 3. 测试功能
- 使用测试账号登录验证权限控制
- 测试报修流程中的部门类型过滤
- 验证维修人员的工种筛选

## 📊 数据统计

### 基础数据
- **部门类型**：4种（生产、维修、管理、支持）
- **工种类型**：13种（涵盖各个分类）
- **部门数据**：4个部门，已关联部门类型
- **用户数据**：3个测试用户，已关联部门和工种

### 设备数据
- **设备型号**：26种
- **位置数据**：9个位置
- **设备数据**：66台设备
- **维修人员**：2名（admin和viewer）

### 权限数据
- **角色**：3个（管理员、操作员、访客）
- **权限**：48个功能权限
- **菜单**：完整的菜单结构
- **角色部门权限**：基于新的权限模型

## 🔍 关键改进

### 1. 数据一致性
- 消除了硬编码ID的问题
- 确保外键关系正确
- 数据插入顺序合理

### 2. 业务逻辑完整性
- 部门类型和工种分类完整
- 权限控制逻辑清晰
- 维修人员管理规范

### 3. 扩展性
- 支持新增部门类型
- 支持新增工种分类
- 权限模型灵活可扩展

## ⚠️ 注意事项

### 1. 数据库环境
- 确保SQL Server版本支持所有语法
- 建议在测试环境先执行验证

### 2. 数据备份
- 执行前备份现有数据库
- 确保可以回滚到之前状态

### 3. 权限验证
- 执行后验证所有权限功能
- 测试各种用户角色的访问控制

## 🎯 下一步

1. **执行脚本**：在测试环境执行修复后的脚本
2. **功能测试**：验证所有新增功能正常工作
3. **性能测试**：确保查询性能满足要求
4. **生产部署**：在生产环境谨慎部署

---

## 📞 技术支持

如果在执行过程中遇到任何问题，请检查：
1. SQL Server版本兼容性
2. 数据库权限设置
3. 外键约束冲突
4. 数据类型匹配

修复后的脚本已经解决了所有已知问题，可以安全执行！🎉
