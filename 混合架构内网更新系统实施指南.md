# CoreHub 混合架构内网更新系统实施指南

## 🏗️ 系统架构概览

### 混合架构设计
```
内网环境:
┌─────────────────┐    ┌─────────────────┐
│  CoreHub.Web    │◄──►│  CoreHub.Maui   │
│  (更新管理服务)  │    │  (Android客户端) │
└─────────────────┘    └─────────────────┘
         ↓
外网环境 (备用):
┌─────────────────┐
│  App Center /   │
│  Firebase       │
│  (备用分发渠道)  │
└─────────────────┘
```

### 核心特性
- ✅ **内网自主控制**: 完全基于内网环境的更新管理
- ✅ **版本管理**: 支持多平台版本发布和回滚
- ✅ **权限控制**: 基于用户角色的更新权限管理
- ✅ **断点续传**: 支持大文件的断点续传下载
- ✅ **文件验证**: MD5校验确保文件完整性
- ✅ **强制更新**: 支持安全补丁的强制更新策略
- ✅ **分阶段发布**: 支持按部门分批发布更新

## 📋 实施步骤

### 1. 数据库初始化

执行数据库脚本创建必要的表结构：

```sql
-- 执行 应用更新系统数据库脚本.sql
-- 这将创建以下表：
-- • AppVersions - 应用版本管理表
-- • AppUpdateLogs - 更新日志表
-- • 相关存储过程和索引
```

### 2. 服务器端配置

#### 2.1 创建更新文件目录
```bash
# 在 CoreHub.Web 项目根目录下创建
mkdir wwwroot/updates
chmod 755 wwwroot/updates
```

#### 2.2 配置文件更新
已在 `appsettings.json` 中添加更新服务配置：
```json
{
  "UpdateService": {
    "BaseUrl": "https://************:8081",
    "UpdatesDirectory": "wwwroot/updates",
    "MaxFileSize": 104857600,
    "AllowedFileTypes": [ ".apk", ".ipa", ".msix", ".dmg" ]
  }
}
```

#### 2.3 服务注册
已在 `Program.cs` 中注册更新服务：
```csharp
builder.Services.AddScoped<IAppUpdateService, AppUpdateService>();
```

### 3. 客户端配置

#### 3.1 MAUI项目配置
已在 `MauiProgram.cs` 中添加：
```csharp
// Android平台更新服务
builder.Services.AddSingleton<IClientUpdateService, Platforms.Android.AndroidUpdateService>();
// HttpClient服务
builder.Services.AddHttpClient();
```

#### 3.2 Android权限配置
在 `Platforms/Android/AndroidManifest.xml` 中添加必要权限：
```xml
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
```

#### 3.3 FileProvider配置
在 `Platforms/Android/Resources/xml/` 目录下创建 `file_paths.xml`：
```xml
<?xml version="1.0" encoding="utf-8"?>
<paths xmlns:android="http://schemas.android.com/apk/res/android">
    <external-files-path name="updates" path="updates/" />
</paths>
```

在 `AndroidManifest.xml` 中添加 FileProvider：
```xml
<provider
    android:name="androidx.core.content.FileProvider"
    android:authorities="${applicationId}.fileprovider"
    android:exported="false"
    android:grantUriPermissions="true">
    <meta-data
        android:name="android.support.FILE_PROVIDER_PATHS"
        android:resource="@xml/file_paths" />
</provider>
```

## 🚀 使用指南

### 1. 版本发布流程

#### 1.1 准备APK文件
```bash
# 构建发布版本
dotnet publish -c Release -f net8.0-android

# 将APK文件复制到更新目录
cp bin/Release/net8.0-android/publish/com.company.corehub-Signed.apk wwwroot/updates/CoreHub_1.1.0.apk
```

#### 1.2 创建版本记录
通过管理界面或API创建新版本：
```json
{
  "versionNumber": "1.1.0",
  "versionCode": 2,
  "platform": "Android",
  "title": "功能增强更新",
  "description": "• 新增设备二维码扫描\n• 优化用户界面\n• 修复已知问题",
  "updateType": "Minor",
  "isForceUpdate": false,
  "downloadUrl": "https://************:8081/api/AppUpdate/download/Android/1.1.0",
  "fileSize": 25600000,
  "targetAudience": "All"
}
```

#### 1.3 发布版本
通过管理界面点击"发布"按钮，或调用API：
```http
POST /api/AppUpdate/versions/{id}/publish
Authorization: Bearer {token}
```

### 2. 客户端更新流程

#### 2.1 自动检查更新
客户端启动时自动检查：
```csharp
// 在 App.xaml.cs 或 MainPage 中
var updateService = ServiceProvider.GetService<IClientUpdateService>();
await updateService.CheckForUpdateAsync();
```

#### 2.2 手动检查更新
用户主动检查：
```csharp
var response = await updateService.CheckForUpdateAsync(silent: false);
if (response.HasUpdate)
{
    // 显示更新对话框
    ShowUpdateDialog(response);
}
```

#### 2.3 下载和安装
```csharp
// 下载更新
var progress = new Progress<DownloadProgress>(p => 
{
    // 更新进度条
    UpdateProgressBar(p.ProgressPercentage);
});

var (success, filePath, error) = await updateService.DownloadUpdateAsync(
    versionInfo, progress, cancellationToken);

if (success && !string.IsNullOrEmpty(filePath))
{
    // 安装更新
    await updateService.InstallUpdateAsync(filePath);
}
```

### 3. 管理界面使用

#### 3.1 访问管理页面
```
https://************:8081/app-update-management
```

#### 3.2 版本管理功能
- **创建版本**: 添加新的应用版本
- **编辑版本**: 修改版本信息
- **发布版本**: 将草稿版本发布给用户
- **撤回版本**: 撤回已发布的版本
- **删除版本**: 删除不需要的版本

## 🔧 高级配置

### 1. 分阶段发布

#### 1.1 按部门发布
```json
{
  "targetAudience": "Department",
  "targetDepartmentIds": "[1,2,3]"  // 特定部门ID列表
}
```

#### 1.2 测试用户发布
```json
{
  "targetAudience": "Beta"
}
```

### 2. 强制更新策略

#### 2.1 安全补丁强制更新
```json
{
  "updateType": "Hotfix",
  "isForceUpdate": true,
  "minSupportedVersionCode": 1  // 低于此版本强制更新
}
```

### 3. 自动更新配置

#### 3.1 设置检查间隔
```csharp
// 设置每12小时检查一次
updateService.SetUpdateCheckInterval(12);
updateService.StartAutoUpdateCheck();
```

#### 3.2 静默更新
```csharp
// 后台静默检查，不弹出提示
await updateService.CheckForUpdateAsync(silent: true);
```

## 🛡️ 安全考虑

### 1. 文件完整性验证
- 所有下载文件都会进行MD5校验
- 损坏的文件会自动重新下载

### 2. 权限控制
- 只有管理员可以创建和发布版本
- 支持基于角色的访问控制

### 3. HTTPS传输
- 所有通信都通过HTTPS加密
- 支持自签名证书

## 📊 监控和日志

### 1. 更新日志
系统会自动记录：
- 更新检查记录
- 下载进度和结果
- 安装成功/失败状态
- 设备信息和错误详情

### 2. 性能监控
- 下载速度统计
- 成功率分析
- 用户采用率跟踪

## 🔄 备用方案

### 1. App Center集成
当内网不可用时，可切换到App Center：
```csharp
// 配置备用更新源
if (!await IsInternalNetworkAvailable())
{
    // 切换到App Center
    await AppCenter.Start("your-app-secret", typeof(Distribute));
}
```

### 2. 手动分发
- 通过企业微信/钉钉分发APK文件
- USB传输安装包
- 局域网文件共享

## 📞 技术支持

### 常见问题
1. **更新检查失败**: 检查网络连接和服务器状态
2. **下载中断**: 支持断点续传，重新开始下载
3. **安装失败**: 检查Android安装权限设置
4. **版本回滚**: 通过管理界面撤回有问题的版本

### 日志位置
- **服务器日志**: `logs/corehub-web-*.log`
- **客户端日志**: Android系统日志 (logcat)
- **数据库日志**: `AppUpdateLogs` 表

---

## 🎯 下一步计划

1. **iOS支持**: 扩展到iOS平台的更新支持
2. **增量更新**: 实现差分更新减少下载大小
3. **A/B测试**: 支持多版本并行测试
4. **统计分析**: 更详细的使用统计和分析报告
