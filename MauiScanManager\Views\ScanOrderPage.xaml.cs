using MauiScanManager.ViewModels;
using MauiScanManager.Attributes;
using MauiScanManager.Constants;

namespace MauiScanManager.Views
{
    [OperationPage(OperationCodes.PR_SZ_RECEIVE_ORDER)]      // PR01 采购接收
    [OperationPage(OperationCodes.PC_RECEIVE_ORDER)]     // PC01 排单接收
    [OperationPage(OperationCodes.AT_RECEIVE_ORDER)]     // AT01 工艺接收
    [OperationPage(OperationCodes.LB_RECEIVE_HL)]        // LB01 手样收样编色号
    [OperationPage(OperationCodes.PC_CONFIRM_DELIVERYTIME)] // PC02 计划签交期
    [OperationPage(OperationCodes.PR_SF_RECEIVE_ORDER)] // TH01 色纺采购签纱
    public partial class ScanOrderPage : BaseOperationPage
    {
        public ScanOrderPage(ScanOrderViewModel viewModel)
            : base(viewModel as BaseOperationViewModel)
        {
            InitializeComponent();
        }
    }
} 