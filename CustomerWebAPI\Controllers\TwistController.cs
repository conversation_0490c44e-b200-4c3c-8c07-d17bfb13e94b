﻿using CustomerWebAPI.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Http;
using CustomerWebAPI.Common;
using CustomerWebAPI.Models;
using System.Threading.Tasks;
using CustomerWebAPI.Models.Twist;


namespace CustomerWebAPI.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class TwistController : ControllerBase
    {
        private readonly ITwistService _twistService;
        private readonly ILogger<TwistController> _logger;


        public TwistController(ITwistService twistService, ILogger<TwistController> logger)
        {
            _twistService = twistService;
            _logger = logger;
        }

        [HttpPost("twCreateBox")]
        [ProducesResponseType(typeof(ApiResponse<string>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ApiResponse<string>), StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<ApiResponse<string>>> CreateBoxByScan([FromBody] TwistCreateBox model)
        {
            var response = await _twistService.CreateBoxByScan(model);
            return response.Success ? Ok(response) : BadRequest(response);
        }


        [HttpPost("twAutoDelivery")]
        [ProducesResponseType(typeof(ApiResponse<string>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ApiResponse<string>), StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<ApiResponse<string>>> AutoDeliveryByScan([FromBody] TwistBoxList model)
        {
            var response = await _twistService.AutoDeliveryByScan(model);
            return response.Success ? Ok(response) : BadRequest(response);
        }


    }
}
