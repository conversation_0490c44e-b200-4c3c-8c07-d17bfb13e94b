﻿using System;
using Dapper.Contrib.Extensions;

namespace CustomerWebAPI.Models
{
    [Table("OrderDetail")]
    public class OrderDetail
    {
        /// <summary>
        /// 
        /// </summary>
        [ExplicitKey]
        public Guid Iden { get; set; }

        public Guid OrderHeaderId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string MessageFunctionCode { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string ContractNumber { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string PoNumber { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string BuyerMemberId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string SellerMemberId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string OrderFunctionCode { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string ComplianceTemplateCode { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string IssueDate { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string RevisionNumber { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string PaymentInitiationTypeCode { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string CurrencyCode { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string IncotermCode { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string IncotermLocationCode { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string IsPartialShipmentAllowed { get; set; }

        /// <summary>
        /// 交地标识
        /// </summary>
        public string ShipDestDestinationKey { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string ShipDestAddressLine1 { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string ShipDestAddressLine2 { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string ShipDestCountryCode { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string BeneficiaryStatementAcknowledgementCode { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string IsPodRequired { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string PodCompletedByCode { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string IsPackingListRequired { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string MarkNumber { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string Mark { get; set; }
    }
}