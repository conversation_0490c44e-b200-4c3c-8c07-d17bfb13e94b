using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using MauiScanManager.Constants;
using MauiScanManager.Models;
using MauiScanManager.Services;
using System.Collections.ObjectModel;

namespace MauiScanManager.ViewModels
{
    public partial class ScanOrderViewModel : BaseOperationViewModel
    {
        private readonly IScanOrderService _scanOrderService;

        [ObservableProperty]
        private string _currentOperation;

        [ObservableProperty]
        private bool _canSave;

        public ObservableCollection<string> ScannedOrders { get; } = new();

        public ScanOrderViewModel(
            IScanService scanService,
            IScanOrderService scanOrderService,
            IDialogService dialogService) : base(scanService, dialogService)
        {
            _scanOrderService = scanOrderService;
        }

        public override void Initialize(Operation operation)
        {
            base.Initialize(operation);
            CurrentOperation = operation.Description;
            ScannedOrders.Clear();
            CanSave = false;
        }

        protected override void ProcessScanResult(string code, string type, byte[] codeSource)
        {
            if (!string.IsNullOrEmpty(code))
            {
                MainThread.BeginInvokeOnMainThread(async () =>
                {
                    if (!ScannedOrders.Contains(code))
                    {
                        ScannedOrders.Add(code);
                        CanSave = true;
                    }
                    else
                    {
                        await _dialogService.ShowInfoAsync("该订单已扫描");
                    }
                });
            }
        }

        [RelayCommand]
        private void DeleteOrder(string order)
        {
            if (ScannedOrders.Remove(order))
            {
                UpdateCanSave();
            }
        }

        private void UpdateCanSave()
        {
            CanSave = ScannedOrders.Any();
        }

        [RelayCommand]
        private async Task SaveAsync()
        {
            if (!ScannedOrders.Any())
            {
                await _dialogService.ShowWarningAsync("请先扫描订单");
                return;
            }

            try
            {
                var model = new ScanOrder
                {
                    SaveType = Operation.Code,
                    Orders = ScannedOrders.ToList()
                };

                var result = await _scanOrderService.SaveAsync(model);
                if (result.IsSuccess)
                {
                    await _dialogService.ShowSuccessAsync("保存成功");
                    ScannedOrders.Clear();
                    UpdateCanSave();
                }
                else
                {
                    await _dialogService.ShowErrorAsync(result.ErrorMessage);
                }
            }
            catch (Exception ex)
            {
                await _dialogService.ShowErrorAsync($"保存失败: {ex.Message}");
            }
        }
        
    }
} 