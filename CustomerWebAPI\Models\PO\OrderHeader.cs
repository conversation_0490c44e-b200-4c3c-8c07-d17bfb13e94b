﻿using System;
using Dapper.Contrib.Extensions;

namespace CustomerWebAPI.Models
{
    [Table("OrderHeader")]
    public class OrderHeader
    {
        [ExplicitKey]
        public Guid Iden { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string version { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string documentType { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string? messageId { get; set; }

        public string trackingId { get; set; }
    }
}