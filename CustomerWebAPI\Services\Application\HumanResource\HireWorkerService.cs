﻿using System;
using System.Threading.Tasks;
using CustomerWebAPI.Models;
using CustomerWebAPI.Common;
using CustomerWebAPI.Database;
using Microsoft.Extensions.Logging;
using SqlSugar;
using System.ComponentModel.DataAnnotations;
using System.Collections.Generic;
using System.Linq;
using FluentValidation;

namespace CustomerWebAPI.Services
{
    public partial class HireWorkerService : IHireWorkerService
    {
        private readonly DbContext _dbContext;
        private readonly ILogger<HireWorkerService> _logger;

        public HireWorkerService(
            DbContext dbContext,
            ILogger<HireWorkerService> logger)
        {
            _dbContext = dbContext;
            _logger = logger;
        }



        public async Task<ApiResponse<bool>> SaveScanMobilePhoneNo(HireWorkerScan hireWorkerScan)
        {
            try
            {
                var result = await _dbContext.ExecuteAsync(async db =>
                {
                    //var checkResult = new SugarParameter("@CheckResult", null, System.Data.DbType.String, System.Data.ParameterDirection.Output);
                    await db.Ado.UseStoredProcedure()
                        .ExecuteCommandAsync("InfCustomer.dbo.usp_InsertHireWorkerScan", new SugarParameter[]
                        {
                            new SugarParameter("@MobilePhoneNo", hireWorkerScan.MobilePhoneNo),
                            new SugarParameter("@Gender", hireWorkerScan.Gender),
                            new SugarParameter("@Age", hireWorkerScan.Age),
                            //checkResult
                        });
                    return true;
                });


                return ApiResponse<bool>.Ok(true, "");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "手机号保存失败");
                return ApiResponse<bool>.Fail(
                    "手机号保存失败", 
                    (int)ApiErrorCodes.DatabaseError);
            }
        }

        
    }

}