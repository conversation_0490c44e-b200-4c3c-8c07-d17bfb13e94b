﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using CustomerWebAPI.Helpers;
using CustomerWebAPI.Models;
using Dapper;
using Dapper.Contrib.Extensions;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;

namespace CustomerWebAPI.Services
{
    public class SalesService : ISalesService
    {
        private readonly IASNService _asnClient;
        private readonly string _connString;
        public string loginUserName;


        public SalesService(IConfiguration config, IASNService asnClient)
        {
            _connString = config.GetConnectionString("SalesDatabase");
            _asnClient = asnClient;
        }

        private Tuple<bool, string> IsCanEditPo(string editTrackingId)
        {
            try
            {
                var sql = "select * from InfCustomer..SalesPO where TrackingId = @TrackingId";
                using IDbConnection connection = new SqlConnection(_connString);
                var selResult = connection.Query<SalesPO>(sql, new
                {
                    TrackingId = editTrackingId
                }).FirstOrDefault();
                if (selResult == null)
                    return new Tuple<bool, string>(false, $"The TrackingID {editTrackingId} is not found!");

                if (selResult.IsCancelled)
                    return new Tuple<bool, string>(false, $"The TrackingID {editTrackingId} Already Cancelled at {selResult.ModifiedDate}!");
                if (selResult.IsSyncedToERP)
                    return new Tuple<bool, string>(false, $"The TrackingID {editTrackingId} Already Used!");


                return new Tuple<bool, string>(true, string.Empty);
            }
            catch (Exception e)
            {
                return new Tuple<bool, string>(false, e.Message);
            }
        }



        #region 实现接口

        public Tuple<bool, string> SaveTalPo(SalesPO originalPO)
        {
            try
            {
                var po = JsonConvert.DeserializeObject<TALPO>(originalPO.PoJsonText);

                var orderHeader = new OrderHeader();
                var orderDetailList = new List<OrderDetail>();
                var orderDetailLineList = new List<OrderDetailLine>();

                orderHeader.Iden = Guid.NewGuid();
                orderHeader.documentType = po.Order.header.documentType;
                orderHeader.version = po.Order.header.version;
                orderHeader.messageId = po.Order.header.messageId;
                orderHeader.trackingId = originalPO.TrackingId;


                foreach (var od in po.Order.orderDetail)
                {
                    var orderDetail = new OrderDetail
                    {
                        Iden = Guid.NewGuid(),
                        OrderHeaderId = orderHeader.Iden,
                        MessageFunctionCode = od.messageFunctionCode,
                        ContractNumber = od.orderIdentification.contractNumber,
                        PoNumber = od.orderIdentification.poNumber,
                        BuyerMemberId = od.orderIdentification.buyerMemberId,
                        SellerMemberId = od.orderIdentification.sellerMemberId,
                        OrderFunctionCode = od.orderFunctionCode,
                        ComplianceTemplateCode = od.complianceTemplateCode,
                        IssueDate = od.orderTerms.issueDate,
                        RevisionNumber = od.orderTerms.revisionNumber,
                        PaymentInitiationTypeCode = od.orderTerms.paymentInitiationTypeCode,
                        CurrencyCode = od.orderTerms.currencyCode,
                        IncotermCode = od.orderTerms.incotermCode,
                        IncotermLocationCode = od.orderTerms.incotermLocationCode,
                        IsPartialShipmentAllowed = od.orderTerms.isPartialShipmentAllowed,
                        ShipDestDestinationKey = od.orderTerms.shipmentDestination.destinationKey,
                        ShipDestAddressLine1 = od.orderTerms.shipmentDestination.address.addressLine1,
                        ShipDestAddressLine2 = od.orderTerms.shipmentDestination.address.addressLine2,
                        ShipDestCountryCode = od.orderTerms.shipmentDestination.address.countryCode,
                        BeneficiaryStatementAcknowledgementCode = od.orderTerms.beneficiaryStatementAcknowledgementCode,
                        IsPodRequired = od.orderTerms.isPodRequired,
                        PodCompletedByCode = od.orderTerms.podCompletedByCode,
                        IsPackingListRequired = od.orderTerms.isPackingListRequired,
                        MarkNumber = od.orderTerms.packageMarkDetail.markNumber,
                        Mark = od.orderTerms.packageMarkDetail.mark
                    };
                    orderDetailList.Add(orderDetail);

                    orderDetailLineList.AddRange(od.orderLineItem.Select(odLine => new OrderDetailLine
                    {
                        Iden = Guid.NewGuid(),
                        OrderDetailId = orderDetail.Iden,
                        ItemKey = odLine.itemKey,
                        ItemSequenceNumber = odLine.baseLineItem.itemSequenceNumber,
                        BuyerNumber = odLine.baseLineItem.buyerNumber,
                        ShortDescription = odLine.baseLineItem.shortDescription,
                        LongDescription = odLine.baseLineItem.longDescription,
                        SkuNumber = odLine.baseLineItem.skuNumber,
                        CountryOfOriginCode = odLine.baseLineItem.countryOfOriginCode,

                        //itemReference array 
                        Construction = odLine.baseLineItem.itemReference.FirstOrDefault(item => string.Equals(item.type, "Construction", StringComparison.CurrentCultureIgnoreCase))?.value,
                        YarnCount = odLine.baseLineItem.itemReference.FirstOrDefault(item => string.Equals(item.type, "Yarn Count", StringComparison.CurrentCultureIgnoreCase))?.value,
                        FabricWidth = odLine.baseLineItem.itemReference.FirstOrDefault(item => string.Equals(item.type, "Fabric Width", StringComparison.CurrentCultureIgnoreCase))?.value,
                        PackagingTerms = odLine.baseLineItem.itemReference.FirstOrDefault(item => string.Equals(item.type, "Packaging Terms", StringComparison.CurrentCultureIgnoreCase))?.value,
                        Quantity = (decimal) odLine.baseLineItem.quantity,
                        UnitOfMeasureCode = odLine.baseLineItem.unitOfMeasureCode,
                        PackMethodCode = odLine.baseLineItem.packMethodCode,
                        DestinationKey = odLine.baseLineItem.destinationKey,
                        EarliestDate = odLine.baseLineItem.earliestDate,
                        LatestDate = odLine.baseLineItem.latestDate,
                        IsInspectionRequired = odLine.baseLineItem.isInspectionRequired,
                        ShipmentMethodCode = odLine.baseLineItem.shipmentMethodCode,
                        UpperVariance = odLine.baseLineItem.itemVariance.upperVariance,
                        LowerVariance = odLine.baseLineItem.itemVariance.lowerVariance,
                        VarianceTypeCode = odLine.baseLineItem.itemVariance.varianceTypeCode,
                        PricePerUnit = (decimal) odLine.lineItemPrice.pricePerUnit
                    }));
                }

                using (IDbConnection connection = new SqlConnection(_connString))
                {
                    //var p = new DynamicParameters();
                    //p.Add("@OrderDetailId", "B8A1B482-357B-4DEA-949F-0E2B09738E59");
                    //p.Add("@")
                    connection.Open();
                    using (var transaction = connection.BeginTransaction()) //dapper transaction
                    {
                        connection.Insert(originalPO, transaction);
                        connection.Insert(orderHeader, transaction);
                        connection.Insert(orderDetailList, transaction);
                        connection.Insert(orderDetailLineList, transaction);
                        //connection.Execute("usp_DeleteOrder", new { OrderDetailIden = "B11F884A-67A9-43C0-A8BB-16A2E4EFFD5D", quantity = 2.99 }, transaction, 3000, CommandType.StoredProcedure);
                        transaction.Commit();
                    }
                }


                return new Tuple<bool, string>(true, string.Empty);
            }
            catch (Exception e)
            {
                return new Tuple<bool, string>(false, e.Message);
            }
        }

        public Tuple<bool, string> DeletePo(string delTrackingId)
        {
            try
            {
                var checkResult = IsCanEditPo(delTrackingId);
                if (checkResult.Item1 == false) throw new Exception(checkResult.Item2);

                var sql = "UPDATE SalesPO SET IsCancelled = 1,ModifiedDate = GETDATE() WHERE TrackingId = @TrackingId AND IsCancelled=0";
                using IDbConnection connection = new SqlConnection(_connString);
                connection.Execute(sql, new
                {
                    TrackingId = delTrackingId
                });
                return new Tuple<bool, string>(true, string.Empty);
            }
            catch (Exception e)
            {
                return new Tuple<bool, string>(false, e.Message);
            }
        }

        public string GetAsnJsonFromDb(string rootId)
        {
            List<ASNRoot> asnRootsList;
            List<ASNItems> asnItemsList;
            List<ASNHeader> asnHeaderList;
            List<ASNDetail> asnDetailList;
            List<ASNDetailParties> asnDetailPartiesList;
            List<ASNDetailOrders> asnDetailOrdersList;
            List<ASNDetailOrdersParties> asnDetailOrdersPartiesList;
            List<ASNDetailOrdersItems> asnDetailOrdersItemsList;
            List<ASNDetailOrdersItemsPackages> asnDetailOrdersItemsPackagesList;
            List<ASNDetailOrdersItemsPackagesWeights> asnDetailOrdersItemsPackagesWeightsList;
            List<ASNDetailOrdersItemsPackagesMeasurements> asnDetailOrdersItemsPackagesMeasurementsList;

            using (IDbConnection connection = new SqlConnection(_connString))
            {
                connection.Open();

                //var rootId = "DEAC1787-8D06-4CDC-8F1C-6F69A97F3483";

                const string sql = "usp_GetASNInfoByTableName";
                asnRootsList = connection.Query<ASNRoot>(sql, new
                {
                    AsnRootID = rootId,
                    TableName = "ASNRoot"
                }, commandType: CommandType.StoredProcedure).ToList();
                asnItemsList = connection.Query<ASNItems>(sql, new
                {
                    AsnRootID = rootId,
                    TableName = "ASNItems"
                }, commandType: CommandType.StoredProcedure).ToList();
                asnHeaderList = connection.Query<ASNHeader>(sql, new
                {
                    AsnRootID = rootId,
                    TableName = "ASNHeader"
                }, commandType: CommandType.StoredProcedure).ToList();
                asnDetailList = connection.Query<ASNDetail>(sql, new
                {
                    AsnRootID = rootId,
                    TableName = "ASNDetail"
                }, commandType: CommandType.StoredProcedure).ToList();
                asnDetailPartiesList = connection.Query<ASNDetailParties>(sql, new
                {
                    AsnRootID = rootId,
                    TableName = "ASNDetailParties"
                }, commandType: CommandType.StoredProcedure).ToList();
                asnDetailOrdersList = connection.Query<ASNDetailOrders>(sql, new
                {
                    AsnRootID = rootId,
                    TableName = "ASNDetailOrders"
                }, commandType: CommandType.StoredProcedure).ToList();
                asnDetailOrdersPartiesList = connection.Query<ASNDetailOrdersParties>(sql, new
                {
                    AsnRootID = rootId,
                    TableName = "ASNDetailOrdersParties"
                }, commandType: CommandType.StoredProcedure).ToList();
                asnDetailOrdersItemsList = connection.Query<ASNDetailOrdersItems>(sql, new
                {
                    AsnRootID = rootId,
                    TableName = "ASNDetailOrdersItems"
                }, commandType: CommandType.StoredProcedure).ToList();
                asnDetailOrdersItemsPackagesList = connection.Query<ASNDetailOrdersItemsPackages>(sql, new
                {
                    AsnRootID = rootId,
                    TableName = "ASNDetailOrdersItemsPackages"
                }, commandType: CommandType.StoredProcedure).ToList();
                asnDetailOrdersItemsPackagesWeightsList = connection.Query<ASNDetailOrdersItemsPackagesWeights>(sql, new
                {
                    AsnRootID = rootId,
                    TableName = "ASNDetailOrdersItemsPackagesWeights"
                }, commandType: CommandType.StoredProcedure).ToList();
                asnDetailOrdersItemsPackagesMeasurementsList = connection.Query<ASNDetailOrdersItemsPackagesMeasurements>(sql, new
                {
                    AsnRootID = rootId,
                    TableName = "ASNDetailOrdersItemsPackagesMeasurements"
                }, commandType: CommandType.StoredProcedure).ToList();
            }


            var rootASNs = new List<RootASN>();
            asnRootsList.ForEach(rt =>
            {
                var asnrtForAdd = new RootASN
                {
                    SenderId = rt.SenderId,
                    SenderIdentifierId = rt.SenderIdentifierId,
                    ReceiverId = rt.ReceiverId,
                    ReceiverIdentifierId = rt.ReceiverIdentifierId,
                    TrackNumber = Convert.ToInt32(rt.TrackNumber),
                    ProductionIndicator = rt.ProductionIndicator
                    //AdvancedShipNotes = advancedShipNotesItemsList
                };

                var advancedShipNotesItemsList = new List<AdvancedShipNotesItem>();


                //AdvancedShipNotesItems
                var filteredItemsList = asnItemsList.Where(x => x.ASNRootId == rt.Id).ToList();
                asnItemsList.ForEach(ai =>
                {
                    var asniForAdd = new AdvancedShipNotesItem();

                    //header
                    var filteredHeaderList = asnHeaderList.Where(x => x.ASNItemsId == ai.Id).ToList();
                    filteredHeaderList.ForEach(hd =>
                    {
                        var hdForAdd = new Models.Header
                        {
                            PurposeCode = hd.PurposeCode,
                            AdvancedShipNoteNumber = hd.AdvancedShipNoteNumber,
                            HierarchicalStructureCode = hd.HierarchicalStructureCode,
                            DocumentIndex = hd.DocumentIndex,
                            TransactionTypeCode = hd.TransactionTypeCode,
                            SentDate = hd.SentDate,
                            SentTime = hd.SentTime
                        };

                        asniForAdd.Header = hdForAdd;
                    });

                    //detail
                    var dtlForAdd = new Detail();
                    var filteredDetailList = asnDetailList.Where(x => x.ASNItemsId == ai.Id).ToList();
                    filteredDetailList.ForEach(dtl =>
                    {
                        var ordersItemsList = new List<OrdersItem>();

                        dtlForAdd.CarrierDetails.PackagingCode = dtl.PackagingCode;
                        dtlForAdd.CarrierDetails.LadingQuantity = dtl.LadingQuantity;
                        dtlForAdd.CarrierDetails.Weights.Add(new WeightsItem
                        {
                            Qualifier = dtl.WeightsQualifier,
                            Unit = dtl.WeightsUnit,
                            Value = Convert.ToDouble(dtl.WeightsValue)
                        });
                        dtlForAdd.CarrierDetails.TransportationTypeCode = dtl.TransportationTypeCode;
                        dtlForAdd.CarrierDetails.ContainerNumber = dtl.ContainerNumber;
                        dtlForAdd.DivisionCode = dtl.DivisionCode;
                        dtlForAdd.Transportation.BillNumber = dtl.BillNumber;
                        dtlForAdd.Transportation.VoyageNumber = dtl.VoyageNumber;
                        dtlForAdd.Transportation.VesselNumber = dtl.VesselNumber;
                        dtlForAdd.Transportation.ShipMode = dtl.ShipMode;
                        dtlForAdd.Dates.SailDate = dtl.SailDate;
                        dtlForAdd.Dates.DeliveryDate = dtl.DeliveryDate;
                        dtlForAdd.Dates.EstimatedArrivalDate = dtl.EstimatedArrivalDate;


                        //asnDetailPartiesList
                        var detailPartiesItemsList = new List<PartiesItem>();
                        var filteredDetailPartiesList = asnDetailPartiesList.Where(ord => ord.ASNDetailId == dtl.Id).ToList();
                        filteredDetailPartiesList.ForEach(dp =>
                        {
                            var dpForAdd = new PartiesItem();
                            dpForAdd.Identifier = dp.Identifier;
                            dpForAdd.Name = dp.Name;
                            dpForAdd.Code = dp.Code;
                            if (string.IsNullOrWhiteSpace(dp.AddressLine1) && string.IsNullOrWhiteSpace(dp.AddressLine2))
                            {
                                dpForAdd.Address = null;
                            }
                            else
                            {
                                dpForAdd.Address.AddressLine1 = dp.AddressLine1;
                                dpForAdd.Address.AddressLine2 = string.IsNullOrWhiteSpace(dp.AddressLine2) ? null : dp.AddressLine2;
                            }


                            detailPartiesItemsList.Add(dpForAdd);
                        });

                        //asnDetailOrdersList
                        var filteredOrdersList = asnDetailOrdersList.Where(ord => ord.ASNDetailId == dtl.Id).ToList();
                        filteredOrdersList.ForEach(od =>
                        {
                            var odForAdd = new OrdersItem
                            {
                                PurchaseOrderNumber = od.PurchaseOrderNumber,
                                PurchaseOrderReleaseNumber = od.PurchaseOrderReleaseNumber
                            };


                            ordersItemsList.Add(odForAdd);

                            //asnDetailOrdersPartiesList
                            var orderPartiesItemsList = new List<PartiesItem>();
                            var filteredOrdersPartiesList = asnDetailOrdersPartiesList.Where(op => op.ASNDetailOrdersId == od.Id).ToList();
                            filteredOrdersPartiesList.ForEach(op =>
                            {
                                var opForAdd = new PartiesItem();
                                opForAdd.Identifier = op.Identifier;
                                opForAdd.Name = op.Name;
                                opForAdd.Code = op.Code;
                                if (string.IsNullOrWhiteSpace(op.AddressLine1) && string.IsNullOrWhiteSpace(op.AddressLine2))
                                {
                                    opForAdd.Address = null;
                                }
                                else
                                {
                                    opForAdd.Address.AddressLine1 = op.AddressLine1;
                                    opForAdd.Address.AddressLine2 = string.IsNullOrWhiteSpace(op.AddressLine2) ? null : op.AddressLine2;
                                }

                                orderPartiesItemsList.Add(opForAdd);
                            });

                            //asnDetailOrdersItemsList
                            var ordersItemsItemList = new List<ItemsItem>();
                            var filteredOrdersItemsList = asnDetailOrdersItemsList.Where(oi => oi.ASNDetailOrdersId == od.Id).ToList();
                            filteredOrdersItemsList.ForEach(oi =>
                            {
                                var oiForAdd = new ItemsItem
                                {
                                    AssignedId = oi.AssignedId,
                                    PoLineNumber = oi.PoLineNumber,
                                    ItemNumber = oi.ItemNumber,
                                    ContractNumber = oi.ContractNumber,
                                    DyeLotSeries = oi.DyeLotSeries,
                                    SubLineNumber = oi.SubLineNumber,
                                    PackQuantity = oi.PackQuantity,
                                    PackCode = oi.PackCode
                                };
                                oiForAdd.Weights.Add(new WeightsItem
                                {
                                    Qualifier = oi.WeightsQualifier,
                                    Unit = oi.WeightsUnit,
                                    Value = Convert.ToDouble(oi.WeightsValue)
                                });
                                oiForAdd.DyeMatch = oi.DyeMatch;
                                oiForAdd.ColorDescription = oi.ColorDescription;
                                oiForAdd.FabricDescription = oi.FabricDescription;
                                oiForAdd.FreeOfChargeQuantity = Convert.ToDouble(oi.FreeOfChargeQuantity);


                                ordersItemsItemList.Add(oiForAdd);


                                //asnDetailOrdersItemsPackagesList
                                var packagesItemsList = new List<PackagesItem>();
                                var oipList = asnDetailOrdersItemsPackagesList.Where(pki => pki.ASNDetailOrdersItemsId == oi.Id).ToList();
                                oipList.ForEach(pk =>
                                {
                                    var weightsItemList = new List<WeightsItem>();
                                    var measurementsItemsList = new List<MeasurementsItem>();

                                    var pkitem = new PackagesItem
                                    {
                                        AssignedId = pk.AssignedId,
                                        Quantity = Convert.ToDouble(pk.Quantity),
                                        QuantityUnit = pk.QuantityUnit,
                                        FabricWidth = Convert.ToDouble(pk.FabricWidth),
                                        WidthUnit = pk.WidthUnit,
                                        QaStatus = pk.QaStatus,
                                        FabricLengthString = pk.FabricLengthString,
                                        UCC = pk.UCC,
                                        RollNumber = pk.RollNumber
                                    };


                                    //asnDetailOrdersItemsPackagesWeightsList                            
                                    var pkwTempList = asnDetailOrdersItemsPackagesWeightsList.Where(pkw => pkw.ASNDetailOrdersItemsPackagesId == pk.Id).ToList();
                                    pkwTempList.ForEach(wi =>
                                    {
                                        weightsItemList.Add(new WeightsItem
                                        {
                                            Qualifier = wi.Qualifier,
                                            Unit = wi.Unit,
                                            Value = Convert.ToDouble(wi.Value)
                                        });
                                    });

                                    //asnDetailOrdersItemsPackagesMeasurementsList
                                    var pkmsTempList = asnDetailOrdersItemsPackagesMeasurementsList.Where(pkms => pkms.ASNDetailOrdersItemsPackagesId == pk.Id).ToList();
                                    pkmsTempList.ForEach(pm =>
                                    {
                                        measurementsItemsList.Add(new MeasurementsItem
                                        {
                                            MeasurementUnit = pm.MeasurementUnit,
                                            Name = pm.Name,
                                            Value = Convert.ToDouble(pm.Value)
                                        });
                                    });

                                    pkitem.Weights = weightsItemList;
                                    pkitem.Measurements = measurementsItemsList;


                                    packagesItemsList.Add(pkitem);
                                });


                                oiForAdd.Packages.AddRange(packagesItemsList);
                            });


                            odForAdd.Parties.AddRange(orderPartiesItemsList);
                            odForAdd.Items.AddRange(ordersItemsItemList);
                        });
                        dtlForAdd.Parties.AddRange(detailPartiesItemsList);
                        dtlForAdd.Orders.AddRange(ordersItemsList);

                        asniForAdd.Detail = dtlForAdd;
                    });

                    advancedShipNotesItemsList.Add(asniForAdd);
                });

                asnrtForAdd.AdvancedShipNotes.AddRange(advancedShipNotesItemsList);

                rootASNs.Add(asnrtForAdd);
            });

            var jsonSetting = new JsonSerializerSettings
            {
                NullValueHandling = NullValueHandling.Ignore
            };
            var result = JsonConvert.SerializeObject(rootASNs.FirstOrDefault(), jsonSetting);

            if (!JsonHelper.ValidateAsnJson(result)) throw new Exception("Invalid JSON for ASN JSON Schema! 请使用JSON工具验证：https://jsonschemalint.com/#!/version/draft-07/markup/json");

            return result;
            ;
        }

        //public async Task<Tuple<bool, string, TrackingIdModel>> SendAsnToTalAsync(dynamic asnRootId)
        //{
            
            //try
            //{
            //    AsnRootIDModel asnRootIdModel = JsonConvert.DeserializeObject<AsnRootIDModel>(asnRootId.ToString());
            //    if (asnRootIdModel == null) throw new Exception("Parameter Json_asnRootId format not correct !");

            //    var seriJson = GetAsnJsonFromDb(asnRootIdModel.RootId);
            //    var trackingIdModel = await _asnClient.SendASN(seriJson);
            //    return new Tuple<bool, string, TrackingIdModel>(true, string.Empty, trackingIdModel);
            //}
            //catch (Exception e)
            //{
            //    var errorMsg = "SendASN failed!" + Environment.NewLine + e.Message;
            //    return new Tuple<bool, string, TrackingIdModel>(false, errorMsg, null);
            //    ;
            //}
        //}

        #endregion
    }
}