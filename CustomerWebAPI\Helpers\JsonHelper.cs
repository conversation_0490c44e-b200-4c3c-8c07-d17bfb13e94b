﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using CustomerWebAPI.Models;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json.Schema;

//using CustomerWebAPI.Model.ASNTest;

namespace CustomerWebAPI.Helpers
{
    public static class JsonHelper
    {
        // public static JToken ReadJSON(string jsonStr)
        // {
        //     return JObject.Parse(jsonStr) as JToken;
        // }

        public static void ReadTest()
        {
            //Json.NET反序列化
            // string json = @"{ 'Name':'C#','Age':'3000','ID':'1','Sex':'女'}";
            // RootPO descJsonStu = JsonConvert.DeserializeObject<RootPO>(json);//反序列化
            // Console.WriteLine(string.Format("反序列化： ID={0},Name={1},Sex={2},Sex={3}", descJsonStu.ID, descJsonStu.Name, descJsonStu.Age, descJsonStu.Sex));
            Console.ReadKey();
        }

        public static string ReadPOJSON(string jsonStr)
        {
            if (string.IsNullOrWhiteSpace(jsonStr))
            {
                TALPO po = JsonConvert.DeserializeObject<TALPO>(jsonStr); //反序列化

                string outputStr = "";
                foreach (var orderDetailItem in po.Order.orderDetail)
                {
                    foreach (var identificationItem in orderDetailItem.orderParties.seller.identification)
                    {
                        outputStr += identificationItem.value;
                    }
                }

                // var identificationValue= po.Order.orderDetail[0].orderParties.seller.identification[1].value.ToString();
                // Console.WriteLine(version);~
                return outputStr;


                // using (System.IO.StreamReader file = System.IO.File.OpenText(jsonfile))
                // {
                //     StringReader sr = new StringReader(jsonfile);
                //     using (JsonTextReader reader = new JsonTextReader(file))
                //     {
                //         JObject o = (JObject)JToken.ReadFrom(reader);
                //         var value = o[key].ToString();
                //         return value;
                //     }
                // }                
            }

            return null;
        }

        public static TALPO ReadJSON()
        {
            string jsonfile = @".\Data\PO.json"; //JSON文件路径
            if (File.Exists(jsonfile))
            {
                string jsonText = File.ReadAllText(jsonfile);
                TALPO po = JsonConvert.DeserializeObject<TALPO>(jsonText); //反序列化

                //string outputStr="";
                //foreach (var orderDetailItem in po.Order.orderDetail)
                //{
                //    foreach (var identificationItem in orderDetailItem.orderParties.seller.identification)
                //    {
                //        outputStr += identificationItem.value.ToString();
                //    }
                //}
                // var identificationValue= po.Order.orderDetail[0].orderParties.seller.identification[1].value.ToString();
                // Console.WriteLine(version);
                return po;


                // using (System.IO.StreamReader file = System.IO.File.OpenText(jsonfile))
                // {
                //     StringReader sr = new StringReader(jsonfile);
                //     using (JsonTextReader reader = new JsonTextReader(file))
                //     {
                //         JObject o = (JObject)JToken.ReadFrom(reader);
                //         var value = o[key].ToString();
                //         return value;
                //     }
                // }                
            }

            return null;
        }

        public static bool ValidateAsnJson(string json)
        {
            return IsValidJsonWithSchema(json, @".\Data\ASN.suppliers.schema.json");
        }


        public static bool IsValidJsonWithSchema(string json, string schemaFilePath)
        {
            var result = false;
            try
            {
                if (File.Exists(schemaFilePath))
                {
                    var schemaText = File.ReadAllText(schemaFilePath);

                    // load schema
                    JSchema schema = JSchema.Parse(schemaText);
                    JToken jsonforValid = JToken.Parse(json);

                    // validate json
                    IList<ValidationError> errors;
                    bool valid = jsonforValid.IsValid(schema, out errors);

                    // return error messages and line info to the browser
                    result = valid;
                }
                else
                {
                    throw new FileNotFoundException($"File Not Found : {schemaFilePath}");
                }
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }

            return result;
        }


        public static string ReadASNJSONFromLocal()
        {
            string result = string.Empty;
            //RootASN asn = new RootASN();
            //AdvancedShipNotesItem asnItem = new AdvancedShipNotesItem();
            //asnItem.Header = new Model.ASN.Header();
            //asnItem.Header.SentDate = DateTime.Now.ToString("yyyy-MM-dd");
            //asnItem.Header.SentTime = DateTime.Now.ToString("T");

            //asn.AdvancedShipNotes.Add(asnItem);

            //return JsonConvert.SerializeObject(asn).ToString();

            string jsonfile = @".\Data\ASN.json"; //JSON文件路径
            if (File.Exists(jsonfile))
            {
                string jsonText = File.ReadAllText(jsonfile);

                RootASN asn = JsonConvert.DeserializeObject<RootASN>(jsonText);

                var jsonSetting = new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                };
                result = JsonConvert.SerializeObject(asn, jsonSetting);
                //var fp = @".\Data\ASNTemp.json";
                //if (!File.Exists(fp))
                //{
                //    File.Create(fp);
                //}
                //File.WriteAllText(fp, ConvertJsonString(jsonSave));
                //string result = File.ReadAllText(fp);
                //string result= ConvertJsonString(jsonSave);
                //result = jsonSave;
                //return result;
            }

            return result;
        }

        public static string ReadASNJSON()
        {
            string result = string.Empty;
            //RootASN asn = new RootASN();
            //AdvancedShipNotesItem asnItem = new AdvancedShipNotesItem();
            //asnItem.Header = new Model.ASN.Header();
            //asnItem.Header.SentDate = DateTime.Now.ToString("yyyy-MM-dd");
            //asnItem.Header.SentTime = DateTime.Now.ToString("T");

            //asn.AdvancedShipNotes.Add(asnItem);

            //return JsonConvert.SerializeObject(asn).ToString();

            string jsonfile = @".\Data\ASN.json"; //JSON文件路径
            if (File.Exists(jsonfile))
            {
                string jsonText = File.ReadAllText(jsonfile);

                RootASN asn = JsonConvert.DeserializeObject<RootASN>(jsonText);

                var jsonSetting = new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                };
                result = JsonConvert.SerializeObject(asn, jsonSetting);
                //var fp = @".\Data\ASNTemp.json";
                //if (!File.Exists(fp))
                //{
                //    File.Create(fp);
                //}
                //File.WriteAllText(fp, ConvertJsonString(jsonSave));
                //string result = File.ReadAllText(fp);
                //string result= ConvertJsonString(jsonSave);
                //result = jsonSave;
                //return result;
            }


            return result;
        }


        public static string ReadASNJSON(bool isProduction)
        {
            string result = string.Empty;

            RootASN asn = new RootASN();
            asn.SenderIdentifierId = "ZZ";
            asn.SenderId = "SAINTYEAR";
            asn.ReceiverId = "TAP";
            asn.TrackNumber = 55555; //
            asn.ProductionIndicator = "Production";

            AdvancedShipNotesItem asni = new AdvancedShipNotesItem();
            asni.Header = new Models.Header();
            asni.Header.PurposeCode = "00";
            asni.Header.AdvancedShipNoteNumber = "8888888888"; //货运单号
            asni.Header.HierarchicalStructureCode = "0002";
            asni.Header.DocumentIndex = 1;
            asni.Header.TransactionTypeCode = "AS";
            asni.Header.SentDate = DateTime.Now.ToString("yyyy-MM-dd");
            asni.Header.SentTime = DateTime.Now.ToString("T");


            //asn.AdvancedShipNotes.Add(asnItem);

            //return JsonConvert.SerializeObject(asn).ToString();

            string jsonfile = @".\Data\ASN.json"; //JSON文件路径
            if (File.Exists(jsonfile))
            {
                string jsonText = File.ReadAllText(jsonfile);
                asn = JsonConvert.DeserializeObject<RootASN>(jsonText);

                var jsonSetting = new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                };
                result = JsonConvert.SerializeObject(asn, jsonSetting);
                //var fp = @".\Data\ASNTemp.json";
                //if (!File.Exists(fp))
                //{
                //    File.Create(fp);
                //}
                //File.WriteAllText(fp, ConvertJsonString(jsonSave));
                //string result = File.ReadAllText(fp);
                //string result= ConvertJsonString(jsonSave);
                //result = jsonSave;
                //return result;
            }

            return result;
        }


        private static string ConvertJsonString(string str)
        {
            //格式化json字符串
            JsonSerializer serializer = new JsonSerializer();
            TextReader tr = new StringReader(str);
            JsonTextReader jtr = new JsonTextReader(tr);
            object obj = serializer.Deserialize(jtr);
            if (obj != null)
            {
                StringWriter textWriter = new StringWriter();
                JsonTextWriter jsonWriter = new JsonTextWriter(textWriter)
                {
                    Formatting = Formatting.Indented,
                    //Indentation = 4,
                    //IndentChar = ' '
                };
                serializer.Serialize(jsonWriter, obj);
                return textWriter.ToString();
            }

            return str;
        }

        /// <summary>
        /// 将webapi中多出的双引号和转义字符(反斜杠),转换为json返回
        /// </summary>
        /// <param name="json"></param>
        /// <returns></returns>
        public static Task<string> GetJson(string json)
        {
            var resp = new HttpResponseMessage
            {
                Content = new StringContent(json, Encoding.UTF8, "application/json")
            };
            return resp.Content.ReadAsStringAsync();
        }
    }
}