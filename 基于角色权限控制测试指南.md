# 基于角色权限控制测试指南

## 测试准备

### 1. 数据库更新
执行更新后的 `数据库脚本_完整版.sql` 文件，该脚本将：
- 创建新的权限控制表
- 添加维修人员管理表
- 插入示例权限数据
- 创建设备技能要求数据

### 2. 测试账号
系统预置了以下测试账号：

| 用户名 | 密码 | 角色 | 所属部门 | 权限说明 |
|--------|------|------|----------|----------|
| admin | admin123 | 管理员 | 整理部 | 所有部门的全部权限 |
| operator | op123 | 操作员 | 整理部 | 只能报修整理部设备，可发送给维修部 |
| viewer | view123 | 访客 | 维修部 | 只有查看权限，无操作权限 |

### 3. 权限配置
- **管理员角色**：可以管理所有部门的设备（报修、接收、维修）
- **操作员角色**：只能报修整理部设备，可以将报修发送给维修部
- **访客角色**：无任何操作权限

## 测试步骤

### 测试1：管理员权限验证

1. **登录**：使用 `admin/admin123` 登录
2. **访问测试页面**：导航到 `/test-user-department`
3. **验证权限显示**：
   - 可报修部门：应显示所有部门（整理部、维修部、动力部、安全部）
   - 可接收报修部门：应显示所有部门
   - 可维修部门：应显示所有部门
   - 维修人员信息：应显示为高级维修人员

4. **访问报修页面**：导航到 `/create-repair-order`
5. **验证功能**：
   - 部门选择：应显示所有部门
   - 设备选择：应显示所有部门的设备
   - 维修部门：应显示所有部门
   - 提交按钮：应该可用

### 测试2：操作员权限验证

1. **登录**：使用 `operator/op123` 登录
2. **访问测试页面**：导航到 `/test-user-department`
3. **验证权限显示**：
   - 可报修部门：应只显示整理部
   - 可接收报修部门：应显示维修部
   - 可维修部门：应为空
   - 维修人员信息：应显示"您不是维修人员"

4. **访问报修页面**：导航到 `/create-repair-order`
5. **验证功能**：
   - 部门选择：应只显示整理部
   - 设备选择：应只显示整理部的设备
   - 维修部门：应显示维修部
   - 提交按钮：应该可用

### 测试3：访客权限验证

1. **登录**：使用 `viewer/view123` 登录
2. **访问测试页面**：导航到 `/test-user-department`
3. **验证权限显示**：
   - 可报修部门：应为空
   - 可接收报修部门：应为空
   - 可维修部门：应为空
   - 维修人员信息：应显示为中级维修人员

4. **访问报修页面**：导航到 `/create-repair-order`
5. **验证功能**：
   - 应显示"您没有设备报修权限"的警告
   - 部门选择：应被禁用
   - 设备选择：应为空
   - 提交按钮：应被禁用

### 测试4：维修人员分配功能

1. **使用管理员账号**：登录 `admin/admin123`
2. **创建报修单**：
   - 选择设备：选择一个定型机设备
   - 选择维修部门：选择维修部
   - 观察维修人员列表：应显示可用的维修人员及其技能信息
   - 紧急程度：测试不同紧急程度对人员推荐的影响

3. **验证智能分配**：
   - 不指定维修人员直接提交
   - 系统应自动分配合适的维修人员
   - 查看分配结果通知

### 测试5：权限边界验证

1. **跨部门设备测试**：
   - 使用操作员账号尝试报修其他部门的设备
   - 系统应阻止此操作

2. **权限提升测试**：
   - 尝试通过URL直接访问无权限的功能
   - 系统应正确拦截

3. **数据一致性测试**：
   - 验证权限数据与实际功能的一致性
   - 确保前端显示与后端权限验证一致

## 预期结果

### 正常情况
- 不同角色用户看到不同的权限范围
- 设备列表根据权限正确过滤
- 维修人员智能分配正常工作
- 权限验证在前端和后端都生效

### 异常处理
- 无权限用户收到明确的提示信息
- 系统优雅处理权限不足的情况
- 错误信息清晰易懂

## 故障排除

### 常见问题

1. **权限数据未生效**
   - 检查数据库脚本是否完整执行
   - 验证角色部门权限数据是否正确插入

2. **维修人员不显示**
   - 检查维修人员数据是否正确插入
   - 验证部门关联是否正确

3. **设备列表为空**
   - 检查设备数据和部门关联
   - 验证用户权限配置

### 调试工具
- 使用 `/test-user-department` 页面查看详细权限信息
- 检查浏览器控制台的错误信息
- 查看数据库中的权限配置数据

## 性能测试

### 测试场景
1. **大量设备数据**：测试权限过滤的性能
2. **多用户并发**：测试权限查询的并发性能
3. **复杂权限配置**：测试复杂权限规则的查询效率

### 性能指标
- 权限查询响应时间 < 100ms
- 设备列表加载时间 < 500ms
- 维修人员查询时间 < 200ms

## 安全测试

### 测试项目
1. **权限绕过**：尝试绕过前端权限控制
2. **数据泄露**：验证用户只能看到授权的数据
3. **权限提升**：测试是否可能获得未授权的权限

### 安全要求
- 所有权限检查在后端进行验证
- 敏感数据不会泄露给无权限用户
- 权限变更有适当的审计记录

## 测试报告模板

### 测试结果记录
```
测试日期：
测试人员：
测试环境：

测试项目1：管理员权限验证
- 预期结果：
- 实际结果：
- 测试状态：通过/失败
- 备注：

测试项目2：操作员权限验证
- 预期结果：
- 实际结果：
- 测试状态：通过/失败
- 备注：

...

总体评估：
问题列表：
改进建议：
```
