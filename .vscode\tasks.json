{"version": "2.0.0", "tasks": [{"label": "build", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/CoreHub.sln", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary;ForceNoAlign"], "problemMatcher": "$msCompile"}, {"label": "build-web", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/CoreHub.Web/CoreHub.Web.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary;ForceNoAlign"], "problemMatcher": "$msCompile"}, {"label": "build-maui", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/CoreHub.Maui/CoreHub.csproj", "-f", "net8.0-windows10.0.19041.0", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary;ForceNoAlign"], "problemMatcher": "$msCompile"}, {"label": "publish", "command": "dotnet", "type": "process", "args": ["publish", "${workspaceFolder}/CoreHub.sln", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary;ForceNoAlign"], "problemMatcher": "$msCompile"}, {"label": "watch", "command": "dotnet", "type": "process", "args": ["watch", "run", "--project", "${workspaceFolder}/CoreHub.sln"], "problemMatcher": "$msCompile"}]}