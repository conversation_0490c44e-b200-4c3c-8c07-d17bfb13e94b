using System;
using System.Collections.Generic;

namespace CustomerWebAPI.Models
{
    public class HealthReport
    {
        public string Status { get; set; }
        public DateTime Timestamp { get; set; }
        public string Environment { get; set; }
        public string Error { get; set; }
        public List<ComponentHealth> Components { get; set; }
    }

    public class ComponentHealth
    {
        public string Name { get; set; }
        public string Status { get; set; }
        public long ResponseTime { get; set; }
        public DateTime LastChecked { get; set; }
        public string Error { get; set; }
        public Dictionary<string, object> Data { get; set; }
    }
} 