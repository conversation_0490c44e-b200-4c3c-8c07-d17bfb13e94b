# 菜单管理系统使用指南

## 概述

本系统提供了完整的菜单管理功能，支持从数据库动态读取和管理菜单项。菜单系统支持层级结构、权限控制和动态配置。

## 使用步骤

### 1. 数据库初始化

**首次使用必须先初始化数据库！**

1. 启动应用程序
2. 访问 `/database-setup` 页面
3. 点击 **"一键完整初始化"** 按钮
4. 等待初始化完成

#### 初始化包含的内容：
- 创建所有数据库表（Users、Roles、Permissions、MenuItems等）
- 创建默认用户：
  - `admin` / `admin123` （系统管理员）
  - `operator` / `op123` （设备操作员）
  - `viewer` / `view123` （访客用户）
- 创建默认角色和权限
- 创建默认菜单结构

### 2. 菜单管理

初始化完成后，可以通过 `/menu-management` 页面管理菜单：

#### 主要功能：
- **查看菜单**：以树形结构显示所有菜单
- **新增菜单**：创建新的菜单项或分组
- **编辑菜单**：修改菜单属性
- **删除菜单**：删除非系统菜单
- **初始化默认菜单**：重新创建默认菜单结构

#### 菜单属性说明：
- **菜单编码**：唯一标识符，用于程序识别
- **菜单名称**：显示在导航栏的名称
- **路由地址**：对应的页面路径
- **图标类名**：Bootstrap Icons 类名
- **父菜单**：设置父级菜单（支持多级嵌套）
- **排序号**：菜单显示顺序
- **菜单类型**：
  - 菜单项：可点击的页面链接
  - 分组标题：用于分组显示的标题
- **权限代码**：访问菜单所需的权限
- **启用菜单**：是否在导航栏显示
- **公开菜单**：未登录用户是否可见
- **系统菜单**：系统菜单不可删除

### 3. 权限控制

菜单系统集成了权限控制：

#### 菜单显示规则：
1. **公开菜单**：所有用户可见（如首页）
2. **需要权限的菜单**：检查用户是否具有对应权限
3. **无权限要求的菜单**：登录用户可见
4. **未登录用户**：只能看到公开菜单

#### 权限管理：
- 通过用户管理页面分配角色
- 通过角色管理配置权限
- 支持用户直接分配权限

### 4. 菜单结构示例

```
├── 首页 (公开菜单)
├── 计数器 (需要权限: Counter.View)
├── 设备扫描 (需要权限: DeviceScanner.View)
├── 摄像头测试 (需要权限: CameraTest.View)
├── 天气预报 (需要权限: Weather.View)
├── 设备报修 (需要权限: DeviceRepair.View)
├── 系统管理 (分组标题)
│   ├── 用户管理 (需要权限: UserManagement.View)
│   ├── 角色管理 (需要权限: RoleManagement.View)
│   ├── 权限管理 (需要权限: PermissionManagement.View)
│   └── 权限分配 (需要权限: UserManagement.AssignRoles)
├── 权限控制示例
└── 认证调试
```

## 技术特性

### 1. 动态菜单
- 菜单数据存储在数据库中
- 支持实时更新，无需重启应用
- 自动响应用户登录/注销状态变化

### 2. 层级结构
- 支持多级菜单嵌套
- 自动缩进显示层级关系
- 分组标题用于菜单分类

### 3. 权限控制
- 基于角色的权限管理（RBAC）
- 支持菜单级权限控制
- 公开菜单和受保护菜单分离

### 4. 灵活配置
- 支持菜单排序
- 支持图标配置
- 支持启用/禁用状态

## 故障排除

### 常见问题：

1. **菜单不显示**
   - 检查数据库是否已初始化
   - 确认用户是否有对应权限
   - 检查菜单是否启用

2. **数据库连接失败**
   - 检查连接字符串配置
   - 确认SQL Server服务正在运行
   - 检查数据库是否存在

3. **权限问题**
   - 确认用户已分配正确角色
   - 检查权限代码是否正确
   - 验证角色权限配置

### 重置菜单：
如果菜单数据出现问题，可以：
1. 访问菜单管理页面
2. 点击"初始化默认菜单"按钮
3. 确认重新初始化

## 开发说明

### 添加新菜单：
1. 在菜单管理页面添加菜单项
2. 设置正确的路由地址
3. 配置所需权限（如果需要）
4. 创建对应的页面组件

### 权限代码命名规范：
```
模块名.操作名
例如：
- UserManagement.View
- UserManagement.Create
- UserManagement.Edit
- UserManagement.Delete
```

### 图标使用：
使用 Bootstrap Icons，格式为：`bi bi-图标名`
常用图标：
- `bi bi-house-door`：首页
- `bi bi-people`：用户管理
- `bi bi-gear`：设置
- `bi bi-list`：列表

## 更新记录

### v1.1 - 完整数据库脚本集成
- ✅ 将菜单相关脚本集成到完整数据库脚本中
- ✅ 添加菜单管理、权限管理、数据库设置相关权限
- ✅ 权限管理页面支持从菜单项中选择路由地址
- ✅ 移除 NavMenu 中的临时链接，改为数据库驱动
- ✅ 完整的菜单数据自动初始化

### 使用步骤（更新后）
1. 执行 `数据库脚本_完整版.sql` 初始化数据库
2. 或使用 `/database-setup` 页面进行数据库初始化
3. 登录系统，菜单将自动从数据库加载并显示
4. 使用管理员账号 `admin/admin123` 访问系统管理功能

### 新增功能
- **权限管理增强**：支持从菜单项中选择路由地址，确保权限与菜单的一致性
- **完整数据库脚本**：包含菜单、权限、用户等所有数据的一键初始化
- **纯数据库驱动**：无需手动配置，所有菜单都从数据库动态加载 