-- =============================================
-- 权限管理系统存储过程 (SQL Server 2012 兼容版)
-- 兼容版本: SQL Server 2012 及以上
-- =============================================

-- 检查并删除已存在的存储过程
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_ValidateUser]') AND type in (N'P', N'PC'))
    DROP PROCEDURE [dbo].[sp_ValidateUser];
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_GetUserInfo]') AND type in (N'P', N'PC'))
    DROP PROCEDURE [dbo].[sp_GetUserInfo];
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_GetAllUsers]') AND type in (N'P', N'PC'))
    DROP PROCEDURE [dbo].[sp_GetAllUsers];
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_GetUserPermissions]') AND type in (N'P', N'PC'))
    DROP PROCEDURE [dbo].[sp_GetUserPermissions];
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_CreateUser]') AND type in (N'P', N'PC'))
    DROP PROCEDURE [dbo].[sp_CreateUser];
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_AssignUserRole]') AND type in (N'P', N'PC'))
    DROP PROCEDURE [dbo].[sp_AssignUserRole];
GO

-- 用户验证存储过程
-- =============================================
-- sp_ValidateUser
-- 验证用户登录，包含密码验证、账户状态检查、登录日志记录
-- =============================================
CREATE PROCEDURE [dbo].[sp_ValidateUser]
    @Username NVARCHAR(50),
    @Password NVARCHAR(255),
    @ClientIP NVARCHAR(50) = NULL,
    @IsSuccess BIT OUTPUT,
    @ErrorMessage NVARCHAR(500) OUTPUT,
    @UserId INT OUTPUT,
    @DisplayName NVARCHAR(100) OUTPUT,
    @Email NVARCHAR(100) OUTPUT,
    @Role NVARCHAR(100) OUTPUT
AS
BEGIN
    SET NOCOUNT ON;
    
    -- 初始化输出参数
    SET @IsSuccess = 0;
    SET @ErrorMessage = '';
    SET @UserId = NULL;
    SET @DisplayName = '';
    SET @Email = '';
    SET @Role = '';
    
    BEGIN TRY
        BEGIN TRANSACTION;
        
        -- 输入参数验证
        IF @Username IS NULL OR @Username = '' OR @Password IS NULL OR @Password = ''
        BEGIN
            SET @ErrorMessage = '用户名和密码不能为空';
            ROLLBACK TRANSACTION;
            RETURN;
        END
        
        -- 查询用户信息
        DECLARE @UserInfo TABLE (
            Id INT,
            Username NVARCHAR(50),
            PasswordHash NVARCHAR(255),
            DisplayName NVARCHAR(100),
            Email NVARCHAR(100),
            IsEnabled BIT,
            IsLocked BIT,
            LockReason NVARCHAR(500),
            LoginFailureCount INT
        );
        
        INSERT INTO @UserInfo
        SELECT Id, Username, PasswordHash, DisplayName, Email, IsEnabled, IsLocked, LockReason, LoginFailureCount
        FROM Users
        WHERE Username = @Username;
        
        -- 检查用户是否存在
        IF NOT EXISTS(SELECT 1 FROM @UserInfo)
        BEGIN
            SET @ErrorMessage = '用户名或密码错误';
            
            -- 记录登录失败日志
            INSERT INTO LoginLogs (Username, ClientIP, IsSuccess, FailureReason, LoginTime)
            VALUES (@Username, @ClientIP, 0, '用户不存在', GETDATE());
            
            ROLLBACK TRANSACTION;
            RETURN;
        END
        
        -- 获取用户信息
        DECLARE @StoredUserId INT, @StoredPasswordHash NVARCHAR(255), @StoredDisplayName NVARCHAR(100);
        DECLARE @StoredEmail NVARCHAR(100), @StoredIsEnabled BIT, @StoredIsLocked BIT;
        DECLARE @StoredLockReason NVARCHAR(500), @StoredLoginFailureCount INT;
        
        SELECT @StoredUserId = Id, @StoredPasswordHash = PasswordHash, @StoredDisplayName = DisplayName,
               @StoredEmail = Email, @StoredIsEnabled = IsEnabled, @StoredIsLocked = IsLocked,
               @StoredLockReason = LockReason, @StoredLoginFailureCount = LoginFailureCount
        FROM @UserInfo;
        
        -- 检查账户状态
        IF @StoredIsEnabled = 0
        BEGIN
            SET @ErrorMessage = '账户已被禁用，请联系管理员';
            
            INSERT INTO LoginLogs (Username, ClientIP, IsSuccess, FailureReason, LoginTime)
            VALUES (@Username, @ClientIP, 0, '账户已禁用', GETDATE());
            
            ROLLBACK TRANSACTION;
            RETURN;
        END
        
        IF @StoredIsLocked = 1
        BEGIN
            SET @ErrorMessage = '账户已被锁定：' + ISNULL(@StoredLockReason, '');
            
            INSERT INTO LoginLogs (Username, ClientIP, IsSuccess, FailureReason, LoginTime)
            VALUES (@Username, @ClientIP, 0, '账户已锁定', GETDATE());
            
            ROLLBACK TRANSACTION;
            RETURN;
        END
        
        -- 验证密码（明文比较，暂不加密）
        DECLARE @IsPasswordValid BIT = 0;
        
        -- 直接进行明文密码比较
        IF @StoredPasswordHash = @Password
        BEGIN
            SET @IsPasswordValid = 1;
        END
        ELSE
        BEGIN
            SET @IsPasswordValid = 0;
        END
        
        IF @IsPasswordValid = 0
        BEGIN
            -- 增加登录失败次数
            UPDATE Users 
            SET LoginFailureCount = LoginFailureCount + 1,
                UpdatedAt = GETDATE()
            WHERE Id = @StoredUserId;
            
            -- 检查是否需要锁定账户
            IF @StoredLoginFailureCount + 1 >= 5
            BEGIN
                UPDATE Users
                SET IsLocked = 1,
                    LockedAt = GETDATE(),
                    LockReason = '连续登录失败' + CAST(@StoredLoginFailureCount + 1 AS NVARCHAR(10)) + '次，账户已自动锁定'
                WHERE Id = @StoredUserId;
            END
            
            SET @ErrorMessage = '用户名或密码错误';
            
            INSERT INTO LoginLogs (Username, ClientIP, IsSuccess, FailureReason, LoginTime)
            VALUES (@Username, @ClientIP, 0, '密码错误', GETDATE());
            
            ROLLBACK TRANSACTION;
            RETURN;
        END
        
        -- 登录成功，更新用户信息
        UPDATE Users
        SET LoginFailureCount = 0,
            LastLoginTime = GETDATE(),
            LastLoginIp = @ClientIP,
            UpdatedAt = GETDATE()
        WHERE Id = @StoredUserId;
        
        -- 获取用户角色
        DECLARE @UserRole NVARCHAR(100);
        SELECT TOP 1 @UserRole = r.Name
        FROM UserRoles ur
        INNER JOIN Roles r ON ur.RoleId = r.Id
        WHERE ur.UserId = @StoredUserId 
          AND ur.IsEnabled = 1 
          AND r.IsEnabled = 1
          AND (ur.ExpiresAt IS NULL OR ur.ExpiresAt > GETDATE())
        ORDER BY r.SortOrder;
        
        -- 设置输出参数
        SET @IsSuccess = 1;
        SET @UserId = @StoredUserId;
        SET @DisplayName = @StoredDisplayName;
        SET @Email = @StoredEmail;
        SET @Role = ISNULL(@UserRole, '未分配角色');
        
        -- 记录登录成功日志
        INSERT INTO LoginLogs (Username, ClientIP, IsSuccess, FailureReason, LoginTime)
        VALUES (@Username, @ClientIP, 1, NULL, GETDATE());
        
        COMMIT TRANSACTION;
        
    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION;
        
        SET @ErrorMessage = '验证过程中发生错误：' + ERROR_MESSAGE();
        
        -- 记录错误日志
        INSERT INTO LoginLogs (Username, ClientIP, IsSuccess, FailureReason, LoginTime)
        VALUES (@Username, @ClientIP, 0, @ErrorMessage, GETDATE());
    END CATCH
END;
GO

-- =============================================
-- sp_GetUserInfo
-- 获取用户基本信息
-- =============================================
CREATE PROCEDURE [dbo].[sp_GetUserInfo]
    @Username NVARCHAR(50)
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        u.Id AS UserId,
        u.Username,
        u.DisplayName,
        u.Email,
        ISNULL(r.Name, '未分配角色') AS Role
    FROM Users u
    LEFT JOIN UserRoles ur ON u.Id = ur.UserId AND ur.IsEnabled = 1 AND (ur.ExpiresAt IS NULL OR ur.ExpiresAt > GETDATE())
    LEFT JOIN Roles r ON ur.RoleId = r.Id AND r.IsEnabled = 1
    WHERE u.Username = @Username AND u.IsEnabled = 1;
END;
GO

-- =============================================
-- sp_GetAllUsers
-- 获取所有启用的用户信息
-- =============================================
CREATE PROCEDURE [dbo].[sp_GetAllUsers]
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        u.Id AS UserId,
        u.Username,
        u.DisplayName,
        u.Email,
        ISNULL(r.Name, '未分配角色') AS Role
    FROM Users u
    LEFT JOIN UserRoles ur ON u.Id = ur.UserId AND ur.IsEnabled = 1 AND (ur.ExpiresAt IS NULL OR ur.ExpiresAt > GETDATE())
    LEFT JOIN Roles r ON ur.RoleId = r.Id AND r.IsEnabled = 1
    WHERE u.IsEnabled = 1
    ORDER BY u.CreatedAt;
END;
GO

-- =============================================
-- sp_GetUserPermissions
-- 获取用户的所有权限（角色权限 + 直接权限 - 拒绝权限）
-- =============================================
CREATE PROCEDURE [dbo].[sp_GetUserPermissions]
    @UserId INT
AS
BEGIN
    SET NOCOUNT ON;
    
    -- 临时表存储最终权限
    DECLARE @FinalPermissions TABLE (
        PermissionCode NVARCHAR(100)
    );
    
    -- 1. 通过角色获取权限
    INSERT INTO @FinalPermissions (PermissionCode)
    SELECT DISTINCT p.Code
    FROM UserRoles ur
    INNER JOIN Roles r ON ur.RoleId = r.Id
    INNER JOIN RolePermissions rp ON r.Id = rp.RoleId
    INNER JOIN Permissions p ON rp.PermissionId = p.Id
    WHERE ur.UserId = @UserId
      AND ur.IsEnabled = 1
      AND r.IsEnabled = 1
      AND rp.IsEnabled = 1
      AND p.IsEnabled = 1
      AND (ur.ExpiresAt IS NULL OR ur.ExpiresAt > GETDATE());
    
    -- 2. 添加用户直接授权的权限
    INSERT INTO @FinalPermissions (PermissionCode)
    SELECT DISTINCT p.Code
    FROM UserPermissions up
    INNER JOIN Permissions p ON up.PermissionId = p.Id
    WHERE up.UserId = @UserId
      AND up.IsEnabled = 1
      AND up.IsGranted = 1
      AND p.IsEnabled = 1
      AND (up.ExpiresAt IS NULL OR up.ExpiresAt > GETDATE())
      AND p.Code NOT IN (SELECT PermissionCode FROM @FinalPermissions);
    
    -- 3. 移除用户直接拒绝的权限
    DELETE FROM @FinalPermissions
    WHERE PermissionCode IN (
        SELECT p.Code
        FROM UserPermissions up
        INNER JOIN Permissions p ON up.PermissionId = p.Id
        WHERE up.UserId = @UserId
          AND up.IsEnabled = 1
          AND up.IsGranted = 0
          AND p.IsEnabled = 1
          AND (up.ExpiresAt IS NULL OR up.ExpiresAt > GETDATE())
    );
    
    -- 返回最终权限列表
    SELECT PermissionCode
    FROM @FinalPermissions
    ORDER BY PermissionCode;
END;
GO

-- =============================================
-- sp_CreateUser
-- 创建新用户
-- =============================================
CREATE PROCEDURE [dbo].[sp_CreateUser]
    @Username NVARCHAR(50),
    @PasswordHash NVARCHAR(255),
    @DisplayName NVARCHAR(100),
    @Email NVARCHAR(100) = NULL,
    @Phone NVARCHAR(20) = NULL,
    @CreatedBy INT = NULL,
    @Remark NVARCHAR(1000) = NULL,
    @IsSuccess BIT OUTPUT,
    @ErrorMessage NVARCHAR(500) OUTPUT,
    @NewUserId INT OUTPUT
AS
BEGIN
    SET NOCOUNT ON;
    
    SET @IsSuccess = 0;
    SET @ErrorMessage = '';
    SET @NewUserId = NULL;
    
    BEGIN TRY
        BEGIN TRANSACTION;
        
        -- 检查用户名是否已存在
        IF EXISTS(SELECT 1 FROM Users WHERE Username = @Username)
        BEGIN
            SET @ErrorMessage = '用户名已存在';
            ROLLBACK TRANSACTION;
            RETURN;
        END
        
        -- 检查邮箱是否已存在（如果提供了邮箱）
        IF @Email IS NOT NULL AND @Email != '' AND EXISTS(SELECT 1 FROM Users WHERE Email = @Email)
        BEGIN
            SET @ErrorMessage = '邮箱已被使用';
            ROLLBACK TRANSACTION;
            RETURN;
        END
        
        -- 插入新用户
        INSERT INTO Users (Username, PasswordHash, DisplayName, Email, Phone, IsEnabled, CreatedAt, CreatedBy, Remark)
        VALUES (@Username, @PasswordHash, @DisplayName, @Email, @Phone, 1, GETDATE(), @CreatedBy, @Remark);
        
        SET @NewUserId = SCOPE_IDENTITY();
        SET @IsSuccess = 1;
        
        COMMIT TRANSACTION;
        
    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION;
        SET @ErrorMessage = '创建用户失败：' + ERROR_MESSAGE();
    END CATCH
END;
GO

-- =============================================
-- sp_AssignUserRole
-- 为用户分配角色
-- =============================================
CREATE PROCEDURE [dbo].[sp_AssignUserRole]
    @UserId INT,
    @RoleId INT,
    @AssignedBy INT = NULL,
    @ExpiresAt DATETIME = NULL,
    @Remark NVARCHAR(500) = NULL,
    @IsSuccess BIT OUTPUT,
    @ErrorMessage NVARCHAR(500) OUTPUT
AS
BEGIN
    SET NOCOUNT ON;
    
    SET @IsSuccess = 0;
    SET @ErrorMessage = '';
    
    BEGIN TRY
        BEGIN TRANSACTION;
        
        -- 检查用户是否存在
        IF NOT EXISTS(SELECT 1 FROM Users WHERE Id = @UserId AND IsEnabled = 1)
        BEGIN
            SET @ErrorMessage = '用户不存在或已禁用';
            ROLLBACK TRANSACTION;
            RETURN;
        END
        
        -- 检查角色是否存在
        IF NOT EXISTS(SELECT 1 FROM Roles WHERE Id = @RoleId AND IsEnabled = 1)
        BEGIN
            SET @ErrorMessage = '角色不存在或已禁用';
            ROLLBACK TRANSACTION;
            RETURN;
        END
        
        -- 检查是否已分配该角色
        IF EXISTS(SELECT 1 FROM UserRoles WHERE UserId = @UserId AND RoleId = @RoleId AND IsEnabled = 1)
        BEGIN
            SET @ErrorMessage = '用户已拥有该角色';
            ROLLBACK TRANSACTION;
            RETURN;
        END
        
        -- 分配角色
        INSERT INTO UserRoles (UserId, RoleId, AssignedAt, AssignedBy, ExpiresAt, Remark)
        VALUES (@UserId, @RoleId, GETDATE(), @AssignedBy, @ExpiresAt, @Remark);
        
        SET @IsSuccess = 1;
        
        COMMIT TRANSACTION;
        
    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION;
        SET @ErrorMessage = '分配角色失败：' + ERROR_MESSAGE();
    END CATCH
END;
GO

-- =============================================
-- 创建登录日志表（如果不存在）
-- =============================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='LoginLogs' AND xtype='U')
BEGIN
    CREATE TABLE LoginLogs (
        Id INT IDENTITY(1,1) PRIMARY KEY,
        Username NVARCHAR(50) NOT NULL,
        ClientIP NVARCHAR(50),
        IsSuccess BIT NOT NULL,
        FailureReason NVARCHAR(500),
        LoginTime DATETIME NOT NULL DEFAULT GETDATE()
    );
END;
GO

-- =============================================
-- 创建索引（提高查询性能）- SQL Server 2012 兼容版本
-- =============================================

-- Users表索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Users_Username_IsEnabled' AND object_id = OBJECT_ID('Users'))
    CREATE INDEX IX_Users_Username_IsEnabled ON Users (Username, IsEnabled);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Users_Email' AND object_id = OBJECT_ID('Users'))
    CREATE INDEX IX_Users_Email ON Users (Email);

-- UserRoles表索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_UserRoles_UserId_IsEnabled' AND object_id = OBJECT_ID('UserRoles'))
    CREATE INDEX IX_UserRoles_UserId_IsEnabled ON UserRoles (UserId, IsEnabled);

-- RolePermissions表索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_RolePermissions_RoleId_IsEnabled' AND object_id = OBJECT_ID('RolePermissions'))
    CREATE INDEX IX_RolePermissions_RoleId_IsEnabled ON RolePermissions (RoleId, IsEnabled);

-- UserPermissions表索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_UserPermissions_UserId_IsEnabled' AND object_id = OBJECT_ID('UserPermissions'))
    CREATE INDEX IX_UserPermissions_UserId_IsEnabled ON UserPermissions (UserId, IsEnabled);

-- Permissions表索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Permissions_Code_IsEnabled' AND object_id = OBJECT_ID('Permissions'))
    CREATE INDEX IX_Permissions_Code_IsEnabled ON Permissions (Code, IsEnabled);

-- LoginLogs表索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_LoginLogs_Username_LoginTime' AND object_id = OBJECT_ID('LoginLogs'))
    CREATE INDEX IX_LoginLogs_Username_LoginTime ON LoginLogs (Username, LoginTime);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_LoginLogs_IsSuccess_LoginTime' AND object_id = OBJECT_ID('LoginLogs'))
    CREATE INDEX IX_LoginLogs_IsSuccess_LoginTime ON LoginLogs (IsSuccess, LoginTime);

PRINT 'SQL Server 2012 兼容版存储过程和索引创建完成！';
PRINT '版本信息: 兼容 SQL Server 2012 及以上版本';

-- =============================================
-- 附加的SQL Server 2012优化建议
-- =============================================
PRINT '提示：SQL Server 2012 优化建议：';
PRINT '1. 定期更新统计信息: UPDATE STATISTICS Users, Roles, Permissions';
PRINT '2. 定期重建索引: ALTER INDEX ALL ON Users REBUILD';
PRINT '3. 监控查询性能: 使用 SQL Server Profiler 或扩展事件'; 