using MauiScanManager.Models;

namespace MauiScanManager.Services;

public interface ISizingService
{
    Task<ServiceResult<SizingMachineUpInfo>> GetMachineUpInfoAsync(SizingMachine sizingMachine);
    Task<ServiceResult<string>> UpMachineAsync(SizingUpMachine sizingUpMachine);
    Task<ServiceResult<string>> DownMachineAsync(SizingUpMachine sizingUpMachine);
    Task<ServiceResult<string>> CancelUpMachineAsync(SizingCancelUpMachine sizingCancelUpMachine);
}
