using MauiScanManager.Constants;
using Microsoft.Extensions.Logging;
using MauiScanManager.Models;

namespace MauiScanManager.Services;

public class ChemicalService : IChemicalService
{
    private readonly IApiService _apiService;
    private readonly IPlatformLoadingService _loadingService;
    private readonly ILogger<ChemicalService> _logger;

    public ChemicalService(
        IApiService apiService,
        IPlatformLoadingService loadingService,
        ILogger<ChemicalService> logger)
    {
        _apiService = apiService;
        _loadingService = loadingService;
        _logger = logger;
    }

    public async Task<ServiceResult<string>> CheckChemicalChangeAsync(ChemicalCheck model)
    {
        _loadingService.ShowNativeLoading("正在校验染料...");
        try
        {
            var response = await _apiService.PostAsync<bool>(
                ApiEndpoints.Chemical.CheckChange, 
                model);

            return response.Success
                ? ServiceResult<string>.Success("校验成功 - 染料箱号匹配")
                : ServiceResult<string>.Failure(response.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "校验染料更换失败");
            return ServiceResult<string>.Failure(ex.Message);
        }
        finally
        {
            _loadingService.HideNativeLoading();
        }
    }

    public async Task<ServiceResult<ChemicalBoxCheckResult>> CheckChemicalBoxConsistencyAsync(ChemicalBatchCheck model)
    {
        _loadingService.ShowNativeLoading("正在校验染料箱号...");
        try
        {
            var response = await _apiService.PostAsync<ChemicalBoxCheckResult>(
                ApiEndpoints.Chemical.CheckBoxConsistency,
                model);

            return response.Success
                ? ServiceResult<ChemicalBoxCheckResult>.Success(response.Data)
                : ServiceResult<ChemicalBoxCheckResult>.Failure(response.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "校验染料箱号失败");
            return ServiceResult<ChemicalBoxCheckResult>.Failure(ex.Message);
        }
        finally
        {
            _loadingService.HideNativeLoading();
        }
    }

    public async Task<int> GetScannedBoxCountAsync(string batchNo, string boxNo)
    {
        try
        {
            var response = await _apiService.GetAsync<int>(
                $"{ApiEndpoints.Chemical.GetScannedBoxCount}?batchNo={batchNo}&boxNo={boxNo}");

            return response.Success ? response.Data : 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取已扫描箱数失败");
            return 0;
        }
    }
} 