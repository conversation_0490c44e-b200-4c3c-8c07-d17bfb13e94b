using System;
using System.Data;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Linq;
using SqlSugar;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Logging;
using CustomerWebAPI.Common;
using System.Diagnostics;
using CustomerWebAPI.Models;
using System.Threading;

namespace CustomerWebAPI.Database
{
    public class DbContext
    {
        private readonly SqlSugarScope _db;
        private readonly ILogger<DbContext> _logger;
        private readonly DbConfig _config;

        public DbContext(IOptions<DbConfig> config, ILogger<DbContext> logger)
        {
            _config = config.Value;
            _logger = logger;

            var connections = new List<ConnectionConfig>
            {
                CreateConfig("prod", _config.ProdDatabase),
                //CreateConfig("public", _config.PublicDatabase)
            };

            _db = new SqlSugarScope(connections);
        }

        private ConnectionConfig CreateConfig(string configId, string baseConnectionString)
        {
            var connectionString = GetConnectionString(baseConnectionString);
            
            return new ConnectionConfig
            {
                ConfigId = configId,
                ConnectionString = connectionString,
                DbType = SqlSugar.DbType.SqlServer,
                IsAutoCloseConnection = true,
                InitKeyType = InitKeyType.Attribute,
                AopEvents = CreateAopEvents(configId)
            };
        }

        private string GetConnectionString(string baseConnectionString)
        {
            if (!baseConnectionString.Contains("Connect Timeout=", StringComparison.OrdinalIgnoreCase))
            {
                baseConnectionString += $";Connect Timeout={_config.ConnectionTimeout}";
            }
            if (!baseConnectionString.Contains("Min Pool Size=", StringComparison.OrdinalIgnoreCase))
            {
                baseConnectionString += $";Min Pool Size={_config.PoolMinSize}";
            }
            if (!baseConnectionString.Contains("Max Pool Size=", StringComparison.OrdinalIgnoreCase))
            {
                baseConnectionString += $";Max Pool Size={_config.PoolMaxSize}";
            }
            return baseConnectionString;
        }

        private AopEvents CreateAopEvents(string configId)
        {
            return new AopEvents
            {
                OnLogExecuting = (sql, parameters) =>
                {
                    if (_config.EnableLogging)
                    {
                        if (sql.Contains("OpenAlways", StringComparison.OrdinalIgnoreCase))
                        {
                            _logger.LogInformation($"数据库[{configId}]准备打开连接...");
                        }
                        else if (sql.Contains("sp_reset_connection", StringComparison.OrdinalIgnoreCase))
                        {
                            _logger.LogInformation($"数据库[{configId}]连接重置");
                        }
                        else if (sql.StartsWith("SELECT @@VERSION", StringComparison.OrdinalIgnoreCase))
                        {
                            _logger.LogInformation($"数据库[{configId}]连接已打开");
                        }

                        var paramInfo = "";
                        if (parameters != null && parameters.Length > 0)
                        {
                            paramInfo = string.Join(", ", parameters.Select(p => 
                                $"{p.ParameterName}={p.Value}"));
                        }

                        _logger.LogInformation($"SQL执行开始: {sql} 参数: {paramInfo}");
                    }
                },
                OnLogExecuted = (sql, parameters) =>
                {
                    if (_config.EnableLogging)
                    {
                        
                        var paramInfo = "";
                        if (parameters != null && parameters.Length > 0)
                        {
                            paramInfo = string.Join(", ", parameters.Select(p => 
                                $"{p.ParameterName}={p.Value}"));
                        }

                        _logger.LogInformation($"SQL执行完成: {sql} 参数: {paramInfo} 耗时: {_db.Ado.SqlExecutionTime}ms");

                    }
                },
                OnError = (ex) =>
                {
                    if (ex.Message.Contains("timeout", StringComparison.OrdinalIgnoreCase))
                    {
                        _logger.LogError(ex, $"数据库[{configId}]SQL执行超时");
                    }
                    else if (ex.Message.Contains("connection", StringComparison.OrdinalIgnoreCase))
                    {
                        _logger.LogError(ex, $"数据库[{configId}]连接超时");
                    }
                    else
                    {
                        _logger.LogError(ex, $"数据库[{configId}]SQL执行异常: {ex.Message}");
                    }
                }
            };
        }

        public SimpleClient<T> GetSimpleClient<T>() where T : class, new()
        {
            return _db.GetSimpleClient<T>();
        }

        public ISqlSugarClient GetDb(string configId = "prod")
        {
            return _db.GetConnection(configId);
        }

        public async Task<T> ExecuteAsync<T>(Func<ISqlSugarClient, Task<T>> action, string configId = "prod")
        {
            return await RetryPolicy.ExecuteWithRetryAsync(async () =>
            {
                var db = GetDb(configId);
                using var tran = db.Ado.UseTran();
                try 
                {
                    var result = await action(db);
                    tran.CommitTran();
                    return result;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"数据库操作失败 - ConfigId: {configId}");
                    tran.RollbackTran();
                    throw;
                }
            }, _logger, _config.MaxRetries, _config.RetryDelayMs);
        }

        public async Task<DatabaseHealthStatus> GetDetailedHealthStatusAsync(string configId = "prod")
        {
            try
            {
                var stopwatch = Stopwatch.StartNew();
                
                var db = GetDb(configId);
                using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(_config.ConnectionTimeout));
                var isValid = await Task.Run(() => db.Ado.IsValidConnection(), cts.Token);
                
                stopwatch.Stop();
                
                return new DatabaseHealthStatus
                {
                    IsHealthy = isValid,
                    ResponseTimeMs = stopwatch.ElapsedMilliseconds,
                    ConfigId = configId,
                    LastChecked = DateTime.Now
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"数据库健康检查失败 - ConfigId: {configId}");
                return new DatabaseHealthStatus
                {
                    IsHealthy = false,
                    Error = "数据库连接失败",
                    ConfigId = configId,
                    LastChecked = DateTime.Now
                };
            }
        }

        public async Task<Dictionary<string, int>> GetConnectionPoolStatsAsync()
        {
            try
            {
                var db = GetDb();
                var sql = @"
                    SELECT 
                        COUNT(*) as total_connections,
                        SUM(CASE WHEN s.status = 'running' THEN 1 ELSE 0 END) as active_connections,
                        SUM(CASE WHEN s.status = 'sleeping' THEN 1 ELSE 0 END) as idle_connections
                    FROM sys.dm_exec_sessions s 
                    INNER JOIN sys.dm_exec_connections c ON s.session_id = c.session_id
                    WHERE s.session_id > 50";

                var result = await db.Ado.SqlQueryAsync<ConnectionPoolStats>(sql);
                
                if (result == null || !result.Any())
                {
                    return new Dictionary<string, int>
                    {
                        { "总连接数", 0 },
                        { "活动连接数", 0 },
                        { "空闲连接数", 0 }
                    };
                }

                var stats = new Dictionary<string, int>
                {
                    { "总连接数", result[0].total_connections },
                    { "活动连接数", result[0].active_connections },
                    { "空闲连接数", result[0].idle_connections }
                };

                _logger.LogInformation($"连接池状态: {string.Join(", ", stats.Select(x => $"{x.Key}={x.Value}"))}");
                return stats;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取连接池统计信息异常");
                return new Dictionary<string, int>();
            }
        }

        public async Task<bool> CheckConnectionAsync()
        {
            try
            {
                var db = GetDb();
                // 使用 IsValidConnection() 检查连接是否有效
                var isValid = await Task.Run(() => db.Ado.IsValidConnection());
                
                if (isValid)
                {
                    _logger.LogInformation("数据库连接正常");
                }
                else
                {
                    _logger.LogWarning("数据库连接异常");
                }
                
                return isValid;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "数据库连接检查失败");
                return false;
            }
        }
    }

    public class ConnectionPoolStats
    {
        public int total_connections { get; set; }
        public int active_connections { get; set; }
        public int idle_connections { get; set; }
    }
} 