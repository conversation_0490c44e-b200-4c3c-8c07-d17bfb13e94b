using CustomerWebAPI.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Http;
using System.Threading.Tasks;
using CustomerWebAPI.Services;

namespace CustomerWebAPI.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class YarnCarLocationController : ControllerBase
    {
        private readonly IYarnCarLocationService _service;
        private readonly ILogger<YarnCarLocationController> _logger;

        public YarnCarLocationController(IYarnCarLocationService service, ILogger<YarnCarLocationController> logger)
        {
            _service = service;
            _logger = logger;
        }

        [HttpPost("save-location")]
        [ProducesResponseType(typeof(ApiResponse<string>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ApiResponse<string>), StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<ApiResponse<string>>> SaveBatch([FromBody] YarnCarLocationBatchDto dto)
        {
            if (string.IsNullOrWhiteSpace(dto.LocationNo) || string.IsNullOrWhiteSpace(dto.CarNos))
                return BadRequest(ApiResponse<string>.Fail("参数无效"));

            var response = await _service.SaveBatchAsync(dto.LocationNo, dto.CarNos);
            return response.Success ? Ok(response) : BadRequest(response);
        }
    }
} 