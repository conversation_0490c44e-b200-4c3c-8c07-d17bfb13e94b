-- =============================================
-- 零件申请管理存储过程
-- 用于外部仓库系统集成和零件发放管理
-- =============================================

USE [CoreHub]
GO

-- =============================================
-- 存储过程1：获取待领用零件信息
-- 存储过程名：sp_GetPendingPartRequests
-- 功能：查询所有状态为"申请中"(Status=1)的零件申请记录
-- =============================================

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_GetPendingPartRequests]') AND type in (N'P', N'PC'))
    DROP PROCEDURE [dbo].[sp_GetPendingPartRequests]
GO

CREATE PROCEDURE [dbo].[sp_GetPendingPartRequests]
    @DepartmentId INT = NULL,           -- 可选：按部门过滤
    @UrgencyLevel INT = NULL,           -- 可选：按紧急程度过滤
    @StartDate DATETIME2 = NULL,        -- 可选：申请开始时间
    @EndDate DATETIME2 = NULL,          -- 可选：申请结束时间
    @PageIndex INT = 1,                 -- 分页：页码（从1开始）
    @PageSize INT = 50                  -- 分页：每页记录数
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        -- 参数验证
        IF @PageIndex < 1 SET @PageIndex = 1;
        IF @PageSize < 1 OR @PageSize > 1000 SET @PageSize = 50;
        
        -- 计算分页偏移量
        DECLARE @Offset INT = (@PageIndex - 1) * @PageSize;
        
        -- 主查询
        SELECT 
            -- 零件申请基本信息
            pr.Id AS PartRequestId,
            pr.PartName,
            pr.Specification,
            pr.RequestedQuantity,
            pr.Unit,
            pr.Reason AS ReplacementReason,
            pr.Remark,
            pr.RequestedAt,
            
            -- 申请人信息
            pr.RequestedBy AS RequesterId,
            ru.DisplayName AS RequesterName,
            ru.Email AS RequesterEmail,
            ru.Phone AS RequesterPhone,
            
            -- 报修单信息
            ro.Id AS RepairOrderId,
            ro.OrderNumber AS RepairOrderNumber,
            ro.FaultDescription,
            ro.UrgencyLevel,
            CASE ro.UrgencyLevel
                WHEN 1 THEN N'紧急'
                WHEN 2 THEN N'高'
                WHEN 3 THEN N'中'
                WHEN 4 THEN N'低'
                ELSE N'未知'
            END AS UrgencyLevelName,
            ro.Status AS RepairOrderStatus,
            CASE ro.Status
                WHEN 1 THEN N'待处理'
                WHEN 2 THEN N'处理中'
                WHEN 3 THEN N'已完成'
                WHEN 4 THEN N'已作废'
                WHEN 5 THEN N'已关闭'
                WHEN 6 THEN N'待确认'
                ELSE N'未知'
            END AS RepairOrderStatusName,
            ro.ReportedAt,
            
            -- 设备信息
            e.Id AS EquipmentId,
            e.Code AS EquipmentCode,
            e.Name AS EquipmentName,
            e.ModelId AS EquipmentModelId,
            em.Name AS EquipmentModelName,
            el.Name AS EquipmentLocationName,
            
            -- 部门信息
            ed.Id AS EquipmentDepartmentId,
            ed.Name AS EquipmentDepartmentName,
            md.Id AS MaintenanceDepartmentId,
            md.Name AS MaintenanceDepartmentName,
            
            -- 报修人信息
            rpu.DisplayName AS ReporterName,
            rpu.Email AS ReporterEmail,
            
            -- 维修人员信息
            au.DisplayName AS AssignedTechnicianName,
            
            -- 统计信息
            COUNT(*) OVER() AS TotalRecords
            
        FROM [dbo].[RepairOrderPartRequests] pr
            INNER JOIN [dbo].[RepairOrders] ro ON pr.RepairOrderId = ro.Id
            INNER JOIN [dbo].[Equipment] e ON ro.EquipmentId = e.Id
            INNER JOIN [dbo].[Users] ru ON pr.RequestedBy = ru.Id
            INNER JOIN [dbo].[Users] rpu ON ro.ReporterId = rpu.Id
            LEFT JOIN [dbo].[Users] au ON ro.AssignedTo = au.Id
            LEFT JOIN [dbo].[Departments] ed ON e.DepartmentId = ed.Id
            LEFT JOIN [dbo].[Departments] md ON ro.MaintenanceDepartmentId = md.Id
            LEFT JOIN [dbo].[EquipmentModels] em ON e.ModelId = em.Id
            LEFT JOIN [dbo].[Locations] el ON e.LocationId = el.Id
        WHERE 
            pr.Status = 1  -- 申请中状态
            AND (@DepartmentId IS NULL OR ed.Id = @DepartmentId OR md.Id = @DepartmentId)
            AND (@UrgencyLevel IS NULL OR ro.UrgencyLevel = @UrgencyLevel)
            AND (@StartDate IS NULL OR pr.RequestedAt >= @StartDate)
            AND (@EndDate IS NULL OR pr.RequestedAt <= @EndDate)
        ORDER BY 
            ro.UrgencyLevel ASC,  -- 紧急程度优先
            pr.RequestedAt ASC    -- 申请时间升序
        OFFSET @Offset ROWS
        FETCH NEXT @PageSize ROWS ONLY;
        
    END TRY
    BEGIN CATCH
        -- 错误处理
        DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE();
        DECLARE @ErrorSeverity INT = ERROR_SEVERITY();
        DECLARE @ErrorState INT = ERROR_STATE();
        
        RAISERROR(@ErrorMessage, @ErrorSeverity, @ErrorState);
    END CATCH
END
GO

-- =============================================
-- 存储过程2：外部系统写入实际领用信息
-- 存储过程名：sp_UpdatePartIssueInfo
-- 功能：供外部仓库系统调用，更新零件的实际发放信息
-- =============================================

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_UpdatePartIssueInfo]') AND type in (N'P', N'PC'))
    DROP PROCEDURE [dbo].[sp_UpdatePartIssueInfo]
GO

CREATE PROCEDURE [dbo].[sp_UpdatePartIssueInfo]
    @PartRequestId INT,                     -- 零件申请ID（必填）
    @ActualQuantity INT,                    -- 实际发放数量（必填）
    @WarehouseOrderNumber NVARCHAR(50),     -- 仓库单号（必填）
    @UnitPrice DECIMAL(18,2) = NULL,       -- 单价（可选）
    @TotalCost DECIMAL(18,2) = NULL,       -- 总成本（可选）
    @ExternalSystemId NVARCHAR(50) = NULL, -- 外部系统ID（可选）
    @ActualPartName NVARCHAR(100) = NULL,  -- 实际发放零件名称（可选）
    @ActualSpecification NVARCHAR(200) = NULL, -- 实际发放规格（可选）
    @ExternalPartNumber NVARCHAR(50) = NULL    -- 外部系统零件编号（可选）
AS
BEGIN
    SET NOCOUNT ON;
    
    -- 声明返回变量
    DECLARE @IsSuccess BIT = 0;
    DECLARE @ErrorMessage NVARCHAR(500) = '';
    DECLARE @UpdatedRecords INT = 0;
    
    BEGIN TRY
        BEGIN TRANSACTION;
        
        -- 参数验证
        IF @PartRequestId IS NULL OR @PartRequestId <= 0
        BEGIN
            SET @ErrorMessage = '零件申请ID无效';
            GOTO ErrorExit;
        END
        
        IF @ActualQuantity IS NULL OR @ActualQuantity <= 0
        BEGIN
            SET @ErrorMessage = '实际发放数量必须大于0';
            GOTO ErrorExit;
        END
        
        IF @WarehouseOrderNumber IS NULL OR LTRIM(RTRIM(@WarehouseOrderNumber)) = ''
        BEGIN
            SET @ErrorMessage = '仓库单号不能为空';
            GOTO ErrorExit;
        END
        
        
        -- 检查零件申请是否存在
        DECLARE @CurrentStatus INT;
        DECLARE @RequestedQuantity INT;
        
        SELECT @CurrentStatus = Status, @RequestedQuantity = RequestedQuantity
        FROM [dbo].[RepairOrderPartRequests]
        WHERE Id = @PartRequestId;
        
        IF @@ROWCOUNT = 0
        BEGIN
            SET @ErrorMessage = '零件申请记录不存在';
            GOTO ErrorExit;
        END
        
        -- 检查状态是否为申请中
        IF @CurrentStatus != 1
        BEGIN
            SET @ErrorMessage = '只有申请中状态的记录才能发放，当前状态：' + 
                CASE @CurrentStatus
                    WHEN 2 THEN '已领用'
                    WHEN 3 THEN '已安装'
                    WHEN 4 THEN '已取消'
                    ELSE '未知'
                END;
            GOTO ErrorExit;
        END

        
        -- 如果没有提供总成本，根据单价和实际数量计算
        IF @TotalCost IS NULL AND @UnitPrice IS NOT NULL
        BEGIN
            SET @TotalCost = @UnitPrice * @ActualQuantity;
        END
        
        -- 更新零件申请记录
        UPDATE [dbo].[RepairOrderPartRequests]
        SET 
            Status = 2,  -- 已领用
            ActualQuantity = @ActualQuantity,
            ActualPartName = COALESCE(@ActualPartName, PartName),
            ActualSpecification = COALESCE(@ActualSpecification, Specification),
            IssuedAt = GETDATE(),
            WarehouseOrderNumber = @WarehouseOrderNumber,
            UnitPrice = COALESCE(@UnitPrice, UnitPrice),
            TotalCost = COALESCE(@TotalCost, TotalCost),
            ExternalPartNumber = @ExternalPartNumber,
            ExternalRequisitionDetailId = @ExternalSystemId,
            UpdatedAt = GETDATE()
        WHERE Id = @PartRequestId;
        
        SET @UpdatedRecords = @@ROWCOUNT;
        
        IF @UpdatedRecords = 0
        BEGIN
            SET @ErrorMessage = '更新零件申请记录失败';
            GOTO ErrorExit;
        END
        
        COMMIT TRANSACTION;
        
        -- 成功返回
        SET @IsSuccess = 1;
        SET @ErrorMessage = '零件发放信息更新成功';
        
        -- 返回更新后的记录信息
        SELECT 
            @IsSuccess AS IsSuccess,
            @ErrorMessage AS Message,
            @UpdatedRecords AS UpdatedRecords,
            pr.Id AS PartRequestId,
            pr.PartName,
            pr.ActualQuantity,
            pr.WarehouseOrderNumber,
            pr.IssuedAt,
            ro.OrderNumber AS RepairOrderNumber
        FROM [dbo].[RepairOrderPartRequests] pr
            INNER JOIN [dbo].[RepairOrders] ro ON pr.RepairOrderId = ro.Id
        WHERE pr.Id = @PartRequestId;
        
        RETURN 0; -- 成功
        
    ErrorExit:
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
            
        -- 错误返回
        SELECT 
            @IsSuccess AS IsSuccess,
            @ErrorMessage AS Message,
            0 AS UpdatedRecords,
            @PartRequestId AS PartRequestId,
            NULL AS PartName,
            NULL AS ActualQuantity,
            NULL AS WarehouseOrderNumber,
            NULL AS IssuedAt,
            NULL AS RepairOrderNumber;
            
        RETURN 1; -- 失败
        
    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
            
        SET @ErrorMessage = '系统错误：' + ERROR_MESSAGE();
        
        -- 系统错误返回
        SELECT 
            0 AS IsSuccess,
            @ErrorMessage AS Message,
            0 AS UpdatedRecords,
            @PartRequestId AS PartRequestId,
            NULL AS PartName,
            NULL AS ActualQuantity,
            NULL AS WarehouseOrderNumber,
            NULL AS IssuedAt,
            NULL AS RepairOrderNumber;
            
        RETURN 2; -- 系统错误
    END CATCH
END
GO

-- =============================================
-- 创建存储过程使用示例和说明
-- =============================================

PRINT '零件申请存储过程创建完成！'
PRINT ''
PRINT '使用示例：'
PRINT ''
PRINT '1. 获取待领用零件信息：'
PRINT '   EXEC sp_GetPendingPartRequests;'
PRINT '   EXEC sp_GetPendingPartRequests @UrgencyLevel = 1; -- 只查询紧急的'
PRINT '   EXEC sp_GetPendingPartRequests @PageIndex = 1, @PageSize = 20; -- 分页查询'
PRINT ''
PRINT '2. 更新零件发放信息：'
PRINT '   EXEC sp_UpdatePartIssueInfo'
PRINT '       @PartRequestId = 1,'
PRINT '       @ActualQuantity = 2,'
PRINT '       @WarehouseOrderNumber = ''WH20250110001'','
PRINT '       @UnitPrice = 150.00,'
PRINT '       @TotalCost = 300.00,'
PRINT '       @ExternalSystemId = ''EXT001'','
PRINT '       @ActualPartName = ''实际发放的零件名称'','
PRINT '       @ExternalPartNumber = ''P001'';'
PRINT ''
PRINT '注意事项：'
PRINT '- sp_GetPendingPartRequests 支持分页查询，默认每页50条记录'
PRINT '- sp_UpdatePartIssueInfo 包含完整的参数验证和错误处理'
PRINT '- 两个存储过程都包含事务处理，确保数据一致性'
PRINT '- 外部系统集成时建议使用 ExternalSystemId 和 ExternalPartNumber 进行追踪'

GO
