using CommunityToolkit.Mvvm.ComponentModel;
using MauiScanManager.Models;
using MauiScanManager.Services;

namespace MauiScanManager.ViewModels;

public partial class ChemicalCheckViewModel : BaseOperationViewModel
{
    private readonly IAudioService _audioService;
    private readonly IChemicalService _chemicalService;
    private readonly IPrintService _printService;

    // 定义扫描状态
    private enum ScanState
    {
        WaitingForChemicalCode,  // 等待扫描染料代号
        WaitingForBoxNo,         // 等待扫描箱号
        Completed                // 扫描完成
    }

    [ObservableProperty]
    private string chemicalCode = string.Empty;

    [ObservableProperty]
    private string chemicalBoxNo = string.Empty;

    [ObservableProperty]
    private string scanPrompt = string.Empty;

    private ScanState _currentState = ScanState.WaitingForChemicalCode;

    public ChemicalCheckViewModel(
        IScanService scanService,
        IDialogService dialogService,
        IAudioService audioService,
        IChemicalService chemicalService,
        IPrintService printService) 
        : base(scanService, dialogService)
    {
        _audioService = audioService;
        _chemicalService = chemicalService;
        _printService = printService;
    }

    public override async void Initialize(Operation operation)
    {
        base.Initialize(operation);
        ResetScanState();
        await _audioService.PlayCheckPrompt();
    }

    protected override void ProcessScanResult(string code, string type, byte[] codeSource)
    {
        if (string.IsNullOrEmpty(code)) return;

        MainThread.BeginInvokeOnMainThread(async () =>
        {
            try
            {
                await ProcessScanStateAsync(code);
            }
            catch (Exception ex)
            {
                await _dialogService.ShowErrorAsync($"处理扫描结果失败：{ex.Message}");
            }
        });
    }

    private async Task ProcessScanStateAsync(string code)
    {
        switch (_currentState)
        {
            case ScanState.WaitingForChemicalCode:
                ChemicalCode = code;
                _currentState = ScanState.WaitingForBoxNo;
                UpdateScanPrompt();
                await _dialogService.ShowInfoAsync("请扫描染料箱号");
                break;

            case ScanState.WaitingForBoxNo:
                ChemicalBoxNo = code;
                _currentState = ScanState.Completed;
                await CheckChemical();
                break;

            case ScanState.Completed:
                // 可以在这里处理完成后的额外逻辑
                break;
        }
    }

    private async Task CheckChemical()
    {
        try
        {
            var chemicalCheck = new ChemicalCheck
            {
                ChemicalCode = ChemicalCode,
                ChemicalBoxNo = ChemicalBoxNo
            };
            
            var result = await _chemicalService.CheckChemicalChangeAsync(chemicalCheck);

            if (result.IsSuccess)
            {
                await _dialogService.ShowSuccessAsync(result.Data);
                await _audioService.PlaySuccessSound();
                
                // 检查成功后打印凭证
                await PrintChemicalCheckReceipt(result.Data);
            }
            else
            {
                await _dialogService.ShowErrorAsync(result.ErrorMessage);
                await _audioService.PlayFailureSound();
            }
        }
        catch (Exception ex)
        {
            await _dialogService.ShowErrorAsync($"请求失败：{ex.Message}");
            await _audioService.PlayFailureSound();
        }
        finally
        {
            ResetScanState();
        }
    }

    private void ResetScanState()
    {
        ChemicalCode = string.Empty;
        ChemicalBoxNo = string.Empty;
        _currentState = ScanState.WaitingForChemicalCode;
        UpdateScanPrompt();
    }

    private void UpdateScanPrompt()
    {
        ScanPrompt = _currentState switch
        {
            ScanState.WaitingForChemicalCode => "请扫描染料代号",
            ScanState.WaitingForBoxNo => "请扫描染料箱号",
            ScanState.Completed => string.Empty,
            _ => string.Empty
        };
    }

    /// <summary>
    /// 打印染料检查凭证
    /// </summary>
    /// <param name="checkResult">检查结果信息</param>
    private async Task PrintChemicalCheckReceipt(string checkResult)
    {
        try
        {
            // 方式1：使用模板打印（推荐）
            var template = CreateChemicalCheckTemplate(checkResult);
            var printResult = await _printService.PrintTemplateAndWaitAsync(template, true); // 启用黑标模式
            
            if (printResult.Result == PrintResult.Success)
            {
                await _dialogService.ShowInfoAsync("凭证打印成功");
            }
            else
            {
                await _dialogService.ShowWarningAsync($"凭证打印失败：{printResult.Message}");
            }
        }
        catch (Exception ex)
        {
            await _dialogService.ShowWarningAsync($"打印凭证时出错：{ex.Message}");
        }
    }

    /// <summary>
    /// 创建染料检查凭证模板
    /// </summary>
    /// <param name="checkResult">检查结果</param>
    /// <returns>打印模板</returns>
    private PrintTemplate CreateChemicalCheckTemplate(string checkResult)
    {
        return new PrintTemplate
        {
            Title = "染料检查凭证",
            Items = new List<PrintTemplate.PrintItem>
            {
                // 标题
                new PrintTemplate.PrintItem
                {
                    Type = PrintTemplate.PrintItemType.Text,
                    Content = "染料检查凭证\n",
                    Config = new PrintConfig
                    {
                        Align = PrintConfig.TextAlign.Center,
                        Size = PrintConfig.FontSize.Large,
                        Bold = true
                    }
                },
                
                // 分隔线
                new PrintTemplate.PrintItem
                {
                    Type = PrintTemplate.PrintItemType.Text,
                    Content = "================================\n",
                    Config = new PrintConfig { Align = PrintConfig.TextAlign.Center }
                },
                
                // 染料代号
                new PrintTemplate.PrintItem
                {
                    Type = PrintTemplate.PrintItemType.Text,
                    Content = $"染料代号：{ChemicalCode}\n",
                    Config = new PrintConfig
                    {
                        Align = PrintConfig.TextAlign.Left,
                        Size = PrintConfig.FontSize.Normal,
                        Bold = true
                    }
                },
                
                // 箱号
                new PrintTemplate.PrintItem
                {
                    Type = PrintTemplate.PrintItemType.Text,
                    Content = $"箱号：{ChemicalBoxNo}\n",
                    Config = new PrintConfig
                    {
                        Align = PrintConfig.TextAlign.Left,
                        Size = PrintConfig.FontSize.Normal,
                        Bold = true
                    }
                },
                
                // 检查时间
                new PrintTemplate.PrintItem
                {
                    Type = PrintTemplate.PrintItemType.Text,
                    Content = $"检查时间：{DateTime.Now:yyyy-MM-dd HH:mm:ss}\n",
                    Config = new PrintConfig { Align = PrintConfig.TextAlign.Left }
                },
                
                // 检查结果
                new PrintTemplate.PrintItem
                {
                    Type = PrintTemplate.PrintItemType.Text,
                    Content = $"检查结果：{checkResult}\n",
                    Config = new PrintConfig
                    {
                        Align = PrintConfig.TextAlign.Left,
                        Size = PrintConfig.FontSize.Normal,
                        Bold = true
                    }
                },
                
                // 空行
                new PrintTemplate.PrintItem
                {
                    Type = PrintTemplate.PrintItemType.Line,
                    Content = "1"
                },
                
                // 条码（染料代号）
                new PrintTemplate.PrintItem
                {
                    Type = PrintTemplate.PrintItemType.Barcode,
                    Content = ChemicalCode,
                    BarcodeType = BarcodeType.Code128,
                    HRIPosition = HRIPosition.Below
                },
                
                // 空行
                new PrintTemplate.PrintItem
                {
                    Type = PrintTemplate.PrintItemType.Line,
                    Content = "1"
                },
                
                // 二维码（包含完整信息）
                new PrintTemplate.PrintItem
                {
                    Type = PrintTemplate.PrintItemType.QRCode,
                    Content = $"染料代号:{ChemicalCode}|箱号:{ChemicalBoxNo}|时间:{DateTime.Now:yyyyMMddHHmmss}|结果:{checkResult}",
                    Align = PrintConfig.TextAlign.Center
                },
                
                // 底部信息
                new PrintTemplate.PrintItem
                {
                    Type = PrintTemplate.PrintItemType.Text,
                    Content = "--------------------------------\n",
                    Config = new PrintConfig { Align = PrintConfig.TextAlign.Center }
                },
                
                new PrintTemplate.PrintItem
                {
                    Type = PrintTemplate.PrintItemType.Text,
                    Content = "请妥善保管此凭证\n",
                    Config = new PrintConfig { Align = PrintConfig.TextAlign.Center }
                }
            }
        };
    }

    /// <summary>
    /// 简单文本打印示例
    /// </summary>
    private async Task PrintSimpleReceipt()
    {
        try
        {
            var config = new PrintConfig
            {
                Align = PrintConfig.TextAlign.Center,
                Size = PrintConfig.FontSize.Normal,
                Bold = true
            };
            
            var content = $"染料检查完成\n代号：{ChemicalCode}\n箱号：{ChemicalBoxNo}\n时间：{DateTime.Now:yyyy-MM-dd HH:mm:ss}\n";
            
            var printResult = await _printService.PrintTextAndWaitAsync(content, config, true);
            
            if (printResult.Result != PrintResult.Success)
            {
                await _dialogService.ShowWarningAsync($"打印失败：{printResult.Message}");
            }
        }
        catch (Exception ex)
        {
            await _dialogService.ShowWarningAsync($"打印时出错：{ex.Message}");
        }
    }

    /// <summary>
    /// 条码打印示例
    /// </summary>
    private async Task PrintBarcodeOnly()
    {
        try
        {
            var printResult = await _printService.PrintBarcodeAndWaitAsync(
                ChemicalCode, 
                BarcodeType.Code128, 
                HRIPosition.Below, 
                true); // 启用黑标模式
            
            if (printResult.Result != PrintResult.Success)
            {
                await _dialogService.ShowWarningAsync($"条码打印失败：{printResult.Message}");
            }
        }
        catch (Exception ex)
        {
            await _dialogService.ShowWarningAsync($"条码打印时出错：{ex.Message}");
        }
    }
} 