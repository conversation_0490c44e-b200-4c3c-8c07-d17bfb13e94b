﻿
using MauiScanManager.Models;
namespace MauiScanManager.Services;

public interface ICPDeliveryService
{

    //获取染台交地
    Task<ServiceResult<List<RTcpMaterialFlowDept>>> GetMaterialFlowDeptAsync();
    //Task<ServiceResult<RTcpMaterialFlowDept>> GetMaterialFlowDeptAsync();
    //检查缸号提示 
    Task<ServiceResult<RTcpFiveLayers>> GetFiveLayersByBatchNOAsync(RTcpDeliveryCheck model);
    //检查缸号是否能出货
    Task<ServiceResult<RTcpCheckReturnMsg>> CheckCanDeliveryAsync(RTcpDeliveryCheck model);
    //检查车号格式是否正确
    Task<ServiceResult<RTcpCheckReturnMsg>> CheckRtCarNoAsync(RTcpDeliveryCheck model);
    //保存
    Task<ServiceResult<string>> SaveCPAutoDeliveryByScanAsync(RTcpDeliveryByScan model);

    //获取工号姓名
    Task<ServiceResult<RTcpWorkerName>> GetWorkerNameAsync(RTcpDeliveryCheck model);

    //获取待取五层样缸号
    Task<ServiceResult<RTcpBatchNO>> GetFiveBatchNOAsync(RTcpDeliveryCheck model);




}
