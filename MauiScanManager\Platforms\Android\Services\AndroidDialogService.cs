using Android.Widget;
using Google.Android.Material.Snackbar;
using Microsoft.Maui.Platform;
using Microsoft.Maui.ApplicationModel;
using MauiScanManager.Services;
using Android.Views;
using AndroidX.CoordinatorLayout.Widget;
using Android.App;

namespace MauiScanManager.Platforms.Android.Services
{
    public class AndroidDialogService : IPlatformDialogService
    {
        private readonly IServiceProvider _services;

        public AndroidDialogService(IServiceProvider services)
        {
            _services = services;
        }

        public async Task ShowToastAsync(string message)
        {
            await MainThread.InvokeOnMainThreadAsync(() =>
            {
                var context = Platform.CurrentActivity;
                if (context == null) return;

                Toast.MakeText(context, message, ToastLength.Short)?.Show();
            });
        }

        public async Task ShowSnackbarAsync(string message, string actionText = null, Action action = null)
        {
            await MainThread.InvokeOnMainThreadAsync(() =>
            {
                var activity = Platform.CurrentActivity;
                var rootView = activity?.Window?.DecorView?.RootView;
                
                if (rootView == null) return;

                var snackbar = Snackbar.Make(rootView, message, Snackbar.LengthLong);

                // 获取 Snackbar 的视图
                var snackbarView = snackbar.View;
                
                // 使用 FrameLayout.LayoutParams
                var layoutParams = new FrameLayout.LayoutParams(
                    ViewGroup.LayoutParams.WrapContent,
                    ViewGroup.LayoutParams.WrapContent
                );

                // 正确设置 GravityFlags
                layoutParams.Gravity = GravityFlags.Top | GravityFlags.Center;
                
                // 设置上边距
                layoutParams.TopMargin = 100;

                snackbarView.LayoutParameters = layoutParams;

                if (!string.IsNullOrEmpty(actionText) && action != null)
                {
                    snackbar.SetAction(actionText, _ => action());
                }

                snackbar.Show();
            });
        }

            public void ShowConfirmationDialog(string title, string message, int iconResourceId, Action onConfirm, Action onCancel = null)
        {
            var activity = Platform.CurrentActivity;
            if (activity == null) return;

            MainThread.InvokeOnMainThreadAsync(() =>
            {
                var builder = new AlertDialog.Builder(activity);
                builder.SetTitle(title);
                builder.SetMessage(message);
                builder.SetIcon(iconResourceId);
                builder.SetPositiveButton("确认", (sender, args) => onConfirm?.Invoke());
                builder.SetNegativeButton("取消", (sender, args) => onCancel?.Invoke());
                builder.Show();
            });
        }


    }
} 