# 存储过程错误修复说明

## 🐛 错误描述

执行 `零件申请存储过程.sql` 时出现以下错误：

```
消息 207，级别 16，状态 1，过程 sp_GetPendingPartRequests，第 67 行
列名 'Model' 无效。
```

## 🔍 错误分析

### 问题根源
在 `sp_GetPendingPartRequests` 存储过程中，我错误地使用了 `e.Model` 字段，但根据数据库表结构，Equipment 表中的字段应该是 `ModelId` 而不是 `Model`。

### Equipment 表结构
```sql
CREATE TABLE [dbo].[Equipment](
    [Id] [int] IDENTITY(1,1) NOT NULL,
    [Code] [nvarchar](50) NOT NULL,
    [Name] [nvarchar](100) NOT NULL,
    [DepartmentId] [int] NOT NULL,
    [LocationId] [int] NOT NULL,
    [ModelId] [int] NOT NULL,           -- ✅ 正确字段名
    [SerialNumber] [nvarchar](100) NULL,
    -- ... 其他字段
)
```

## ✅ 修复内容

### 修复前的错误代码
```sql
-- 设备信息
e.Id AS EquipmentId,
e.Code AS EquipmentCode,
e.Name AS EquipmentName,
e.Model AS EquipmentModel,              -- ❌ 错误：不存在的字段
em.Name AS EquipmentModelName,
el.Name AS EquipmentLocationName,
```

### 修复后的正确代码
```sql
-- 设备信息
e.Id AS EquipmentId,
e.Code AS EquipmentCode,
e.Name AS EquipmentName,
e.ModelId AS EquipmentModelId,          -- ✅ 正确：使用 ModelId 字段
em.Name AS EquipmentModelName,
el.Name AS EquipmentLocationName,
```

## 🔧 修复验证

修复后，存储过程应该能够正常执行。可以通过以下步骤验证：

### 1. 重新执行创建脚本
```sql
-- 执行修复后的 零件申请存储过程.sql
```

### 2. 测试存储过程
```sql
-- 基本测试
EXEC sp_GetPendingPartRequests;

-- 检查返回的字段
SELECT TOP 1 * FROM (
    EXEC sp_GetPendingPartRequests @PageSize = 1
) AS result;
```

### 3. 验证字段映射
修复后的存储过程将返回：
- `EquipmentModelId`: 设备型号ID（数字）
- `EquipmentModelName`: 设备型号名称（通过 JOIN EquipmentModels 表获取）

## 📋 相关表关系

```
Equipment 表
├── ModelId (int) → EquipmentModels.Id
├── DepartmentId (int) → Departments.Id
└── LocationId (int) → Locations.Id

EquipmentModels 表
├── Id (int) - 主键
├── Code (nvarchar) - 型号编码
├── Name (nvarchar) - 型号名称
└── Category (nvarchar) - 分类
```

## 🎯 经验教训

1. **字段名验证**: 在编写存储过程时，应该仔细检查表结构中的实际字段名
2. **外键关系**: Equipment 表通过 `ModelId` 关联到 EquipmentModels 表，而不是直接包含 Model 字段
3. **测试重要性**: 应该在开发环境中先测试存储过程，确保语法正确

## ✅ 修复完成

现在存储过程应该可以正常执行了。如果还有其他错误，请检查：

1. **表是否存在**: 确保所有相关表都已创建
2. **数据完整性**: 确保外键关系正确
3. **权限问题**: 确保执行用户有足够的权限

修复后的存储过程将正确返回设备型号相关信息，包括型号ID和型号名称。
