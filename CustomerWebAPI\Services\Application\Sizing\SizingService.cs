﻿using System;
using System.Threading.Tasks;
using CustomerWebAPI.Models;
using CustomerWebAPI.Common;
using CustomerWebAPI.Database;
using Microsoft.Extensions.Logging;

namespace CustomerWebAPI.Services
{
    public class SizingService : ISizingService
    {
        private readonly DbContext _dbContext;
        private readonly ILogger<SizingService> _logger;

        public SizingService(DbContext dbContext, ILogger<SizingService> logger)
        {
            _dbContext = dbContext;
            _logger = logger;
        }

        public async Task<ApiResponse<SizingMachineUpInfo>> GetSizingMachineUpInfo(SizingMachine model)
        {
            try
            {
                var result = await _dbContext.ExecuteAsync(async db =>
                {
                    return await db.Ado.UseStoredProcedure()
                        .SqlQuerySingleAsync<SizingMachineUpInfo>(
                            "WVMDB.dbo.usp_wvGetSizingMachineUpInfo", 
                            new { sizingMachineId = model.SizingMachineId }
                        );
                });

                return result != null
                    ? ApiResponse<SizingMachineUpInfo>.Ok(result)
                    : ApiResponse<SizingMachineUpInfo>.Ok(default, "获取上机信息成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取浆纱机台上机信息失败");
                return ApiResponse<SizingMachineUpInfo>.Fail(
                    $"获取浆纱机台上机信息失败 - {ex.Message}",
                    (int)ApiErrorCodes.DatabaseError);
            }
        }

        public async Task<ApiResponse<string>> SaveSizingUpMachine(SizingUpMachine model)
        {
            try
            {
                await _dbContext.ExecuteAsync(async db =>
                {
                    await db.Ado.UseStoredProcedure()
                        .ExecuteCommandAsync("WVMDB.dbo.usp_pwSizingYieldByScan", new
                        {
                            SaveType = model.SaveType,
                            Machine_ID = model.SizingMachineId,
                            SizingSerialNumber = model.SizingBatchSerialNo,
                            Wv_card = model.WvCardNo
                        });
                    return true;
                });

                return ApiResponse<string>.Ok("浆纱上机保存成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "浆纱上机保存失败");
                return ApiResponse<string>.Fail(
                    $"浆纱上机保存失败 - {ex.Message}",
                    (int)ApiErrorCodes.DatabaseError);
            }
        }

        public async Task<ApiResponse<string>> SaveSizingDownMachine(SizingUpMachine model)
        {
            try
            {
                await _dbContext.ExecuteAsync(async db =>
                {
                    await db.Ado.UseStoredProcedure()
                        .ExecuteCommandAsync("WVMDB.dbo.usp_pwSizingYieldByScan", new
                        {
                            SaveType = model.SaveType,
                            Machine_ID = model.SizingMachineId,
                            SizingSerialNumber = model.SizingBatchSerialNo,
                            Wv_card = model.WvCardNo
                        });
                    return true;
                });

                return ApiResponse<string>.Ok("浆纱下机保存成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "浆纱下机保存失败");
                return ApiResponse<string>.Fail(
                    $"浆纱下机保存失败 - {ex.Message}",
                    (int)ApiErrorCodes.DatabaseError);
            }
        }

        public async Task<ApiResponse<string>> SaveSizingCancelUpMachine(SizingCancelUpMachine model)
        {
            try
            {
                await _dbContext.ExecuteAsync(async db =>
                {
                    await db.Ado.UseStoredProcedure()
                        .ExecuteCommandAsync("WVMDB.dbo.usp_pwSizingCancelUpMachine", new
                        {
                            Wv_card = model.WvCardNo
                        });
                    return true;
                });

                return ApiResponse<string>.Ok("取消轴卡上机成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "取消轴卡上机失败");
                return ApiResponse<string>.Fail(
                    $"取消轴卡上机失败 - {ex.Message}",
                    (int)ApiErrorCodes.DatabaseError);
            }
        }
    }
}