using CoreHub.Shared.Services;
using Windows.Storage;

namespace CoreHub.Platforms.Windows
{
    /// <summary>
    /// Windows平台认证状态存储实现 - 使用ApplicationData
    /// </summary>
    public class WindowsAuthenticationStateStorage : IAuthenticationStateStorage
    {
        private readonly ApplicationDataContainer _localSettings;

        public WindowsAuthenticationStateStorage()
        {
            _localSettings = ApplicationData.Current.LocalSettings;
        }

        public bool IsAvailable => _localSettings != null;

        public Task SaveAuthStateAsync(string key, string data)
        {
            try
            {
                _localSettings.Values[key] = data;
                System.Diagnostics.Debug.WriteLine($"Windows存储保存成功: {key}");
                return Task.CompletedTask;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Windows存储保存失败: {ex.Message}");
                throw;
            }
        }

        public Task<string?> GetAuthStateAsync(string key)
        {
            try
            {
                var data = _localSettings.Values[key] as string;
                System.Diagnostics.Debug.WriteLine($"Windows存储读取: {key} = {(data != null ? "有数据" : "无数据")}");
                return Task.FromResult(data);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Windows存储读取失败: {ex.Message}");
                return Task.FromResult<string?>(null);
            }
        }

        public Task ClearAuthStateAsync(string key)
        {
            try
            {
                _localSettings.Values.Remove(key);
                System.Diagnostics.Debug.WriteLine($"Windows存储清除成功: {key}");
                return Task.CompletedTask;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Windows存储清除失败: {ex.Message}");
                return Task.CompletedTask;
            }
        }
    }
} 