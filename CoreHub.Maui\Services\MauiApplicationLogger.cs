using CoreHub.Shared.Services;
using Microsoft.Extensions.Logging;

namespace CoreHub.Services
{
    /// <summary>
    /// MAUI应用程序日志服务实现
    /// </summary>
    public class MauiApplicationLogger : IApplicationLogger
    {
        private readonly ILogger<MauiApplicationLogger> _logger;

        public MauiApplicationLogger(ILogger<MauiApplicationLogger> logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// 记录调试信息
        /// </summary>
        public void LogDebug(string message, params object[] args)
        {
            _logger.LogDebug(message, args);
        }

        /// <summary>
        /// 记录信息
        /// </summary>
        public void LogInformation(string message, params object[] args)
        {
            _logger.LogInformation(message, args);
        }

        /// <summary>
        /// 记录警告
        /// </summary>
        public void LogWarning(string message, params object[] args)
        {
            _logger.LogWarning(message, args);
        }

        /// <summary>
        /// 记录错误
        /// </summary>
        public void LogError(Exception exception, string message, params object[] args)
        {
            _logger.LogError(exception, message, args);
        }

        /// <summary>
        /// 记录严重错误
        /// </summary>
        public void LogCritical(Exception exception, string message, params object[] args)
        {
            _logger.LogCritical(exception, message, args);
        }

        /// <summary>
        /// 记录用户操作
        /// </summary>
        public void LogUserAction(string userId, string action, object? details = null)
        {
            using var scope = _logger.BeginScope(new Dictionary<string, object>
            {
                ["EventType"] = "UserAction",
                ["UserId"] = userId,
                ["Action"] = action
            });

            if (details != null)
            {
                _logger.LogInformation("用户操作: {Action} by {UserId}, 详情: {@Details}", action, userId, details);
            }
            else
            {
                _logger.LogInformation("用户操作: {Action} by {UserId}", action, userId);
            }
        }

        /// <summary>
        /// 记录性能指标
        /// </summary>
        public void LogPerformance(string operation, long duration, object? additionalData = null)
        {
            using var scope = _logger.BeginScope(new Dictionary<string, object>
            {
                ["EventType"] = "Performance",
                ["Operation"] = operation,
                ["Duration"] = duration
            });

            if (additionalData != null)
            {
                if (duration > 5000)
                {
                    _logger.LogWarning("性能警告: {Operation} 耗时 {Duration}ms, 数据: {@AdditionalData}", operation, duration, additionalData);
                }
                else if (duration > 1000)
                {
                    _logger.LogInformation("性能监控: {Operation} 耗时 {Duration}ms, 数据: {@AdditionalData}", operation, duration, additionalData);
                }
                else
                {
                    _logger.LogDebug("性能监控: {Operation} 耗时 {Duration}ms, 数据: {@AdditionalData}", operation, duration, additionalData);
                }
            }
            else
            {
                if (duration > 5000)
                {
                    _logger.LogWarning("性能警告: {Operation} 耗时 {Duration}ms", operation, duration);
                }
                else if (duration > 1000)
                {
                    _logger.LogInformation("性能监控: {Operation} 耗时 {Duration}ms", operation, duration);
                }
                else
                {
                    _logger.LogDebug("性能监控: {Operation} 耗时 {Duration}ms", operation, duration);
                }
            }
        }

        /// <summary>
        /// 记录安全事件
        /// </summary>
        public void LogSecurityEvent(string eventType, string? userId, object? details = null)
        {
            using var scope = _logger.BeginScope(new Dictionary<string, object>
            {
                ["EventType"] = "Security",
                ["SecurityEventType"] = eventType,
                ["UserId"] = userId ?? "anonymous"
            });

            if (details != null)
            {
                _logger.LogWarning("安全事件: {EventType}, 用户: {UserId}, 详情: {@Details}", eventType, userId, details);
            }
            else
            {
                _logger.LogWarning("安全事件: {EventType}, 用户: {UserId}", eventType, userId);
            }
        }
    }
}
