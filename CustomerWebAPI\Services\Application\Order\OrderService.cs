using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using System;
using System.Data;
using System.Threading.Tasks;
using CustomerWebAPI.Models;

namespace CustomerWebAPI.Services
{
    public class OrderService : IOrderService
    {
        private readonly string _connString;

        public OrderService(IConfiguration configuration)
        {
            _connString = configuration.GetConnectionString("ProdDatabase");

            if (string.IsNullOrEmpty(_connString))
            {

                throw new InvalidOperationException(
                    $"Connection string 'ProdDatabase' not found");
            }
        }

        public async Task<ApiResponse<bool>> SaveOrdersAsync(ScanOrder model)
        {
            try
            {
                using (var connection = new SqlConnection(_connString))
                {
                    await connection.OpenAsync();

                    using (var command = new SqlCommand("PPODB.dbo.usp_pcScanSigninForProcesses", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        // 添加参数
                        command.Parameters.AddWithValue("@OpType", model.SaveType);
                        command.Parameters.AddWithValue("@BarcodeList", string.Join(",", model.Orders));
                        
                        await command.ExecuteNonQueryAsync();

                        return new ApiResponse<bool>
                        {
                            Success = true,
                            Message = "保存成功",
                            Data = true
                        };
                    }
                }
            }
            catch (Exception ex)
            {
                return new ApiResponse<bool>
                {
                    Success = false,
                    Message = $"保存失败: {ex.Message}",
                    Data = false
                };
            }
        }
    }
} 