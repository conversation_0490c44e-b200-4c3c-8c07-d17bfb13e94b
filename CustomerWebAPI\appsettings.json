{"Logging": {"LogLevel": {"Default": "Warning", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "Microsoft.EntityFrameworkCore": "Warning"}}, "AllowedHosts": "*", "Kestrel": {"EndPoints": {"Http": {"Url": "http://0.0.0.0:8898"}, "Https": {"Url": "https://0.0.0.0:8899", "Certificate": {"Path": "Data/api_saintyeartex_com.pfx", "Password": "5YFwDlGlDxPtN03m"}, "Protocols": "Http1AndHttp2", "SslProtocols": ["Tls12", "Tls13"]}}}, "Jwt": {"Key": "AlanSaintyeartexSecurityKey", "Issuer": "<EMAIL>"}, "userid_TAL": "TAL", "password_TAL": "Saintyeartex", "ConnectionStrings": {"SalesDatabase": "Server=***********;Database=InfCustomer;User Id=sa;Password=****;TrustServerCertificate=True;", "AliSalesDatabase": "Server=*************;Database=InfCustomer;User Id=sa;Password=****;TrustServerCertificate=True;", "ProdDatabase": "Server=**********;Database=YDMDB;User Id=sa;Password=****;TrustServerCertificate=True;"}, "IsForceHttps": "True", "TalAsnUsername": "saint<PERSON>artex", "TalAsnPassword": "saint<PERSON>artex", "AppUpdate": {"Android": {"LatestVersion": "1.0.7", "MinimumVersion": "1.0.0", "ForceUpdate": true, "ReleaseNotes": "1. 增加部门选择\n2. 增加操作选择\n3. 增加版本号显示\n4. 修复已知问题", "UpdateTime": "2024-10-29"}, "iOS": {"LatestVersion": "1.0.0", "MinimumVersion": "1.0.0", "ForceUpdate": false, "ReleaseNotes": "1. 首次发布\n2. 基础功能实现", "UpdateTime": "2024-03-19"}}, "UpdateConfigAPK": {"Version": "1.0.0", "ForceUpdate": "false", "ReleaseNotes": "1. 修复已知问题\n2. 性能优化"}, "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning", "Microsoft.AspNetCore": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "WriteTo": [{"Name": "File", "Args": {"path": "Logs/log-.txt", "rollingInterval": "Day", "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}{Exception}"}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"]}, "DirectorySettings": {"RequiredDirectories": ["Updates", "Logs"]}, "Database": {"ProdDatabase": "Server=**********;Database=WVMDB;User Id=sa;Password=****;TrustServerCertificate=True;Connect Timeout=30", "PublicDatabase": "Server=**********;Database=PUBDB;User Id=sa;Password=****;TrustServerCertificate=True;Connect Timeout=30", "CommandTimeout": 30, "EnableLogging": true, "MaxRetries": 3, "RetryDelayMs": 1000, "PoolMinSize": 10, "PoolMaxSize": 100, "PoolStepSize": 5, "ConnectionTimeout": 30}}