# 用户密码修改功能实现说明

## 概述

为 CoreHub 系统的用户管理页面添加了密码修改功能，用户现在可以在编辑用户信息时同时修改密码。

## 实现方式

### 1. 界面设计

在用户编辑对话框中添加了密码输入字段：

```razor
@if (!isEditMode)
{
    <!-- 创建用户时：必填密码字段 -->
    <MudItem xs="12">
        <MudTextField @bind-Value="currentUser.PasswordHash"
                      Label="密码"
                      InputType="InputType.Password"
                      Required="true"
                      RequiredError="请输入密码"
                      Variant="Variant.Outlined" />
        <MudText Typo="Typo.caption" Color="Color.Default">密码长度至少6位</MudText>
    </MudItem>
}
else
{
    <!-- 编辑用户时：可选密码字段 -->
    <MudItem xs="12">
        <MudTextField @bind-Value="currentUser.PasswordHash"
                      Label="密码"
                      InputType="InputType.Password"
                      Variant="Variant.Outlined"
                      Placeholder="输入新密码以修改，留空则保持不变" />
        <MudText Typo="Typo.caption" Color="Color.Default">如需修改密码请输入新密码，留空则保持原密码不变</MudText>
    </MudItem>
}
```

### 2. 业务逻辑

#### 创建用户模式：
- 密码字段为必填项
- 直接使用 `UserManagementService.CreateUserAsync(currentUser)` 创建用户

#### 编辑用户模式：
- 密码字段为可选项，默认为空
- 如果用户输入了新密码，则在更新用户信息后单独调用密码修改接口
- 如果密码字段为空，则只更新其他用户信息，不修改密码

### 3. 核心代码逻辑

```csharp
if (isEditMode)
{
    // 保存密码（如果有输入新密码）
    string? passwordToUpdate = null;
    if (!string.IsNullOrWhiteSpace(currentUser.PasswordHash))
    {
        passwordToUpdate = currentUser.PasswordHash;
        currentUser.PasswordHash = ""; // 清空密码字段，避免在UpdateUserAsync中更新
    }

    // 更新用户基本信息
    var result = await UserManagementService.UpdateUserAsync(currentUser);
    if (result.IsSuccess)
    {
        // 如果有新密码，单独更新密码
        if (!string.IsNullOrWhiteSpace(passwordToUpdate))
        {
            var passwordResult = await UserManagementService.ChangePasswordAsync(currentUser.Id, passwordToUpdate);
            if (passwordResult.IsSuccess)
            {
                Snackbar.Add("用户信息和密码更新成功", Severity.Success);
            }
            else
            {
                Snackbar.Add($"用户信息更新成功，但密码更新失败: {passwordResult.ErrorMessage}", Severity.Warning);
            }
        }
        else
        {
            Snackbar.Add("用户更新成功", Severity.Success);
        }
        
        CloseUserDialog();
        await LoadUsers();
    }
    else
    {
        Snackbar.Add($"用户更新失败: {result.ErrorMessage}", Severity.Error);
    }
}
```

## 技术特点

### 1. 数据安全
- 编辑用户时不显示原密码（密码字段为空）
- 只有在用户主动输入新密码时才进行密码更新
- 使用专门的 `ChangePasswordAsync` 方法更新密码，确保安全性

### 2. 用户体验
- 清晰的提示信息：用户知道留空表示不修改密码
- 分离的成功/失败提示：区分用户信息更新和密码更新的结果
- 一致的界面设计：与其他字段保持相同的样式

### 3. 错误处理
- 用户信息更新失败时，不会尝试更新密码
- 密码更新失败时，会给出明确的警告信息
- 保持事务性：要么全部成功，要么明确告知哪部分失败

## 使用方式

### 1. 编辑用户信息（不修改密码）
1. 点击用户列表中的"编辑"按钮
2. 修改需要更改的用户信息（用户名、显示名称、邮箱等）
3. **密码字段保持为空**
4. 点击"更新"按钮
5. 系统只更新用户基本信息，密码保持不变

### 2. 编辑用户信息并修改密码
1. 点击用户列表中的"编辑"按钮
2. 修改需要更改的用户信息
3. **在密码字段中输入新密码**
4. 点击"更新"按钮
5. 系统更新用户基本信息和密码

### 3. 只修改密码
1. 点击用户列表中的"编辑"按钮
2. 不修改其他信息
3. **只在密码字段中输入新密码**
4. 点击"更新"按钮
5. 系统只更新密码，其他信息保持不变

## 权限要求

- **查看用户管理页面**：需要 `UserManagement.View` 权限
- **编辑用户信息**：需要 `UserManagement.Edit` 权限
- **修改用户密码**：需要 `UserManagement.Edit` 权限

## 后端服务支持

利用了现有的后端服务方法：

1. **`UserManagementService.UpdateUserAsync(user)`**
   - 更新用户基本信息
   - 自动忽略 `PasswordHash` 字段（在服务中配置）

2. **`UserManagementService.ChangePasswordAsync(userId, newPassword)`**
   - 专门用于密码更新
   - 包含密码验证和安全处理

## 注意事项

### 1. 密码存储
当前系统使用明文密码存储（开发环境），生产环境应该：
- 实现密码哈希加密
- 添加密码强度验证
- 考虑密码历史记录

### 2. 安全建议
- 建议添加密码复杂度要求
- 考虑添加密码确认字段
- 可以添加"强制下次登录时修改密码"选项

### 3. 用户体验优化
- 可以添加密码强度指示器
- 可以添加"显示/隐藏密码"切换按钮
- 可以添加密码生成器功能

## 测试建议

### 1. 功能测试
- 测试只修改用户信息（不修改密码）
- 测试只修改密码（不修改其他信息）
- 测试同时修改用户信息和密码
- 测试密码字段为空时的行为

### 2. 边界测试
- 测试空密码的处理
- 测试特殊字符密码
- 测试超长密码
- 测试网络异常情况

### 3. 权限测试
- 测试不同权限用户的访问情况
- 测试权限不足时的提示信息

## 总结

通过这次实现，用户密码修改功能已经完全集成到用户管理页面中，提供了：

1. ✅ **直观的用户界面** - 在编辑对话框中直接修改密码
2. ✅ **灵活的操作方式** - 可选择是否修改密码
3. ✅ **清晰的反馈信息** - 明确告知操作结果
4. ✅ **安全的处理逻辑** - 使用专门的密码更新接口
5. ✅ **一致的用户体验** - 与其他字段编辑保持一致

现在管理员可以方便地管理用户信息和密码，无需额外的对话框或复杂的操作流程。
