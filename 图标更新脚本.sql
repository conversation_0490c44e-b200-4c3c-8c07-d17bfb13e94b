-- =============================================
-- 菜单图标系统升级脚本
-- 从SVG路径迁移到MudBlazor图标名称常量
-- =============================================

USE [PermissionSystem]
GO

-- 更新现有菜单项的图标为MudBlazor图标名称常量
UPDATE MenuItems SET Icon = 'Icons.Material.Filled.Home' WHERE Code = 'Home';
UPDATE MenuItems SET Icon = 'Icons.Material.Filled.Add' WHERE Code = 'Counter';
UPDATE MenuItems SET Icon = 'Icons.Material.Filled.QrCodeScanner' WHERE Code = 'DeviceScanner';
UPDATE MenuItems SET Icon = 'Icons.Material.Filled.Camera' WHERE Code = 'CameraTest';
UPDATE MenuItems SET Icon = 'Icons.Material.Filled.WbSunny' WHERE Code = 'Weather';
UPDATE MenuItems SET Icon = 'Icons.Material.Filled.Build' WHERE Code = 'DeviceRepair';
UPDATE MenuItems SET Icon = 'Icons.Material.Filled.Settings' WHERE Code = 'SystemManagement';
UPDATE MenuItems SET Icon = 'Icons.Material.Filled.People' WHERE Code = 'UserManagement';
UPDATE MenuItems SET Icon = 'Icons.Material.Filled.Security' WHERE Code = 'RoleManagement';
UPDATE MenuItems SET Icon = 'Icons.Material.Filled.Key' WHERE Code = 'PermissionManagement';
UPDATE MenuItems SET Icon = 'Icons.Material.Filled.Menu' WHERE Code = 'MenuManagement';
UPDATE MenuItems SET Icon = 'Icons.Material.Filled.Storage' WHERE Code = 'DatabaseSetup';
UPDATE MenuItems SET Icon = 'Icons.Material.Filled.PersonAdd' WHERE Code = 'UserPermissions';
UPDATE MenuItems SET Icon = 'Icons.Material.Filled.Shield' WHERE Code = 'AuthorizeViewExample';
UPDATE MenuItems SET Icon = 'Icons.Material.Filled.BugReport' WHERE Code = 'DebugAuth';

-- 显示更新结果
SELECT 
    Code,
    Name,
    Icon,
    'Updated to MudBlazor constant' AS Status
FROM MenuItems 
WHERE Icon LIKE 'Icons.Material.%'
ORDER BY SortOrder;

-- 显示需要手动处理的菜单项（如果有的话）
SELECT 
    Code,
    Name,
    Icon,
    'Needs manual update' AS Status
FROM MenuItems 
WHERE Icon NOT LIKE 'Icons.Material.%' 
  AND Icon IS NOT NULL 
  AND Icon != ''
ORDER BY SortOrder;

PRINT '图标更新完成！';
PRINT '所有图标已更新为MudBlazor图标名称常量格式。';
PRINT '现在菜单系统将使用简洁的图标名称而不是复杂的SVG路径。'; 