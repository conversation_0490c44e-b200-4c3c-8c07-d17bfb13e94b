using System;
using Dapper.Contrib.Extensions;

namespace CustomerWebAPI.Models
{
    /// <summary>
    /// 
    /// </summary>
    /// 
    [Table("ASNRoot")]
    public class ASNRoot
    {
        /// <summary>
        /// 
        /// </summary>
        /// 
        [ExplicitKey]
        public Guid Id { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string SenderIdentifierId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string SenderId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string ReceiverIdentifierId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string ReceiverId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string TrackNumber { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string ProductionIndicator { get; set; }
    }
}