using MauiScanManager.Services;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Text;  // 添加这行
using System.Windows.Input;

namespace MauiScanManager.ViewModels
{
    public class MainPageViewModel : INotifyPropertyChanged
    {
        private readonly IScanService _scanService;
        private string _lastScanResult;
        private string _scanHistory = string.Empty;

        public event PropertyChangedEventHandler PropertyChanged;

        public ICommand ClearHistoryCommand { get; }

        public string LastScanResult
        {
            get => _lastScanResult;
            set
            {
                if (_lastScanResult != value)
                {
                    _lastScanResult = value;
                    OnPropertyChanged();
                }
            }
        }

        public string ScanHistory
        {
            get => _scanHistory;
            set
            {
                if (_scanHistory != value)
                {
                    _scanHistory = value;
                    OnPropertyChanged();
                }
            }
        }

        public MainPageViewModel(IScanService scanService)
        {
            _scanService = scanService;
            _scanService.OnScanResult += ScanService_OnScanResult;
            _scanService.Initialize();

            ClearHistoryCommand = new Command(() => ScanHistory = string.Empty);
        }

        private void ScanService_OnScanResult(object sender, ScanEventArgs e)
        {
            try
            {
                MainThread.BeginInvokeOnMainThread(() =>
                {
                    try
                    {
                        // 使用StringBuilder来构建结果字符串
                        var result = new StringBuilder()
                            .AppendLine($"扫描结果: {e.Code ?? "无"}")
                            .Append($"类型: {e.Type ?? "未知"}");

                        LastScanResult = result.ToString();

                        // 添加到历史记录
                        var timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                        var historyEntry = $"[{timestamp}] {result}";
                        
                        // 如果历史记录为空，直接赋值，否则添加换行
                        ScanHistory = string.IsNullOrEmpty(ScanHistory) 
                            ? historyEntry 
                            : $"{historyEntry}\n{ScanHistory}";
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"UI更新错误: {ex}");
                    }
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"扫描结果处理错误: {ex}");
            }
        }

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
