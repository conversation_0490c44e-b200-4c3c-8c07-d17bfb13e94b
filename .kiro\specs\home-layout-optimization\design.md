# 设计文档

## 概述

本设计文档基于 Home.razor 布局优化需求，旨在改善 CoreHub 系统首页仪表板的用户体验、视觉效果和性能表现。设计将充分利用 MudBlazor 组件库的特性，实现现代化、响应式的仪表板界面。

## 架构

### 组件架构

```
Home.razor (主页面)
├── DashboardHeader (页面头部组件)
├── LoadingState (加载状态组件)
├── MetricsCards (关键指标卡片区域)
│   ├── MetricCard (单个指标卡片)
│   └── MetricCardSkeleton (骨架屏)
├── ChartsSection (图表区域)
│   ├── EquipmentStatusChart (设备状态图表)
│   ├── RepairOrderChart (报修单统计图表)
│   └── ChartSkeleton (图表骨架屏)
├── PerformanceSection (性能指标区域)
│   ├── SystemHealthCard (系统健康度)
│   ├── MaintenanceEfficiencyCard (维修效率)
│   └── QuickActionsCard (快速操作)
└── ActivitySection (活动区域)
    ├── RecentRepairOrdersTable (最近报修单表格)
    └── SystemNotifications (系统通知)
```

### 数据流架构

```
AuthenticationStateProvider
    ↓
Home.razor
    ├── IMaintenanceDashboardService
    ├── IEquipmentService
    └── IRepairOrderService
        ↓
Dashboard Data Models
    ├── MaintenanceDashboardData
    ├── EquipmentStatisticsDto
    └── RepairOrderStatisticsDto
```

## 组件和接口

### 1. 核心组件设计

#### DashboardHeader 组件
```csharp
// 页面头部组件，包含标题、用户欢迎信息和快速操作
public partial class DashboardHeader : ComponentBase
{
    [Parameter] public string UserName { get; set; } = string.Empty;
    [Parameter] public EventCallback OnRefresh { get; set; }
    [Parameter] public bool IsLoading { get; set; }
}
```

#### MetricCard 组件
```csharp
// 可重用的指标卡片组件
public partial class MetricCard : ComponentBase
{
    [Parameter] public string Title { get; set; } = string.Empty;
    [Parameter] public string Value { get; set; } = string.Empty;
    [Parameter] public string SubText { get; set; } = string.Empty;
    [Parameter] public string Icon { get; set; } = string.Empty;
    [Parameter] public Color Color { get; set; } = Color.Primary;
    [Parameter] public string? TrendText { get; set; }
    [Parameter] public Color TrendColor { get; set; } = Color.Default;
    [Parameter] public EventCallback OnClick { get; set; }
    [Parameter] public bool IsClickable { get; set; } = false;
}
```

#### ChartCard 组件
```csharp
// 图表卡片基础组件
public partial class ChartCard : ComponentBase
{
    [Parameter] public string Title { get; set; } = string.Empty;
    [Parameter] public string Icon { get; set; } = string.Empty;
    [Parameter] public RenderFragment ChildContent { get; set; } = default!;
    [Parameter] public string Height { get; set; } = "400px";
    [Parameter] public EventCallback OnRefresh { get; set; }
}
```

### 2. 数据可视化组件

#### EquipmentStatusChart 组件
```csharp
// 设备状态分布图表组件
public partial class EquipmentStatusChart : ComponentBase
{
    [Parameter] public EquipmentStatisticsDto Statistics { get; set; } = new();
    [Parameter] public bool ShowAsDonutChart { get; set; } = true;
    
    // 支持饼图和环形图两种显示模式
    // 集成 ApexCharts 或使用 MudBlazor 的进度组件
}
```

#### RepairOrderTrendChart 组件
```csharp
// 报修单趋势图表组件
public partial class RepairOrderTrendChart : ComponentBase
{
    [Parameter] public List<RepairOrderTrendData> TrendData { get; set; } = new();
    [Parameter] public int DaysRange { get; set; } = 30;
    
    // 显示时间序列趋势图
}
```

### 3. 性能和交互组件

#### SystemHealthIndicator 组件
```csharp
// 系统健康度指示器
public partial class SystemHealthIndicator : ComponentBase
{
    [Parameter] public double HealthPercentage { get; set; }
    [Parameter] public EquipmentStatisticsDto Statistics { get; set; } = new();
    [Parameter] public bool ShowAnimation { get; set; } = true;
}
```

#### QuickActionPanel 组件
```csharp
// 快速操作面板
public partial class QuickActionPanel : ComponentBase
{
    [Parameter] public List<QuickAction> Actions { get; set; } = new();
    [Parameter] public EventCallback<string> OnActionClick { get; set; }
}

public class QuickAction
{
    public string Id { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Icon { get; set; } = string.Empty;
    public Color Color { get; set; } = Color.Primary;
    public string NavigationUrl { get; set; } = string.Empty;
    public bool RequiresPermission { get; set; } = false;
    public string Permission { get; set; } = string.Empty;
}
```

## 数据模型

### 扩展的数据传输对象

```csharp
// 扩展设备统计数据
public class EquipmentStatisticsDto
{
    // 现有属性...
    public int TotalCount { get; set; }
    public int NormalCount { get; set; }
    public int MaintenanceCount { get; set; }
    public int DisabledCount { get; set; }
    public int ScrapCount { get; set; }
    public int WarrantyExpiredCount { get; set; }
    public int MaintenanceDueCount { get; set; }
    
    // 新增趋势数据
    public List<EquipmentTrendData> TrendData { get; set; } = new();
    public double HealthScore { get; set; }
    public DateTime LastUpdated { get; set; }
}

// 设备趋势数据
public class EquipmentTrendData
{
    public DateTime Date { get; set; }
    public int NormalCount { get; set; }
    public int MaintenanceCount { get; set; }
    public int DisabledCount { get; set; }
}

// 报修单趋势数据
public class RepairOrderTrendData
{
    public DateTime Date { get; set; }
    public int NewOrders { get; set; }
    public int CompletedOrders { get; set; }
    public int PendingOrders { get; set; }
    public double AverageResponseTime { get; set; }
}

// 用户偏好设置
public class DashboardPreferences
{
    public int UserId { get; set; }
    public List<string> HiddenCards { get; set; } = new();
    public Dictionary<string, int> CardOrder { get; set; } = new();
    public bool AutoRefresh { get; set; } = true;
    public int RefreshInterval { get; set; } = 30; // 秒
    public string DefaultChartType { get; set; } = "donut";
}
```

## 错误处理

### 错误处理策略

1. **分层错误处理**
   - 组件级别：单个组件的错误不影响整个页面
   - 数据加载错误：显示友好的错误提示，允许重试
   - 网络错误：提供离线模式或缓存数据

2. **错误边界组件**
```csharp
public partial class ErrorBoundary : ComponentBase
{
    [Parameter] public RenderFragment ChildContent { get; set; } = default!;
    [Parameter] public RenderFragment<Exception> ErrorContent { get; set; } = default!;
    [Parameter] public string FallbackMessage { get; set; } = "加载数据时出现错误";
    
    private Exception? currentException;
    
    protected override void OnParametersSet()
    {
        currentException = null;
    }
    
    public void Recover()
    {
        currentException = null;
        StateHasChanged();
    }
}
```

3. **数据加载错误处理**
```csharp
public class DataLoadingState
{
    public bool IsLoading { get; set; }
    public bool HasError { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime? LastSuccessfulLoad { get; set; }
    public int RetryCount { get; set; }
    
    public void SetLoading() => (IsLoading, HasError, ErrorMessage) = (true, false, null);
    public void SetSuccess() => (IsLoading, HasError, LastSuccessfulLoad) = (false, false, DateTime.Now);
    public void SetError(string message) => (IsLoading, HasError, ErrorMessage) = (false, true, message);
}
```

## 测试策略

### 1. 单元测试

- **组件测试**：使用 bUnit 测试框架
- **数据服务测试**：模拟数据服务接口
- **计算逻辑测试**：测试百分比计算、健康度评分等

### 2. 集成测试

- **端到端数据流测试**：从服务到组件的完整数据流
- **权限测试**：不同角色用户的界面差异
- **响应式测试**：不同屏幕尺寸的布局测试

### 3. 性能测试

- **加载性能测试**：页面首次加载时间
- **数据刷新性能**：增量更新性能
- **内存使用测试**：长时间运行的内存泄漏检测

### 4. 可访问性测试

- **键盘导航测试**：确保所有交互元素可通过键盘访问
- **屏幕阅读器测试**：ARIA 标签和语义化标记
- **颜色对比度测试**：确保视觉障碍用户可读性

## 技术实现细节

### 1. 响应式设计实现

MudBlazor 的网格系统自动处理响应式布局，使用内置断点：

```razor
<!-- 关键指标卡片 - 自动响应式布局 -->
<MudGrid Class="mb-6">
    <MudItem xs="12" sm="6" md="3">
        <!-- 移动端：全宽，平板：半宽，桌面：1/4宽 -->
        <MetricCard />
    </MudItem>
    <!-- 重复其他卡片 -->
</MudGrid>

<!-- 图表区域 - 自动响应式布局 -->
<MudGrid Class="mb-6">
    <MudItem xs="12" md="6">
        <!-- 移动端：全宽，桌面：半宽 -->
        <ChartCard />
    </MudItem>
    <MudItem xs="12" md="6">
        <ChartCard />
    </MudItem>
</MudGrid>

<!-- 活动区域 - 自动响应式布局 -->
<MudGrid>
    <MudItem xs="12" lg="8">
        <!-- 移动端和平板：全宽，大屏：2/3宽 -->
        <RecentRepairOrdersTable />
    </MudItem>
    <MudItem xs="12" lg="4">
        <!-- 移动端和平板：全宽，大屏：1/3宽 -->
        <SystemNotifications />
    </MudItem>
</MudGrid>
```

MudBlazor 断点系统：
- `xs`: 0px 及以上（超小屏幕）
- `sm`: 600px 及以上（小屏幕）
- `md`: 960px 及以上（中等屏幕）
- `lg`: 1280px 及以上（大屏幕）
- `xl`: 1920px 及以上（超大屏幕）

### 2. 性能优化策略

1. **虚拟化长列表**：对于大量数据的表格使用虚拟滚动
2. **懒加载图表**：图表组件按需加载
3. **数据缓存**：实现客户端数据缓存机制
4. **增量更新**：只更新变化的数据部分

### 3. 状态管理

```csharp
// 使用 Fluxor 或简单的状态管理
public class DashboardState
{
    public DashboardData Data { get; set; } = new();
    public DashboardPreferences Preferences { get; set; } = new();
    public Dictionary<string, DataLoadingState> LoadingStates { get; set; } = new();
    public DateTime LastRefresh { get; set; }
    public bool AutoRefreshEnabled { get; set; } = true;
}

public class DashboardStateService
{
    private DashboardState _state = new();
    public event Action? StateChanged;
    
    public DashboardState State => _state;
    
    public async Task LoadDataAsync()
    {
        // 实现数据加载逻辑
        StateChanged?.Invoke();
    }
    
    public void UpdatePreferences(DashboardPreferences preferences)
    {
        _state.Preferences = preferences;
        StateChanged?.Invoke();
    }
}
```

### 4. 图表集成方案

考虑集成以下图表库之一：
- **ApexCharts.Blazor**：功能丰富，交互性强
- **ChartJs.Blazor**：轻量级，易于集成
- **Plotly.Blazor**：适合复杂数据可视化

推荐使用 ApexCharts.Blazor，因为它与 MudBlazor 兼容性好，功能完整。