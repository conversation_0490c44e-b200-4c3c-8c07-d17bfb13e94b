using System;
using Dapper.Contrib.Extensions;

namespace CustomerWebAPI.Models
{
    /// <summary>
    /// 
    /// </summary>
    /// 
    [Table("ASNDetailOrdersItemsPackages")]
    public class ASNDetailOrdersItemsPackages
    {
        /// <summary>
        /// 
        /// </summary>
        /// 
        [ExplicitKey]
        public Guid Id { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public Guid? ASNDetailOrdersItemsId { get; set; }


        /// <summary>
        /// 
        /// </summary>
        public string AssignedId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public decimal? Quantity { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string WidthUnit { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public decimal? FabricWidth { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string QuantityUnit { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string QaStatus { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string FabricLengthString { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string UCC { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string RollNumber { get; set; }
    }
}