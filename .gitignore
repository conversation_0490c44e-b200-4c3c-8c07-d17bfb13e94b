# Build results
[Dd]ebug/
[Dd]ebugPublic/
[Rr]elease/
[Rr]eleases/
x64/
x86/
[Ww][Ii][Nn]32/
[Aa][Rr][Mm]/
[Aa][Rr][Mm]64/
bld/
[Bb]in/
[Oo]bj/
[Ll]og/
[Ll]ogs/

# Visual Studio cache/options directory
.vs/

# Visual Studio auto generated files
Generated\ Files/

# Test Results
[Tt]est[Rr]esult*/
[Bb]uild[Ll]og.*
TestResult.xml
nunit-*.xml
TestResults/
*.trx
*.coverage
*.coveragexml

# .NET Core
project.lock.json
project.fragment.lock.json
artifacts/

# Files built by Visual Studio
*.obj
*.pdb
*.log
*.tlog
*.tmp
*.tmp_proj
*_wpftmp.csproj
*.vspscc
*.vssscc
.builds
*.pidb
*.svclog
*.scc

# Visual Studio profiler
*.psess
*.vsp
*.vspx
*.sap

# ReSharper
_ReSharper*/
*.[Rr]e[Ss]harper
*.DotSettings.user

# Web workbench (sass)
.sass-cache/

# Publish Web Output
*.[Pp]ublish.xml
*.azurePubxml
*.pubxml
*.publishproj

# Windows Store app package files
AppPackages/
BundleArtifacts/
Package.StoreAssociation.xml
_pkginfo.txt
*.appx
*.appxbundle
*.appxupload

# Visual Studio cache files
*.[Cc]ache
!?*.[Cc]ache/

# Others
ClientBin/
~$*
*~
*.dbmdl
*.jfm
*.pfx
*.publishsettings

# SQL Server files
*.mdf
*.ldf
*.ndf

# Node.js
node_modules/

# Paket dependency manager
.paket/paket.exe
paket-files/

# FAKE - F# Make
.fake/

# CodeRush personal settings
.cr/personal

# Python Tools for Visual Studio (PTVS)
__pycache__/
*.pyc

# MSBuild Binary and Structured Log
*.binlog

# MFractors (Xamarin productivity tool) working folder
.mfractor/

# Local History for Visual Studio
.localhistory/

# Visual Studio History (VSHistory) files
.vshistory/

# BeatPulse healthcheck temp database
healthchecksdb

# Backup folder for Package Reference Convert tool
MigrationBackup/

# Ionide (cross platform F# VS Code tools) working folder
.ionide/

# Fody - auto-generated XML schema
FodyWeavers.xsd

# VS Code files
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
*.code-workspace

# Local History for Visual Studio Code
.history/

# Windows Installer files
*.cab
*.msi
*.msix
*.msm
*.msp

# JetBrains Rider
*.sln.iml
.idea/

# MAUI specific
**/Platforms/**/bin/
**/Platforms/**/obj/

# OS specific files
.DS_Store
Thumbs.db

# Sensitive files
appsettings.local.json
*.p12

# User specific files
*.suo
*.user
*.userprefs

# NuGet packages
*.nupkg
*.snupkg
**/packages/

# Temporary files
*.tmp
*.temp

# Log files
script_output.log

# CoreHub.Web updates folder
CoreHub.Web/wwwroot/updates/