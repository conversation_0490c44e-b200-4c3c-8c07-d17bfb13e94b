@page "/auth-demo"
@using Microsoft.AspNetCore.Components.Authorization

<PageTitle>授权演示</PageTitle>

<MudContainer MaxWidth="MaxWidth.Large">
    <MudText Typo="Typo.h3" Class="mb-4">授权系统演示</MudText>

    <!-- 基本认证检查 -->
    <MudPaper Class="pa-4 mb-4">
        <MudText Typo="Typo.h5" GutterBottom="true">基本认证检查</MudText>
        <AuthorizeView>
            <Authorized>
                <MudAlert Severity="Severity.Success">
                    <div class="d-flex align-center">
                        <MudIcon Icon="@Icons.Material.Filled.CheckCircle" Class="mr-2" />
                        <span>您已登录，用户名: @context.User.Identity?.Name</span>
                    </div>
                </MudAlert>
            </Authorized>
            <NotAuthorized>
                <MudAlert Severity="Severity.Warning">
                    <div class="d-flex align-center">
                        <MudIcon Icon="@Icons.Material.Filled.Warning" Class="mr-2" />
                        <span>您尚未登录，请先登录以访问受保护的功能</span>
                    </div>
                </MudAlert>
            </NotAuthorized>
        </AuthorizeView>
    </MudPaper>

    <!-- 基于角色的授权 -->
    <MudPaper Class="pa-4 mb-4">
        <MudText Typo="Typo.h5" GutterBottom="true">基于角色的授权</MudText>
        <AuthorizeView Roles="Admin">
            <Authorized>
                <MudAlert Severity="Severity.Success">
                    <div class="d-flex align-center">
                        <MudIcon Icon="@Icons.Material.Filled.AdminPanelSettings" Class="mr-2" />
                        <span>您拥有管理员权限，可以访问管理功能</span>
                    </div>
                </MudAlert>
            </Authorized>
            <NotAuthorized>
                <MudAlert Severity="Severity.Info">
                    <div class="d-flex align-center">
                        <MudIcon Icon="@Icons.Material.Filled.Info" Class="mr-2" />
                        <span>您不是管理员，无法访问管理功能</span>
                    </div>
                </MudAlert>
            </NotAuthorized>
        </AuthorizeView>
    </MudPaper>

    <!-- 基于自定义权限的授权 -->
    <MudPaper Class="pa-4 mb-4">
        <MudText Typo="Typo.h5" GutterBottom="true">基于自定义权限的授权</MudText>
        
        <!-- 用户管理权限 -->
        <div class="mb-3">
            <MudText Typo="Typo.subtitle1" GutterBottom="true">用户管理权限:</MudText>
            <AuthorizeView Policy="CanManageUsers">
                <Authorized>
                    <MudChip T="string" Color="Color.Success" Icon="@Icons.Material.Filled.Check">
                        有用户管理权限
                    </MudChip>
                </Authorized>
                <NotAuthorized>
                    <MudChip T="string" Color="Color.Error" Icon="@Icons.Material.Filled.Block">
                        无用户管理权限
                    </MudChip>
                </NotAuthorized>
            </AuthorizeView>
        </div>

        <!-- 设备扫描权限 -->
        <div class="mb-3">
            <MudText Typo="Typo.subtitle1" GutterBottom="true">设备扫描权限:</MudText>
            <AuthorizeView Policy="CanScanDevices">
                <Authorized>
                    <MudChip T="string" Color="Color.Success" Icon="@Icons.Material.Filled.QrCodeScanner">
                        有设备扫描权限
                    </MudChip>
                </Authorized>
                <NotAuthorized>
                    <MudChip T="string" Color="Color.Error" Icon="@Icons.Material.Filled.Block">
                        无设备扫描权限
                    </MudChip>
                </NotAuthorized>
            </AuthorizeView>
        </div>
    </MudPaper>

    <!-- 实时状态更新 -->
    <MudPaper Class="pa-4">
        <MudText Typo="Typo.h5" GutterBottom="true">实时状态更新</MudText>
        <AuthorizeView>
            <Authorized>
                <MudAlert Severity="Severity.Success" Class="mb-3">
                    当前用户已认证，可以实时获取状态更新
                </MudAlert>
            </Authorized>
            <NotAuthorized>
                <MudAlert Severity="Severity.Info" Class="mb-3">
                    用户未认证，状态更新功能受限
                </MudAlert>
            </NotAuthorized>
        </AuthorizeView>

        <MudText Typo="Typo.body1" Class="mb-2">
            <strong>当前时间:</strong> @DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
        </MudText>
        <MudText Typo="Typo.body1">
            <strong>页面加载时间:</strong> @loadTime.ToString("yyyy-MM-dd HH:mm:ss")
        </MudText>
    </MudPaper>
</MudContainer>

@code {
    private DateTime loadTime = DateTime.Now;

    protected override void OnInitialized()
    {
        loadTime = DateTime.Now;
        
        // 每秒更新时间显示
        var timer = new System.Timers.Timer(1000);
        timer.Elapsed += (sender, e) => InvokeAsync(() => StateHasChanged());
        timer.Start();
    }
} 