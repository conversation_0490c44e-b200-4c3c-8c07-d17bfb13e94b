using Serilog;
using Serilog.Events;
using Serilog.Formatting.Compact;

namespace CoreHub.Web.Extensions
{
    /// <summary>
    /// 日志配置扩展类
    /// </summary>
    public static class LoggingExtensions
    {
        /// <summary>
        /// 配置 Serilog 日志
        /// </summary>
        /// <param name="builder">WebApplicationBuilder</param>
        /// <returns>WebApplicationBuilder</returns>
        public static WebApplicationBuilder ConfigureSerilog(this WebApplicationBuilder builder)
        {
            // 清除默认的日志提供程序
            builder.Logging.ClearProviders();

            // 配置 Serilog
            builder.Host.UseSerilog((context, services, configuration) =>
            {
                configuration
                    .ReadFrom.Configuration(context.Configuration)
                    .ReadFrom.Services(services)
                    .Enrich.FromLogContext()
                    .Enrich.WithProperty("Application", "CoreHub.Web")
                    .Enrich.WithProperty("Version", GetApplicationVersion())
                    .Enrich.WithMachineName()
                    .Enrich.WithProcessId()
                    .Enrich.WithThreadId()
                    .Enrich.WithEnvironmentName();

                // 根据环境配置不同的日志级别
                if (context.HostingEnvironment.IsDevelopment())
                {
                    configuration.MinimumLevel.Debug();
                }
                else
                {
                    configuration.MinimumLevel.Information();
                }

                // 配置特定命名空间的日志级别
                configuration
                    .MinimumLevel.Override("Microsoft", LogEventLevel.Warning)
                    .MinimumLevel.Override("Microsoft.AspNetCore", LogEventLevel.Warning)
                    .MinimumLevel.Override("Microsoft.EntityFrameworkCore", LogEventLevel.Warning)
                    .MinimumLevel.Override("System", LogEventLevel.Warning);

                // 开发环境下显示更多 CoreHub 相关日志
                if (context.HostingEnvironment.IsDevelopment())
                {
                    configuration.MinimumLevel.Override("CoreHub", LogEventLevel.Debug);
                }
            });

            return builder;
        }

        /// <summary>
        /// 添加请求日志中间件
        /// </summary>
        /// <param name="app">WebApplication</param>
        /// <returns>WebApplication</returns>
        public static WebApplication UseRequestLogging(this WebApplication app)
        {
            // 配置请求日志，排除静态文件和健康检查
            app.UseSerilogRequestLogging(options =>
            {
                options.MessageTemplate = "HTTP {RequestMethod} {RequestPath} responded {StatusCode} in {Elapsed:0.0000} ms";
                options.GetLevel = GetLogLevel;
                options.EnrichDiagnosticContext = EnrichFromRequest;
            });

            return app;
        }

        /// <summary>
        /// 获取应用程序版本
        /// </summary>
        /// <returns>版本字符串</returns>
        private static string GetApplicationVersion()
        {
            try
            {
                var assembly = System.Reflection.Assembly.GetExecutingAssembly();
                var version = assembly.GetName().Version;
                return version?.ToString() ?? "Unknown";
            }
            catch
            {
                return "Unknown";
            }
        }

        /// <summary>
        /// 根据请求确定日志级别
        /// </summary>
        /// <param name="httpContext">HTTP上下文</param>
        /// <param name="elapsed">请求耗时</param>
        /// <param name="ex">异常</param>
        /// <returns>日志级别</returns>
        private static LogEventLevel GetLogLevel(HttpContext httpContext, double elapsed, Exception? ex)
        {
            if (ex != null)
                return LogEventLevel.Error;

            if (httpContext.Response.StatusCode > 499)
                return LogEventLevel.Error;

            if (httpContext.Response.StatusCode > 399)
                return LogEventLevel.Warning;

            // 静态文件请求使用较低级别
            if (IsStaticFile(httpContext.Request.Path))
                return LogEventLevel.Debug;

            // 健康检查请求使用较低级别
            if (httpContext.Request.Path.StartsWithSegments("/health"))
                return LogEventLevel.Debug;

            // 长时间请求使用警告级别
            if (elapsed > 5000)
                return LogEventLevel.Warning;

            return LogEventLevel.Information;
        }

        /// <summary>
        /// 丰富请求日志上下文
        /// </summary>
        /// <param name="diagnosticContext">诊断上下文</param>
        /// <param name="httpContext">HTTP上下文</param>
        private static void EnrichFromRequest(IDiagnosticContext diagnosticContext, HttpContext httpContext)
        {
            var request = httpContext.Request;

            // 添加用户信息
            if (httpContext.User.Identity?.IsAuthenticated == true)
            {
                diagnosticContext.Set("UserId", httpContext.User.Identity.Name);
            }

            // 添加客户端信息
            diagnosticContext.Set("ClientIP", GetClientIpAddress(httpContext));
            diagnosticContext.Set("UserAgent", request.Headers.UserAgent.ToString());

            // 添加请求信息
            diagnosticContext.Set("RequestHost", request.Host.Value);
            diagnosticContext.Set("RequestScheme", request.Scheme);

            // 添加响应信息
            diagnosticContext.Set("ResponseContentType", httpContext.Response.ContentType);
            diagnosticContext.Set("ResponseContentLength", httpContext.Response.ContentLength);
        }

        /// <summary>
        /// 获取客户端IP地址
        /// </summary>
        /// <param name="httpContext">HTTP上下文</param>
        /// <returns>IP地址</returns>
        private static string GetClientIpAddress(HttpContext httpContext)
        {
            // 检查 X-Forwarded-For 头（代理服务器）
            var xForwardedFor = httpContext.Request.Headers["X-Forwarded-For"].FirstOrDefault();
            if (!string.IsNullOrEmpty(xForwardedFor))
            {
                return xForwardedFor.Split(',')[0].Trim();
            }

            // 检查 X-Real-IP 头
            var xRealIp = httpContext.Request.Headers["X-Real-IP"].FirstOrDefault();
            if (!string.IsNullOrEmpty(xRealIp))
            {
                return xRealIp;
            }

            // 返回远程IP地址
            return httpContext.Connection.RemoteIpAddress?.ToString() ?? "Unknown";
        }

        /// <summary>
        /// 判断是否为静态文件请求
        /// </summary>
        /// <param name="path">请求路径</param>
        /// <returns>是否为静态文件</returns>
        private static bool IsStaticFile(PathString path)
        {
            var staticExtensions = new[] { ".css", ".js", ".png", ".jpg", ".jpeg", ".gif", ".ico", ".svg", ".woff", ".woff2", ".ttf", ".eot" };
            return staticExtensions.Any(ext => path.Value?.EndsWith(ext, StringComparison.OrdinalIgnoreCase) == true);
        }
    }
}
