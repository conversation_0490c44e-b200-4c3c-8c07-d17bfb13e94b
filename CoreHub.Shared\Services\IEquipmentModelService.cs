using CoreHub.Shared.Models.Database;

namespace CoreHub.Shared.Services
{
    /// <summary>
    /// 设备型号服务接口
    /// </summary>
    public interface IEquipmentModelService
    {
        /// <summary>
        /// 获取所有设备型号
        /// </summary>
        Task<List<EquipmentModel>> GetAllEquipmentModelsAsync();

        /// <summary>
        /// 根据ID获取设备型号
        /// </summary>
        Task<EquipmentModel?> GetEquipmentModelByIdAsync(int id);

        /// <summary>
        /// 根据编码获取设备型号
        /// </summary>
        Task<EquipmentModel?> GetEquipmentModelByCodeAsync(string code);

        /// <summary>
        /// 创建设备型号
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> CreateEquipmentModelAsync(EquipmentModel equipmentModel);

        /// <summary>
        /// 更新设备型号
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> UpdateEquipmentModelAsync(EquipmentModel equipmentModel);

        /// <summary>
        /// 删除设备型号
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> DeleteEquipmentModelAsync(int id);

        /// <summary>
        /// 切换设备型号状态
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> ToggleStatusAsync(int id);

        /// <summary>
        /// 获取启用的设备型号列表
        /// </summary>
        Task<List<EquipmentModel>> GetEnabledEquipmentModelsAsync();

        /// <summary>
        /// 根据类别获取设备型号
        /// </summary>
        Task<List<EquipmentModel>> GetEquipmentModelsByCategoryAsync(string category);

        /// <summary>
        /// 获取所有设备类别
        /// </summary>
        Task<List<string>> GetAllCategoriesAsync();

        /// <summary>
        /// 检查设备型号编码是否存在
        /// </summary>
        Task<bool> IsCodeExistsAsync(string code, int? excludeId = null);
    }
}
