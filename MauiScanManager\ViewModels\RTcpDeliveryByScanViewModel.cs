﻿using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using MauiScanManager.Models;
using MauiScanManager.Services;
using Microsoft.Maui.Devices.Sensors;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Reflection.PortableExecutable;
using System.Runtime.CompilerServices;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Windows.Input;
using static System.Net.Mime.MediaTypeNames;



namespace MauiScanManager.ViewModels
{

    public enum CPSendScanStep { Name, Destination, inCarNo, inBatch }

    public partial class RTcpDeliveryByScanViewModel : BaseOperationViewModel
    {
        private readonly IAudioService _audioService;
        private readonly ICPDeliveryService _cpService;
        private readonly IPlatformLoadingService _loadingService;

        private string Jw = string.Empty;
        private string TakeSamples = string.Empty;
        private string IssendSS = string.Empty;
        private string YarnTypeCD = string.Empty;
        private string IsBatchNoOK = string.Empty;
        private string YarnTypeR2R = string.Empty;

        private string ReturnMsg;

        private bool IsCheck;

        [ObservableProperty]
        private string qcSuggestion = string.Empty;

         private string curDept = string.Empty;

        [ObservableProperty]
        private string carNO = string.Empty;

        [ObservableProperty]
        [NotifyPropertyChangedFor(nameof(IsPickerEnabled))]
        private string workerName = "";

       // [ObservableProperty]
        
        private ObservableCollection<string> batchNoList = new();

        [ObservableProperty]
        private string batchNO = string.Empty;

        public ObservableCollection<RTcpMaterialFlowDept> cpCurDeptList { get; } = new();
      
        private ObservableCollection<string> FiveBatchNOList { get; } = new();

        [ObservableProperty]
        private ObservableCollection<string> carNOs = new();

        [ObservableProperty]
        private CPSendScanStep scanStep = CPSendScanStep.Name;

        [ObservableProperty]
        [NotifyPropertyChangedFor(nameof(IsCarNOEnabled))]
        private string scanPrompt = "请输入工号";

        [ObservableProperty]
        [NotifyCanExecuteChangedFor(nameof(SaveCPAutoDeliveryByScanCommand))]
        private ObservableCollection<string> batchNoCount = new();

        [ObservableProperty]
        [NotifyCanExecuteChangedFor(nameof(SaveCPAutoDeliveryByScanCommand))]
        private bool isBusy;

        //[ObservableProperty]
        //public bool isPickerEnabled;

        [ObservableProperty]
        [NotifyPropertyChangedFor(nameof(IsCarNOEnabled))]
        private RTcpMaterialFlowDept selectedCurDept;

        [ObservableProperty]
        private bool isSwitchToggled;

 
        public bool CanSaveCPAutoDeliveryByScan() => !string.IsNullOrWhiteSpace(curDept) && BatchNoCount.Count > 0 && !IsBusy;

        public bool IsCarNOEnabled => SelectedCurDept != null && (ScanPrompt == "请扫描车号");
        public bool IsPickerEnabled => WorkerName != null && WorkerName.Contains('-');

        //public bool IsSwitchToggled => isSwitch;

        public RTcpDeliveryByScanViewModel(
            IScanService scanService,
            IDialogService dialogService,
            IAudioService audioService,
            ICPDeliveryService cpService,
            IPlatformLoadingService loadingService)
            : base(scanService, dialogService)
        {
            _audioService = audioService;
            _cpService = cpService;
            _loadingService = loadingService;
            //carNoList = new ObservableCollection<string> { "架子", "小缸", "小样" };
           
            batchNoCount.CollectionChanged += (s, e) => SaveCPAutoDeliveryByScanCommand.NotifyCanExecuteChanged();
        }

        public override async void Initialize(Operation operation)
        {
            base.Initialize(operation);
            ResetScanState();
            await GetMaterialFlowDept();
        }

        private void ResetScanState()
        {

            if (!WorkerName.Contains('-'))
            {
                ChangeToNameStep();
            }
            else
            {
                ChangeToDestinationStep();
            }
            //carNoList.Clear();
            curDept = string.Empty;
            CarNO = string.Empty;
            BatchNO = string.Empty;
            IsSwitchToggled = false;
          //  isSwitch = false;
            batchNoList.Clear();
            BatchNoCount.Clear();
            QcSuggestion = string.Empty;
            CarNOs.Clear();
         //   IsPickerEnabled = false;
            

        }

        private async Task GetMaterialFlowDept()
        {
            try
            {
                var result = await _cpService.GetMaterialFlowDeptAsync();
                if (!result.IsSuccess)
                {
                    await _dialogService.ShowErrorAsync(result.ErrorMessage);
                    return;
                }
                cpCurDeptList.Clear();
                foreach (var Dept in result.Data)
                {
                    cpCurDeptList.Add(Dept);
                }

            }
            catch (Exception ex)
            {
                await _dialogService.ShowErrorAsync($"获取出货地点失败：{GetFriendlyErrorMessage(ex)}");
            }
            finally
            {
                _loadingService?.HideNativeLoading();
            }
        }

        protected override void ProcessScanResult(string code, string type, byte[] codeSource)
        {
            if (string.IsNullOrEmpty(code)) return;

            MainThread.BeginInvokeOnMainThread(async () =>
            {
                try
                {
                    await ProcessScanStateAsync(code);
                }
                catch (Exception ex)
                {
                    await _dialogService.ShowErrorAsync($"处理扫描结果失败：{ex.Message}");
                }
            });
        }

        private async Task ProcessScanStateAsync(string code)
        {

            switch (ScanStep)
            {
                case CPSendScanStep.Name:
                    await GetWorkerName(code);
                    if (WorkerName.Contains('-'))
                    {
                        ChangeToDestinationStep();
                    }
                    break;
                case CPSendScanStep.Destination:
                    //CurDept = code;
                    //CarNO = "";
                    //BatchNO = string.Empty;
                    //batchNoList.Clear();
                    ChangeToCarNoStep();
                    break;

                case CPSendScanStep.inCarNo:
                    await AddCarNo(code);
                    break;

                case CPSendScanStep.inBatch:
                    await AddBatchNo(code);
                    break;

            }


        }

        private string GetFriendlyErrorMessage(Exception ex)
        {
            if (ex.Message.Contains("HttpClient.Timeout") ||
                ex.Message.Contains("The request was canceled"))
            {
                return "网络请求超时，请检查网络连接后重试";
            }
            return ex.Message;
        }

        private void ChangeToNameStep()
        {
            //IsPickerEnabled = false;
            curDept = string.Empty;
            ScanStep = CPSendScanStep.Name;
            ScanPrompt = "请输入工号";
        }


        private void ChangeToDestinationStep()
        {
         //   IsPickerEnabled = true;
            ScanStep = CPSendScanStep.Destination;
            ScanPrompt = "请选择交地";
        }

        private void ChangeToCarNoStep()
        {
  
            ScanStep = CPSendScanStep.inCarNo;
            ScanPrompt = "请扫描车号";
        }
        private void ChangeToBatchNoStep()
        {
            if (IsSwitchToggled)
            {
                ScanStep = CPSendScanStep.inCarNo;
                ScanPrompt = "请扫描车号";
            }
            else
            {
             
                ScanStep = CPSendScanStep.inBatch;
                ScanPrompt = "请扫描缸号";
            }
        }

        partial void OnWorkerNameChanged(string value)
        {
            if (!string.IsNullOrWhiteSpace(value))
            {
                if (WorkerName.Contains("-"))
                {
                    ChangeToDestinationStep();
                }
            }
            else
            {
                ChangeToNameStep();
            }

            
        }


        //显示五层样，套筒等缸号提示
        private async Task GetFiveLayersByBatchNO(string bybatchNO)
        {
            if (string.IsNullOrEmpty(bybatchNO)) return;

            try
            {
                IsCheck = true; ;
                _loadingService?.ShowNativeLoading();


                var model = new RTcpDeliveryCheck { BatchNO = bybatchNO };
                var result = await _cpService.GetFiveLayersByBatchNOAsync(model);

                if (!result.IsSuccess)
                {
                    await _dialogService.ShowErrorAsync(result.ErrorMessage);
                    IsCheck = false;
                    return;
                }

                if (result.Data == null) { IsCheck = false; return; }

                Jw = result.Data.JW;
                TakeSamples = result.Data.TakeSamples;
                IssendSS = result.Data.sendSS;
                QcSuggestion = result.Data.QCSuggestion;
                YarnTypeCD = result.Data.YarnTypeCD;
                YarnTypeR2R = result.Data.YarnTypeR2R;
                IsBatchNoOK = result.Data.IsBatchNoOK;

                if (IssendSS != "")
                {
                    await _dialogService.ShowWarningAsync($"{IssendSS}");
                }
                if (YarnTypeCD != "")
                {
                    await _dialogService.ShowWarningAsync($"{YarnTypeCD}");
                }
                if (YarnTypeR2R != "")
                {
                    await _dialogService.ShowWarningAsync($"{YarnTypeR2R}");
                }
                if (IsBatchNoOK != "OK")
                {
                    await _dialogService.ShowWarningAsync($"{IsBatchNoOK}");
                    IsCheck = false;
                    return;
                }

                if (TakeSamples == "Y")
                {
                    await _dialogService.ShowWarningAsync("取五层样!");
                }

                if (YarnTypeCD == "出货到小样!")
                {
                    curDept = "PX";
                }

                if (curDept == "FT")
                {
                    if ((CarNO != "小缸" && CarNO != "小样") && Jw != "J")
                    {
                        await _dialogService.ShowWarningAsync("请选择正确交地!");
                        IsCheck = false;
                        return;
                    }
                }

            }
            catch (Exception ex)
            {
                await _dialogService.ShowErrorAsync($"检查五层样信息失败：{GetFriendlyErrorMessage(ex)}");
                IsCheck = false;
            }
            finally
            {
                _loadingService?.HideNativeLoading();

            }
        }

        //检查车号
        private async Task CheckRtCarNo(string byCarNo)
        {
            if (string.IsNullOrEmpty(byCarNo))
            {
                IsCheck = false;
                return;
            }
            try
            {
                _loadingService?.ShowNativeLoading();


                var model = new RTcpDeliveryCheck { CarNo = byCarNo };
                var result = await _cpService.CheckRtCarNoAsync(model);

                if (!result.IsSuccess)
                {
                    await _dialogService.ShowErrorAsync(result.ErrorMessage);
                    IsCheck = false;
                    return;
                }

                if (result.Data == null)
                {
                    IsCheck = false;
                    return;
                }

                if (result.Data.ReturnMsg != "OK")
                {
                    await _dialogService.ShowWarningAsync($"{result.Data.ReturnMsg}");
                    IsCheck = false;
                    return;
                }
            }
            catch (Exception ex)
            {
                IsCheck = false;
                await _dialogService.ShowErrorAsync($"车号格式未通过：{GetFriendlyErrorMessage(ex)}");
            }
            finally
            {
                _loadingService?.HideNativeLoading();
            }
            IsCheck = true;
        }

        //检查缸号是否能出货
        private async Task CheckCanDelivery(string byBatchNO, string byCarNo)
        {
            IsCheck = true;
            if (string.IsNullOrEmpty(byBatchNO))
            {
                IsCheck = false;
                return;
            }

            try
            {

                _loadingService?.ShowNativeLoading();


                var model = new RTcpDeliveryCheck { BatchNO = byBatchNO, CarNo = byCarNo };
                var result = await _cpService.CheckCanDeliveryAsync(model);

                if (!result.IsSuccess)
                {
                    await _dialogService.ShowErrorAsync(result.ErrorMessage);
                    IsCheck = false;
                    return;
                }

                if (result.Data == null)
                {
                    IsCheck = false;
                    return;
                }


                if (result.Data.ReturnMsg != "OK")
                {
                    await _dialogService.ShowWarningAsync(result.Data.ReturnMsg);
                    IsCheck = false;
                    return;
                }
            }
            catch (Exception ex)
            {
                await _dialogService.ShowErrorAsync($"缸号位置检查未通过：{GetFriendlyErrorMessage(ex)}");
                IsCheck = false;
                return;
            }
            finally
            {
                _loadingService?.HideNativeLoading();
            }
        }

        //获取操作工姓名
        private async Task GetWorkerName(string Number)
        {
            if (string.IsNullOrEmpty(Number)) return;

            try
            {
                _loadingService?.ShowNativeLoading();


                var model = new RTcpDeliveryCheck { BatchNO = Number};
                var result = await _cpService.GetWorkerNameAsync(model);

                if (!result.IsSuccess)
                {
                    await _dialogService.ShowErrorAsync(result.ErrorMessage);
                    return;
                }

                if (result.Data == null) return;

                WorkerName = result.Data.WorkerName;
            }
            catch (Exception ex)
            {
                await _dialogService.ShowErrorAsync($"获取员工姓名失败：{GetFriendlyErrorMessage(ex)}");
            }
            finally
            {
                _loadingService?.HideNativeLoading();

            }
        }

        //获取待取五层样缸号
        private async Task GetFiveBatchNO(string byBatchNO)
        {
            if (string.IsNullOrEmpty(byBatchNO)) return;
            try
            {
                _loadingService?.ShowNativeLoading();


                var model = new RTcpDeliveryCheck { BatchNO = byBatchNO };
                var result = await _cpService.GetFiveBatchNOAsync(model);

                if (!result.IsSuccess)
                {
                    await _dialogService.ShowErrorAsync(result.ErrorMessage);
                    return;
                }

                if (result.Data == null) return;

                FiveBatchNOList.Clear();
                foreach (var Dept in result.Data.BatchNO)
                {
                    FiveBatchNOList.Add(Dept.ToString());
                }
            }
            catch (Exception ex)
            {
                await _dialogService.ShowErrorAsync($"获取待取五层样缸号失败：{GetFriendlyErrorMessage(ex)}");
            }
            finally
            {
                _loadingService?.HideNativeLoading();
            }
        }


        private async Task AddCarNo(string addCarNo)
        {
            if (string.IsNullOrWhiteSpace(addCarNo))
                return;
            if (!CarNOs.Contains(addCarNo) )
            {
                //检查车号
                await CheckRtCarNo(addCarNo);
                if (!IsCheck) return;
                    CarNOs.Add(addCarNo);
                         //     CarNO = addCarNo + "," + CarNO;
                CarNO = "";
            }
            ChangeToBatchNoStep();
        }

        private async Task AddBatchNo(string addBatchNo)
        {
            if (string.IsNullOrWhiteSpace(addBatchNo))
                return;

            if (!BatchNoCount.Contains(addBatchNo))
            {

                //检查交地
                if (addBatchNo[..1] != "K" && curDept == "RT")
                {
                    await _dialogService.ShowInfoAsync("请选择正确交地!");
                    return;
                }
                if (addBatchNo[..1] != "Z" && curDept == "SS")
                {
                    await _dialogService.ShowInfoAsync("请选择正确交地!");
                    return;
                }
                //检查缸号对应位置
                if (CarNOs.Contains("小缸") || CarNOs.Contains("小样"))
                    foreach (var item in CarNOs)
                    {
                        await CheckCanDelivery(addBatchNo, item);
                        if (!IsCheck) return;
                    }
                //检查提示和是否能出货
                    await GetFiveLayersByBatchNO(addBatchNo);

                if (IsCheck)
                {

                    if (CarNOs.Count > 0)
                    {
                        foreach (var item in CarNOs)
                        {
                            batchNoList.Add(addBatchNo + ";" + item);
                        }
                    }
                    BatchNoCount.Add(addBatchNo);

                    ScanPrompt = "请保存！";
                }
            }
        }



        //保存
        [RelayCommand(CanExecute = nameof(SaveCPAutoDeliveryByScan))]
        private async Task SaveCPAutoDeliveryByScanAsync()
        {
            if (string.IsNullOrWhiteSpace(curDept) || BatchNoCount.Count == 0)
            {
                await _dialogService.ShowWarningAsync("交地和缸号不能为空");
                return;
            }
            IsBusy = true;
            try
            {
                _loadingService?.ShowNativeLoading("正在保存...");
                var batchNoStr = string.Join(",", batchNoList);

  
                var model = new RTcpDeliveryByScan
                {
                    BatchNoList = batchNoStr,
                    Destination = curDept,
                    WorkerName = WorkerName
                };

                var result = await _cpService.SaveCPAutoDeliveryByScanAsync(model);
                if (!result.IsSuccess)
                {
                    await _dialogService.ShowErrorAsync(result.ErrorMessage);
                    return;
                }

                ResetScanState();
                await _dialogService.ShowSuccessAsync($"出货成功！");
            }
            catch (Exception ex)
            {
                await _dialogService.ShowErrorAsync($"染台出货失败：{ex.Message}");
            }
            finally
            {
                IsBusy = false;
                _loadingService?.HideNativeLoading();
            }
        }
        public bool SaveCPAutoDeliveryByScan() => !string.IsNullOrWhiteSpace(curDept) && BatchNoCount.Count > 0 && !IsBusy;

        //获取操作工号
        [RelayCommand]
        private async Task AddWorkerNameAsync()
        {
            if (string.IsNullOrWhiteSpace(WorkerName))
                return;

            if (!WorkerName.Contains('-'))
            {
                await GetWorkerName(WorkerName);
                if (WorkerName.Contains('-'))
                {
                    ChangeToDestinationStep();
                }
            }

        }

        //[RelayCommand]
        //private async Task CurDeptChangedAsync()
        //{
        //  //  if (string.IsNullOrEmpty(CurDept)) return;
        //    CarNO = string.Empty;
        //    BatchNO = string.Empty;
        //    IsSwitchToggled = false;
        //    batchNoList.Clear();
        //    BatchNoCount.Clear();
        //    QcSuggestion = string.Empty;
        //    ChangeToCarNoStep();
        //}

        [RelayCommand]
        private async Task AddCarNoAsync()
        {
            await AddCarNo(CarNO);

        }

        [RelayCommand]
        private async Task AddBatchNoAsync()
        {
            await AddBatchNo(BatchNO);

        }

        [RelayCommand]
        private async Task ClearStateAsync()
        {
            bool confirm = await _dialogService.ShowConfirmAsync(
                "确认清空",
                "确定要清空所有输入吗？",
                "确定",
                "取消");
            if (!confirm) return;
            ResetScanState();
        }
        [RelayCommand]
        private async Task BatchNoFocusedAsync()
        {
            if (IsSwitchToggled)
            {
                IsSwitchToggled = false;
            }
        }
      
        [RelayCommand]
        private async Task SwitchToggledAsync()
        {
           // isSwitch = IsSwitchToggled;
            if (ScanPrompt == "请扫描缸号")
            {
                
                if (IsSwitchToggled)
                {
       
                    ChangeToCarNoStep();
                }
            }
            if (ScanPrompt == "请扫描车号")
            {
                if (!IsSwitchToggled)
                {
               
                    ChangeToBatchNoStep();
                }
            }
        }

       
        partial void OnSelectedCurDeptChanged(RTcpMaterialFlowDept value)
        {
            curDept = value.Code;
            CarNO = string.Empty;
            BatchNO = string.Empty;
              IsSwitchToggled = false;
       //     isSwitch = false;
            batchNoList.Clear();
            BatchNoCount.Clear();
            QcSuggestion = string.Empty;
            ChangeToCarNoStep();
        }

        //private RTcpMaterialFlowDept _selectedCurDept;
        //public RTcpMaterialFlowDept SelectedCurDept
        //{
        //    get => _selectedCurDept;
        //    set
        //    {
        //        _selectedCurDept = value;
        //        curDept = SelectedCurDept.Code;
        //    }
        //}
    }

}
