﻿using CustomerWebAPI.Models;
using CustomerWebAPI.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;

namespace CustomerWebAPI.Controllers
{
    [Authorize, Route("api/[controller]"), ApiController]
    public class ASNController : Controller
    {
        private readonly IASNService _asnService;

        public ASNController(IASNService asnService)
        {
            _asnService = asnService;
        }

        [HttpPost]
        [ProducesResponseType(typeof(TrackingIdModel), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
        public async Task<IActionResult> SendASN([FromBody] DeliveryNoteModel deliveryNote)
        {
            (bool IsSuccess, string ErrorMsg, TrackingIdModel ASNResponseFromTAL) sendResult = 
                await _asnService.SendAsnToTalAsync(deliveryNote);

            if (sendResult.IsSuccess)
            {
                return Ok(sendResult.ASNResponseFromTAL);
            }

            return BadRequest(sendResult.ErrorMsg);
        }
    }
}
