using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using CustomerWebAPI.Services;
using CustomerWebAPI.Models;
using CustomerWebAPI.Common;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;

namespace CustomerWebAPI.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class DepartmentsController : ControllerBase
    {
        private readonly IDepartmentService _departmentService;
        private readonly ILogger<DepartmentsController> _logger;

        public DepartmentsController(
            IDepartmentService departmentService,
            ILogger<DepartmentsController> logger)
        {
            _departmentService = departmentService ?? throw new ArgumentNullException(nameof(departmentService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        [HttpGet]
        [ProducesResponseType(typeof(ApiResponse<List<Department>>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ApiResponse<List<Department>>), StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<ApiResponse<List<Department>>>> GetDepartments()
        {
            var result = await _departmentService.GetDepartmentsAsync();
            return result.Success ? Ok(result) : BadRequest(result);
        }
    }
}
