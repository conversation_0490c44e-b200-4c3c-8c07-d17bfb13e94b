using MauiScanManager.Services;
using Microsoft.Maui.Controls;

namespace MauiScanManager.Pages;

public partial class PrintTestPage : ContentPage
{
    private readonly IPrintService _printService;

    public PrintTestPage(IPrintService printService)
    {
        InitializeComponent();
        _printService = printService;
        
        // 设置默认值
        AlignPicker.SelectedIndex = 1; // Center
        FontSizePicker.SelectedIndex = 1; // Normal
        BoldCheckBox.IsChecked = true;
        BarcodeTypePicker.SelectedIndex = 0; // Code128
        HRIPositionPicker.SelectedIndex = 1; // 下方
        QRCodeAlignPicker.SelectedIndex = 1; // 居中
        BlackMarkCheckBox.IsChecked = true; // 默认启用黑标打印
        
        // 更新状态
        UpdateStatus();
    }

    private void UpdateStatus()
    {
        StatusLabel.Text = _printService.IsReady ? "已就绪" : "未就绪";
        StatusLabel.TextColor = _printService.IsReady ? Colors.Green : Colors.Red;
    }

    private void DisplayPrintResult(PrintEventArgs e)
    {
        // 主要结果显示
        ResultLabel.Text = e.Message;
        ResultLabel.TextColor = e.Result == PrintResult.Success ? Colors.Green : Colors.Red;
        
        // 详细信息显示
        var resultText = GetPrintResultDescription(e.Result);
        var errorCodeText = GetErrorCodeDescription(e.ErrorCode);
        DetailLabel.Text = $"结果: {resultText} | 错误代码: {e.ErrorCode} ({errorCodeText})";
        DetailLabel.TextColor = e.Result == PrintResult.Success ? Colors.DarkGreen : Colors.DarkRed;
        
        // 时间戳显示
        TimestampLabel.Text = $"时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}";
        TimestampLabel.TextColor = Colors.Gray;
    }

    private string GetPrintResultDescription(PrintResult result)
    {
        return result switch
        {
            PrintResult.Success => "成功",
            PrintResult.NoPaper => "缺纸",
            PrintResult.DeviceBusy => "设备忙",
            PrintResult.DeviceNotOpen => "设备未打开",
            PrintResult.DataError => "数据错误",
            PrintResult.CommandError => "指令错误",
            PrintResult.UnknownError => "未知错误",
            _ => "未定义错误"
        };
    }

    private string GetErrorCodeDescription(int errorCode)
    {
        return errorCode switch
        {
            0 => "无错误",
            1 => "设备忙",
            2 => "高温",
            3 => "缺纸",
            4 => "低电",
            5 => "正在走纸",
            6 => "正在打印",
            7 => "黑标检测异常",
            16 => "设备未打开",
            17 => "打印数据不能为空",
            18 => "数据非法",
            19 => "指令错误",
            20 => "浓度非法",
            160 => "打印文本错误",
            161 => "打印位图错误",
            162 => "打印条码错误",
            163 => "打印二维码错误",
            164 => "打印位图宽度溢出",
            165 => "输入参数错误",
            166 => "参数错误",
            167 => "Mac校验错误",
            168 => "结果已存在",
            169 => "超时",
            255 => "未知错误",
            _ => "未定义的错误代码"
        };
    }

    private async void OnPrintSimpleTextClicked(object sender, EventArgs e)
    {
        if (string.IsNullOrWhiteSpace(SimpleTextEntry.Text))
        {
            await DisplayAlert("错误", "请输入要打印的文本", "确定");
            return;
        }

        ResultLabel.Text = "正在打印...";
        ResultLabel.TextColor = Colors.Orange;
        DetailLabel.Text = "";
        TimestampLabel.Text = "";

        try
        {
            bool enableBlackMark = BlackMarkCheckBox.IsChecked;
            var result = await _printService.PrintTextAndWaitAsync(SimpleTextEntry.Text + "\n", new PrintConfig(), enableBlackMark);
            DisplayPrintResult(result);
        }
        catch (Exception ex)
        {
            DisplayPrintResult(new PrintEventArgs
            {
                Result = PrintResult.UnknownError,
                Message = "打印异常",
                ErrorCode = 255
            });
        }
    }

    private async void OnPrintFormattedTextClicked(object sender, EventArgs e)
    {
        if (string.IsNullOrWhiteSpace(FormattedTextEntry.Text))
        {
            await DisplayAlert("错误", "请输入要打印的文本", "确定");
            return;
        }

        var config = new PrintConfig
        {
            Align = AlignPicker.SelectedIndex switch
            {
                0 => PrintConfig.TextAlign.Left,
                1 => PrintConfig.TextAlign.Center,
                2 => PrintConfig.TextAlign.Right,
                _ => PrintConfig.TextAlign.Left
            },
            Size = FontSizePicker.SelectedIndex switch
            {
                0 => PrintConfig.FontSize.Small,
                1 => PrintConfig.FontSize.Normal,
                2 => PrintConfig.FontSize.Large,
                _ => PrintConfig.FontSize.Normal
            },
            Bold = BoldCheckBox.IsChecked,
            Underline = UnderlineCheckBox.IsChecked
        };

        ResultLabel.Text = "正在打印...";
        ResultLabel.TextColor = Colors.Orange;
        DetailLabel.Text = "";
        TimestampLabel.Text = "";

        try
        {
            bool enableBlackMark = BlackMarkCheckBox.IsChecked;
            var result = await _printService.PrintTextAndWaitAsync(FormattedTextEntry.Text + "\n", config, enableBlackMark);
            DisplayPrintResult(result);
        }
        catch (Exception ex)
        {
            DisplayPrintResult(new PrintEventArgs
            {
                Result = PrintResult.UnknownError,
                Message = "打印异常",
                ErrorCode = 255
            });
        }
    }

    private async void OnPrintBarcodeClicked(object sender, EventArgs e)
    {
        var content = BarcodeEntry.Text?.Trim();
        if (string.IsNullOrEmpty(content))
        {
            await DisplayAlert("错误", "请输入条码内容", "确定");
            return;
        }

        var selectedIndex = BarcodeTypePicker.SelectedIndex;
        if (selectedIndex < 0) selectedIndex = 0; // 默认选择Code128
        
        var barcodeTypes = new[] { BarcodeType.Code128, BarcodeType.Code39, BarcodeType.Ean13, 
                                 BarcodeType.Ean8, BarcodeType.UpcA, BarcodeType.Itf };
        var selectedType = barcodeTypes[selectedIndex];

        var hriIndex = HRIPositionPicker.SelectedIndex;
        if (hriIndex < 0) hriIndex = 1; // 默认选择下方
        
        var hriPositions = new[] { HRIPosition.None, HRIPosition.Below, HRIPosition.Above };
        var selectedHRIPosition = hriPositions[hriIndex];
        
        bool enableBlackMark = BlackMarkCheckBox.IsChecked;
        
        ResultLabel.Text = "正在打印条码...";
        ResultLabel.TextColor = Colors.Orange;
        DetailLabel.Text = "";
        TimestampLabel.Text = "";
        
        try
        {
            var result = await _printService.PrintBarcodeAndWaitAsync(content, selectedType, selectedHRIPosition, enableBlackMark);
            DisplayPrintResult(result);
        }
        catch (Exception ex)
        {
            DisplayPrintResult(new PrintEventArgs
            {
                Result = PrintResult.UnknownError,
                Message = "打印异常",
                ErrorCode = 255
            });
        }
    }

    private async void OnPrintQRCodeClicked(object sender, EventArgs e)
    {
        var content = QRCodeEntry.Text?.Trim();
        if (string.IsNullOrEmpty(content))
        {
            await DisplayAlert("错误", "请输入二维码内容", "确定");
            return;
        }

        // 获取用户输入的二维码高度
        if (!int.TryParse(QRCodeHeightEntry.Text, out int height))
        {
            height = 184; // 如果解析失败，使用默认高度
        }

        // 获取对齐方式
        var alignIndex = QRCodeAlignPicker.SelectedIndex;
        if (alignIndex < 0) alignIndex = 1; // 默认选择居中
        
        var alignOptions = new[] { PrintConfig.TextAlign.Left, PrintConfig.TextAlign.Center, PrintConfig.TextAlign.Right };
        var selectedAlign = alignOptions[alignIndex];

        bool enableBlackMark = BlackMarkCheckBox.IsChecked;
        
        ResultLabel.Text = "正在打印二维码...";
        ResultLabel.TextColor = Colors.Orange;
        DetailLabel.Text = "";
        TimestampLabel.Text = "";
        
        try
        {
            var result = await _printService.PrintQRCodeAndWaitAsync(content, selectedAlign, height, enableBlackMark);
            DisplayPrintResult(result);
        }
        catch (Exception ex)
        {
            DisplayPrintResult(new PrintEventArgs
            {
                Result = PrintResult.UnknownError,
                Message = "打印异常",
                ErrorCode = 255
            });
        }
    }

    private async void OnPrintReceiptClicked(object sender, EventArgs e)
    {
        var template = new PrintTemplate
        {
            Title = "停车缴费小票",
            Items = new List<PrintTemplate.PrintItem>
            {
                new PrintTemplate.PrintItem
                {
                    Type = PrintTemplate.PrintItemType.Text,
                    Content = "收费单位：深圳市xxx科技\n",
                    Config = new PrintConfig { Align = PrintConfig.TextAlign.Left, Size = PrintConfig.FontSize.Normal }
                },
                new PrintTemplate.PrintItem
                {
                    Type = PrintTemplate.PrintItemType.Text,
                    Content = "收费员：张三\n",
                    Config = new PrintConfig { Align = PrintConfig.TextAlign.Left, Size = PrintConfig.FontSize.Normal }
                },
                new PrintTemplate.PrintItem
                {
                    Type = PrintTemplate.PrintItemType.Text,
                    Content = "道路名称：深南大道1008号\n",
                    Config = new PrintConfig { Align = PrintConfig.TextAlign.Left, Size = PrintConfig.FontSize.Normal }
                },
                new PrintTemplate.PrintItem
                {
                    Type = PrintTemplate.PrintItemType.Text,
                    Content = "车位号：B01\n",
                    Config = new PrintConfig { Align = PrintConfig.TextAlign.Left, Size = PrintConfig.FontSize.Normal }
                },
                new PrintTemplate.PrintItem
                {
                    Type = PrintTemplate.PrintItemType.Line,
                    Content = "1"
                },
                new PrintTemplate.PrintItem
                {
                    Type = PrintTemplate.PrintItemType.Text,
                    Content = "车牌：粤Bxxxxxx\n",
                    Config = new PrintConfig { Align = PrintConfig.TextAlign.Left, Size = PrintConfig.FontSize.Large, Bold = true }
                },
                new PrintTemplate.PrintItem
                {
                    Type = PrintTemplate.PrintItemType.Text,
                    Content = "车辆类型：小车\n",
                    Config = new PrintConfig { Align = PrintConfig.TextAlign.Left, Size = PrintConfig.FontSize.Normal }
                },
                new PrintTemplate.PrintItem
                {
                    Type = PrintTemplate.PrintItemType.Text,
                    Content = $"驶入时间：{DateTime.Now.AddHours(-5):yyyy-MM-dd HH:mm:ss}\n",
                    Config = new PrintConfig { Align = PrintConfig.TextAlign.Left, Size = PrintConfig.FontSize.Normal }
                },
                new PrintTemplate.PrintItem
                {
                    Type = PrintTemplate.PrintItemType.Text,
                    Content = $"驶离时间：{DateTime.Now:yyyy-MM-dd HH:mm:ss}\n",
                    Config = new PrintConfig { Align = PrintConfig.TextAlign.Left, Size = PrintConfig.FontSize.Normal }
                },
                new PrintTemplate.PrintItem
                {
                    Type = PrintTemplate.PrintItemType.Text,
                    Content = "停车时长：5小时10分钟\n",
                    Config = new PrintConfig { Align = PrintConfig.TextAlign.Left, Size = PrintConfig.FontSize.Normal }
                },
                new PrintTemplate.PrintItem
                {
                    Type = PrintTemplate.PrintItemType.Text,
                    Content = "------------------------------------------------------\n",
                    Config = new PrintConfig { Align = PrintConfig.TextAlign.Center, Size = PrintConfig.FontSize.Normal }
                },
                new PrintTemplate.PrintItem
                {
                    Type = PrintTemplate.PrintItemType.Text,
                    Content = "温馨提示\n",
                    Config = new PrintConfig { Align = PrintConfig.TextAlign.Center, Size = PrintConfig.FontSize.Normal }
                },
                new PrintTemplate.PrintItem
                {
                    Type = PrintTemplate.PrintItemType.Text,
                    Content = "请妥善保管此小票作为停车凭证\n",
                    Config = new PrintConfig { Align = PrintConfig.TextAlign.Left, Size = PrintConfig.FontSize.Normal }
                }
            }
        };

        ResultLabel.Text = "正在打印模板...";
        ResultLabel.TextColor = Colors.Orange;
        DetailLabel.Text = "";
        TimestampLabel.Text = "";

        try
        {
            bool enableBlackMark = BlackMarkCheckBox.IsChecked;
            var result = await _printService.PrintTemplateAndWaitAsync(template, enableBlackMark);
            DisplayPrintResult(result);
        }
        catch (Exception ex)
        {
            DisplayPrintResult(new PrintEventArgs
            {
                Result = PrintResult.UnknownError,
                Message = "打印异常",
                ErrorCode = 255
            });
        }
    }

    private async void OnPrintOrderClicked(object sender, EventArgs e)
    {
        var template = new PrintTemplate
        {
            Title = "订单信息",
            Items = new List<PrintTemplate.PrintItem>
            {
                new PrintTemplate.PrintItem
                {
                    Type = PrintTemplate.PrintItemType.Text,
                    Content = "订单详情\n",
                    Config = new PrintConfig { Align = PrintConfig.TextAlign.Center, Size = PrintConfig.FontSize.Large, Bold = true }
                },
                new PrintTemplate.PrintItem
                {
                    Type = PrintTemplate.PrintItemType.Line,
                    Content = "1"
                },
                new PrintTemplate.PrintItem
                {
                    Type = PrintTemplate.PrintItemType.Text,
                    Content = $"订单号：{DateTime.Now:yyyyMMddHHmmss}\n",
                    Config = new PrintConfig { Align = PrintConfig.TextAlign.Left, Size = PrintConfig.FontSize.Normal }
                },
                new PrintTemplate.PrintItem
                {
                    Type = PrintTemplate.PrintItemType.Text,
                    Content = $"下单时间：{DateTime.Now:yyyy-MM-dd HH:mm:ss}\n",
                    Config = new PrintConfig { Align = PrintConfig.TextAlign.Left, Size = PrintConfig.FontSize.Normal }
                },
                new PrintTemplate.PrintItem
                {
                    Type = PrintTemplate.PrintItemType.Text,
                    Content = "商品名称：测试商品\n",
                    Config = new PrintConfig { Align = PrintConfig.TextAlign.Left, Size = PrintConfig.FontSize.Normal }
                },
                new PrintTemplate.PrintItem
                {
                    Type = PrintTemplate.PrintItemType.Text,
                    Content = "数量：1\n",
                    Config = new PrintConfig { Align = PrintConfig.TextAlign.Left, Size = PrintConfig.FontSize.Normal }
                },
                new PrintTemplate.PrintItem
                {
                    Type = PrintTemplate.PrintItemType.Text,
                    Content = "单价：￥99.00\n",
                    Config = new PrintConfig { Align = PrintConfig.TextAlign.Left, Size = PrintConfig.FontSize.Normal }
                },
                new PrintTemplate.PrintItem
                {
                    Type = PrintTemplate.PrintItemType.Text,
                    Content = "总计：￥99.00\n",
                    Config = new PrintConfig { Align = PrintConfig.TextAlign.Right, Size = PrintConfig.FontSize.Large, Bold = true }
                }
            }
        };

        ResultLabel.Text = "正在打印订单...";
        ResultLabel.TextColor = Colors.Orange;
        DetailLabel.Text = "";
        TimestampLabel.Text = "";

        try
        {
            bool enableBlackMark = BlackMarkCheckBox.IsChecked;
            var result = await _printService.PrintTemplateAndWaitAsync(template, enableBlackMark);
            DisplayPrintResult(result);
        }
        catch (Exception ex)
        {
            DisplayPrintResult(new PrintEventArgs
            {
                Result = PrintResult.UnknownError,
                Message = "打印异常",
                ErrorCode = 255
            });
        }
    }

    private async void OnPrintBarcodeTemplateClicked(object sender, EventArgs e)
    {
        bool enableBlackMark = BlackMarkCheckBox.IsChecked;
        
        var template = new PrintTemplate
        {
            Title = "条码打印模板",
            Items = new List<PrintTemplate.PrintItem>
            {
                new PrintTemplate.PrintItem
                {
                    Type = PrintTemplate.PrintItemType.Text,
                    Content = "HRI位置演示\n",
                    Config = new PrintConfig
                    {
                        Align = PrintConfig.TextAlign.Center,
                        Size = PrintConfig.FontSize.Large,
                        Bold = true
                    }
                },
                new PrintTemplate.PrintItem
                {
                    Type = PrintTemplate.PrintItemType.Text,
                    Content = "1. 文本在下方（默认）",
                    Config = new PrintConfig
                    {
                        Align = PrintConfig.TextAlign.Left,
                        Size = PrintConfig.FontSize.Normal
                    }
                },
                new PrintTemplate.PrintItem
                {
                    Type = PrintTemplate.PrintItemType.Barcode,
                    Content = "123456789ABC",
                    BarcodeType = BarcodeType.Code128,
                    HRIPosition = HRIPosition.Below
                },
                new PrintTemplate.PrintItem
                {
                    Type = PrintTemplate.PrintItemType.Line,
                    Content = "1"
                },
                new PrintTemplate.PrintItem
                {
                    Type = PrintTemplate.PrintItemType.Text,
                    Content = "2. 文本在上方",
                    Config = new PrintConfig
                    {
                        Align = PrintConfig.TextAlign.Left,
                        Size = PrintConfig.FontSize.Normal
                    }
                },
                new PrintTemplate.PrintItem
                {
                    Type = PrintTemplate.PrintItemType.Barcode,
                    Content = "987654321XYZ",
                    BarcodeType = BarcodeType.Code128,
                    HRIPosition = HRIPosition.Above
                },
                new PrintTemplate.PrintItem
                {
                    Type = PrintTemplate.PrintItemType.Line,
                    Content = "1"
                },
                new PrintTemplate.PrintItem
                {
                    Type = PrintTemplate.PrintItemType.Text,
                    Content = "3. 不显示文本",
                    Config = new PrintConfig
                    {
                        Align = PrintConfig.TextAlign.Left,
                        Size = PrintConfig.FontSize.Normal
                    }
                },
                new PrintTemplate.PrintItem
                {
                    Type = PrintTemplate.PrintItemType.Barcode,
                    Content = "ABCDEF123456",
                    BarcodeType = BarcodeType.Code128,
                    HRIPosition = HRIPosition.None
                },
                new PrintTemplate.PrintItem
                {
                    Type = PrintTemplate.PrintItemType.Line,
                    Content = "2"
                },
                new PrintTemplate.PrintItem
                {
                    Type = PrintTemplate.PrintItemType.Text,
                    Content = "二维码信息",
                    Config = new PrintConfig
                    {
                        Align = PrintConfig.TextAlign.Center,
                        Size = PrintConfig.FontSize.Normal,
                        Bold = true
                    }
                },
                new PrintTemplate.PrintItem
                {
                    Type = PrintTemplate.PrintItemType.QRCode,
                    Content = "https://www.example.com/product/123456789ABC"
                }
            }
        };

        ResultLabel.Text = "正在打印条码模板...";
        ResultLabel.TextColor = Colors.Orange;
        DetailLabel.Text = "";
        TimestampLabel.Text = "";
        
        try
        {
            var result = await _printService.PrintTemplateAndWaitAsync(template, enableBlackMark);
            DisplayPrintResult(result);
        }
        catch (Exception ex)
        {
            DisplayPrintResult(new PrintEventArgs
            {
                Result = PrintResult.UnknownError,
                Message = "打印异常",
                ErrorCode = 255
            });
        }
    }

    private void OnInitializeClicked(object sender, EventArgs e)
    {
        _printService.Initialize();
        UpdateStatus();
        ResultLabel.Text = "打印服务已初始化";
        ResultLabel.TextColor = Colors.Blue;
    }

    private void OnDisposeClicked(object sender, EventArgs e)
    {
        _printService.Dispose();
        UpdateStatus();
        ResultLabel.Text = "打印服务已释放";
        ResultLabel.TextColor = Colors.Gray;
    }

    protected override void OnDisappearing()
    {
        base.OnDisappearing();
        // 不再需要取消订阅事件，因为我们使用的是新的等待方法
    }

    private async void OnPrintTextClicked(object sender, EventArgs e)
    {
        var text = SimpleTextEntry.Text?.Trim();
        if (string.IsNullOrEmpty(text))
        {
            await DisplayAlert("错误", "请输入要打印的文本", "确定");
            return;
        }

        bool enableBlackMark = BlackMarkCheckBox.IsChecked;
        
        var config = new PrintConfig
        {
            Align = PrintConfig.TextAlign.Center,
            Size = PrintConfig.FontSize.Normal,
            Bold = true,
            Underline = false
        };
        
        ResultLabel.Text = "正在打印文本...";
        ResultLabel.TextColor = Colors.Orange;
        DetailLabel.Text = "";
        TimestampLabel.Text = "";
        
        try
        {
            var result = await _printService.PrintTextAndWaitAsync(text, config, enableBlackMark);
            DisplayPrintResult(result);
        }
        catch (Exception ex)
        {
            DisplayPrintResult(new PrintEventArgs
            {
                Result = PrintResult.UnknownError,
                Message = "打印异常",
                ErrorCode = 255
            });
        }
    }
} 