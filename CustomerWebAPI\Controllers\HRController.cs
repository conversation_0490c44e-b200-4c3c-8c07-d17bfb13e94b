﻿using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using CustomerWebAPI.Models;
using CustomerWebAPI.Services;
using CustomerWebAPI.Common;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;


namespace CustomerWebAPI.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class HRController : ControllerBase
    {
        private readonly IHireWorkerService _hireWorkerService;
        private readonly ILogger<HRController> _logger;

        public HRController(IHireWorkerService hireWorkerService, ILogger<HRController> logger)
        {
            _hireWorkerService = hireWorkerService ?? throw new ArgumentNullException(nameof(hireWorkerService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        [HttpPost("HireScan")]
        [ProducesResponseType(typeof(ApiResponse<bool>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ApiResponse<bool>), StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<ApiResponse<bool>>> SaveHireScan([FromBody] HireWorkerScan model)
        {
            var result = await _hireWorkerService.SaveScanMobilePhoneNo(model);
            return result.Success ? Ok(result) : BadRequest(result);
        }
    }
}