using SqlSugar;
using System.ComponentModel.DataAnnotations;

namespace CoreHub.Shared.Models.Database
{
    /// <summary>
    /// 位置实体
    /// </summary>
    [SugarTable("Locations")]
    public class Location
    {
        /// <summary>
        /// 位置ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        /// <summary>
        /// 位置编码（唯一）
        /// </summary>
        [SugarColumn(Length = 50, IsNullable = false)]
        [Required(ErrorMessage = "位置编码不能为空")]
        [StringLength(50, ErrorMessage = "位置编码长度不能超过50个字符")]
        public string Code { get; set; } = string.Empty;

        /// <summary>
        /// 位置名称
        /// </summary>
        [SugarColumn(Length = 100, IsNullable = false)]
        [Required(ErrorMessage = "位置名称不能为空")]
        [StringLength(100, ErrorMessage = "位置名称长度不能超过100个字符")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 所属部门ID
        /// </summary>
        [SugarColumn(IsNullable = false)]
        [Required(ErrorMessage = "所属部门不能为空")]
        public int DepartmentId { get; set; }

        /// <summary>
        /// 父级位置ID
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? ParentId { get; set; }

        /// <summary>
        /// 位置级别
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int Level { get; set; } = 1;

        /// <summary>
        /// 详细地址
        /// </summary>
        [SugarColumn(Length = 200, IsNullable = true)]
        [StringLength(200, ErrorMessage = "详细地址长度不能超过200个字符")]
        public string? Address { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        [SugarColumn(Length = 500, IsNullable = true)]
        [StringLength(500, ErrorMessage = "描述长度不能超过500个字符")]
        public string? Description { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 创建时间
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 创建人ID
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? CreatedBy { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// 更新人ID
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? UpdatedBy { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [SugarColumn(Length = 1000, IsNullable = true)]
        [StringLength(1000, ErrorMessage = "备注长度不能超过1000个字符")]
        public string? Remark { get; set; }

        // 导航属性
        /// <summary>
        /// 所属部门
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public Department? Department { get; set; }

        /// <summary>
        /// 子位置
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public List<Location> Children { get; set; } = new List<Location>();

        /// <summary>
        /// 父位置
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public Location? Parent { get; set; }

        /// <summary>
        /// 该位置的设备列表
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public List<Equipment> Equipment { get; set; } = new List<Equipment>();
    }
}
