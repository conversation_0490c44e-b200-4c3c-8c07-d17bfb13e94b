using CoreHub.Shared.Data;
using CoreHub.Shared.Models.Database;
using Microsoft.Extensions.Logging;
using SqlSugar;

namespace CoreHub.Shared.Services
{
    /// <summary>
    /// 位置服务实现
    /// </summary>
    public class LocationService : ILocationService
    {
        private readonly DatabaseContext _dbContext;
        private readonly ILogger<LocationService> _logger;

        public LocationService(DatabaseContext dbContext, ILogger<LocationService> logger)
        {
            _dbContext = dbContext;
            _logger = logger;
        }

        public async Task<List<Location>> GetAllLocationsAsync()
        {
            try
            {
                return await _dbContext.Db.Queryable<Location>()
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取所有位置失败");
                throw;
            }
        }

        public async Task<Location?> GetLocationByIdAsync(int id)
        {
            try
            {
                return await _dbContext.Db.Queryable<Location>()
                    .Where(l => l.Id == id)
                    .FirstAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据ID获取位置失败: {id}", id);
                throw;
            }
        }

        public async Task<Location?> GetLocationByCodeAsync(string code)
        {
            try
            {
                return await _dbContext.Db.Queryable<Location>()
                    .Where(l => l.Code == code)
                    .FirstAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据编码获取位置失败: {code}", code);
                throw;
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage)> CreateLocationAsync(Location location)
        {
            try
            {
                // 检查编码是否存在
                if (await IsCodeExistsAsync(location.Code))
                {
                    return (false, "位置编码已存在");
                }

                // 检查部门是否存在
                var department = await _dbContext.Db.Queryable<Department>()
                    .Where(d => d.Id == location.DepartmentId && d.IsEnabled)
                    .FirstAsync();
                if (department == null)
                {
                    return (false, "所属部门不存在或已禁用");
                }

                // 设置级别
                if (location.ParentId.HasValue)
                {
                    var parent = await GetLocationByIdAsync(location.ParentId.Value);
                    if (parent == null)
                    {
                        return (false, "父级位置不存在");
                    }
                    location.Level = parent.Level + 1;
                }
                else
                {
                    location.Level = 1;
                }

                location.CreatedAt = DateTime.Now;
                var result = await _dbContext.Db.Insertable(location).ExecuteReturnIdentityAsync();

                _logger.LogInformation("创建位置成功: {name} ({code})", location.Name, location.Code);
                return (true, string.Empty);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建位置失败: {name}", location.Name);
                return (false, $"创建位置失败: {ex.Message}");
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage)> UpdateLocationAsync(Location location)
        {
            try
            {
                // 检查编码是否存在（排除自己）
                if (await IsCodeExistsAsync(location.Code, location.Id))
                {
                    return (false, "位置编码已存在");
                }

                // 检查部门是否存在
                var department = await _dbContext.Db.Queryable<Department>()
                    .Where(d => d.Id == location.DepartmentId && d.IsEnabled)
                    .FirstAsync();
                if (department == null)
                {
                    return (false, "所属部门不存在或已禁用");
                }

                // 设置级别
                if (location.ParentId.HasValue)
                {
                    var parent = await GetLocationByIdAsync(location.ParentId.Value);
                    if (parent == null)
                    {
                        return (false, "父级位置不存在");
                    }
                    if (parent.Id == location.Id)
                    {
                        return (false, "不能将自己设为父级位置");
                    }
                    location.Level = parent.Level + 1;
                }
                else
                {
                    location.Level = 1;
                }

                location.UpdatedAt = DateTime.Now;
                var result = await _dbContext.Db.Updateable(location).ExecuteCommandAsync();

                if (result > 0)
                {
                    _logger.LogInformation("更新位置成功: {name} ({code})", location.Name, location.Code);
                    return (true, string.Empty);
                }
                else
                {
                    return (false, "位置不存在或未发生变更");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新位置失败: {id}", location.Id);
                return (false, $"更新位置失败: {ex.Message}");
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage)> DeleteLocationAsync(int id)
        {
            try
            {
                // 检查是否有子位置
                var hasChildren = await _dbContext.Db.Queryable<Location>()
                    .Where(l => l.ParentId == id)
                    .AnyAsync();

                if (hasChildren)
                {
                    return (false, "该位置下还有子位置，无法删除");
                }

                // 检查是否有关联的设备
                var hasEquipment = await _dbContext.Db.Queryable<Equipment>()
                    .Where(e => e.LocationId == id)
                    .AnyAsync();

                if (hasEquipment)
                {
                    return (false, "该位置下还有设备，无法删除");
                }

                var result = await _dbContext.Db.Deleteable<Location>()
                    .Where(l => l.Id == id)
                    .ExecuteCommandAsync();

                if (result > 0)
                {
                    _logger.LogInformation("删除位置成功: {id}", id);
                    return (true, string.Empty);
                }
                else
                {
                    return (false, "位置不存在");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除位置失败: {id}", id);
                return (false, $"删除位置失败: {ex.Message}");
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage)> ToggleStatusAsync(int id)
        {
            try
            {
                var location = await GetLocationByIdAsync(id);
                if (location == null)
                {
                    return (false, "位置不存在");
                }

                location.IsEnabled = !location.IsEnabled;
                location.UpdatedAt = DateTime.Now;

                var result = await _dbContext.Db.Updateable(location)
                    .UpdateColumns(l => new { l.IsEnabled, l.UpdatedAt })
                    .ExecuteCommandAsync();

                if (result > 0)
                {
                    _logger.LogInformation("切换位置状态成功: {id} -> {status}", id, location.IsEnabled);
                    return (true, string.Empty);
                }
                else
                {
                    return (false, "操作失败");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "切换位置状态失败: {id}", id);
                return (false, $"操作失败: {ex.Message}");
            }
        }

        public async Task<List<Location>> GetEnabledLocationsAsync()
        {
            try
            {
                return await _dbContext.Db.Queryable<Location>()
                    .Where(l => l.IsEnabled)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取启用的位置列表失败");
                throw;
            }
        }

        public async Task<List<Location>> GetLocationsByDepartmentAsync(int departmentId)
        {
            try
            {
                return await _dbContext.Db.Queryable<Location>()
                    .Where(l => l.DepartmentId == departmentId && l.IsEnabled)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据部门获取位置失败: {departmentId}", departmentId);
                throw;
            }
        }

        public async Task<List<Location>> GetLocationTreeAsync()
        {
            try
            {
                var allLocations = await GetAllLocationsAsync();
                return BuildLocationTree(allLocations);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取位置树形结构失败");
                throw;
            }
        }

        public async Task<List<LocationDetailDto>> GetLocationDetailsAsync()
        {
            try
            {
                // 简化查询，避免复杂的联表操作
                var locations = await _dbContext.Db.Queryable<Location>().ToListAsync();
                var departments = await _dbContext.Db.Queryable<Department>().ToListAsync();
                var equipmentCounts = await _dbContext.Db.Queryable<Equipment>()
                    .GroupBy(e => e.LocationId)
                    .Select(g => new { LocationId = g.LocationId, Count = SqlFunc.AggregateCount(g.Id) })
                    .ToListAsync();

                var result = new List<LocationDetailDto>();
                foreach (var location in locations)
                {
                    var department = departments.FirstOrDefault(d => d.Id == location.DepartmentId);
                    var parent = locations.FirstOrDefault(l => l.Id == location.ParentId);
                    var equipmentCount = equipmentCounts.FirstOrDefault(ec => ec.LocationId == location.Id)?.Count ?? 0;

                    result.Add(new LocationDetailDto
                    {
                        Id = location.Id,
                        Code = location.Code,
                        Name = location.Name,
                        DepartmentId = location.DepartmentId,
                        DepartmentName = department?.Name ?? "",
                        ParentId = location.ParentId,
                        ParentName = parent?.Name,
                        Level = location.Level,
                        Address = location.Address,
                        Description = location.Description,
                        IsEnabled = location.IsEnabled,
                        CreatedAt = location.CreatedAt,
                        Remark = location.Remark,
                        EquipmentCount = equipmentCount
                    });
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取位置详细信息失败");
                throw;
            }
        }

        public async Task<bool> IsCodeExistsAsync(string code, int? excludeId = null)
        {
            try
            {
                var query = _dbContext.Db.Queryable<Location>()
                    .Where(l => l.Code == code);

                if (excludeId.HasValue)
                {
                    query = query.Where(l => l.Id != excludeId.Value);
                }

                return await query.AnyAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查位置编码是否存在失败: {code}", code);
                throw;
            }
        }

        private List<Location> BuildLocationTree(List<Location> locations)
        {
            var locationDict = locations.ToDictionary(l => l.Id);
            var rootLocations = new List<Location>();

            foreach (var location in locations)
            {
                if (location.ParentId.HasValue && locationDict.ContainsKey(location.ParentId.Value))
                {
                    var parent = locationDict[location.ParentId.Value];
                    parent.Children.Add(location);
                    location.Parent = parent;
                }
                else
                {
                    rootLocations.Add(location);
                }
            }

            return rootLocations;
        }
    }
}
