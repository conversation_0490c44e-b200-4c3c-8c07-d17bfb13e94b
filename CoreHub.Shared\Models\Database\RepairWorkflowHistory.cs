using SqlSugar;
using System.ComponentModel.DataAnnotations;

namespace CoreHub.Shared.Models.Database
{
    /// <summary>
    /// 报修单工作流历史记录实体
    /// </summary>
    [SugarTable("RepairWorkflowHistory")]
    public class RepairWorkflowHistory
    {
        /// <summary>
        /// 历史记录ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        /// <summary>
        /// 报修单ID
        /// </summary>
        [SugarColumn(IsNullable = false)]
        [Required(ErrorMessage = "报修单ID不能为空")]
        public int RepairOrderId { get; set; }

        /// <summary>
        /// 操作用户ID
        /// </summary>
        [SugarColumn(IsNullable = false)]
        [Required(ErrorMessage = "操作用户ID不能为空")]
        public int UserId { get; set; }

        /// <summary>
        /// 操作动作
        /// </summary>
        [SugarColumn(Length = 100, IsNullable = false)]
        [Required(ErrorMessage = "操作动作不能为空")]
        [StringLength(100, ErrorMessage = "操作动作长度不能超过100个字符")]
        public string Action { get; set; } = string.Empty;

        /// <summary>
        /// 操作备注
        /// </summary>
        [SugarColumn(Length = 1000, IsNullable = true)]
        [StringLength(1000, ErrorMessage = "操作备注长度不能超过1000个字符")]
        public string? Comment { get; set; }

        /// <summary>
        /// 原状态
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? FromStatus { get; set; }

        /// <summary>
        /// 目标状态
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? ToStatus { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 附加数据（JSON格式）
        /// </summary>
        [SugarColumn(ColumnDataType = "nvarchar(max)", IsNullable = true)]
        public string? AdditionalData { get; set; }

        // 导航属性（不映射到数据库）
        
        /// <summary>
        /// 关联的报修单
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public RepairOrder? RepairOrder { get; set; }

        /// <summary>
        /// 操作用户
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public User? User { get; set; }
    }

    /// <summary>
    /// 工作流历史记录DTO
    /// </summary>
    public class RepairWorkflowHistoryDto
    {
        /// <summary>
        /// 历史记录ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 报修单ID
        /// </summary>
        public int RepairOrderId { get; set; }

        /// <summary>
        /// 报修单号
        /// </summary>
        public string OrderNumber { get; set; } = string.Empty;

        /// <summary>
        /// 操作用户ID
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// 操作用户姓名
        /// </summary>
        public string UserName { get; set; } = string.Empty;

        /// <summary>
        /// 操作用户用户名
        /// </summary>
        public string UserLoginName { get; set; } = string.Empty;

        /// <summary>
        /// 操作动作
        /// </summary>
        public string Action { get; set; } = string.Empty;

        /// <summary>
        /// 操作备注
        /// </summary>
        public string? Comment { get; set; }

        /// <summary>
        /// 原状态
        /// </summary>
        public int? FromStatus { get; set; }

        /// <summary>
        /// 目标状态
        /// </summary>
        public int? ToStatus { get; set; }

        /// <summary>
        /// 原状态名称
        /// </summary>
        public string? FromStatusName { get; set; }

        /// <summary>
        /// 目标状态名称
        /// </summary>
        public string? ToStatusName { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// 附加数据
        /// </summary>
        public string? AdditionalData { get; set; }

        /// <summary>
        /// 设备名称
        /// </summary>
        public string EquipmentName { get; set; } = string.Empty;

        /// <summary>
        /// 设备编码
        /// </summary>
        public string EquipmentCode { get; set; } = string.Empty;

        /// <summary>
        /// 格式化的时间显示
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string FormattedTime => CreatedAt.ToString("MM-dd HH:mm");

        /// <summary>
        /// 详细时间显示
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string DetailedTime => CreatedAt.ToString("yyyy-MM-dd HH:mm:ss");

        /// <summary>
        /// 状态变更描述
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string StatusChangeDescription
        {
            get
            {
                if (FromStatus.HasValue && ToStatus.HasValue && FromStatus != ToStatus)
                {
                    return $"{FromStatusName} → {ToStatusName}";
                }
                return string.Empty;
            }
        }

        /// <summary>
        /// 是否为状态变更操作
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public bool IsStatusChange => FromStatus.HasValue && ToStatus.HasValue && FromStatus != ToStatus;

        /// <summary>
        /// 操作类型图标
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string ActionIcon
        {
            get
            {
                return Action.ToLower() switch
                {
                    var a when a.Contains("创建") => "Icons.Material.Filled.Add",
                    var a when a.Contains("分配") => "Icons.Material.Filled.Assignment",
                    var a when a.Contains("开始") || a.Contains("接受") => "Icons.Material.Filled.PlayArrow",
                    var a when a.Contains("暂停") => "Icons.Material.Filled.Pause",
                    var a when a.Contains("恢复") => "Icons.Material.Filled.PlayArrow",
                    var a when a.Contains("完成") => "Icons.Material.Filled.CheckCircle",
                    var a when a.Contains("关闭") => "Icons.Material.Filled.Close",
                    var a when a.Contains("作废") || a.Contains("取消") => "Icons.Material.Filled.Cancel",
                    var a when a.Contains("审批") => "Icons.Material.Filled.Approval",
                    var a when a.Contains("支援") || a.Contains("帮助") => "Icons.Material.Filled.Help",
                    var a when a.Contains("评价") || a.Contains("评分") => "Icons.Material.Filled.Star",
                    _ => "Icons.Material.Filled.History"
                };
            }
        }

        /// <summary>
        /// 操作类型颜色
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string ActionColor
        {
            get
            {
                return Action.ToLower() switch
                {
                    var a when a.Contains("创建") => "Color.Primary",
                    var a when a.Contains("分配") => "Color.Info",
                    var a when a.Contains("开始") || a.Contains("接受") => "Color.Success",
                    var a when a.Contains("暂停") => "Color.Warning",
                    var a when a.Contains("恢复") => "Color.Success",
                    var a when a.Contains("完成") => "Color.Success",
                    var a when a.Contains("关闭") => "Color.Secondary",
                    var a when a.Contains("作废") || a.Contains("取消") => "Color.Error",
                    var a when a.Contains("审批") => "Color.Primary",
                    var a when a.Contains("支援") || a.Contains("帮助") => "Color.Warning",
                    var a when a.Contains("评价") || a.Contains("评分") => "Color.Tertiary",
                    _ => "Color.Default"
                };
            }
        }
    }

    /// <summary>
    /// 创建工作流历史记录请求DTO
    /// </summary>
    public class CreateWorkflowHistoryDto
    {
        /// <summary>
        /// 报修单ID
        /// </summary>
        [Required(ErrorMessage = "报修单ID不能为空")]
        public int RepairOrderId { get; set; }

        /// <summary>
        /// 操作用户ID
        /// </summary>
        [Required(ErrorMessage = "操作用户ID不能为空")]
        public int UserId { get; set; }

        /// <summary>
        /// 操作动作
        /// </summary>
        [Required(ErrorMessage = "操作动作不能为空")]
        [StringLength(100, ErrorMessage = "操作动作长度不能超过100个字符")]
        public string Action { get; set; } = string.Empty;

        /// <summary>
        /// 操作备注
        /// </summary>
        [StringLength(1000, ErrorMessage = "操作备注长度不能超过1000个字符")]
        public string? Comment { get; set; }

        /// <summary>
        /// 原状态
        /// </summary>
        public int? FromStatus { get; set; }

        /// <summary>
        /// 目标状态
        /// </summary>
        public int? ToStatus { get; set; }

        /// <summary>
        /// 附加数据（JSON格式）
        /// </summary>
        public string? AdditionalData { get; set; }
    }
}
