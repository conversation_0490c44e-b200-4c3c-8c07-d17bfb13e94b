@using System.Reflection
@using MudBlazor

@inject IDialogService DialogService

<MudGrid>
    <MudItem xs="10" Class="d-flex align-center">
        <MudTextField @bind-Value="SelectedIcon" 
                      Label="@Label"
                      Placeholder="@Placeholder"
                      Variant="@Variant"
                      Adornment="Adornment.Start"
                      AdornmentIcon="@GetCurrentIcon()"
                      Class="@Class"
                      ReadOnly="false" />
    </MudItem>
    <MudItem xs="2" Class="d-flex align-center">
        <MudButton Variant="Variant.Outlined"
                   Color="Color.Primary"
                   Size="Size.Medium"
                   OnClick="OpenIconDialog"
                   Style="height: 56px; min-width: 56px;">
            <MudIcon Icon="@Icons.Material.Filled.Search" />
        </MudButton>
    </MudItem>
</MudGrid>

@code {
    [Parameter] public string? SelectedIcon { get; set; }
    [Parameter] public EventCallback<string?> SelectedIconChanged { get; set; }
    [Parameter] public string Label { get; set; } = "图标";
    [Parameter] public string Placeholder { get; set; } = "选择图标";
    [Parameter] public Variant Variant { get; set; } = Variant.Outlined;
    [Parameter] public string Class { get; set; } = "";

    private string GetCurrentIcon()
    {
        if (string.IsNullOrEmpty(SelectedIcon))
            return Icons.Material.Filled.Circle;
            
        // 如果是MudBlazor图标名称常量，需要转换为实际值
        if (SelectedIcon.StartsWith("Icons.Material."))
        {
            return ConvertIconConstantToValue(SelectedIcon);
        }
        
        // 兼容旧格式的图标映射
        return SelectedIcon switch
        {
            "home" => Icons.Material.Filled.Home,
            "add" => Icons.Material.Filled.Add,
            "qr_code_scanner" => Icons.Material.Filled.QrCodeScanner,
            "camera_alt" => Icons.Material.Filled.Camera,
            "wb_sunny" => Icons.Material.Filled.WbSunny,
            "build" => Icons.Material.Filled.Build,
            "settings" => Icons.Material.Filled.Settings,
            "people" => Icons.Material.Filled.People,
            "security" => Icons.Material.Filled.Security,
            "key" => Icons.Material.Filled.Key,
            "menu" => Icons.Material.Filled.Menu,
            "person_add" => Icons.Material.Filled.PersonAdd,
            "storage" => Icons.Material.Filled.Storage,
            "shield" => Icons.Material.Filled.Shield,
            "bug_report" => Icons.Material.Filled.BugReport,
            "login" => Icons.Material.Filled.Login,
            "code" => Icons.Material.Filled.Code,
            "palette" => Icons.Material.Filled.Palette,
            "account_tree" => Icons.Material.Filled.AccountTree,
            
            // Bootstrap图标兼容
            "bi bi-house-door-fill-nav-menu" or "bi bi-house-door-fill" => Icons.Material.Filled.Home,
            "bi bi-plus-square-fill-nav-menu" or "bi bi-plus-square-fill" => Icons.Material.Filled.Add,
            "bi bi-list-nested-nav-menu" or "bi bi-list-nested" => Icons.Material.Filled.List,
            "bi bi-camera-fill-nav-menu" or "bi bi-camera-fill" => Icons.Material.Filled.QrCodeScanner,
            "bi bi-camera2-nav-menu" => Icons.Material.Filled.Camera,
            "bi bi-tools-nav-menu" or "bi bi-tools" => Icons.Material.Filled.Build,
            "bi bi-gear-nav-menu" or "bi bi-gear" => Icons.Material.Filled.Settings,
            "bi bi-people-nav-menu" or "bi bi-people" => Icons.Material.Filled.People,
            "bi bi-person-badge-nav-menu" or "bi bi-person-badge" => Icons.Material.Filled.Security,
            "bi bi-key-nav-menu" or "bi bi-key" => Icons.Material.Filled.Key,
            "bi bi-list-ul" => Icons.Material.Filled.Menu,
            "bi bi-person-gear-nav-menu" or "bi bi-person-gear" => Icons.Material.Filled.PersonAdd,
            "bi bi-database-gear" => Icons.Material.Filled.Storage,
            "bi bi-shield-check-nav-menu" => Icons.Material.Filled.Shield,
            "bi bi-bug-nav-menu" or "bi bi-bug" => Icons.Material.Filled.BugReport,
            "bi bi-box-arrow-in-right-nav-menu" => Icons.Material.Filled.Login,
            "bi bi-code-slash" => Icons.Material.Filled.Code,
            "bi bi-palette" => Icons.Material.Filled.Palette,
            "bi bi-diagram-2" => Icons.Material.Filled.AccountTree,
            "bi bi-wrench" => Icons.Material.Filled.Build,
            
            _ => Icons.Material.Filled.Circle
        };
    }

    private string ConvertIconConstantToValue(string iconConstant)
    {
        return iconConstant switch
        {
            "Icons.Material.Filled.Home" => Icons.Material.Filled.Home,
            "Icons.Material.Filled.Add" => Icons.Material.Filled.Add,
            "Icons.Material.Filled.QrCodeScanner" => Icons.Material.Filled.QrCodeScanner,
            "Icons.Material.Filled.Camera" => Icons.Material.Filled.Camera,
            "Icons.Material.Filled.WbSunny" => Icons.Material.Filled.WbSunny,
            "Icons.Material.Filled.Build" => Icons.Material.Filled.Build,
            "Icons.Material.Filled.Settings" => Icons.Material.Filled.Settings,
            "Icons.Material.Filled.People" => Icons.Material.Filled.People,
            "Icons.Material.Filled.Security" => Icons.Material.Filled.Security,
            "Icons.Material.Filled.Key" => Icons.Material.Filled.Key,
            "Icons.Material.Filled.Menu" => Icons.Material.Filled.Menu,
            "Icons.Material.Filled.PersonAdd" => Icons.Material.Filled.PersonAdd,
            "Icons.Material.Filled.Storage" => Icons.Material.Filled.Storage,
            "Icons.Material.Filled.Shield" => Icons.Material.Filled.Shield,
            "Icons.Material.Filled.BugReport" => Icons.Material.Filled.BugReport,
            "Icons.Material.Filled.Login" => Icons.Material.Filled.Login,
            "Icons.Material.Filled.Logout" => Icons.Material.Filled.Logout,
            "Icons.Material.Filled.Code" => Icons.Material.Filled.Code,
            "Icons.Material.Filled.Palette" => Icons.Material.Filled.Palette,
            "Icons.Material.Filled.AccountTree" => Icons.Material.Filled.AccountTree,
            _ => Icons.Material.Filled.Circle // 默认图标
        };
    }

    private async Task OpenIconDialog()
    {
        var parameters = new DialogParameters
        {
            ["InitialValue"] = SelectedIcon
        };

        var options = new DialogOptions
        {
            MaxWidth = MaxWidth.Large,
            FullWidth = true,
            CloseButton = true,
            BackdropClick = false
        };

        try
        {
            var dialog = await DialogService.ShowAsync<IconPickerDialog>("选择图标", parameters, options);
            var result = await dialog.Result;

            if (!result.Canceled && result.Data is string selectedIcon)
            {
                SelectedIcon = selectedIcon;
                await SelectedIconChanged.InvokeAsync(SelectedIcon);
                StateHasChanged(); // 触发界面更新
            }
        }
        catch (Exception)
        {
            // 如果IconPickerDialog不可用，提供一个简单的输入提示
            // 这里可以添加简单的手动输入或者预设选项
        }
    }
} 