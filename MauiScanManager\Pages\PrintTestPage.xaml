<?xml version="1.0" encoding="utf-8" ?>
<ContentPage x:Class="MauiScanManager.Pages.PrintTestPage"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             Title="打印测试">
    <ScrollView>
        <StackLayout Padding="20" Spacing="15">
            <Label Text="打印服务测试" 
                   FontSize="24" 
                   FontAttributes="Bold" 
                   HorizontalOptions="Center" 
                   Margin="0,0,0,20"/>

            <!-- 控制按钮 -->
            <Frame BackgroundColor="LightSteelBlue" Padding="15">
                <StackLayout>
                    <Label Text="服务控制" FontAttributes="Bold" Margin="0,0,0,10"/>
                    <StackLayout Orientation="Horizontal" HorizontalOptions="Center" Spacing="20">
                        <Button x:Name="InitButton" 
                                Text="初始化打印服务" 
                                Clicked="OnInitializeClicked"/>
                        <Button x:Name="DisposeButton" 
                                Text="释放打印服务" 
                                Clicked="OnDisposeClicked"/>
                    </StackLayout>
                </StackLayout>
            </Frame>

            <!-- 打印状态 -->
            <Frame BackgroundColor="LightBlue" Padding="15">
                <StackLayout>
                    <Label Text="打印服务状态" FontAttributes="Bold" Margin="0,0,0,5"/>
                    <Label x:Name="StatusLabel" Text="未初始化" TextColor="Red"/>
                </StackLayout>
            </Frame>

            <!-- 打印结果 -->
            <Frame BackgroundColor="LightCoral" Padding="15">
                <StackLayout>
                    <Label Text="打印结果" FontAttributes="Bold" Margin="0,0,0,5"/>
                    <Label x:Name="ResultLabel" Text="暂无结果"/>
                    <Label x:Name="DetailLabel" Text="" FontSize="12" TextColor="Gray" Margin="0,5,0,0"/>
                    <Label x:Name="TimestampLabel" Text="" FontSize="10" TextColor="DarkGray" Margin="0,2,0,0"/>
                </StackLayout>
            </Frame>

            <!-- 黑标模式设置 -->
            <Frame BackgroundColor="Orange" Padding="15">
                <StackLayout>
                    <Label Text="打印模式设置" FontAttributes="Bold" Margin="0,0,0,10"/>
                    <StackLayout Orientation="Horizontal">
                        <CheckBox x:Name="BlackMarkCheckBox"/>
                        <Label Text="启用黑标打印模式（标签纸）" VerticalOptions="Center"/>
                    </StackLayout>
                    <Label Text="• 黑标模式：适用于有黑标的标签纸，打印机自动检测黑标位置" 
                           FontSize="12" TextColor="Gray" Margin="20,5,0,0"/>
                    <Label Text="• 无标签模式：适用于连续纸，打印后自动添加空行" 
                           FontSize="12" TextColor="Gray" Margin="20,0,0,0"/>
                </StackLayout>
            </Frame>
            <!-- 条码打印 -->
            <Frame BackgroundColor="LightPink" Padding="15">
                <StackLayout>
                    <Label Text="条码打印" FontAttributes="Bold" Margin="0,0,0,10"/>
                    <Entry x:Name="BarcodeEntry" 
                           Placeholder="输入条码内容"
                           Text="123456789ABC"/>
                    
                    <Grid ColumnDefinitions="*,*" Margin="0,10" ColumnSpacing="10">
                        <StackLayout Grid.Column="0">
                            <Label Text="条码类型："/>
                            <Picker x:Name="BarcodeTypePicker">
                                <Picker.Items>
                                    <x:String>Code128</x:String>
                                    <x:String>Code39</x:String>
                                    <x:String>Ean13</x:String>
                                    <x:String>Ean8</x:String>
                                    <x:String>UpcA</x:String>
                                    <x:String>Itf</x:String>
                                </Picker.Items>
                            </Picker>
                        </StackLayout>
                        
                        <StackLayout Grid.Column="1">
                            <Label Text="文本位置："/>
                            <Picker x:Name="HRIPositionPicker">
                                <Picker.Items>
                                    <x:String>不显示</x:String>
                                    <x:String>下方</x:String>
                                    <x:String>上方</x:String>
                                </Picker.Items>
                            </Picker>
                        </StackLayout>
                    </Grid>
                    
                    <Label Text="• 文本位置：控制条码下方/上方是否显示文本内容" 
                           FontSize="12" TextColor="Gray" Margin="0,5"/>
                    
                    <Button x:Name="PrintBarcodeButton" 
                            Text="打印条码" 
                            Clicked="OnPrintBarcodeClicked"/>
                </StackLayout>
            </Frame>            

            <!-- 简单文本打印 -->
            <Frame BackgroundColor="LightGray" Padding="15">
                <StackLayout>
                    <Label Text="简单文本打印" FontAttributes="Bold" Margin="0,0,0,10"/>
                    <Entry x:Name="SimpleTextEntry" 
                           Placeholder="输入要打印的文本"
                           Text="测试打印文本"/>
                    <Button x:Name="PrintSimpleTextButton" 
                            Text="打印文本" 
                            Clicked="OnPrintSimpleTextClicked"/>
                </StackLayout>
            </Frame>

            <!-- 格式化文本打印 -->
            <Frame BackgroundColor="LightGreen" Padding="15">
                <StackLayout>
                    <Label Text="格式化文本打印" FontAttributes="Bold" Margin="0,0,0,10"/>
                    <Entry x:Name="FormattedTextEntry" 
                           Placeholder="输入要打印的文本"
                           Text="居中加粗文本"/>
                    
                    <Grid ColumnDefinitions="*,*,*" Margin="0,10">
                        <StackLayout Grid.Column="0">
                            <Label Text="对齐方式"/>
                            <Picker x:Name="AlignPicker">
                                <Picker.Items>
                                    <x:String>Left</x:String>
                                    <x:String>Center</x:String>
                                    <x:String>Right</x:String>
                                </Picker.Items>
                            </Picker>
                        </StackLayout>
                        
                        <StackLayout Grid.Column="1">
                            <Label Text="字体大小"/>
                            <Picker x:Name="FontSizePicker">
                                <Picker.Items>
                                    <x:String>Small</x:String>
                                    <x:String>Normal</x:String>
                                    <x:String>Large</x:String>
                                </Picker.Items>
                            </Picker>
                        </StackLayout>
                        
                        <StackLayout Grid.Column="2">
                            <Label Text="样式"/>
                            <CheckBox x:Name="BoldCheckBox"/>
                            <Label Text="粗体"/>
                            <CheckBox x:Name="UnderlineCheckBox"/>
                            <Label Text="下划线"/>
                        </StackLayout>
                    </Grid>
                    
                    <Button x:Name="PrintFormattedTextButton" 
                            Text="打印格式化文本" 
                            Clicked="OnPrintFormattedTextClicked"/>
                </StackLayout>
            </Frame>



            <!-- 二维码打印 -->
            <Frame BackgroundColor="LightCyan" Padding="15">
                <StackLayout>
                    <Label Text="二维码打印" FontAttributes="Bold" Margin="0,0,0,10"/>
                    <Entry x:Name="QRCodeEntry" 
                           Placeholder="输入二维码内容"
                           Text="https://www.example.com"/>
                    
                    <Grid ColumnDefinitions="*,*" Margin="0,10" ColumnSpacing="10">
                        <StackLayout Grid.Column="0" Orientation="Horizontal">
                            <Label Text="高度：" VerticalOptions="Center"/>
                            <Entry x:Name="QRCodeHeightEntry" 
                                   WidthRequest="80"
                                   Text="184"
                                   Keyboard="Numeric"/>
                            <Label Text="像素" VerticalOptions="Center"/>
                        </StackLayout>
                        
                        <StackLayout Grid.Column="1">
                            <Label Text="对齐方式："/>
                            <Picker x:Name="QRCodeAlignPicker">
                                <Picker.Items>
                                    <x:String>左对齐</x:String>
                                    <x:String>居中</x:String>
                                    <x:String>右对齐</x:String>
                                </Picker.Items>
                            </Picker>
                        </StackLayout>
                    </Grid>
                    
                    <Label Text="• 对齐方式：控制二维码在纸张上的位置（左、中、右）" 
                           FontSize="12" TextColor="Gray" Margin="0,5"/>
                    
                    <Button x:Name="PrintQRCodeButton" 
                            Text="打印二维码" 
                            Clicked="OnPrintQRCodeClicked"/>
                </StackLayout>
            </Frame>

            <!-- 模板打印 -->
            <Frame BackgroundColor="LightYellow" Padding="15">
                <StackLayout>
                    <Label Text="模板打印示例" FontAttributes="Bold" Margin="0,0,0,10"/>
                    <Button x:Name="PrintReceiptButton" 
                            Text="打印停车小票" 
                            Clicked="OnPrintReceiptClicked"/>
                    <Button x:Name="PrintOrderButton" 
                            Text="打印订单信息" 
                            Clicked="OnPrintOrderClicked"/>
                    <Button x:Name="PrintBarcodeTemplateButton" 
                            Text="打印条码模板" 
                            Clicked="OnPrintBarcodeTemplateClicked"/>
                </StackLayout>
            </Frame>


        </StackLayout>
    </ScrollView>
</ContentPage> 