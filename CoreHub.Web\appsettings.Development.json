{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "Serilog": {"MinimumLevel": {"Default": "Debug", "Override": {"Microsoft": "Information", "Microsoft.AspNetCore": "Information", "Microsoft.EntityFrameworkCore": "Information", "System": "Information", "CoreHub": "Debug"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {SourceContext}: {Message:lj}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/corehub-web-dev-.log", "rollingInterval": "Day", "retainedFileCountLimit": 7, "fileSizeLimitBytes": ********, "rollOnFileSizeLimit": true, "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {SourceContext}: {Message:lj} {Properties:j}{NewLine}{Exception}"}}], "Properties": {"Environment": "Development"}}, "ConnectionStrings": {"DefaultConnection": "Server=***********;Database=CoreHub;User Id=sa;Password=****;TrustServerCertificate=True;Connect Timeout=30;MultipleActiveResultSets=True"}, "AuthenticationSettings": {"UseStoredProcedure": true, "MaxLoginFailureCount": 5, "AccountLockoutDuration": 30, "PasswordComplexity": {"MinLength": 6, "RequireDigit": false, "RequireLowercase": false, "RequireUppercase": false, "RequireNonAlphanumeric": false}}}