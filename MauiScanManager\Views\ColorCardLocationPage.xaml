<?xml version="1.0" encoding="utf-8" ?>
<views:BaseOperationPage 
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:views="clr-namespace:MauiScanManager.Views"
             xmlns:viewmodels="clr-namespace:MauiScanManager.ViewModels"
             x:Class="MauiScanManager.Views.ColorCardLocationPage"
             Title="{Binding Operation.Description}">

    <Grid RowDefinitions="Auto,Auto,*,Auto,Auto,Auto" 
          Padding="10" 
          RowSpacing="5">
        
        <!-- 标题和数量 -->
        <Grid Grid.Row="0" ColumnDefinitions="*,Auto" Margin="5,0,5,2">
            <Label Text="已扫描缸号"
                   FontSize="18"
                   FontAttributes="Bold"
                   VerticalOptions="Center"/>
            <StackLayout Grid.Column="1" 
                         Orientation="Horizontal" 
                         Spacing="5"
                         VerticalOptions="Center">
                <Label Text="{Binding BatchNos.Count}"
                       FontSize="32"
                       FontAttributes="Bold"
                       TextColor="{StaticResource Primary}"/>
                <Label Text="条"
                       FontSize="18"
                       VerticalOptions="Center"/>
            </StackLayout>
        </Grid>

        <!-- 手动输入区域 -->
        <Grid Grid.Row="1" ColumnDefinitions="*,Auto" Margin="0,0,0,10">
            <Entry Placeholder="输入缸号"
                   Text="{Binding CurrentBatchNo}"
                   FontSize="16"
                   ReturnCommand="{Binding AddBatchNoCommand}"
                   ReturnType="Done"/>
            <Button Grid.Column="1"
                    Text="添加"
                    Command="{Binding AddBatchNoCommand}"
                    IsEnabled="{Binding CanAdd}"
                    Margin="10,0,0,0"
                    BackgroundColor="{StaticResource Primary}"/>
        </Grid>

        <!-- 缸号列表 -->
        <CollectionView Grid.Row="2" 
                       x:Name="BatchNosList"
                       ItemsSource="{Binding BatchNos}">
            <CollectionView.Header>
                <Grid BackgroundColor="#f0f0f0" 
                      Padding="15,5">
                    <Label Text="缸号列表" 
                           FontSize="14" 
                           TextColor="Gray"/>
                </Grid>
            </CollectionView.Header>
            <CollectionView.ItemTemplate>
                <DataTemplate>
                    <Grid Padding="15,2">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <Label Text="{Binding}"
                               FontSize="16"
                               VerticalOptions="Center"/>
                        <Button Grid.Column="1"
                                Text="删除"
                                TextColor="White"
                                BackgroundColor="Red"
                                Command="{Binding Source={RelativeSource AncestorType={x:Type viewmodels:ColorCardLocationViewModel}}, Path=RemoveBatchNoCommand}"
                                CommandParameter="{Binding}"
                                HeightRequest="28"
                                Padding="8,0"/>
                    </Grid>
                </DataTemplate>
            </CollectionView.ItemTemplate>
            <CollectionView.EmptyView>
                <Label Text="请扫描或输入缸号"
                       HorizontalOptions="Center"
                       VerticalOptions="Center"
                       TextColor="Gray"
                       FontSize="16"/>
            </CollectionView.EmptyView>
        </CollectionView>

        <!-- 位置选择标题 -->
        <Label Grid.Row="3" 
               Text="位置和定位人"
               FontSize="18"
               FontAttributes="Bold"
               Margin="5,0,5,2"/>
        
        <!-- 位置选择器和定位人输入框 -->
        <Grid Grid.Row="4" ColumnDefinitions="*,*" ColumnSpacing="10">
            <Picker Grid.Column="0"
                    ItemsSource="{Binding Locations}"
                    SelectedItem="{Binding SelectedLocation}"
                    Title="请选择位置"
                    FontSize="16"
                    Margin="0,5"/>

            <Entry Grid.Column="1"
                   Placeholder="请输入定位人"
                   Text="{Binding LocatePerson}"
                   FontSize="16"
                   Margin="0,5"/>
        </Grid>

        <!-- 保存按钮 -->
        <Button Grid.Row="5"
                Text="保存"
                Command="{Binding SaveCommand}"
                HeightRequest="50"
                FontSize="18"
                BackgroundColor="{StaticResource Primary}"
                Margin="0,10,0,0"/>
    </Grid>

</views:BaseOperationPage>
