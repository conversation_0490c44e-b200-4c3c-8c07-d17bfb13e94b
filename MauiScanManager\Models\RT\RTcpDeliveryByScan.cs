﻿using System.ComponentModel.DataAnnotations;
namespace MauiScanManager.Models
{
    public class RTcpDeliveryByScan
    {
        [Required(ErrorMessage = "保存缸号必填")]
        public string BatchNoList { get; set; } = string.Empty;

        [Required(ErrorMessage = "当前部门必填")]
        public string CurDept { get; set; }

        [Required(ErrorMessage = "交地必填")]
        public string Destination { get; set; }

        [Required(ErrorMessage = "出货员工必填")]
        public string WorkerName { get; set; } = string.Empty;
    }
}
