using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using CustomerWebAPI.Database;
using CustomerWebAPI.Models;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Serilog;

namespace CustomerWebAPI.Services.Health
{
    public class HealthCheckService : IHealthCheckService
    {
        private readonly DbContext _dbContext;
        private readonly ILogger<HealthCheckService> _logger;
        private readonly IHostEnvironment _environment;

        public HealthCheckService(
            DbContext dbContext,
            ILogger<HealthCheckService> logger,
            IHostEnvironment environment)
        {
            _dbContext = dbContext;
            _logger = logger;
            _environment = environment;
        }

        public async Task<HealthReport> CheckHealthAsync(CancellationToken cancellationToken = default)
        {
            var healthReport = new HealthReport
            {
                Status = "Healthy",
                Timestamp = DateTime.Now,
                Environment = _environment.EnvironmentName,
                Components = new List<ComponentHealth>()
            };

            try
            {
                healthReport.Components.Add(await CheckApiHealthAsync());
                var dbHealths = await CheckDatabasesHealthAsync();
                healthReport.Components.AddRange(dbHealths);

                healthReport.Status = healthReport.Components.All(x => x.Status == "Healthy")
                    ? "Healthy"
                    : "Unhealthy";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Health check failed");
                healthReport.Status = "Unhealthy";
                healthReport.Error = "Health check failed";
            }

            return healthReport;
        }

        private async Task<ComponentHealth> CheckApiHealthAsync()
        {
            var stopwatch = Stopwatch.StartNew();
            try
            {
                await Task.Yield();
                
                return new ComponentHealth
                {
                    Name = "API",
                    Status = "Healthy",
                    ResponseTime = stopwatch.ElapsedMilliseconds,
                    LastChecked = DateTime.Now,
                    Data = new Dictionary<string, object>
                    {
                        { "Version", GetType().Assembly.GetName().Version.ToString() }
                    }
                };
            }
            finally
            {
                stopwatch.Stop();
            }
        }

        private async Task<List<ComponentHealth>> CheckDatabasesHealthAsync()
        {
            var results = new List<ComponentHealth>();
            var configs = new[] { "prod" };

            foreach (var config in configs)
            {
                var stopwatch = Stopwatch.StartNew();
                try
                {
                    _logger.LogInformation($"Checking database health for {config}");
                    var isValid = await _dbContext.CheckConnectionAsync();

                    results.Add(new ComponentHealth
                    {
                        Name = $"Database_{config}",
                        Status = isValid ? "Healthy" : "Unhealthy",
                        ResponseTime = stopwatch.ElapsedMilliseconds,
                        LastChecked = DateTime.Now,
                        Data = new Dictionary<string, object>
                        {
                            { "Database", config }
                        }
                    });
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"Database health check failed for {config}");
                    results.Add(new ComponentHealth
                    {
                        Name = $"Database_{config}",
                        Status = "Unhealthy",
                        Error = "Database connection failed",
                        ResponseTime = stopwatch.ElapsedMilliseconds,
                        LastChecked = DateTime.Now,
                        Data = new Dictionary<string, object>
                        {
                            { "Database", config }
                        }
                    });
                }
                finally
                {
                    stopwatch.Stop();
                }
            }
            return results;
        }
    }
} 