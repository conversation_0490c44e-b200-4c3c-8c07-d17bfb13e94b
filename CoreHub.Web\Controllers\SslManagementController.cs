using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using CoreHub.Web.Services;
using CoreHub.Shared.Services;

namespace CoreHub.Web.Controllers
{
    /// <summary>
    /// SSL 证书管理控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Authorize] // 需要认证才能访问
    public class SslManagementController : ControllerBase
    {
        private readonly ISslCertificateService _sslService;
        private readonly ICertificateReplacementService _replacementService;
        private readonly ICertificateMonitoringService _monitoringService;
        private readonly IApplicationLogger _logger;
        private readonly IConfiguration _configuration;

        public SslManagementController(
            ISslCertificateService sslService,
            ICertificateReplacementService replacementService,
            ICertificateMonitoringService monitoringService,
            IApplicationLogger logger,
            IConfiguration configuration)
        {
            _sslService = sslService;
            _replacementService = replacementService;
            _monitoringService = monitoringService;
            _logger = logger;
            _configuration = configuration;
        }

        /// <summary>
        /// 获取当前 SSL 证书信息
        /// </summary>
        /// <returns>证书信息</returns>
        [HttpGet("certificate-info")]
        public async Task<IActionResult> GetCertificateInfo()
        {
            try
            {
                var certPath = _configuration["Kestrel:EndPoints:Https:Certificate:Path"];
                var certPassword = _configuration["Kestrel:EndPoints:Https:Certificate:Password"];

                if (string.IsNullOrEmpty(certPath) || string.IsNullOrEmpty(certPassword))
                {
                    return BadRequest(new { message = "SSL 证书配置不完整" });
                }

                var certInfo = await _sslService.GetCertificateInfoAsync(certPath, certPassword);
                if (certInfo == null)
                {
                    return NotFound(new { message = "无法获取证书信息" });
                }

                _logger.LogUserAction(
                    User.Identity?.Name ?? "Anonymous", 
                    "查看SSL证书信息", 
                    new { CertificatePath = certPath, Thumbprint = certInfo.Thumbprint }
                );

                return Ok(new
                {
                    success = true,
                    data = new
                    {
                        subject = certInfo.Subject,
                        issuer = certInfo.Issuer,
                        serialNumber = certInfo.SerialNumber,
                        thumbprint = certInfo.Thumbprint,
                        notBefore = certInfo.NotBefore,
                        notAfter = certInfo.NotAfter,
                        daysUntilExpiry = certInfo.DaysUntilExpiry,
                        isExpired = certInfo.IsExpired,
                        isExpiringWithin30Days = certInfo.IsExpiringWithin30Days,
                        hasPrivateKey = certInfo.HasPrivateKey,
                        keyAlgorithm = certInfo.KeyAlgorithm,
                        signatureAlgorithm = certInfo.SignatureAlgorithm,
                        version = certInfo.Version
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取SSL证书信息失败");
                return StatusCode(500, new { message = "获取证书信息失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 验证 SSL 证书
        /// </summary>
        /// <returns>验证结果</returns>
        [HttpPost("validate-certificate")]
        public async Task<IActionResult> ValidateCertificate()
        {
            try
            {
                var certPath = _configuration["Kestrel:EndPoints:Https:Certificate:Path"];
                var certPassword = _configuration["Kestrel:EndPoints:Https:Certificate:Password"];

                if (string.IsNullOrEmpty(certPath) || string.IsNullOrEmpty(certPassword))
                {
                    return BadRequest(new { message = "SSL 证书配置不完整" });
                }

                var validationResult = await _sslService.ValidateCertificateAsync(certPath, certPassword);

                _logger.LogUserAction(
                    User.Identity?.Name ?? "Anonymous", 
                    "验证SSL证书", 
                    new { 
                        CertificatePath = certPath, 
                        IsValid = validationResult.IsValid,
                        ErrorMessage = validationResult.ErrorMessage
                    }
                );

                return Ok(new
                {
                    success = true,
                    data = new
                    {
                        isValid = validationResult.IsValid,
                        errorMessage = validationResult.ErrorMessage,
                        subject = validationResult.Subject,
                        issuer = validationResult.Issuer,
                        notBefore = validationResult.NotBefore,
                        notAfter = validationResult.NotAfter,
                        thumbprint = validationResult.Thumbprint,
                        isExpiringSoon = validationResult.IsExpiringSoon,
                        daysUntilExpiry = validationResult.DaysUntilExpiry
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证SSL证书失败");
                return StatusCode(500, new { message = "验证证书失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 检查证书过期状态
        /// </summary>
        /// <param name="warningDays">提前警告天数（默认30天）</param>
        /// <returns>过期检查结果</returns>
        [HttpGet("expiry-check")]
        public async Task<IActionResult> CheckCertificateExpiry([FromQuery] int warningDays = 30)
        {
            try
            {
                var certPath = _configuration["Kestrel:EndPoints:Https:Certificate:Path"];
                var certPassword = _configuration["Kestrel:EndPoints:Https:Certificate:Password"];

                if (string.IsNullOrEmpty(certPath) || string.IsNullOrEmpty(certPassword))
                {
                    return BadRequest(new { message = "SSL 证书配置不完整" });
                }

                var isExpiring = await _sslService.IsCertificateExpiringAsync(certPath, certPassword, warningDays);
                var certInfo = await _sslService.GetCertificateInfoAsync(certPath, certPassword);

                var result = new
                {
                    isExpiring = isExpiring,
                    warningDays = warningDays,
                    daysUntilExpiry = certInfo?.DaysUntilExpiry ?? 0,
                    expiryDate = certInfo?.NotAfter,
                    isExpired = certInfo?.IsExpired ?? true,
                    recommendation = GetExpiryRecommendation(certInfo?.DaysUntilExpiry ?? 0)
                };

                _logger.LogInformation("SSL证书过期检查完成: {@ExpiryCheck}", result);

                return Ok(new { success = true, data = result });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查SSL证书过期状态失败");
                return StatusCode(500, new { message = "检查证书过期状态失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 获取 SSL 配置信息（不包含敏感信息）
        /// </summary>
        /// <returns>SSL 配置信息</returns>
        [HttpGet("ssl-config")]
        public IActionResult GetSslConfiguration()
        {
            try
            {
                var httpUrl = _configuration["Kestrel:EndPoints:Http:Url"];
                var httpsUrl = _configuration["Kestrel:EndPoints:Https:Url"];
                var certPath = _configuration["Kestrel:EndPoints:Https:Certificate:Path"];
                var protocols = _configuration["Kestrel:EndPoints:Https:Protocols"];
                var sslProtocols = _configuration.GetSection("Kestrel:EndPoints:Https:SslProtocols").Get<string[]>();

                var config = new
                {
                    endpoints = new
                    {
                        http = httpUrl,
                        https = httpsUrl
                    },
                    certificate = new
                    {
                        path = certPath,
                        exists = !string.IsNullOrEmpty(certPath) && System.IO.File.Exists(certPath)
                    },
                    protocols = protocols,
                    sslProtocols = sslProtocols,
                    httpsRedirection = true // 假设启用了 HTTPS 重定向
                };

                return Ok(new { success = true, data = config });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取SSL配置信息失败");
                return StatusCode(500, new { message = "获取SSL配置失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 验证新证书
        /// </summary>
        /// <param name="request">证书验证请求</param>
        /// <returns>验证结果</returns>
        [HttpPost("validate-new-certificate")]
        public async Task<IActionResult> ValidateNewCertificate([FromBody] ValidateNewCertificateRequest request)
        {
            try
            {
                if (string.IsNullOrEmpty(request.CertificatePath) || string.IsNullOrEmpty(request.Password))
                {
                    return BadRequest(new { message = "证书路径和密码不能为空" });
                }

                var validationResult = await _replacementService.ValidateNewCertificateAsync(request.CertificatePath, request.Password);

                _logger.LogUserAction(
                    User.Identity?.Name ?? "Anonymous",
                    "验证新SSL证书",
                    new { CertificatePath = request.CertificatePath, IsValid = validationResult.IsValid }
                );

                return Ok(new { success = true, data = validationResult });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证新证书失败");
                return StatusCode(500, new { message = "验证新证书失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 更换证书
        /// </summary>
        /// <param name="request">证书更换请求</param>
        /// <returns>更换结果</returns>
        [HttpPost("replace-certificate")]
        public async Task<IActionResult> ReplaceCertificate([FromBody] ReplaceCertificateRequest request)
        {
            try
            {
                if (string.IsNullOrEmpty(request.NewCertificatePath) || string.IsNullOrEmpty(request.NewPassword))
                {
                    return BadRequest(new { message = "新证书路径和密码不能为空" });
                }

                var replacementResult = await _replacementService.ReplaceCertificateAsync(
                    request.NewCertificatePath,
                    request.NewPassword,
                    request.CreateBackup);

                _logger.LogUserAction(
                    User.Identity?.Name ?? "Anonymous",
                    "更换SSL证书",
                    new {
                        NewCertificatePath = request.NewCertificatePath,
                        IsSuccess = replacementResult.IsSuccess,
                        BackupPath = replacementResult.BackupPath
                    }
                );

                return Ok(new { success = true, data = replacementResult });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更换证书失败");
                return StatusCode(500, new { message = "更换证书失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 回滚证书
        /// </summary>
        /// <param name="request">证书回滚请求</param>
        /// <returns>回滚结果</returns>
        [HttpPost("rollback-certificate")]
        public async Task<IActionResult> RollbackCertificate([FromBody] RollbackCertificateRequest request)
        {
            try
            {
                if (string.IsNullOrEmpty(request.BackupPath))
                {
                    return BadRequest(new { message = "备份证书路径不能为空" });
                }

                var rollbackResult = await _replacementService.RollbackCertificateAsync(request.BackupPath);

                _logger.LogUserAction(
                    User.Identity?.Name ?? "Anonymous",
                    "回滚SSL证书",
                    new { BackupPath = request.BackupPath, IsSuccess = rollbackResult.IsSuccess }
                );

                return Ok(new { success = true, data = rollbackResult });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "回滚证书失败");
                return StatusCode(500, new { message = "回滚证书失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 获取证书更换历史
        /// </summary>
        /// <returns>更换历史</returns>
        [HttpGet("replacement-history")]
        public async Task<IActionResult> GetReplacementHistory()
        {
            try
            {
                var history = await _replacementService.GetReplacementHistoryAsync();
                return Ok(new { success = true, data = history });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取证书更换历史失败");
                return StatusCode(500, new { message = "获取更换历史失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 立即检查证书状态
        /// </summary>
        /// <returns>监控结果</returns>
        [HttpPost("check-certificate-now")]
        public async Task<IActionResult> CheckCertificateNow()
        {
            try
            {
                var monitoringResult = await _monitoringService.CheckCertificateNowAsync();

                _logger.LogUserAction(
                    User.Identity?.Name ?? "Anonymous",
                    "手动检查SSL证书状态",
                    new { IsSuccess = monitoringResult.IsSuccess, DaysUntilExpiry = monitoringResult.DaysUntilExpiry }
                );

                return Ok(new { success = true, data = monitoringResult });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查证书状态失败");
                return StatusCode(500, new { message = "检查证书状态失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 获取过期建议
        /// </summary>
        /// <param name="daysUntilExpiry">距离过期天数</param>
        /// <returns>建议信息</returns>
        private static string GetExpiryRecommendation(int daysUntilExpiry)
        {
            return daysUntilExpiry switch
            {
                <= 0 => "证书已过期，请立即更新证书！",
                <= 7 => "证书将在一周内过期，请尽快更新证书！",
                <= 30 => "证书将在30天内过期，建议准备更新证书。",
                <= 90 => "证书将在90天内过期，可以开始准备更新证书。",
                _ => "证书有效期充足。"
            };
        }
    }

    /// <summary>
    /// 验证新证书请求
    /// </summary>
    public class ValidateNewCertificateRequest
    {
        public string CertificatePath { get; set; } = string.Empty;
        public string Password { get; set; } = string.Empty;
    }

    /// <summary>
    /// 更换证书请求
    /// </summary>
    public class ReplaceCertificateRequest
    {
        public string NewCertificatePath { get; set; } = string.Empty;
        public string NewPassword { get; set; } = string.Empty;
        public bool CreateBackup { get; set; } = true;
    }

    /// <summary>
    /// 回滚证书请求
    /// </summary>
    public class RollbackCertificateRequest
    {
        public string BackupPath { get; set; } = string.Empty;
    }
}
