using CoreHub.Shared.Data;
using CoreHub.Shared.Models.Database;
using SqlSugar;

namespace CoreHub.Shared.Services
{
    /// <summary>
    /// 部门类型服务实现
    /// </summary>
    public class DepartmentTypeService : IDepartmentTypeService
    {
        private readonly DatabaseContext _context;

        public DepartmentTypeService(DatabaseContext context)
        {
            _context = context;
        }

        public async Task<List<DepartmentType>> GetEnabledDepartmentTypesAsync()
        {
            return await _context.Db.Queryable<DepartmentType>()
                .Where(dt => dt.IsEnabled)
                .OrderBy(dt => dt.SortOrder)
                .ToListAsync();
        }

        public async Task<List<DepartmentType>> GetAllDepartmentTypesAsync()
        {
            return await _context.Db.Queryable<DepartmentType>()
                .OrderBy(dt => dt.SortOrder)
                .ToListAsync();
        }

        public async Task<DepartmentType?> GetDepartmentTypeByIdAsync(int id)
        {
            return await _context.Db.Queryable<DepartmentType>()
                .Where(dt => dt.Id == id)
                .FirstAsync();
        }

        public async Task<DepartmentType?> GetDepartmentTypeByCodeAsync(string code)
        {
            return await _context.Db.Queryable<DepartmentType>()
                .Where(dt => dt.Code == code)
                .FirstAsync();
        }

        public async Task<bool> CreateDepartmentTypeAsync(DepartmentType departmentType)
        {
            departmentType.CreatedAt = DateTime.Now;
            var result = await _context.Db.Insertable(departmentType).ExecuteCommandAsync();
            return result > 0;
        }

        public async Task<bool> UpdateDepartmentTypeAsync(DepartmentType departmentType)
        {
            departmentType.UpdatedAt = DateTime.Now;
            var result = await _context.Db.Updateable(departmentType).ExecuteCommandAsync();
            return result > 0;
        }

        public async Task<bool> DeleteDepartmentTypeAsync(int id)
        {
            var result = await _context.Db.Deleteable<DepartmentType>()
                .Where(dt => dt.Id == id)
                .ExecuteCommandAsync();
            return result > 0;
        }

        public async Task<bool> ExistsByCodeAsync(string code, int? excludeId = null)
        {
            var query = _context.Db.Queryable<DepartmentType>()
                .Where(dt => dt.Code == code);

            if (excludeId.HasValue)
            {
                query = query.Where(dt => dt.Id != excludeId.Value);
            }

            return await query.AnyAsync();
        }

        public async Task<List<Department>> GetMaintenanceDepartmentsAsync()
        {
            return await _context.Db.Queryable<Department>()
                .LeftJoin<DepartmentType>((d, dt) => d.DepartmentTypeId == dt.Id)
                .Where((d, dt) => dt.Code == DepartmentTypeCodes.Maintenance && d.IsEnabled)
                .Select((d, dt) => d)
                .OrderBy(d => d.SortOrder)
                .ToListAsync();
        }

        public async Task<List<Department>> GetProductionDepartmentsAsync()
        {
            return await _context.Db.Queryable<Department>()
                .LeftJoin<DepartmentType>((d, dt) => d.DepartmentTypeId == dt.Id)
                .Where((d, dt) => dt.Code == DepartmentTypeCodes.Production && d.IsEnabled)
                .Select((d, dt) => d)
                .OrderBy(d => d.SortOrder)
                .ToListAsync();
        }

        public async Task<List<Department>> GetDepartmentsByTypeAsync(string departmentTypeCode)
        {
            return await _context.Db.Queryable<Department>()
                .LeftJoin<DepartmentType>((d, dt) => d.DepartmentTypeId == dt.Id)
                .Where((d, dt) => dt.Code == departmentTypeCode && d.IsEnabled)
                .Select((d, dt) => new Department
                {
                    Id = d.Id,
                    Code = d.Code,
                    Name = d.Name,
                    Description = d.Description,
                    DepartmentTypeId = d.DepartmentTypeId,
                    ParentId = d.ParentId,
                    Level = d.Level,
                    SortOrder = d.SortOrder,
                    IsEnabled = d.IsEnabled,
                    CreatedAt = d.CreatedAt,
                    DepartmentType = dt
                })
                .OrderBy(d => d.SortOrder)
                .ToListAsync();
        }
    }
}
