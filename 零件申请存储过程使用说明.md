# 零件申请存储过程使用说明

## 📋 概述

基于 CoreHub 系统的零件申请逻辑和数据库表结构，创建了两个存储过程用于外部仓库系统集成和零件发放管理。

## 🗃️ 数据库表结构

### RepairOrderPartRequests 表字段说明

| 字段名 | 类型 | 说明 | 备注 |
|--------|------|------|------|
| Id | int | 主键ID | 自增 |
| RepairOrderId | int | 关联的维修单ID | 外键 |
| PartName | nvarchar(100) | 零件名称 | 必填 |
| Specification | nvarchar(200) | 规格型号 | 可选 |
| RequestedQuantity | int | 申请数量 | 默认1 |
| Unit | nvarchar(20) | 计量单位 | 默认'个' |
| Status | int | 状态 | 1=申请中,2=已领用,3=已安装,4=已取消 |
| RequestedBy | int | 申请人ID | 外键 |
| RequestedAt | datetime2 | 申请时间 | 默认当前时间 |
| ActualQuantity | int | 实际领用数量 | 外部系统写入 |
| IssuedBy | int | 发放人ID | 外键 |
| IssuedAt | datetime2 | 发放时间 | 系统自动设置 |
| WarehouseOrderNumber | nvarchar(50) | 仓库出库单号 | 外部系统提供 |
| UnitPrice | decimal(18,2) | 单价 | 可选 |
| TotalCost | decimal(18,2) | 总成本 | 可选 |
| ExternalPartNumber | nvarchar(50) | 外部系统零件编号 | 集成字段 |
| ExternalRequisitionDetailId | nvarchar(50) | 外部系统领用单明细ID | 集成字段 |

## 🔧 存储过程详细说明

### 1. sp_GetPendingPartRequests - 获取待领用零件信息

#### 功能描述
查询所有状态为"申请中"(Status=1)的零件申请记录，为仓库管理员提供发放零件的依据和参考信息。

#### 参数说明
```sql
@DepartmentId INT = NULL           -- 可选：按部门过滤
@UrgencyLevel INT = NULL           -- 可选：按紧急程度过滤（1=紧急,2=高,3=中,4=低）
@StartDate DATETIME2 = NULL        -- 可选：申请开始时间
@EndDate DATETIME2 = NULL          -- 可选：申请结束时间
@PageIndex INT = 1                 -- 分页：页码（从1开始）
@PageSize INT = 50                 -- 分页：每页记录数（最大1000）
```

#### 返回字段
- **零件申请信息**：申请ID、零件名称、规格型号、申请数量、申请时间、申请人信息
- **报修单信息**：报修单号、设备名称、设备编码、故障描述、紧急程度
- **部门信息**：设备所属部门、维修部门
- **统计信息**：总记录数（用于分页）

#### 使用示例
```sql
-- 基本查询
EXEC sp_GetPendingPartRequests;

-- 按紧急程度过滤
EXEC sp_GetPendingPartRequests @UrgencyLevel = 1;

-- 分页查询
EXEC sp_GetPendingPartRequests @PageIndex = 1, @PageSize = 20;

-- 按时间范围查询
EXEC sp_GetPendingPartRequests 
    @StartDate = '2025-01-01', 
    @EndDate = '2025-01-31';

-- 组合条件查询
EXEC sp_GetPendingPartRequests 
    @UrgencyLevel = 1,
    @PageIndex = 1, 
    @PageSize = 10;
```

### 2. sp_UpdatePartIssueInfo - 更新零件发放信息

#### 功能描述
供外部仓库系统调用，更新零件的实际发放信息，将状态从"申请中"更新为"已领用"。

#### 参数说明
```sql
@PartRequestId INT                     -- 零件申请ID（必填）
@ActualQuantity INT                    -- 实际发放数量（必填，必须>0）
@WarehouseOrderNumber NVARCHAR(50)     -- 仓库单号（必填）
@UnitPrice DECIMAL(18,2) = NULL       -- 单价（可选）
@TotalCost DECIMAL(18,2) = NULL       -- 总成本（可选，如果未提供会根据单价计算）
@IssuerId INT                          -- 发放人员ID（必填）
@ExternalSystemId NVARCHAR(50) = NULL -- 外部系统ID（可选，用于集成追踪）
@ActualPartName NVARCHAR(100) = NULL  -- 实际发放零件名称（可选）
@ActualSpecification NVARCHAR(200) = NULL -- 实际发放规格（可选）
@ExternalPartNumber NVARCHAR(50) = NULL   -- 外部系统零件编号（可选）
```

#### 返回结果
返回一个结果集，包含以下字段：
- `IsSuccess`: 操作是否成功（1=成功，0=失败）
- `Message`: 操作结果消息
- `UpdatedRecords`: 更新的记录数
- `PartRequestId`: 零件申请ID
- `PartName`: 零件名称
- `ActualQuantity`: 实际发放数量
- `WarehouseOrderNumber`: 仓库单号
- `IssuedAt`: 发放时间
- `IssuedByName`: 发放人姓名
- `RepairOrderNumber`: 关联的报修单号

#### 使用示例
```sql
-- 基本发放
EXEC sp_UpdatePartIssueInfo
    @PartRequestId = 1,
    @ActualQuantity = 2,
    @WarehouseOrderNumber = 'WH20250110001',
    @IssuerId = 5;

-- 完整信息发放
EXEC sp_UpdatePartIssueInfo
    @PartRequestId = 1,
    @ActualQuantity = 2,
    @WarehouseOrderNumber = 'WH20250110001',
    @UnitPrice = 150.00,
    @TotalCost = 300.00,
    @IssuerId = 5,
    @ExternalSystemId = 'EXT001',
    @ActualPartName = '实际发放的零件名称',
    @ActualSpecification = '实际规格',
    @ExternalPartNumber = 'P001';
```

## 🔒 错误处理和验证

### sp_GetPendingPartRequests 错误处理
- 自动修正无效的分页参数
- 包含完整的异常捕获和错误信息返回

### sp_UpdatePartIssueInfo 验证规则
1. **参数验证**：
   - 零件申请ID必须有效
   - 实际发放数量必须大于0
   - 仓库单号不能为空
   - 发放人员ID必须有效

2. **业务验证**：
   - 零件申请记录必须存在
   - 状态必须为"申请中"(1)
   - 发放人员必须存在于用户表中

3. **事务处理**：
   - 使用事务确保数据一致性
   - 失败时自动回滚

## 🔄 集成建议

### 外部仓库系统集成流程

1. **获取待发放零件列表**
   ```sql
   EXEC sp_GetPendingPartRequests @UrgencyLevel = 1; -- 优先处理紧急的
   ```

2. **处理发放操作**
   ```sql
   EXEC sp_UpdatePartIssueInfo
       @PartRequestId = [从步骤1获取的ID],
       @ActualQuantity = [实际发放数量],
       @WarehouseOrderNumber = [仓库系统生成的单号],
       @IssuerId = [发放人员ID],
       @ExternalSystemId = [外部系统追踪ID];
   ```

3. **检查操作结果**
   - 检查返回的 `IsSuccess` 字段
   - 记录 `Message` 中的详细信息
   - 处理失败情况

### 性能优化建议

1. **索引使用**：
   - 已创建状态索引：`IX_RepairOrderPartRequests_Status`
   - 已创建时间索引：`IX_RepairOrderPartRequests_RequestedAt`
   - 已创建报修单索引：`IX_RepairOrderPartRequests_RepairOrderId`

2. **分页查询**：
   - 建议使用分页查询，避免一次性加载大量数据
   - 默认页大小为50，最大不超过1000

3. **并发控制**：
   - `sp_UpdatePartIssueInfo` 使用事务处理
   - 建议在高并发环境下实现应用层锁定机制

## 📊 监控和维护

### 建议监控指标
1. 待发放零件数量
2. 发放处理时间
3. 失败操作统计
4. 紧急零件申请响应时间

### 维护建议
1. 定期清理历史数据
2. 监控存储过程执行性能
3. 检查外部系统集成状态
4. 备份重要的零件申请数据

## 🚀 部署步骤

1. **执行创建脚本**：
   ```sql
   -- 执行零件申请存储过程.sql
   ```

2. **运行测试脚本**：
   ```sql
   -- 执行零件申请存储过程测试.sql
   ```

3. **验证功能**：
   - 检查存储过程是否创建成功
   - 验证测试结果是否符合预期
   - 确认权限设置正确

4. **配置外部系统**：
   - 提供数据库连接信息
   - 配置调用权限
   - 设置监控和日志

通过这两个存储过程，外部仓库系统可以高效地获取待发放零件信息并更新发放状态，实现与 CoreHub 系统的无缝集成。
