using System.ComponentModel.DataAnnotations;

namespace CustomerWebAPI.Models
{
    public class ChemicalBoxResult
    {
        [Required(ErrorMessage = "化学品代码不能为空")]
        public string ChemicalCode { get; set; }

        [Required(ErrorMessage = "化学品名称不能为空")]
        public string ChemicalName { get; set; }

        [Required(ErrorMessage = "化学品箱号不能为空")]
        public string ChemicalBoxNo { get; set; }
    }
}