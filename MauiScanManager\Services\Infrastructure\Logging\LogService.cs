using System;
using System.IO;

namespace MauiScanManager.Services
{
    public static class LogService
    {
        private static readonly string LogFile = Path.Combine(FileSystem.AppDataDirectory, "update.log");

        public static void Log(string message)
        {
            try
            {
                var logMessage = $"{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff} - {message}{Environment.NewLine}";
                File.AppendAllText(LogFile, logMessage);
            }
            catch { }
        }

        public static void Clear()
        {
            try
            {
                if (File.Exists(LogFile))
                    File.Delete(LogFile);
            }
            catch { }
        }

        public static string GetLogs()
        {
            try
            {
                return File.Exists(LogFile) ? File.ReadAllText(LogFile) : string.Empty;
            }
            catch
            {
                return string.Empty;
            }
        }
    }
}