-- =============================================
-- 设备管理系统数据库脚本 (SQL Server 2012+)
-- 创建日期: 2024-12-19
-- 描述: 包含部门、设备型号、位置、设备和报修单管理的完整数据库结构
-- =============================================

USE [master]
GO

-- 创建数据库（如果不存在）
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = N'EquipmentManagement')
BEGIN
    CREATE DATABASE [EquipmentManagement]
    COLLATE Chinese_PRC_CI_AS
END
GO

USE [EquipmentManagement]
GO

-- =============================================
-- 1. 部门表 (Departments)
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[Departments]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[Departments](
        [Id] [int] IDENTITY(1,1) NOT NULL,
        [Code] [nvarchar](50) NOT NULL,
        [Name] [nvarchar](100) NOT NULL,
        [Description] [nvarchar](500) NULL,
        [ParentId] [int] NULL,
        [Level] [int] NOT NULL DEFAULT(1),
        [SortOrder] [int] NOT NULL DEFAULT(0),
        [IsEnabled] [bit] NOT NULL DEFAULT(1),
        [CreatedAt] [datetime] NOT NULL DEFAULT(GETDATE()),
        [CreatedBy] [int] NULL,
        [UpdatedAt] [datetime] NULL,
        [UpdatedBy] [int] NULL,
        [Remark] [nvarchar](1000) NULL,
        CONSTRAINT [PK_Departments] PRIMARY KEY CLUSTERED ([Id] ASC),
        CONSTRAINT [UK_Departments_Code] UNIQUE NONCLUSTERED ([Code] ASC),
        CONSTRAINT [UK_Departments_Name] UNIQUE NONCLUSTERED ([Name] ASC)
    )
END
GO

-- 部门表索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[Departments]') AND name = N'IX_Departments_ParentId')
    CREATE NONCLUSTERED INDEX [IX_Departments_ParentId] ON [dbo].[Departments] ([ParentId] ASC)
GO

-- =============================================
-- 2. 设备型号表 (EquipmentModels)
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[EquipmentModels]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[EquipmentModels](
        [Id] [int] IDENTITY(1,1) NOT NULL,
        [Code] [nvarchar](50) NOT NULL,
        [Name] [nvarchar](100) NOT NULL,
        [Category] [nvarchar](50) NOT NULL,
        [Brand] [nvarchar](100) NULL,
        [Model] [nvarchar](100) NULL,
        [Specifications] [nvarchar](1000) NULL,
        [Description] [nvarchar](500) NULL,
        [IsEnabled] [bit] NOT NULL DEFAULT(1),
        [CreatedAt] [datetime] NOT NULL DEFAULT(GETDATE()),
        [CreatedBy] [int] NULL,
        [UpdatedAt] [datetime] NULL,
        [UpdatedBy] [int] NULL,
        [Remark] [nvarchar](1000) NULL,
        CONSTRAINT [PK_EquipmentModels] PRIMARY KEY CLUSTERED ([Id] ASC),
        CONSTRAINT [UK_EquipmentModels_Code] UNIQUE NONCLUSTERED ([Code] ASC)
    )
END
GO

-- 设备型号表索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[EquipmentModels]') AND name = N'IX_EquipmentModels_Category')
    CREATE NONCLUSTERED INDEX [IX_EquipmentModels_Category] ON [dbo].[EquipmentModels] ([Category] ASC)
GO

-- =============================================
-- 3. 位置表 (Locations)
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[Locations]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[Locations](
        [Id] [int] IDENTITY(1,1) NOT NULL,
        [Code] [nvarchar](50) NOT NULL,
        [Name] [nvarchar](100) NOT NULL,
        [DepartmentId] [int] NOT NULL,
        [ParentId] [int] NULL,
        [Level] [int] NOT NULL DEFAULT(1),
        [Address] [nvarchar](200) NULL,
        [Description] [nvarchar](500) NULL,
        [IsEnabled] [bit] NOT NULL DEFAULT(1),
        [CreatedAt] [datetime] NOT NULL DEFAULT(GETDATE()),
        [CreatedBy] [int] NULL,
        [UpdatedAt] [datetime] NULL,
        [UpdatedBy] [int] NULL,
        [Remark] [nvarchar](1000) NULL,
        CONSTRAINT [PK_Locations] PRIMARY KEY CLUSTERED ([Id] ASC),
        CONSTRAINT [UK_Locations_Code] UNIQUE NONCLUSTERED ([Code] ASC),
        CONSTRAINT [FK_Locations_Departments] FOREIGN KEY([DepartmentId]) REFERENCES [dbo].[Departments] ([Id])
    )
END
GO

-- 位置表索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[Locations]') AND name = N'IX_Locations_DepartmentId')
    CREATE NONCLUSTERED INDEX [IX_Locations_DepartmentId] ON [dbo].[Locations] ([DepartmentId] ASC)
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[Locations]') AND name = N'IX_Locations_ParentId')
    CREATE NONCLUSTERED INDEX [IX_Locations_ParentId] ON [dbo].[Locations] ([ParentId] ASC)
GO

-- =============================================
-- 4. 设备表 (Equipment)
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[Equipment]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[Equipment](
        [Id] [int] IDENTITY(1,1) NOT NULL,
        [Code] [nvarchar](50) NOT NULL,
        [Name] [nvarchar](100) NOT NULL,
        [DepartmentId] [int] NOT NULL,
        [ModelId] [int] NOT NULL,
        [LocationId] [int] NOT NULL,
        [SerialNumber] [nvarchar](100) NULL,
        [AssetNumber] [nvarchar](100) NULL,
        [PurchaseDate] [datetime] NULL,
        [WarrantyExpiry] [datetime] NULL,
        [Status] [int] NOT NULL DEFAULT(1), -- 1=正常,2=维修中,3=停用,4=报废
        [LastMaintenanceDate] [datetime] NULL,
        [NextMaintenanceDate] [datetime] NULL,
        [Description] [nvarchar](500) NULL,
        [IsEnabled] [bit] NOT NULL DEFAULT(1),
        [CreatedAt] [datetime] NOT NULL DEFAULT(GETDATE()),
        [CreatedBy] [int] NULL,
        [UpdatedAt] [datetime] NULL,
        [UpdatedBy] [int] NULL,
        [Remark] [nvarchar](1000) NULL,
        CONSTRAINT [PK_Equipment] PRIMARY KEY CLUSTERED ([Id] ASC),
        CONSTRAINT [UK_Equipment_Code] UNIQUE NONCLUSTERED ([Code] ASC),
        CONSTRAINT [FK_Equipment_Departments] FOREIGN KEY([DepartmentId]) REFERENCES [dbo].[Departments] ([Id]),
        CONSTRAINT [FK_Equipment_EquipmentModels] FOREIGN KEY([ModelId]) REFERENCES [dbo].[EquipmentModels] ([Id]),
        CONSTRAINT [FK_Equipment_Locations] FOREIGN KEY([LocationId]) REFERENCES [dbo].[Locations] ([Id])
    )
END
GO

-- 设备表索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[Equipment]') AND name = N'IX_Equipment_DepartmentId')
    CREATE NONCLUSTERED INDEX [IX_Equipment_DepartmentId] ON [dbo].[Equipment] ([DepartmentId] ASC)
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[Equipment]') AND name = N'IX_Equipment_ModelId')
    CREATE NONCLUSTERED INDEX [IX_Equipment_ModelId] ON [dbo].[Equipment] ([ModelId] ASC)
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[Equipment]') AND name = N'IX_Equipment_LocationId')
    CREATE NONCLUSTERED INDEX [IX_Equipment_LocationId] ON [dbo].[Equipment] ([LocationId] ASC)
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[Equipment]') AND name = N'IX_Equipment_Status')
    CREATE NONCLUSTERED INDEX [IX_Equipment_Status] ON [dbo].[Equipment] ([Status] ASC)
GO

-- =============================================
-- 5. 报修单表 (RepairOrders)
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[RepairOrders]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[RepairOrders](
        [Id] [int] IDENTITY(1,1) NOT NULL,
        [OrderNumber] [nvarchar](50) NOT NULL,
        [EquipmentId] [int] NOT NULL,
        [ReporterId] [int] NOT NULL,
        [FaultDescription] [nvarchar](1000) NOT NULL,
        [UrgencyLevel] [int] NOT NULL DEFAULT(2), -- 1=紧急,2=高,3=中,4=低
        [MaintenanceDepartmentId] [int] NOT NULL,
        [Status] [int] NOT NULL DEFAULT(1), -- 1=待处理,2=处理中,3=已完成,4=已作废,5=已关闭
        [AssignedTo] [int] NULL,
        [AssignedAt] [datetime] NULL,
        [StartedAt] [datetime] NULL,
        [CompletedAt] [datetime] NULL,
        [CancelledAt] [datetime] NULL,
        [CancelReason] [nvarchar](500) NULL,
        [RepairDescription] [nvarchar](1000) NULL,
        [RepairCost] [decimal](18,2) NULL,
        [PartsUsed] [nvarchar](1000) NULL,
        [TestResult] [nvarchar](500) NULL,
        [ReporterRating] [int] NULL, -- 1-5星评价
        [ReporterComment] [nvarchar](500) NULL,
        [CreatedAt] [datetime] NOT NULL DEFAULT(GETDATE()),
        [UpdatedAt] [datetime] NULL,
        [Remark] [nvarchar](1000) NULL,
        CONSTRAINT [PK_RepairOrders] PRIMARY KEY CLUSTERED ([Id] ASC),
        CONSTRAINT [UK_RepairOrders_OrderNumber] UNIQUE NONCLUSTERED ([OrderNumber] ASC),
        CONSTRAINT [FK_RepairOrders_Equipment] FOREIGN KEY([EquipmentId]) REFERENCES [dbo].[Equipment] ([Id]),
        CONSTRAINT [FK_RepairOrders_MaintenanceDepartments] FOREIGN KEY([MaintenanceDepartmentId]) REFERENCES [dbo].[Departments] ([Id])
    )
END
GO

-- 报修单表索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[RepairOrders]') AND name = N'IX_RepairOrders_EquipmentId')
    CREATE NONCLUSTERED INDEX [IX_RepairOrders_EquipmentId] ON [dbo].[RepairOrders] ([EquipmentId] ASC)
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[RepairOrders]') AND name = N'IX_RepairOrders_ReporterId')
    CREATE NONCLUSTERED INDEX [IX_RepairOrders_ReporterId] ON [dbo].[RepairOrders] ([ReporterId] ASC)
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[RepairOrders]') AND name = N'IX_RepairOrders_Status')
    CREATE NONCLUSTERED INDEX [IX_RepairOrders_Status] ON [dbo].[RepairOrders] ([Status] ASC)
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[RepairOrders]') AND name = N'IX_RepairOrders_CreatedAt')
    CREATE NONCLUSTERED INDEX [IX_RepairOrders_CreatedAt] ON [dbo].[RepairOrders] ([CreatedAt] DESC)
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[RepairOrders]') AND name = N'IX_RepairOrders_UrgencyLevel')
    CREATE NONCLUSTERED INDEX [IX_RepairOrders_UrgencyLevel] ON [dbo].[RepairOrders] ([UrgencyLevel] ASC)
GO

-- =============================================
-- 6. 报修单附件表 (RepairOrderAttachments)
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[RepairOrderAttachments]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[RepairOrderAttachments](
        [Id] [int] IDENTITY(1,1) NOT NULL,
        [RepairOrderId] [int] NOT NULL,
        [FileName] [nvarchar](255) NOT NULL,
        [OriginalFileName] [nvarchar](255) NOT NULL,
        [FilePath] [nvarchar](500) NOT NULL,
        [FileSize] [bigint] NOT NULL,
        [ContentType] [nvarchar](100) NOT NULL,
        [AttachmentType] [int] NOT NULL DEFAULT(1), -- 1=故障图片,2=维修图片,3=其他文档
        [UploadedBy] [int] NOT NULL,
        [UploadedAt] [datetime] NOT NULL DEFAULT(GETDATE()),
        [Description] [nvarchar](500) NULL,
        CONSTRAINT [PK_RepairOrderAttachments] PRIMARY KEY CLUSTERED ([Id] ASC),
        CONSTRAINT [FK_RepairOrderAttachments_RepairOrders] FOREIGN KEY([RepairOrderId]) REFERENCES [dbo].[RepairOrders] ([Id]) ON DELETE CASCADE
    )
END
GO

-- 报修单附件表索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[RepairOrderAttachments]') AND name = N'IX_RepairOrderAttachments_RepairOrderId')
    CREATE NONCLUSTERED INDEX [IX_RepairOrderAttachments_RepairOrderId] ON [dbo].[RepairOrderAttachments] ([RepairOrderId] ASC)
GO

-- =============================================
-- 创建视图
-- =============================================

-- 设备详细信息视图
IF EXISTS (SELECT * FROM sys.views WHERE object_id = OBJECT_ID(N'[dbo].[V_EquipmentDetails]'))
    DROP VIEW [dbo].[V_EquipmentDetails]
GO

CREATE VIEW [dbo].[V_EquipmentDetails]
AS
SELECT
    e.Id,
    e.Code,
    e.Name,
    e.SerialNumber,
    e.AssetNumber,
    e.Status,
    CASE e.Status
        WHEN 1 THEN N'正常'
        WHEN 2 THEN N'维修中'
        WHEN 3 THEN N'停用'
        WHEN 4 THEN N'报废'
        ELSE N'未知'
    END AS StatusName,
    d.Name AS DepartmentName,
    d.Code AS DepartmentCode,
    em.Name AS ModelName,
    em.Category AS ModelCategory,
    em.Brand AS ModelBrand,
    l.Name AS LocationName,
    l.Address AS LocationAddress,
    e.PurchaseDate,
    e.WarrantyExpiry,
    e.LastMaintenanceDate,
    e.NextMaintenanceDate,
    e.Description,
    e.IsEnabled,
    e.CreatedAt,
    e.Remark
FROM [dbo].[Equipment] e
    INNER JOIN [dbo].[Departments] d ON e.DepartmentId = d.Id
    INNER JOIN [dbo].[EquipmentModels] em ON e.ModelId = em.Id
    INNER JOIN [dbo].[Locations] l ON e.LocationId = l.Id
WHERE e.IsEnabled = 1
GO

-- 报修单详细信息视图
IF EXISTS (SELECT * FROM sys.views WHERE object_id = OBJECT_ID(N'[dbo].[V_RepairOrderDetails]'))
    DROP VIEW [dbo].[V_RepairOrderDetails]
GO

CREATE VIEW [dbo].[V_RepairOrderDetails]
AS
SELECT
    ro.Id,
    ro.OrderNumber,
    ro.FaultDescription,
    ro.UrgencyLevel,
    CASE ro.UrgencyLevel
        WHEN 1 THEN N'紧急'
        WHEN 2 THEN N'高'
        WHEN 3 THEN N'中'
        WHEN 4 THEN N'低'
        ELSE N'未知'
    END AS UrgencyLevelName,
    ro.Status,
    CASE ro.Status
        WHEN 1 THEN N'待处理'
        WHEN 2 THEN N'处理中'
        WHEN 3 THEN N'已完成'
        WHEN 4 THEN N'已作废'
        WHEN 5 THEN N'已关闭'
        ELSE N'未知'
    END AS StatusName,
    e.Code AS EquipmentCode,
    e.Name AS EquipmentName,
    ed.Name AS EquipmentDepartmentName,
    em.Name AS EquipmentModelName,
    el.Name AS EquipmentLocationName,
    md.Name AS MaintenanceDepartmentName,
    ro.ReporterId,
    ro.AssignedTo,
    ro.AssignedAt,
    ro.StartedAt,
    ro.CompletedAt,
    ro.CancelledAt,
    ro.CancelReason,
    ro.RepairDescription,
    ro.RepairCost,
    ro.PartsUsed,
    ro.TestResult,
    ro.ReporterRating,
    ro.ReporterComment,
    ro.CreatedAt,
    ro.UpdatedAt,
    ro.Remark
FROM [dbo].[RepairOrders] ro
    INNER JOIN [dbo].[Equipment] e ON ro.EquipmentId = e.Id
    INNER JOIN [dbo].[Departments] ed ON e.DepartmentId = ed.Id
    INNER JOIN [dbo].[EquipmentModels] em ON e.ModelId = em.Id
    INNER JOIN [dbo].[Locations] el ON e.LocationId = el.Id
    INNER JOIN [dbo].[Departments] md ON ro.MaintenanceDepartmentId = md.Id
GO

-- =============================================
-- 创建存储过程
-- =============================================

-- 生成报修单号的存储过程
IF EXISTS (SELECT * FROM sys.procedures WHERE object_id = OBJECT_ID(N'[dbo].[sp_GenerateRepairOrderNumber]'))
    DROP PROCEDURE [dbo].[sp_GenerateRepairOrderNumber]
GO

CREATE PROCEDURE [dbo].[sp_GenerateRepairOrderNumber]
    @OrderNumber NVARCHAR(50) OUTPUT
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @Today NVARCHAR(8) = CONVERT(NVARCHAR(8), GETDATE(), 112)
    DECLARE @Prefix NVARCHAR(10) = 'RO' + @Today
    DECLARE @MaxSeq INT = 0

    -- 获取今天的最大序号
    SELECT @MaxSeq = ISNULL(MAX(CAST(RIGHT(OrderNumber, 3) AS INT)), 0)
    FROM [dbo].[RepairOrders]
    WHERE OrderNumber LIKE @Prefix + '%'

    -- 生成新的序号
    SET @MaxSeq = @MaxSeq + 1
    SET @OrderNumber = @Prefix + RIGHT('000' + CAST(@MaxSeq AS NVARCHAR(3)), 3)
END
GO
