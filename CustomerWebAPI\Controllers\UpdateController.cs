using System;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using System.IO;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace CustomerWebAPI.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class UpdateController : ControllerBase
    {
        private readonly IUpdateService _updateService;
        private readonly ILogger<UpdateController> _logger;

        public UpdateController(IUpdateService updateService, ILogger<UpdateController> logger)
        {
            _updateService = updateService;
            _logger = logger;
        }

        [HttpGet("check/{platform}")]
        public async Task<IActionResult> CheckUpdate(string platform)
        {
            _logger.LogDebug($"Checking updates for platform: {platform}");
            var updateInfo = await _updateService.GetUpdateInfo(platform);
            if (updateInfo == null)
            {
                _logger.LogDebug($"No update info found for platform: {platform}");
                return NotFound($"未找到 {platform} 平台的更新信息");
            }
            _logger.LogDebug($"Update info: {JsonSerializer.Serialize(updateInfo)}");
            return Ok(updateInfo);
        }

        [HttpGet("latest")]
        public async Task<IActionResult> GetLatestVersion()
        {
            var updateInfo = await _updateService.GetUpdateInfo("android");
            if (updateInfo == null)
            {
                return NotFound("未找到更新信息");
            }

            var version = ((dynamic)updateInfo).Version ?? ((dynamic)updateInfo).LatestVersion;
            var fileName = $"MauiScanManager-v{version}-release.apk";

            var response = new
            {
                Version = version,
                ForceUpdate = ((dynamic)updateInfo).ForceUpdate,
                UpdateUrl = $"/api/update/download?version={version}",
                FileName = fileName
            };

            return Ok(response);
        }

        [HttpGet("download/{platform}")]
        public IActionResult DownloadUpdate(string platform, [FromQuery] string version)
        {
            _logger.LogDebug($"Downloading update for platform: {platform}, version: {version}");
            if (string.IsNullOrEmpty(version))
            {
                return BadRequest(new { message = "版本号不能为空" });
            }

            try
            {
                _logger.LogDebug($"开始下载 {platform} 平台版本 {version} 的更新包");
                
                var stream = _updateService.GetUpdatePackage(version, platform);
                
                string fileName;
                string contentType;
                
                switch (platform.ToLower())
                {
                    case "android":
                        fileName = $"MauiScanManager-v{version}-release.apk";
                        contentType = "application/vnd.android.package-archive";
                        break;
                    case "windows":
                        fileName = $"MauiScanManager-v{version}-setup.exe";
                        contentType = "application/x-msdownload";
                        break;
                    default:
                        return BadRequest(new { message = $"不支持的平台类型: {platform}" });
                }

                return File(stream, contentType, fileName);
            }
            catch (FileNotFoundException ex)
            {
                _logger.LogError(ex, $"未找到更新包文件");
                Response.ContentType = "application/json";
                return NotFound(new { 
                    message = $"未找到 {platform} 平台版本 {version} 的更新包",
                    error = ex.Message,
                    statusCode = 404
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"下载更新包时发生错误");
                Response.ContentType = "application/json";
                return StatusCode(500, new { 
                    message = "下载更新包时发生错误", 
                    error = ex.Message,
                    statusCode = 500
                });
            }
        }

        [HttpGet("download/latest")]
        public async Task<IActionResult> DownloadLatestApk()
        {
            try
            {
                var updateInfo = await _updateService.GetUpdateInfo("android");
                if (updateInfo == null)
                {
                    return NotFound(new { message = "未找到更新信息" });
                }

                var version = ((dynamic)updateInfo).Version ?? ((dynamic)updateInfo).LatestVersion;
                var stream = _updateService.GetUpdatePackage(version);
                var fileName = $"MauiScanManager-v{version}-release.apk";
                
                return File(stream, "application/vnd.android.package-archive", fileName);
            }
            catch (FileNotFoundException ex)
            {
                Response.ContentType = "application/json";
                return NotFound(new { 
                    message = "未找到最新版本的更新包",
                    statusCode = 404
                });
            }
            catch (Exception ex)
            {
                Response.ContentType = "application/json";
                return StatusCode(500, new { 
                    message = "下载更新包时发生错误", 
                    error = ex.Message,
                    statusCode = 500
                });
            }
        }

        [HttpGet("latest/{platform}")]
        public async Task<IActionResult> DownloadLatest(string platform)
        {
            _logger.LogDebug($"Received request for platform: {platform}");
            try
            {
                _logger.LogDebug($"开始获取 {platform} 平台的更新信息");
                
                var updateInfo = await _updateService.GetUpdateInfo(platform);
                if (updateInfo == null)
                {
                    _logger.LogWarning($"未找到 {platform} 平台的更新信息");
                    return NotFound(new { 
                        message = $"未找到 {platform} 平台的更新信息",
                        statusCode = 404
                    });
                }

                _logger.LogDebug($"获取到更新信息: {System.Text.Json.JsonSerializer.Serialize(updateInfo)}");

                var version = ((dynamic)updateInfo).Version ?? ((dynamic)updateInfo).LatestVersion;
                _logger.LogDebug($"版本号: {version}");

                if (string.IsNullOrEmpty(version?.ToString()))
                {
                    _logger.LogWarning("版本号为空");
                    return NotFound(new { 
                        message = $"无法获取 {platform} 平台的版本信息",
                        statusCode = 404
                    });
                }

                _logger.LogDebug($"开始获取更新包文件流，版本: {version}");
                var stream = _updateService.GetUpdatePackage(version.ToString(), platform);
                
                if (stream == null)
                {
                    _logger.LogWarning($"未找到版本 {version} 的更新包文件");
                    return NotFound(new { 
                        message = $"未找到版本 {version} 的更新包文件",
                        statusCode = 404
                    });
                }

                string fileName;
                string contentType;
                
                switch (platform.ToLower())
                {
                    case "android":
                        fileName = $"MauiScanManager-v{version}-release.apk";
                        contentType = "application/vnd.android.package-archive";
                        break;
                    case "windows":
                        fileName = $"MauiScanManager-v{version}-setup.exe";
                        contentType = "application/x-msdownload";
                        break;
                    default:
                        _logger.LogWarning($"不支持的平台类型: {platform}");
                        return BadRequest(new { 
                            message = $"不支持的平台类型: {platform}",
                            statusCode = 400
                        });
                }

                _logger.LogDebug($"准备返回文件: {fileName}");
                return File(stream, contentType, fileName);
            }
            catch (FileNotFoundException ex)
            {
                _logger.LogError(ex, $"未找到更新包文件");
                Response.ContentType = "application/json";
                return NotFound(new { 
                    message = $"未找到 {platform} 平台最新版本的更新包",
                    error = ex.Message,
                    statusCode = 404
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"下载更新包时发生错误");
                Response.ContentType = "application/json";
                return StatusCode(500, new { 
                    message = "下载更新包时发生错误", 
                    error = ex.Message,
                    statusCode = 500
                });
            }
        }

        [HttpGet("test")]
        public IActionResult Test()
        {
            return Ok("Update controller is working!");
        }

        [HttpGet("test/{platform}")]
        public IActionResult TestWithPlatform(string platform)
        {
            return Ok($"Platform: {platform}");
        }
    }
}
