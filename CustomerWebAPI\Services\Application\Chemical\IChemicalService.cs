using CustomerWebAPI.Models;
using System.Threading.Tasks;
using CustomerWebAPI.Common;

namespace CustomerWebAPI.Services
{
    public interface IChemicalService
    {
        Task<ApiResponse<ChemicalBoxResult>> GetNewChemicalBoxNo(Chemical chemicalCode);
        Task<ApiResponse<bool>> CheckChemicalChange(ChemicalCheck chemicalCheck);
        Task<ApiResponse<ChemicalBoxCheckResult>> CheckChemicalBoxConsistency(ChemicalBatchCheck batchCheck);
    }
} 