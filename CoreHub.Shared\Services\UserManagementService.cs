using CoreHub.Shared.Data;
using CoreHub.Shared.Models.Database;
using Microsoft.Extensions.Logging;
using System.Linq;


namespace CoreHub.Shared.Services
{
    /// <summary>
    /// 用户管理服务实现
    /// </summary>
    public class UserManagementService : IUserManagementService
    {
        private readonly DatabaseContext _dbContext;
        private readonly ILogger<UserManagementService> _logger;

        public UserManagementService(DatabaseContext dbContext, ILogger<UserManagementService> logger)
        {
            _dbContext = dbContext;
            _logger = logger;
        }

        #region 用户管理

        /// <summary>
        /// 创建新用户
        /// </summary>
        public async Task<(bool IsSuccess, string ErrorMessage, int? UserId)> CreateUserAsync(User user, string plainPassword)
        {
            try
            {
                _logger.LogInformation("开始创建用户：{username}", user.Username);

                // 验证输入
                if (string.IsNullOrWhiteSpace(user.Username))
                    return (false, "用户名不能为空", null);

                if (string.IsNullOrWhiteSpace(plainPassword))
                    return (false, "密码不能为空", null);

                if (string.IsNullOrWhiteSpace(user.DisplayName))
                    return (false, "显示名称不能为空", null);

                // 检查用户名是否已存在
                var existingUser = await _dbContext.Db.Queryable<User>()
                    .Where(u => u.Username == user.Username)
                    .FirstAsync();

                if (existingUser != null)
                    return (false, "用户名已存在", null);

                // 检查邮箱是否已存在（如果提供了邮箱）
                if (!string.IsNullOrEmpty(user.Email))
                {
                    var existingEmailUser = await _dbContext.Db.Queryable<User>()
                        .Where(u => u.Email == user.Email)
                        .FirstAsync();

                    if (existingEmailUser != null)
                        return (false, "邮箱已被使用", null);
                }

                // 加密密码
                user.PasswordHash = plainPassword; // 明文密码，暂不加密
                user.CreatedAt = DateTime.Now;

                // 插入用户
                var result = await _dbContext.Db.Insertable(user).ExecuteReturnEntityAsync();

                _logger.LogInformation("用户创建成功：{username}，ID：{userId}", user.Username, result.Id);
                return (true, "", result.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建用户失败：{username}", user.Username);
                return (false, $"创建用户失败：{ex.Message}", null);
            }
        }

        /// <summary>
        /// 更新用户信息
        /// </summary>
        public async Task<(bool IsSuccess, string ErrorMessage)> UpdateUserAsync(User user)
        {
            try
            {
                _logger.LogInformation("开始更新用户：{userId}", user.Id);

                // 检查用户是否存在
                var existingUser = await GetUserByIdAsync(user.Id);
                if (existingUser == null)
                    return (false, "用户不存在");

                // 检查用户名是否已被其他用户使用
                if (existingUser.Username != user.Username)
                {
                    var duplicateUser = await _dbContext.Db.Queryable<User>()
                        .Where(u => u.Username == user.Username && u.Id != user.Id)
                        .FirstAsync();

                    if (duplicateUser != null)
                        return (false, "用户名已被其他用户使用");
                }

                // 检查邮箱是否已被其他用户使用
                if (!string.IsNullOrEmpty(user.Email) && existingUser.Email != user.Email)
                {
                    var duplicateEmailUser = await _dbContext.Db.Queryable<User>()
                        .Where(u => u.Email == user.Email && u.Id != user.Id)
                        .FirstAsync();

                    if (duplicateEmailUser != null)
                        return (false, "邮箱已被其他用户使用");
                }

                user.UpdatedAt = DateTime.Now;

                // 更新用户信息（排除密码哈希）
                await _dbContext.Db.Updateable(user)
                    .IgnoreColumns(u => new { u.PasswordHash, u.CreatedAt, u.CreatedBy })
                    .ExecuteCommandAsync();

                _logger.LogInformation("用户更新成功：{userId}", user.Id);
                return (true, "");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新用户失败：{userId}", user.Id);
                return (false, $"更新用户失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 修改用户密码
        /// </summary>
        public async Task<(bool IsSuccess, string ErrorMessage)> ChangePasswordAsync(int userId, string newPassword, int? operatorId = null)
        {
            try
            {
                _logger.LogInformation("开始修改用户密码：{userId}", userId);

                if (string.IsNullOrWhiteSpace(newPassword))
                    return (false, "新密码不能为空");

                var user = await GetUserByIdAsync(userId);
                if (user == null)
                    return (false, "用户不存在");

                // 加密新密码
                var newPasswordHash = newPassword; // 明文密码，暂不加密

                await _dbContext.Db.Updateable<User>()
                    .SetColumns(u => new User { PasswordHash = newPasswordHash, UpdatedAt = DateTime.Now, UpdatedBy = operatorId })
                    .Where(u => u.Id == userId)
                    .ExecuteCommandAsync();

                _logger.LogInformation("用户密码修改成功：{userId}", userId);
                return (true, "");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "修改用户密码失败：{userId}", userId);
                return (false, $"修改密码失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 启用/禁用用户
        /// </summary>
        public async Task<(bool IsSuccess, string ErrorMessage)> SetUserEnabledAsync(int userId, bool isEnabled, int? operatorId = null)
        {
            try
            {
                _logger.LogInformation("设置用户启用状态：{userId}，状态：{isEnabled}", userId, isEnabled);

                var user = await GetUserByIdAsync(userId);
                if (user == null)
                    return (false, "用户不存在");

                await _dbContext.Db.Updateable<User>()
                    .SetColumns(u => new User { IsEnabled = isEnabled, UpdatedAt = DateTime.Now, UpdatedBy = operatorId })
                    .Where(u => u.Id == userId)
                    .ExecuteCommandAsync();

                _logger.LogInformation("用户状态设置成功：{userId}，状态：{isEnabled}", userId, isEnabled);
                return (true, "");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "设置用户状态失败：{userId}", userId);
                return (false, $"设置用户状态失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 锁定/解锁用户
        /// </summary>
        public async Task<(bool IsSuccess, string ErrorMessage)> SetUserLockedAsync(int userId, bool isLocked, string? reason = null, int? operatorId = null)
        {
            try
            {
                _logger.LogInformation("设置用户锁定状态：{userId}，状态：{isLocked}，原因：{reason}", userId, isLocked, reason);

                var user = await GetUserByIdAsync(userId);
                if (user == null)
                    return (false, "用户不存在");

                var updateData = new User
                {
                    IsLocked = isLocked,
                    UpdatedAt = DateTime.Now,
                    UpdatedBy = operatorId
                };

                if (isLocked)
                {
                    updateData.LockedAt = DateTime.Now;
                    updateData.LockReason = reason ?? "管理员锁定";
                }
                else
                {
                    updateData.LockedAt = null;
                    updateData.LockReason = null;
                }

                await _dbContext.Db.Updateable(updateData)
                    .UpdateColumns(u => new { u.IsLocked, u.LockedAt, u.LockReason, u.UpdatedAt, u.UpdatedBy })
                    .Where(u => u.Id == userId)
                    .ExecuteCommandAsync();

                _logger.LogInformation("用户锁定状态设置成功：{userId}，状态：{isLocked}", userId, isLocked);
                return (true, "");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "设置用户锁定状态失败：{userId}", userId);
                return (false, $"设置用户锁定状态失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 删除用户（软删除）
        /// </summary>
        public async Task<(bool IsSuccess, string ErrorMessage)> DeleteUserAsync(int userId, int? operatorId = null)
        {
            try
            {
                _logger.LogInformation("删除用户：{userId}", userId);

                var user = await GetUserByIdAsync(userId);
                if (user == null)
                    return (false, "用户不存在");

                // 软删除：禁用用户并标记删除时间
                await _dbContext.Db.Updateable<User>()
                    .SetColumns(u => new User { IsEnabled = false, UpdatedAt = DateTime.Now, UpdatedBy = operatorId })
                    .Where(u => u.Id == userId)
                    .ExecuteCommandAsync();

                _logger.LogInformation("用户删除成功：{userId}", userId);
                return (true, "");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除用户失败：{userId}", userId);
                return (false, $"删除用户失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 重置用户登录失败次数
        /// </summary>
        public async Task<(bool IsSuccess, string ErrorMessage)> ResetLoginFailureCountAsync(int userId, int? operatorId = null)
        {
            try
            {
                _logger.LogInformation("重置用户登录失败次数：{userId}", userId);

                var user = await GetUserByIdAsync(userId);
                if (user == null)
                    return (false, "用户不存在");

                await _dbContext.Db.Updateable<User>()
                    .SetColumns(u => new User { LoginFailureCount = 0, UpdatedAt = DateTime.Now, UpdatedBy = operatorId })
                    .Where(u => u.Id == userId)
                    .ExecuteCommandAsync();

                _logger.LogInformation("用户登录失败次数重置成功：{userId}", userId);
                return (true, "");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "重置用户登录失败次数失败：{userId}", userId);
                return (false, $"重置失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 获取用户详细信息
        /// </summary>
        public async Task<User?> GetUserByIdAsync(int userId)
        {
            try
            {
                return await _dbContext.Db.Queryable<User>()
                    .Where(u => u.Id == userId)
                    .FirstAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户详细信息失败：{userId}", userId);
                return null;
            }
        }

        /// <summary>
        /// 根据用户名获取用户
        /// </summary>
        public async Task<User?> GetUserByUsernameAsync(string username)
        {
            try
            {
                return await _dbContext.Db.Queryable<User>()
                    .Where(u => u.Username == username)
                    .FirstAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据用户名获取用户失败：{username}", username);
                return null;
            }
        }

        /// <summary>
        /// 获取用户列表（分页）
        /// </summary>
        public async Task<(List<User> Users, int TotalCount)> GetUsersAsync(int page = 1, int pageSize = 20, string? searchKeyword = null, bool? isEnabled = null, bool? isLocked = null)
        {
            try
            {
                // 使用SqlSugar构建查询
                var query = _dbContext.Db.Queryable<User>();

                // 搜索条件
                if (!string.IsNullOrEmpty(searchKeyword))
                {
                    query = query.Where(u => u.Username.Contains(searchKeyword) 
                                         || u.DisplayName.Contains(searchKeyword) 
                                         || (u.Email != null && u.Email.Contains(searchKeyword)));
                }

                if (isEnabled.HasValue)
                {
                    query = query.Where(u => u.IsEnabled == isEnabled.Value);
                }

                if (isLocked.HasValue)
                {
                    query = query.Where(u => u.IsLocked == isLocked.Value);
                }

                // 获取总数
                var totalCount = await query.CountAsync();

                // 分页查询
                var users = await query
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                _logger.LogInformation("获取用户列表成功，共 {totalCount} 个用户，当前页 {users} 个", totalCount, users.Count);
                return (users, totalCount);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户列表失败");
                return (new List<User>(), 0);
            }
        }

        /// <summary>
        /// 获取所有用户列表
        /// </summary>
        public async Task<List<User>> GetAllUsersAsync()
        {
            try
            {
                var users = await _dbContext.Db.Queryable<User>()
                    .ToListAsync();

                _logger.LogInformation("获取所有用户列表成功，共 {count} 个用户", users.Count);
                return users;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取所有用户列表失败");
                return new List<User>();
            }
        }

        /// <summary>
        /// 锁定用户
        /// </summary>
        public async Task<(bool IsSuccess, string ErrorMessage)> LockUserAsync(int userId, string? reason = null)
        {
            return await SetUserLockedAsync(userId, true, reason);
        }

        /// <summary>
        /// 解锁用户
        /// </summary>
        public async Task<(bool IsSuccess, string ErrorMessage)> UnlockUserAsync(int userId)
        {
            return await SetUserLockedAsync(userId, false, "手动解锁");
        }

        #endregion

        #region 角色管理

        /// <summary>
        /// 为用户分配角色
        /// </summary>
        public async Task<(bool IsSuccess, string ErrorMessage)> AssignUserRoleAsync(int userId, int roleId, int? operatorId = null, DateTime? expiresAt = null, string? remark = null)
        {
            try
            {
                _logger.LogInformation("为用户分配角色：{userId}，角色：{roleId}", userId, roleId);

                // 检查用户是否存在
                var user = await GetUserByIdAsync(userId);
                if (user == null)
                    return (false, "用户不存在");

                // 检查角色是否存在
                var role = await _dbContext.Db.Queryable<Role>()
                    .Where(r => r.Id == roleId && r.IsEnabled)
                    .FirstAsync();
                if (role == null)
                    return (false, "角色不存在或已禁用");

                // 检查是否已分配该角色
                var existingUserRole = await _dbContext.Db.Queryable<UserRole>()
                    .Where(ur => ur.UserId == userId && ur.RoleId == roleId && ur.IsEnabled)
                    .FirstAsync();

                if (existingUserRole != null)
                    return (false, "用户已拥有该角色");

                // 分配角色
                var userRole = new UserRole
                {
                    UserId = userId,
                    RoleId = roleId,
                    AssignedAt = DateTime.Now,
                    AssignedBy = operatorId,
                    ExpiresAt = expiresAt,
                    Remark = remark
                };

                await _dbContext.Db.Insertable(userRole).ExecuteCommandAsync();

                _logger.LogInformation("用户角色分配成功：{userId}，角色：{roleId}", userId, roleId);
                return (true, "");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "分配用户角色失败：{userId}，角色：{roleId}", userId, roleId);
                return (false, $"分配角色失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 移除用户角色
        /// </summary>
        public async Task<(bool IsSuccess, string ErrorMessage)> RemoveUserRoleAsync(int userId, int roleId, int? operatorId = null)
        {
            try
            {
                _logger.LogInformation("移除用户角色：{userId}，角色：{roleId}", userId, roleId);

                await _dbContext.Db.Updateable<UserRole>()
                    .SetColumns(ur => new UserRole { IsEnabled = false })
                    .Where(ur => ur.UserId == userId && ur.RoleId == roleId)
                    .ExecuteCommandAsync();

                _logger.LogInformation("用户角色移除成功：{userId}，角色：{roleId}", userId, roleId);
                return (true, "");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "移除用户角色失败：{userId}，角色：{roleId}", userId, roleId);
                return (false, $"移除角色失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 获取用户的所有角色
        /// </summary>
        public async Task<List<Role>> GetUserRolesAsync(int userId)
        {
            try
            {
                return await _dbContext.Db.Queryable<UserRole>()
                    .LeftJoin<Role>((ur, r) => ur.RoleId == r.Id)
                    .Where((ur, r) => ur.UserId == userId && ur.IsEnabled && r.IsEnabled && (ur.ExpiresAt == null || ur.ExpiresAt > DateTime.Now))
                    .Select((ur, r) => r)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户角色失败：{userId}", userId);
                return new List<Role>();
            }
        }

        /// <summary>
        /// 获取所有可用角色
        /// </summary>
        public async Task<List<Role>> GetAllRolesAsync()
        {
            try
            {
                return await _dbContext.Roles
                    .Where(r => r.IsEnabled)
                    .OrderBy(r => r.SortOrder)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取所有角色失败");
                return new List<Role>();
            }
        }

        #endregion

        #region 权限管理

        /// <summary>
        /// 为用户直接分配权限
        /// </summary>
        public async Task<(bool IsSuccess, string ErrorMessage)> AssignUserPermissionAsync(int userId, int permissionId, bool isGranted = true, int? operatorId = null, DateTime? expiresAt = null, string? remark = null)
        {
            try
            {
                _logger.LogInformation("为用户分配直接权限：{userId}，权限：{permissionId}，授权：{isGranted}", userId, permissionId, isGranted);

                // 检查用户是否存在
                var user = await GetUserByIdAsync(userId);
                if (user == null)
                    return (false, "用户不存在");

                // 检查权限是否存在
                var permission = await _dbContext.Permissions.Where(p => p.Id == permissionId && p.IsEnabled).FirstAsync();
                if (permission == null)
                    return (false, "权限不存在或已禁用");

                // 检查是否已有该权限的直接分配
                var existingUserPermission = await _dbContext.UserPermissions
                    .Where(up => up.UserId == userId && up.PermissionId == permissionId && up.IsEnabled)
                    .FirstAsync();

                if (existingUserPermission != null)
                {
                    // 更新现有权限
                    await _dbContext.Db.Updateable<UserPermission>()
                        .SetColumns(up => new UserPermission { IsGranted = isGranted, AssignedAt = DateTime.Now, AssignedBy = operatorId, ExpiresAt = expiresAt, Remark = remark })
                        .Where(up => up.Id == existingUserPermission.Id)
                        .ExecuteCommandAsync();
                }
                else
                {
                    // 新增权限
                    var userPermission = new UserPermission
                    {
                        UserId = userId,
                        PermissionId = permissionId,
                        IsGranted = isGranted,
                        AssignedAt = DateTime.Now,
                        AssignedBy = operatorId,
                        ExpiresAt = expiresAt,
                        Remark = remark
                    };

                    await _dbContext.Db.Insertable(userPermission).ExecuteCommandAsync();
                }

                _logger.LogInformation("用户直接权限分配成功：{userId}，权限：{permissionId}，授权：{isGranted}", userId, permissionId, isGranted);
                return (true, "");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "分配用户直接权限失败：{userId}，权限：{permissionId}", userId, permissionId);
                return (false, $"分配权限失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 移除用户直接权限
        /// </summary>
        public async Task<(bool IsSuccess, string ErrorMessage)> RemoveUserPermissionAsync(int userId, int permissionId, int? operatorId = null)
        {
            try
            {
                _logger.LogInformation("移除用户直接权限：{userId}，权限：{permissionId}", userId, permissionId);

                await _dbContext.Db.Updateable<UserPermission>()
                    .SetColumns(up => new UserPermission { IsEnabled = false })
                    .Where(up => up.UserId == userId && up.PermissionId == permissionId)
                    .ExecuteCommandAsync();

                _logger.LogInformation("用户直接权限移除成功：{userId}，权限：{permissionId}", userId, permissionId);
                return (true, "");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "移除用户直接权限失败：{userId}，权限：{permissionId}", userId, permissionId);
                return (false, $"移除权限失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 获取用户的所有权限（包括角色权限和直接权限）
        /// </summary>
        public async Task<List<Permission>> GetUserAllPermissionsAsync(int userId)
        {
            try
            {
                var permissions = new List<Permission>();

                // 简化版本：先只通过角色获取权限
                try
                {
                    // 1. 通过角色获取权限
                    var rolePermissions = await _dbContext.Db.Queryable<UserRole>()
                        .LeftJoin<Role>((ur, r) => ur.RoleId == r.Id)
                        .LeftJoin<RolePermission>((ur, r, rp) => r.Id == rp.RoleId)
                        .LeftJoin<Permission>((ur, r, rp, p) => rp.PermissionId == p.Id)
                        .Where((ur, r, rp, p) => ur.UserId == userId 
                            && ur.IsEnabled 
                            && r.IsEnabled 
                            && rp.IsEnabled 
                            && p.IsEnabled)
                        .Select((ur, r, rp, p) => p)
                        .ToListAsync();

                    permissions.AddRange(rolePermissions.Where(p => p != null));
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "获取用户角色权限失败：{userId}", userId);
                }

                // 2. 尝试获取用户直接权限
                try
                {
                    var directPermissions = await _dbContext.Db.Queryable<UserPermission>()
                        .LeftJoin<Permission>((up, p) => up.PermissionId == p.Id)
                        .Where((up, p) => up.UserId == userId 
                            && up.IsEnabled 
                            && up.IsGranted 
                            && p.IsEnabled)
                        .Select((up, p) => p)
                        .ToListAsync();

                    permissions.AddRange(directPermissions.Where(p => p != null));
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "获取用户直接权限失败：{userId}", userId);
                }

                // 去重并排序
                var result = permissions.GroupBy(p => p.Id).Select(g => g.First()).OrderBy(p => p.Module).ThenBy(p => p.SortOrder).ToList();
                
                _logger.LogInformation("用户 {userId} 共获取到 {count} 个权限", userId, result.Count);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户所有权限失败：{userId}", userId);
                return new List<Permission>();
            }
        }

        /// <summary>
        /// 获取用户的直接权限
        /// </summary>
        public async Task<List<Permission>> GetUserDirectPermissionsAsync(int userId)
        {
            try
            {
                return await _dbContext.Db.Queryable<UserPermission>()
                    .LeftJoin<Permission>((up, p) => up.PermissionId == p.Id)
                    .Where((up, p) => up.UserId == userId && up.IsEnabled && p.IsEnabled && (up.ExpiresAt == null || up.ExpiresAt > DateTime.Now))
                    .Select((up, p) => p)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户直接权限失败：{userId}", userId);
                return new List<Permission>();
            }
        }

        /// <summary>
        /// 获取所有可用权限
        /// </summary>
        public async Task<List<Permission>> GetAllPermissionsAsync()
        {
            try
            {
                // 使用更简单的查询方式，先获取所有权限再过滤
                var allPermissions = await _dbContext.Db.Queryable<Permission>()
                    .ToListAsync();
                
                var enabledPermissions = allPermissions
                    .Where(p => p.IsEnabled)
                    .OrderBy(p => p.Module)
                    .ThenBy(p => p.SortOrder)
                    .ToList();
                
                _logger.LogInformation("成功获取到 {count} 个可用权限", enabledPermissions.Count);
                return enabledPermissions;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取所有权限失败");
                
                // 如果数据库查询失败，尝试使用原始SQL查询
                try
                {
                    _logger.LogWarning("尝试使用原始SQL查询权限数据");
                    var permissions = await _dbContext.Db.Ado.SqlQueryAsync<Permission>(
                        "SELECT * FROM Permissions WHERE IsEnabled = 1 ORDER BY Module, SortOrder");
                    
                    _logger.LogInformation("通过原始SQL成功获取到 {count} 个权限", permissions.Count);
                    return permissions.ToList();
                }
                catch (Exception sqlEx)
                {
                    _logger.LogError(sqlEx, "原始SQL查询也失败");
                    return new List<Permission>();
                }
            }
        }

        /// <summary>
        /// 检查用户是否有指定权限
        /// </summary>
        public async Task<bool> CheckUserPermissionAsync(int userId, string permissionCode)
        {
            try
            {
                var userPermissions = await GetUserAllPermissionsAsync(userId);
                return userPermissions.Any(p => p.Code == permissionCode);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查用户权限失败：{userId}，权限：{permissionCode}", userId, permissionCode);
                return false;
            }
        }

        #endregion

        #region 登录日志

        /// <summary>
        /// 获取用户登录日志
        /// </summary>
        public async Task<List<LoginLog>> GetUserLoginLogsAsync(int userId, int page = 1, int pageSize = 20)
        {
            try
            {
                var user = await GetUserByIdAsync(userId);
                if (user == null) return new List<LoginLog>();

                return await _dbContext.Db.Ado
                    .SqlQueryAsync<LoginLog>("SELECT * FROM LoginLogs WHERE Username = @Username ORDER BY LoginTime DESC OFFSET @Offset ROWS FETCH NEXT @PageSize ROWS ONLY",
                        new { Username = user.Username, Offset = (page - 1) * pageSize, PageSize = pageSize });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户登录日志失败：{userId}", userId);
                return new List<LoginLog>();
            }
        }

        /// <summary>
        /// 获取系统登录日志
        /// </summary>
        public async Task<(List<LoginLog> Logs, int TotalCount)> GetSystemLoginLogsAsync(int page = 1, int pageSize = 20, string? username = null, bool? isSuccess = null, DateTime? startTime = null, DateTime? endTime = null)
        {
            try
            {
                var whereConditions = new List<string>();
                var parameters = new Dictionary<string, object>();

                if (!string.IsNullOrEmpty(username))
                {
                    whereConditions.Add("Username LIKE @Username");
                    parameters.Add("Username", $"%{username}%");
                }

                if (isSuccess.HasValue)
                {
                    whereConditions.Add("IsSuccess = @IsSuccess");
                    parameters.Add("IsSuccess", isSuccess.Value);
                }

                if (startTime.HasValue)
                {
                    whereConditions.Add("LoginTime >= @StartTime");
                    parameters.Add("StartTime", startTime.Value);
                }

                if (endTime.HasValue)
                {
                    whereConditions.Add("LoginTime <= @EndTime");
                    parameters.Add("EndTime", endTime.Value);
                }

                var whereClause = whereConditions.Count > 0 ? " WHERE " + string.Join(" AND ", whereConditions) : "";

                // 获取总数
                var countSql = $"SELECT COUNT(*) FROM LoginLogs{whereClause}";
                var totalCount = await _dbContext.Db.Ado.GetIntAsync(countSql, parameters);

                // 获取分页数据
                var dataSql = $"SELECT * FROM LoginLogs{whereClause} ORDER BY LoginTime DESC OFFSET @Offset ROWS FETCH NEXT @PageSize ROWS ONLY";
                parameters.Add("Offset", (page - 1) * pageSize);
                parameters.Add("PageSize", pageSize);

                var logs = await _dbContext.Db.Ado.SqlQueryAsync<LoginLog>(dataSql, parameters);

                return (logs.ToList(), totalCount);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取系统登录日志失败");
                return (new List<LoginLog>(), 0);
            }
        }

        #endregion

        #region 新增方法

        /// <summary>
        /// 创建新用户（简化版本）
        /// </summary>
        public async Task<(bool IsSuccess, string ErrorMessage)> CreateUserAsync(User user)
        {
            var result = await CreateUserAsync(user, user.PasswordHash);
            return (result.IsSuccess, result.ErrorMessage);
        }

        /// <summary>
        /// 创建角色
        /// </summary>
        public async Task<(bool IsSuccess, string ErrorMessage)> CreateRoleAsync(Role role)
        {
            try
            {
                _logger.LogInformation("开始创建角色：{roleName}", role.Name);

                // 验证输入
                if (string.IsNullOrWhiteSpace(role.Code))
                    return (false, "角色编码不能为空");

                if (string.IsNullOrWhiteSpace(role.Name))
                    return (false, "角色名称不能为空");

                // 检查角色编码是否已存在
                var existingRole = await _dbContext.Db.Queryable<Role>()
                    .Where(r => r.Code == role.Code)
                    .FirstAsync();

                if (existingRole != null)
                    return (false, "角色编码已存在");

                role.CreatedAt = DateTime.Now;

                await _dbContext.Db.Insertable(role).ExecuteCommandAsync();

                _logger.LogInformation("角色创建成功：{roleName}", role.Name);
                return (true, "");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建角色失败：{roleName}", role.Name);
                return (false, $"创建角色失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 更新角色
        /// </summary>
        public async Task<(bool IsSuccess, string ErrorMessage)> UpdateRoleAsync(Role role)
        {
            try
            {
                _logger.LogInformation("开始更新角色：{roleId}", role.Id);

                // 检查角色是否存在
                var existingRole = await _dbContext.Db.Queryable<Role>()
                    .Where(r => r.Id == role.Id)
                    .FirstAsync();

                if (existingRole == null)
                    return (false, "角色不存在");

                // 检查角色编码是否已被其他角色使用
                if (existingRole.Code != role.Code)
                {
                    var duplicateRole = await _dbContext.Db.Queryable<Role>()
                        .Where(r => r.Code == role.Code && r.Id != role.Id)
                        .FirstAsync();

                    if (duplicateRole != null)
                        return (false, "角色编码已被其他角色使用");
                }

                role.UpdatedAt = DateTime.Now;

                await _dbContext.Db.Updateable(role)
                    .IgnoreColumns(r => new { r.CreatedAt, r.CreatedBy })
                    .ExecuteCommandAsync();

                _logger.LogInformation("角色更新成功：{roleId}", role.Id);
                return (true, "");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新角色失败：{roleId}", role.Id);
                return (false, $"更新角色失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 删除角色
        /// </summary>
        public async Task<(bool IsSuccess, string ErrorMessage)> DeleteRoleAsync(int roleId)
        {
            try
            {
                _logger.LogInformation("开始删除角色：{roleId}", roleId);

                var role = await _dbContext.Db.Queryable<Role>()
                    .Where(r => r.Id == roleId)
                    .FirstAsync();

                if (role == null)
                    return (false, "角色不存在");

                if (role.IsSystem)
                    return (false, "系统角色不能删除");

                // 检查是否有用户使用该角色
                var userRoleCount = await _dbContext.Db.Queryable<UserRole>()
                    .Where(ur => ur.RoleId == roleId && ur.IsEnabled)
                    .CountAsync();

                if (userRoleCount > 0)
                    return (false, "该角色正在被用户使用，无法删除");

                // 删除角色权限关联
                await _dbContext.Db.Deleteable<RolePermission>()
                    .Where(rp => rp.RoleId == roleId)
                    .ExecuteCommandAsync();

                // 删除角色
                await _dbContext.Db.Deleteable<Role>()
                    .Where(r => r.Id == roleId)
                    .ExecuteCommandAsync();

                _logger.LogInformation("角色删除成功：{roleId}", roleId);
                return (true, "");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除角色失败：{roleId}", roleId);
                return (false, $"删除角色失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 获取角色权限
        /// </summary>
        public async Task<List<Permission>> GetRolePermissionsAsync(int roleId)
        {
            try
            {
                return await _dbContext.Db.Queryable<RolePermission>()
                    .LeftJoin<Permission>((rp, p) => rp.PermissionId == p.Id)
                    .Where((rp, p) => rp.RoleId == roleId && rp.IsEnabled && p.IsEnabled)
                    .Select((rp, p) => p)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取角色权限失败：{roleId}", roleId);
                return new List<Permission>();
            }
        }

        /// <summary>
        /// 更新角色权限
        /// </summary>
        public async Task<(bool IsSuccess, string ErrorMessage)> UpdateRolePermissionsAsync(int roleId, List<int> permissionIds)
        {
            try
            {
                _logger.LogInformation("开始更新角色权限：{roleId}", roleId);

                // 检查角色是否存在
                var role = await _dbContext.Db.Queryable<Role>()
                    .Where(r => r.Id == roleId)
                    .FirstAsync();

                if (role == null)
                    return (false, "角色不存在");

                // 删除现有权限关联
                await _dbContext.Db.Deleteable<RolePermission>()
                    .Where(rp => rp.RoleId == roleId)
                    .ExecuteCommandAsync();

                // 添加新的权限关联
                if (permissionIds.Any())
                {
                    var rolePermissions = permissionIds.Select(permissionId => new RolePermission
                    {
                        RoleId = roleId,
                        PermissionId = permissionId,
                        AssignedAt = DateTime.Now
                    }).ToList();

                    await _dbContext.Db.Insertable(rolePermissions).ExecuteCommandAsync();
                }

                _logger.LogInformation("角色权限更新成功：{roleId}，权限数量：{count}", roleId, permissionIds.Count);
                return (true, "");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新角色权限失败：{roleId}", roleId);
                return (false, $"更新角色权限失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 更新用户角色
        /// </summary>
        public async Task<(bool IsSuccess, string ErrorMessage)> UpdateUserRolesAsync(int userId, List<int> roleIds)
        {
            try
            {
                _logger.LogInformation("开始更新用户角色：{userId}", userId);

                // 检查用户是否存在
                var user = await GetUserByIdAsync(userId);
                if (user == null)
                    return (false, "用户不存在");

                // 删除现有角色关联
                await _dbContext.Db.Deleteable<UserRole>()
                    .Where(ur => ur.UserId == userId)
                    .ExecuteCommandAsync();

                // 添加新的角色关联
                if (roleIds.Any())
                {
                    var userRoles = roleIds.Select(roleId => new UserRole
                    {
                        UserId = userId,
                        RoleId = roleId,
                        AssignedAt = DateTime.Now
                    }).ToList();

                    await _dbContext.Db.Insertable(userRoles).ExecuteCommandAsync();
                }

                _logger.LogInformation("用户角色更新成功：{userId}，角色数量：{count}", userId, roleIds.Count);
                return (true, "");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新用户角色失败：{userId}", userId);
                return (false, $"更新用户角色失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 创建权限
        /// </summary>
        public async Task<(bool IsSuccess, string ErrorMessage)> CreatePermissionAsync(Permission permission)
        {
            try
            {
                _logger.LogInformation("开始创建权限：{permissionName}", permission.Name);

                // 验证输入
                if (string.IsNullOrWhiteSpace(permission.Code))
                    return (false, "权限编码不能为空");

                if (string.IsNullOrWhiteSpace(permission.Name))
                    return (false, "权限名称不能为空");

                if (string.IsNullOrWhiteSpace(permission.Module))
                    return (false, "权限模块不能为空");

                if (string.IsNullOrWhiteSpace(permission.Action))
                    return (false, "权限操作不能为空");

                // 检查权限编码是否已存在
                var existingPermission = await _dbContext.Db.Queryable<Permission>()
                    .Where(p => p.Code == permission.Code)
                    .FirstAsync();

                if (existingPermission != null)
                    return (false, "权限编码已存在");

                permission.CreatedAt = DateTime.Now;

                await _dbContext.Db.Insertable(permission).ExecuteCommandAsync();

                _logger.LogInformation("权限创建成功：{permissionName}", permission.Name);
                return (true, "");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建权限失败：{permissionName}", permission.Name);
                return (false, $"创建权限失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 更新权限
        /// </summary>
        public async Task<(bool IsSuccess, string ErrorMessage)> UpdatePermissionAsync(Permission permission)
        {
            try
            {
                _logger.LogInformation("开始更新权限：{permissionId}", permission.Id);

                // 检查权限是否存在
                var existingPermission = await _dbContext.Db.Queryable<Permission>()
                    .Where(p => p.Id == permission.Id)
                    .FirstAsync();

                if (existingPermission == null)
                    return (false, "权限不存在");

                // 检查权限编码是否已被其他权限使用
                if (existingPermission.Code != permission.Code)
                {
                    var duplicatePermission = await _dbContext.Db.Queryable<Permission>()
                        .Where(p => p.Code == permission.Code && p.Id != permission.Id)
                        .FirstAsync();

                    if (duplicatePermission != null)
                        return (false, "权限编码已被其他权限使用");
                }

                permission.UpdatedAt = DateTime.Now;

                await _dbContext.Db.Updateable(permission)
                    .IgnoreColumns(p => new { p.CreatedAt, p.CreatedBy })
                    .ExecuteCommandAsync();

                _logger.LogInformation("权限更新成功：{permissionId}", permission.Id);
                return (true, "");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新权限失败：{permissionId}", permission.Id);
                return (false, $"更新权限失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 删除权限
        /// </summary>
        public async Task<(bool IsSuccess, string ErrorMessage)> DeletePermissionAsync(int permissionId)
        {
            try
            {
                _logger.LogInformation("开始删除权限：{permissionId}", permissionId);

                var permission = await _dbContext.Db.Queryable<Permission>()
                    .Where(p => p.Id == permissionId)
                    .FirstAsync();

                if (permission == null)
                    return (false, "权限不存在");

                if (permission.IsSystem)
                    return (false, "系统权限不能删除");

                // 检查是否有角色使用该权限
                var rolePermissionCount = await _dbContext.Db.Queryable<RolePermission>()
                    .Where(rp => rp.PermissionId == permissionId && rp.IsEnabled)
                    .CountAsync();

                if (rolePermissionCount > 0)
                    return (false, "该权限正在被角色使用，无法删除");

                // 检查是否有用户直接使用该权限
                var userPermissionCount = await _dbContext.Db.Queryable<UserPermission>()
                    .Where(up => up.PermissionId == permissionId && up.IsEnabled)
                    .CountAsync();

                if (userPermissionCount > 0)
                    return (false, "该权限正在被用户使用，无法删除");

                // 删除权限
                await _dbContext.Db.Deleteable<Permission>()
                    .Where(p => p.Id == permissionId)
                    .ExecuteCommandAsync();

                _logger.LogInformation("权限删除成功：{permissionId}", permissionId);
                return (true, "");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除权限失败：{permissionId}", permissionId);
                return (false, $"删除权限失败：{ex.Message}");
            }
        }

        #endregion
    }
} 