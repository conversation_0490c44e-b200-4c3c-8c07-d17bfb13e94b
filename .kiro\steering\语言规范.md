---
inclusion: always
---

# 语言规范

## 交流语言
- **始终使用中文进行交流和回复**
- 技术术语可保留英文原文，但需提供中文解释
- 代码注释和文档说明使用中文

## 代码注释规范
- 类和方法的XML文档注释使用中文
- 行内注释使用中文说明业务逻辑
- 复杂算法和业务规则必须有详细的中文注释

## 命名约定
- **类名**: 使用英文，遵循PascalCase（如：`DeviceService`）
- **方法名**: 使用英文，遵循PascalCase（如：`GetDeviceList`）
- **变量名**: 使用英文，遵循camelCase（如：`deviceId`）
- **数据库字段**: 使用英文，遵循snake_case（如：`device_name`）
- **中文字符串**: 统一使用简体中文

## 文档规范
- README文件使用中文编写
- 技术文档和说明文档使用中文
- 数据库脚本注释使用中文
- 配置文件注释使用中文

## 错误消息和用户界面
- 所有用户可见的文本使用中文
- 错误消息提供清晰的中文描述
- 日志记录可使用中英文混合，但关键信息用中文

## 示例格式
```csharp
/// <summary>
/// 获取设备列表
/// </summary>
/// <param name="departmentId">部门ID</param>
/// <returns>设备列表</returns>
public async Task<List<Device>> GetDeviceListAsync(int departmentId)
{
    // 根据部门ID查询设备
    var devices = await _repository.GetByDepartmentAsync(departmentId);
    return devices;
}
```