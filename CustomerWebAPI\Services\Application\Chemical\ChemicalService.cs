﻿using System;
using System.Threading.Tasks;
using CustomerWebAPI.Models;
using CustomerWebAPI.Common;
using CustomerWebAPI.Database;
using Microsoft.Extensions.Logging;
using SqlSugar;
using System.ComponentModel.DataAnnotations;
using System.Collections.Generic;
using System.Linq;
using FluentValidation;

namespace CustomerWebAPI.Services
{
    public partial class ChemicalService : IChemicalService
    {
        private readonly DbContext _dbContext;
        private readonly ILogger<ChemicalService> _logger;
        private readonly IValidator<ChemicalBoxResult> _validator;

        public ChemicalService(
            DbContext dbContext,
            ILogger<ChemicalService> logger,
            IValidator<ChemicalBoxResult> validator)
        {
            _dbContext = dbContext;
            _logger = logger;
            _validator = validator;
        }

        public async Task<ApiResponse<ChemicalBoxResult>> GetNewChemicalBoxNo(Chemical chemicalCode)
        {
            try
            {
                var result = await _dbContext.ExecuteAsync(async db =>
                {
                    return await db.Ado.UseStoredProcedure()
                        .SqlQuerySingleAsync<ChemicalBoxResult>(
                            "LABDB.dbo.usp_lbCreateNewChemicalBoxNo",
                            new SugarParameter[] 
                            {
                                new SugarParameter("@ChemicalCode", chemicalCode.ChemicalCode)
                            }
                        );
                });

                if (result == null)
                {
                    return ApiResponse<ChemicalBoxResult>.Fail("获取新染料箱号失败 - 未返回结果", (int)ApiErrorCodes.DatabaseError);
                }

                var validationResult = await _validator.ValidateAsync(result);
                if (!validationResult.IsValid)
                {
                    _logger.LogWarning("染料箱号数据验证失败: {Errors}", 
                        string.Join(", ", validationResult.Errors.Select(e => e.ErrorMessage)));
                        
                    return ApiResponse<ChemicalBoxResult>.Fail(
                        string.Join(", ", validationResult.Errors.Select(x => x.ErrorMessage)),
                        (int)ApiErrorCodes.ValidationError);
                }

                return ApiResponse<ChemicalBoxResult>.Ok(result, "获取新染料箱号成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取新染料箱号失败");
                return ApiResponse<ChemicalBoxResult>.Fail(
                    "获取新染料箱号失败", 
                    (int)ApiErrorCodes.DatabaseError);
            }
        }

        public async Task<ApiResponse<bool>> CheckChemicalChange(ChemicalCheck chemicalCheck)
        {
            try
            {
                var result = await _dbContext.ExecuteAsync(async db =>
                {
                    var checkResult = new SugarParameter("@CheckResult", null, System.Data.DbType.String, System.Data.ParameterDirection.Output);
                    await db.Ado.UseStoredProcedure()
                        .ExecuteCommandAsync("LABDB.dbo.usp_lbCheckChemicalChange", new SugarParameter[]
                        {
                            new SugarParameter("@ChemicalBoxNo", chemicalCheck.ChemicalBoxNo),
                            new SugarParameter("@ChemicalCode", chemicalCheck.ChemicalCode),
                            checkResult
                        });
                    return checkResult.Value?.ToString() == "相同";
                });

                _logger.LogInformation("Chemical change check result: {Result}", result);

                return result
                    ? ApiResponse<bool>.Ok(true, "染料箱号匹配")
                    : ApiResponse<bool>.Fail("染料箱号不匹配", (int)ApiErrorCodes.ValidationError);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "染料更换校验失败");
                return ApiResponse<bool>.Fail(
                    "染料更换校验失败", 
                    (int)ApiErrorCodes.DatabaseError);
            }
        }

        public async Task<ApiResponse<ChemicalBoxCheckResult>> CheckChemicalBoxConsistency(ChemicalBatchCheck batchCheck)
        {
            try
            {
                var result = await _dbContext.ExecuteAsync(async db =>
                {
                    var parameters = new SugarParameter[]
                    {
                        new SugarParameter("@Batch_NO", batchCheck.BatchNo),
                        new SugarParameter("@ChemicalBoxNo", batchCheck.ChemicalBoxNo)
                    };

                    var checkResult = await db.Ado.UseStoredProcedure()
                        .SqlQuerySingleAsync<ChemicalBoxCheckResult>(
                            "LABDB.dbo.usp_lbCheckDyestuffConsitentWithBill",
                            parameters
                        );

                    return checkResult;
                });

                if (result == null)
                {
                    return ApiResponse<ChemicalBoxCheckResult>.Fail(
                        "校验染料箱号失败 - 未返回结果", 
                        (int)ApiErrorCodes.DatabaseError);
                }

                return ApiResponse<ChemicalBoxCheckResult>.Ok(result, "校验完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "校验染料箱号失败");
                return ApiResponse<ChemicalBoxCheckResult>.Fail(
                    ex.Message, 
                    (int)ApiErrorCodes.DatabaseError);
            }
        }
    }

    public class ChemicalBoxCheckResult
    {
        public string IsConsitent { get; set; }
        public int BoxNum { get; set; }
    }

    public class ChemicalBatchCheck
    {
        public string BatchNo { get; set; }
        public string ChemicalBoxNo { get; set; }
    }
}