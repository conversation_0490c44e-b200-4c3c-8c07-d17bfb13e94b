<?xml version="1.0" encoding="utf-8" ?>
<views:BaseOperationPage
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:toolkit="http://schemas.microsoft.com/dotnet/2022/maui/toolkit"
    xmlns:viewmodels="clr-namespace:MauiScanManager.ViewModels"
    xmlns:views="clr-namespace:MauiScanManager.Views"
    x:Class="MauiScanManager.Views.YarnCarLocationPage"
    x:DataType="viewmodels:YarnCarLocationViewModel"
    Title="{Binding Operation.Description}">
    <Grid RowDefinitions="Auto,*,Auto">
        <ScrollView Grid.Row="1">
            <VerticalStackLayout Spacing="5" Padding="10">
                <Frame BackgroundColor="#FFF8E1" Padding="8" Margin="0,0,0,10" CornerRadius="8" HasShadow="False">
                    <Label Text="{Binding ScanPrompt}" FontSize="22" TextColor="#FF9800" FontAttributes="Bold" HorizontalOptions="Center"/>
                </Frame>
                <!-- 架位号输入 -->
                <Label Text="架位号" FontSize="18"/>
                <Entry Text="{Binding LocationNo, Mode=TwoWay}" Placeholder="请扫描或输入架位号" FontSize="18" HeightRequest="45"/>
                <!-- 车号数量统计（更明显） -->
                <HorizontalStackLayout Spacing="8" VerticalOptions="Center">
                    <Label Text="车号" FontSize="18"/>
                    <Label Text="{Binding CarNos.Count, StringFormat='（已添加：{0}）'}" FontSize="18" FontAttributes="Bold" TextColor="#1976D2"/>
                </HorizontalStackLayout>
                <!-- 车号输入框和添加按钮同一行，使用Grid保证宽度自适应 -->
                <Grid ColumnDefinitions="*,Auto" HorizontalOptions="FillAndExpand" Margin="0,0,0,0">
                    <Entry x:Name="CarNoEntry" Text="{Binding CarNo, Mode=TwoWay}" Placeholder="请扫描或输入车号" FontSize="18" HeightRequest="45" Grid.Column="0"/>
                    <Button Grid.Column="1" Text="添加" Command="{Binding AddCarNoCommand}" HeightRequest="45" WidthRequest="80"/>
                </Grid>
                <!-- <CollectionView ItemsSource="{Binding CarNos}" HeightRequest="80">
                    <CollectionView.ItemTemplate>
                        <DataTemplate>
                            <Frame Padding="5" Margin="2" BackgroundColor="#F0F0F0" CornerRadius="5">
                                <Label Text="{Binding .}" FontSize="16"/>
                            </Frame>
                        </DataTemplate>
                    </CollectionView.ItemTemplate>
                </CollectionView> -->
            </VerticalStackLayout>
        </ScrollView>
        <!-- 底部操作按钮区，纵向排列 -->
        <Grid Grid.Row="2" Padding="10,8" BackgroundColor="White">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="10"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            <Button Grid.Row="0" Text="保存" Command="{Binding SaveCommand}" BackgroundColor="{StaticResource Primary}" HeightRequest="48" FontSize="20" FontAttributes="Bold" HorizontalOptions="Fill"/>
            <Button Grid.Row="2" Text="清空" Command="{Binding ClearStateCommand}" IsEnabled="{Binding IsBusy, Converter={StaticResource InverseBoolConverter}}" BackgroundColor="{StaticResource Primary}" HeightRequest="48" FontSize="20" FontAttributes="Bold" HorizontalOptions="Fill"/>
        </Grid>
        <ActivityIndicator 
            IsRunning="{Binding IsBusy}"
            IsVisible="{Binding IsBusy}"
            HorizontalOptions="Center"
            VerticalOptions="Center"
            Color="{StaticResource Primary}"/>
    </Grid>
</views:BaseOperationPage> 