# CoreHub.Web Serilog 日志系统

本文档介绍 CoreHub.Web 项目中集成的 Serilog 日志系统的使用方法和最佳实践。

## 功能特性

### 1. 多种日志输出
- **控制台输出**: 开发时实时查看日志
- **文件输出**: 持久化存储，支持日志轮转
- **Seq 输出**: 结构化日志查询和分析（可选）

### 2. 日志级别
- **Debug**: 详细的调试信息
- **Information**: 一般信息
- **Warning**: 警告信息
- **Error**: 错误信息
- **Fatal**: 严重错误

### 3. 结构化日志
- 支持结构化数据记录
- 自动添加上下文信息（机器名、进程ID、线程ID等）
- 支持自定义属性

### 4. 性能监控
- 自动记录请求响应时间
- 性能阈值告警
- 详细的性能数据分析

### 5. 安全审计
- 用户操作记录
- 安全事件追踪
- 访问日志记录

## 配置说明

### appsettings.json 配置
```json
{
  "Serilog": {
    "MinimumLevel": {
      "Default": "Information",
      "Override": {
        "Microsoft": "Warning",
        "System": "Warning"
      }
    },
    "WriteTo": [
      {
        "Name": "Console",
        "Args": {
          "outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {SourceContext}: {Message:lj}{NewLine}{Exception}"
        }
      },
      {
        "Name": "File",
        "Args": {
          "path": "logs/corehub-web-.log",
          "rollingInterval": "Day",
          "retainedFileCountLimit": 30
        }
      }
    ]
  }
}
```

### 环境特定配置
- **Development**: 更详细的日志级别，更短的保留期
- **Production**: 优化的日志级别，更长的保留期

## 使用方法

### 1. 基本日志记录
```csharp
public class MyService
{
    private readonly IApplicationLogger _logger;

    public MyService(IApplicationLogger logger)
    {
        _logger = logger;
    }

    public void DoSomething()
    {
        _logger.LogInformation("开始执行操作");
        
        try
        {
            // 业务逻辑
            _logger.LogDebug("执行步骤1");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "操作执行失败");
        }
    }
}
```

### 2. 结构化日志
```csharp
var orderData = new { OrderId = 123, Amount = 299.99m };
_logger.LogInformation("订单创建: {@OrderData}", orderData);
```

### 3. 用户操作记录
```csharp
_logger.LogUserAction(userId, "创建订单", new { OrderId = 123 });
```

### 4. 性能监控
```csharp
var stopwatch = Stopwatch.StartNew();
// 执行操作
stopwatch.Stop();
_logger.LogPerformance("数据库查询", stopwatch.ElapsedMilliseconds);
```

### 5. 安全事件记录
```csharp
_logger.LogSecurityEvent("登录失败", userId, new { Reason = "密码错误" });
```

## 日志文件位置

- **开发环境**: `logs/corehub-web-dev-{date}.log`
- **生产环境**: `logs/corehub-web-{date}.log`

## 日志轮转策略

- **按天轮转**: 每天生成新的日志文件
- **大小限制**: 单个文件最大 10MB
- **保留期限**: 开发环境 7 天，生产环境 30 天

## 性能考虑

1. **异步写入**: 所有日志写入都是异步的，不会阻塞主线程
2. **缓冲机制**: 使用内存缓冲提高写入性能
3. **级别过滤**: 根据环境自动调整日志级别

## 监控和告警

### 性能阈值
- **警告**: 请求耗时 > 1秒
- **错误**: 请求耗时 > 5秒

### 错误追踪
- 自动记录异常堆栈
- 包含请求上下文信息
- 支持错误聚合分析

## 最佳实践

1. **使用结构化日志**: 便于查询和分析
2. **合理设置日志级别**: 避免日志过多或过少
3. **包含上下文信息**: 用户ID、请求ID等
4. **避免敏感信息**: 不要记录密码、令牌等
5. **性能监控**: 记录关键操作的执行时间

## 故障排查

### 常见问题
1. **日志文件权限**: 确保应用有写入权限
2. **磁盘空间**: 监控日志文件大小
3. **Seq 连接**: 检查 Seq 服务器状态

### 调试技巧
1. 临时调整日志级别
2. 使用结构化查询
3. 关联请求ID追踪

## API 测试端点

项目包含测试控制器 `LoggingExampleController`，提供以下端点：

- `GET /api/LoggingExample/test-levels` - 测试不同日志级别
- `POST /api/LoggingExample/test-user-action` - 测试用户操作日志
- `GET /api/LoggingExample/test-performance` - 测试性能监控
- `POST /api/LoggingExample/test-security-event` - 测试安全事件
- `GET /api/LoggingExample/test-structured` - 测试结构化日志
