﻿<?xml version = "1.0" encoding = "UTF-8" ?>
<Application xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:local="clr-namespace:MauiScanManager"
             xmlns:converters="clr-namespace:MauiScanManager.Converters"
             x:Class="MauiScanManager.App">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="Resources/Styles/Colors.xaml" />
                <ResourceDictionary Source="Resources/Styles/Styles.xaml" />
            </ResourceDictionary.MergedDictionaries>
            
            <!-- 添加布尔值反转转换器 -->
            <converters:InverseBoolConverter x:Key="InverseBoolConverter"/>
        </ResourceDictionary>
    </Application.Resources>
</Application>
