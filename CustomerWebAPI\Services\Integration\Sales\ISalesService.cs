﻿using System;
using System.Threading.Tasks;
using CustomerWebAPI.Models;

namespace CustomerWebAPI.Services
{
    public interface ISalesService
    {
        public Tuple<bool, string> DeletePo(string delTrackingId);
        public string GetAsnJsonFromDb(string rootId);
        public Tuple<bool, string> SaveTalPo(SalesPO originalPo);
        //public Task<Tuple<bool, string, TrackingIdModel>> SendAsnToTalAsync(dynamic asnRootId);
    }
}