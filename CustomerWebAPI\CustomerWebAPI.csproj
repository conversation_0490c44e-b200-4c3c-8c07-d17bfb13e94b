﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <UserSecretsId>4924d8f0-9b78-4372-bb03-1ffcfc6c04bb</UserSecretsId>
  </PropertyGroup>

  <PropertyGroup>
    <SatelliteResourceLanguages>en;zh-CN</SatelliteResourceLanguages>
  </PropertyGroup>

  <ItemGroup>
    <Content Remove="Data\ASN.json" />
    <Content Remove="Data\ASN.suppliers.schema.json" />
    <Content Remove="Data\ASNTemp.json" />
    <Content Remove="Data\PO -TALTest.json" />
    <Content Remove="Data\PO.json" />
    <Content Remove="Data\PO.schema.json" />
    <Content Remove="Data\SAINTYEAR.6008677_GuangDong.json" />
  </ItemGroup>

  <ItemGroup>
    <None Remove="Data\api_saintyeartex_com.pfx" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="Data\api_saintyeartex_com.pfx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="CertificateManager" Version="1.0.9" />
    <PackageReference Include="Dapper" Version="2.1.35" />
    <PackageReference Include="Dapper.Contrib" Version="2.0.78" />
    <PackageReference Include="FluentValidation.AspNetCore" Version="11.3.0" />
    <PackageReference Include="IdentityModel" Version="7.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.Certificate" Version="8.0.10" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.10" />
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="8.0.10" />
    <PackageReference Include="Microsoft.Data.SqlClient" Version="5.2.2" />
    <PackageReference Include="Microsoft.VisualStudio.Web.CodeGeneration.Design" Version="8.0.6" />
    <PackageReference Include="Newtonsoft.Json.Schema" Version="4.0.1" />
    <PackageReference Include="Novell.Directory.Ldap.NETStandard" Version="3.6.0" />
    <PackageReference Include="Serilog.AspNetCore" Version="8.0.3" />
    <PackageReference Include="Serilog.Settings.Configuration" Version="8.0.4" />
    <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
    <PackageReference Include="Serilog.Sinks.File" Version="6.0.0" />
    <PackageReference Include="SqlSugarCore" Version="5.1.4.170" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.9.0" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.1.2" />
    <PackageReference Include="System.Data.SqlClient" Version="4.8.6" />
    <PackageReference Include="Microsoft.Extensions.Diagnostics.HealthChecks" Version="7.0.0" />
    <PackageReference Include="EPPlus" Version="7.0.8" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="System.Configuration.ConfigurationManager, Version=4.0.1.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51">
      <HintPath>bin\Debug\netcoreapp3.0\System.Configuration.ConfigurationManager.dll</HintPath>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <None Update="saintyeartex-com-iis-**********.pfx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Data\saintyeartex-com-iis-**********.pfx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Logs\" />
  </ItemGroup>

  <ItemGroup>
    <None Include="Data\ASN.json">
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </None>
    <None Include="Data\ASN.suppliers.schema.json">
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </None>
    <None Include="Data\ASNTemp.json">
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </None>
    <None Include="Data\PO -TALTest.json">
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </None>
    <None Include="Data\PO.json">
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </None>
    <None Include="Data\PO.schema.json" />
    <None Include="Data\SAINTYEAR.6008677_GuangDong.json">
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
