using System.ComponentModel.DataAnnotations;

namespace MauiScanManager.Models
{
    public class SizingUpMachine
    {
        [Required(ErrorMessage = "保存类型必填")]
        public string SaveType { get; set; } = string.Empty;

        [Required(ErrorMessage = "机台号必填")]
        public string SizingMachineId { get; set; } = string.Empty;

        [Required(ErrorMessage = "浆纱批号必填")]
        public int SizingBatchSerialNo { get; set; }

        [Required(ErrorMessage = "织轴卡号必填")]
        public string WvCardNo { get; set; } = string.Empty;
    }
} 