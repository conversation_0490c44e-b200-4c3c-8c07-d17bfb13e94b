﻿@page "/"
@inject NavigationManager Navigation
@inject ISnackbar Snackbar

<PageTitle>首页</PageTitle>



<MudContainer MaxWidth="MaxWidth.Large">
    <MudText Typo="Typo.h3" Class="mb-4">欢迎使用设备管理系统</MudText>
    
    <MudPaper Class="pa-4 mb-4">
        <MudText Typo="Typo.h5" GutterBottom="true">系统功能</MudText>
        <MudText Typo="Typo.body1" Class="mb-3">
            这是一个基于.NET 8 MAUI Blazor Hybrid的跨平台设备管理系统。
            您可以使用二维码扫描功能来快速识别和管理设备。
        </MudText>
        
        <MudGrid>
            <MudItem xs="12" sm="6" md="4">
                <MudButton Variant="Variant.Filled"
                           Color="Color.Primary"
                           OnClick="NavigateToDeviceScanner"
                           StartIcon="@Icons.Material.Filled.QrCodeScanner"
                           FullWidth="true">
                    开始扫描设备
                </MudButton>
            </MudItem>

            <MudItem xs="12" sm="6" md="4">
                <MudButton Variant="Variant.Outlined"
                           Color="Color.Info"
                           OnClick="NavigateToVersionManagement"
                           StartIcon="@Icons.Material.Filled.Settings"
                           FullWidth="true">
                    版本管理
                </MudButton>
            </MudItem>
        </MudGrid>
    </MudPaper>

    <!-- 日期选择器演示 -->
    <MudPaper Class="pa-4" Style="min-height: 400px;">
        <MudText Typo="Typo.h6" GutterBottom="true">日期选择器演示</MudText>
        <MudGrid Class="date-picker-container">
            <MudItem xs="12" md="4" Class="mb-4">
                <MudDatePicker Label="选择日期 1" 
                              @bind-Date="selectedDate1" 
                              Elevation="8"
                              PickerVariant="PickerVariant.Dialog"
                              Culture="@System.Globalization.CultureInfo.GetCultureInfo("zh-CN")" />
            </MudItem>
            <MudItem xs="12" md="4" Class="mb-4">
                <MudDatePicker Label="选择日期 2" 
                              @bind-Date="selectedDate2" 
                              Elevation="8"
                              PickerVariant="PickerVariant.Dialog"
                              Culture="@System.Globalization.CultureInfo.GetCultureInfo("zh-CN")" />
            </MudItem>
            <MudItem xs="12" md="4" Class="mb-4">
                <MudDatePicker Label="选择日期 3" 
                              @bind-Date="selectedDate3" 
                              Elevation="8"
                              PickerVariant="PickerVariant.Dialog"
                              Culture="@System.Globalization.CultureInfo.GetCultureInfo("zh-CN")" />
            </MudItem>
        </MudGrid>
        
        <!-- 显示选择的日期 -->
        <MudDivider Class="my-4" />
        <MudText Typo="Typo.subtitle1">已选择的日期：</MudText>
        <MudText Typo="Typo.body2">日期 1: @(selectedDate1?.ToString("yyyy-MM-dd") ?? "未选择")</MudText>
        <MudText Typo="Typo.body2">日期 2: @(selectedDate2?.ToString("yyyy-MM-dd") ?? "未选择")</MudText>
        <MudText Typo="Typo.body2">日期 3: @(selectedDate3?.ToString("yyyy-MM-dd") ?? "未选择")</MudText>
    </MudPaper>
</MudContainer>

@code {
    private DateTime? selectedDate1;
    private DateTime? selectedDate2;
    private DateTime? selectedDate3;

    private void NavigateToDeviceScanner()
    {
        Navigation.NavigateTo("/devicescanner");
    }

    private void NavigateToVersionManagement()
    {
        Navigation.NavigateTo("/app-update-management");
    }
}

