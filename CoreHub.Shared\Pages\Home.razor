@page "/"
@using CoreHub.Shared.Models.Database
@using CoreHub.Shared.Services
@using CoreHub.Shared.Utils
@using Microsoft.AspNetCore.Components.Authorization
@using System.Security.Claims
@inject NavigationManager Navigation
@inject ISnackbar Snackbar
@inject IMaintenanceDashboardService MaintenanceDashboardService
@inject IEquipmentService EquipmentService
@inject IRepairOrderService RepairOrderService
@inject AuthenticationStateProvider AuthStateProvider
@inject IJSRuntime JSRuntime

<PageTitle>系统仪表板</PageTitle>

<MudContainer MaxWidth="MaxWidth.ExtraLarge" Class="mt-4">
    <!-- 页面标题 -->
    <MudStack Row Justify="Justify.SpaceBetween" AlignItems="AlignItems.Center" Class="mb-6">
        <div>
            <MudText Typo="Typo.h4" Class="mb-2">
                <MudIcon Icon="@Icons.Material.Filled.Dashboard" Class="mr-2" />
                系统仪表板
            </MudText>
            <MudText Typo="Typo.body1" Color="Color.Secondary">
                欢迎回来，@currentUserName！今天是 @DateTime.Now.ToString("yyyy年MM月dd日")
            </MudText>
        </div>
        <MudButton Variant="Variant.Filled"
                   Color="Color.Primary"
                   StartIcon="@Icons.Material.Filled.Refresh"
                   OnClick="RefreshData">
            刷新数据
        </MudButton>
    </MudStack>

    @if (loading)
    {
        <MudStack AlignItems="AlignItems.Center" Class="pa-8">
            <MudProgressCircular Color="Color.Primary" Size="Size.Large" Indeterminate="true" />
            <MudText Typo="Typo.h6" Class="mt-4">正在加载仪表板数据...</MudText>
        </MudStack>
    }
    else
    {
        <!-- 关键指标卡片 -->
        <MudGrid Class="mb-6">
            <MudItem xs="12" sm="6" md="3">
                <MudCard Elevation="4" Class="pa-4" Style="height: 140px;">
                    <MudStack Row Justify="Justify.SpaceBetween" AlignItems="AlignItems.Center">
                        <div>
                            <MudText Typo="Typo.h4" Color="Color.Primary">@equipmentStats.TotalCount</MudText>
                            <MudText Typo="Typo.body1">设备总数</MudText>
                            <MudText Typo="Typo.caption" Color="Color.Success">
                                <MudIcon Icon="@Icons.Material.Filled.TrendingUp" Size="Size.Small" />
                                正常运行: @equipmentStats.NormalCount
                            </MudText>
                        </div>
                        <MudAvatar Color="Color.Primary" Size="Size.Large">
                            <MudIcon Icon="@Icons.Material.Filled.Devices" Size="Size.Large" />
                        </MudAvatar>
                    </MudStack>
                </MudCard>
            </MudItem>

            <MudItem xs="12" sm="6" md="3">
                <MudCard Elevation="4" Class="pa-4" Style="height: 140px;">
                    <MudStack Row Justify="Justify.SpaceBetween" AlignItems="AlignItems.Center">
                        <div>
                            <MudText Typo="Typo.h4" Color="Color.Warning">@repairOrderStats.TotalCount</MudText>
                            <MudText Typo="Typo.body1">报修单总数</MudText>
                            <MudText Typo="Typo.caption" Color="Color.Error">
                                <MudIcon Icon="@Icons.Material.Filled.Warning" Size="Size.Small" />
                                紧急: @repairOrderStats.UrgentCount
                            </MudText>
                        </div>
                        <MudAvatar Color="Color.Warning" Size="Size.Large">
                            <MudIcon Icon="@Icons.Material.Filled.Build" Size="Size.Large" />
                        </MudAvatar>
                    </MudStack>
                </MudCard>
            </MudItem>

            <MudItem xs="12" sm="6" md="3">
                <MudCard Elevation="4" Class="pa-4" Style="height: 140px;">
                    <MudStack Row Justify="Justify.SpaceBetween" AlignItems="AlignItems.Center">
                        <div>
                            <MudText Typo="Typo.h4" Color="Color.Success">@repairOrderStats.CompletedCount</MudText>
                            <MudText Typo="Typo.body1">已完成维修</MudText>
                            <MudText Typo="Typo.caption" Color="Color.Info">
                                <MudIcon Icon="@Icons.Material.Filled.Schedule" Size="Size.Small" />
                                平均: @repairOrderStats.AverageRepairTime.ToString("F1")小时
                            </MudText>
                        </div>
                        <MudAvatar Color="Color.Success" Size="Size.Large">
                            <MudIcon Icon="@Icons.Material.Filled.CheckCircle" Size="Size.Large" />
                        </MudAvatar>
                    </MudStack>
                </MudCard>
            </MudItem>

            <MudItem xs="12" sm="6" md="3">
                <MudCard Elevation="4" Class="pa-4" Style="height: 140px;">
                    <MudStack Row Justify="Justify.SpaceBetween" AlignItems="AlignItems.Center">
                        <div>
                            <MudText Typo="Typo.h4" Color="Color.Error">@repairOrderStats.OverdueCount</MudText>
                            <MudText Typo="Typo.body1">超时维修</MudText>
                            <MudText Typo="Typo.caption" Color="Color.Warning">
                                <MudIcon Icon="@Icons.Material.Filled.AccessTime" Size="Size.Small" />
                                需要关注
                            </MudText>
                        </div>
                        <MudAvatar Color="Color.Error" Size="Size.Large">
                            <MudIcon Icon="@Icons.Material.Filled.Error" Size="Size.Large" />
                        </MudAvatar>
                    </MudStack>
                </MudCard>
            </MudItem>
        </MudGrid>

        <!-- 统计图表区域 -->
        <MudGrid Class="mb-6">
            <!-- 设备状态分布 -->
            <MudItem xs="12" md="6">
                <MudCard Elevation="4" Class="pa-4" Style="height: 400px;">
                    <MudCardHeader>
                        <CardHeaderContent>
                            <MudText Typo="Typo.h6">
                                <MudIcon Icon="@Icons.Material.Filled.DonutLarge" Class="mr-2" />
                                设备状态分布
                            </MudText>
                        </CardHeaderContent>
                    </MudCardHeader>
                    <MudCardContent>
                        <MudStack Row Spacing="4" AlignItems="AlignItems.Center">
                            <!-- ECharts 甜甜圈图表 -->
                            <div style="flex: 1; display: flex; justify-content: center;">
                                <div id="equipmentChart" style="width: 300px; height: 300px;"></div>
                            </div>

                            <!-- 图例 -->
                            <MudStack Spacing="2" Style="flex: 1;">
                                <div style="display: flex; align-items: center; gap: 8px;">
                                    <div style="width: 16px; height: 16px; background-color: #4caf50; border-radius: 50%;"></div>
                                    <MudText Typo="Typo.body2">正常</MudText>
                                    <MudSpacer />
                                    <MudText Typo="Typo.body2" Style="font-weight: bold;">@equipmentStats.NormalCount</MudText>
                                    <MudText Typo="Typo.caption" Color="Color.Secondary">(@GetEquipmentPercentage(equipmentStats.NormalCount).ToString("F1")%)</MudText>
                                </div>
                                <div style="display: flex; align-items: center; gap: 8px;">
                                    <div style="width: 16px; height: 16px; background-color: #ff9800; border-radius: 50%;"></div>
                                    <MudText Typo="Typo.body2">维修中</MudText>
                                    <MudSpacer />
                                    <MudText Typo="Typo.body2" Style="font-weight: bold;">@equipmentStats.MaintenanceCount</MudText>
                                    <MudText Typo="Typo.caption" Color="Color.Secondary">(@GetEquipmentPercentage(equipmentStats.MaintenanceCount).ToString("F1")%)</MudText>
                                </div>
                                <div style="display: flex; align-items: center; gap: 8px;">
                                    <div style="width: 16px; height: 16px; background-color: #9e9e9e; border-radius: 50%;"></div>
                                    <MudText Typo="Typo.body2">停用</MudText>
                                    <MudSpacer />
                                    <MudText Typo="Typo.body2" Style="font-weight: bold;">@equipmentStats.DisabledCount</MudText>
                                    <MudText Typo="Typo.caption" Color="Color.Secondary">(@GetEquipmentPercentage(equipmentStats.DisabledCount).ToString("F1")%)</MudText>
                                </div>
                                <div style="display: flex; align-items: center; gap: 8px;">
                                    <div style="width: 16px; height: 16px; background-color: #f44336; border-radius: 50%;"></div>
                                    <MudText Typo="Typo.body2">报废</MudText>
                                    <MudSpacer />
                                    <MudText Typo="Typo.body2" Style="font-weight: bold;">@equipmentStats.ScrapCount</MudText>
                                    <MudText Typo="Typo.caption" Color="Color.Secondary">(@GetEquipmentPercentage(equipmentStats.ScrapCount).ToString("F1")%)</MudText>
                                </div>
                            </MudStack>
                        </MudStack>
                    </MudCardContent>
                </MudCard>
            </MudItem>

            <!-- 报修单状态统计 -->
            <MudItem xs="12" md="6">
                <MudCard Elevation="4" Class="pa-4" Style="height: 400px;">
                    <MudCardHeader>
                        <CardHeaderContent>
                            <MudText Typo="Typo.h6">
                                <MudIcon Icon="@Icons.Material.Filled.DonutLarge" Class="mr-2" />
                                报修单状态统计
                            </MudText>
                        </CardHeaderContent>
                    </MudCardHeader>
                    <MudCardContent>
                        <MudStack Row Spacing="4" AlignItems="AlignItems.Center">
                            <!-- ECharts 甜甜圈图表 -->
                            <div style="flex: 1; display: flex; justify-content: center;">
                                <div id="repairOrderChart" style="width: 300px; height: 300px;"></div>
                            </div>

                            <!-- 图例 -->
                            <MudStack Spacing="1" Style="flex: 1;">
                                <div style="display: flex; align-items: center; gap: 8px;">
                                    <div style="width: 16px; height: 16px; background-color: #ff9800; border-radius: 50%;"></div>
                                    <MudText Typo="Typo.body2">待处理</MudText>
                                    <MudSpacer />
                                    <MudText Typo="Typo.body2" Style="font-weight: bold;">@repairOrderStats.PendingCount</MudText>
                                </div>
                                <div style="display: flex; align-items: center; gap: 8px;">
                                    <div style="width: 16px; height: 16px; background-color: #2196f3; border-radius: 50%;"></div>
                                    <MudText Typo="Typo.body2">处理中</MudText>
                                    <MudSpacer />
                                    <MudText Typo="Typo.body2" Style="font-weight: bold;">@repairOrderStats.InProgressCount</MudText>
                                </div>
                                <div style="display: flex; align-items: center; gap: 8px;">
                                    <div style="width: 16px; height: 16px; background-color: #4caf50; border-radius: 50%;"></div>
                                    <MudText Typo="Typo.body2">已完成</MudText>
                                    <MudSpacer />
                                    <MudText Typo="Typo.body2" Style="font-weight: bold;">@repairOrderStats.CompletedCount</MudText>
                                </div>
                                <div style="display: flex; align-items: center; gap: 8px;">
                                    <div style="width: 16px; height: 16px; background-color: #9e9e9e; border-radius: 50%;"></div>
                                    <MudText Typo="Typo.body2">已作废</MudText>
                                    <MudSpacer />
                                    <MudText Typo="Typo.body2" Style="font-weight: bold;">@repairOrderStats.CancelledCount</MudText>
                                </div>
                                <div style="display: flex; align-items: center; gap: 8px;">
                                    <div style="width: 16px; height: 16px; background-color: #607d8b; border-radius: 50%;"></div>
                                    <MudText Typo="Typo.body2">已关闭</MudText>
                                    <MudSpacer />
                                    <MudText Typo="Typo.body2" Style="font-weight: bold;">@repairOrderStats.ClosedCount</MudText>
                                </div>
                                <div style="display: flex; align-items: center; gap: 8px;">
                                    <div style="width: 16px; height: 16px; background-color: #00bcd4; border-radius: 50%;"></div>
                                    <MudText Typo="Typo.body2">待确认</MudText>
                                    <MudSpacer />
                                    <MudText Typo="Typo.body2" Style="font-weight: bold;">@repairOrderStats.PendingConfirmationCount</MudText>
                                </div>
                            </MudStack>
                        </MudStack>
                    </MudCardContent>
                </MudCard>
            </MudItem>
        </MudGrid>

        <!-- 第三行：系统健康度和维修效率 -->
        <MudGrid Class="mb-6">
            <!-- 系统健康度 -->
            <MudItem xs="12" md="6">
                <MudCard Elevation="4" Class="pa-4" Style="height: 280px;">
                    <MudCardHeader>
                        <CardHeaderContent>
                            <MudText Typo="Typo.h6">
                                <MudIcon Icon="@Icons.Material.Filled.Favorite" Class="mr-2" />
                                系统健康度
                            </MudText>
                        </CardHeaderContent>
                    </MudCardHeader>
                    <MudCardContent>
                        <MudStack AlignItems="AlignItems.Center" Spacing="3">
                            <MudProgressCircular Color="@GetHealthColor()" Size="Size.Large" Value="@systemHealthPercentage">
                                <MudText Typo="Typo.h5">@systemHealthPercentage%</MudText>
                            </MudProgressCircular>
                            <MudText Typo="Typo.body1" Align="Align.Center">
                                @GetHealthDescription()
                            </MudText>
                            <MudStack Row Spacing="2" Justify="Justify.Center">
                                <MudChip T="string" Color="Color.Success" Size="Size.Small">
                                    正常: @equipmentStats.NormalCount
                                </MudChip>
                                <MudChip T="string" Color="Color.Warning" Size="Size.Small">
                                    维护: @equipmentStats.MaintenanceCount
                                </MudChip>
                            </MudStack>
                        </MudStack>
                    </MudCardContent>
                </MudCard>
            </MudItem>

            <!-- 维修效率 -->
            <MudItem xs="12" md="6">
                <MudCard Elevation="4" Class="pa-4" Style="height: 280px;">
                    <MudCardHeader>
                        <CardHeaderContent>
                            <MudText Typo="Typo.h6">
                                <MudIcon Icon="@Icons.Material.Filled.Speed" Class="mr-2" />
                                维修效率
                            </MudText>
                        </CardHeaderContent>
                    </MudCardHeader>
                    <MudCardContent>
                        <MudStack Spacing="4">
                            <div>
                                <MudStack Row Justify="Justify.SpaceBetween" AlignItems="AlignItems.Center" Class="mb-1">
                                    <MudText Typo="Typo.body2">完成率</MudText>
                                    <MudText Typo="Typo.body2" Style="font-weight: bold;">@completionRate.ToString("F1")%</MudText>
                                </MudStack>
                                <MudProgressLinear Color="Color.Success" Value="@completionRate" Size="Size.Medium" />
                            </div>
                            <div>
                                <MudStack Row Justify="Justify.SpaceBetween" AlignItems="AlignItems.Center" Class="mb-1">
                                    <MudText Typo="Typo.body2">及时率</MudText>
                                    <MudText Typo="Typo.body2" Style="font-weight: bold;">@timelyRate.ToString("F1")%</MudText>
                                </MudStack>
                                <MudProgressLinear Color="Color.Info" Value="@timelyRate" Size="Size.Medium" />
                            </div>
                            <div>
                                <MudStack Row Justify="Justify.SpaceBetween" AlignItems="AlignItems.Center" Class="mb-1">
                                    <MudText Typo="Typo.body2">响应速度</MudText>
                                    <MudText Typo="Typo.body2" Style="font-weight: bold;">@responseRate.ToString("F1")%</MudText>
                                </MudStack>
                                <MudProgressLinear Color="Color.Primary" Value="@responseRate" Size="Size.Medium" />
                            </div>
                        </MudStack>
                    </MudCardContent>
                </MudCard>
            </MudItem>
        </MudGrid>

        <!-- 最近活动和数据表格 -->
        <MudGrid>
            <!-- 最近报修单 -->
            <MudItem xs="12" md="8">
                <MudCard Elevation="4" Class="pa-4">
                    <MudCardHeader>
                        <CardHeaderContent>
                            <MudText Typo="Typo.h6">
                                <MudIcon Icon="@Icons.Material.Filled.History" Class="mr-2" />
                                最近报修单
                            </MudText>
                        </CardHeaderContent>
                        <CardHeaderActions>
                            <MudButton Variant="Variant.Text"
                                       Color="Color.Primary"
                                       EndIcon="@Icons.Material.Filled.ArrowForward"
                                       OnClick="NavigateToRepairOrderManagement">
                                查看全部
                            </MudButton>
                        </CardHeaderActions>
                    </MudCardHeader>
                    <MudCardContent>
                        @if (dashboardData?.RecentRepairOrders?.Any() == true)
                        {
                            <MudTable Items="@dashboardData.RecentRepairOrders.Take(5)"
                                      Hover="true"
                                      Dense="true"
                                      FixedHeader="true"
                                      Height="300px">
                                <HeaderContent>
                                    <MudTh>报修单号</MudTh>
                                    <MudTh>设备名称</MudTh>
                                    <MudTh>状态</MudTh>
                                    <MudTh>紧急程度</MudTh>
                                    <MudTh>报修时间</MudTh>
                                </HeaderContent>
                                <RowTemplate>
                                    <MudTd DataLabel="报修单号">
                                        <MudLink Href="@($"/repair-order-detail/{context.Id}")" Color="Color.Primary">
                                            @context.OrderNumber
                                        </MudLink>
                                    </MudTd>
                                    <MudTd DataLabel="设备名称">@context.EquipmentName</MudTd>
                                    <MudTd DataLabel="状态">
                                        <MudChip T="string" Color="@GetStatusColor(context.Status)" Size="Size.Small">
                                            @GetStatusName(context.Status)
                                        </MudChip>
                                    </MudTd>
                                    <MudTd DataLabel="紧急程度">
                                        <MudChip T="string" Color="@GetUrgencyColor(context.UrgencyLevel)" Size="Size.Small">
                                            @GetUrgencyName(context.UrgencyLevel)
                                        </MudChip>
                                    </MudTd>
                                    <MudTd DataLabel="报修时间">@context.ReportedAt.ToString("MM-dd HH:mm")</MudTd>
                                </RowTemplate>
                            </MudTable>
                        }
                        else
                        {
                            <MudAlert Severity="Severity.Info">暂无最近报修单数据</MudAlert>
                        }
                    </MudCardContent>
                </MudCard>
            </MudItem>

            <!-- 系统通知和提醒 -->
            <MudItem xs="12" md="4">
                <MudCard Elevation="4" Class="pa-4" Style="height: 400px;">
                    <MudCardHeader>
                        <CardHeaderContent>
                            <MudText Typo="Typo.h6">
                                <MudIcon Icon="@Icons.Material.Filled.Notifications" Class="mr-2" />
                                系统提醒
                            </MudText>
                        </CardHeaderContent>
                    </MudCardHeader>
                    <MudCardContent>
                        <MudStack Spacing="3">
                            @if (equipmentStats.WarrantyExpiredCount > 0)
                            {
                                <MudAlert Severity="Severity.Warning" Dense="true">
                                    <MudText Typo="Typo.body2">
                                        <MudIcon Icon="@Icons.Material.Filled.Warning" Size="Size.Small" Class="mr-1" />
                                        有 @equipmentStats.WarrantyExpiredCount 台设备保修期已过期
                                    </MudText>
                                </MudAlert>
                            }

                            @if (equipmentStats.MaintenanceDueCount > 0)
                            {
                                <MudAlert Severity="Severity.Info" Dense="true">
                                    <MudText Typo="Typo.body2">
                                        <MudIcon Icon="@Icons.Material.Filled.Schedule" Size="Size.Small" Class="mr-1" />
                                        有 @equipmentStats.MaintenanceDueCount 台设备需要维护
                                    </MudText>
                                </MudAlert>
                            }

                            @if (repairOrderStats.UrgentCount > 0)
                            {
                                <MudAlert Severity="Severity.Error" Dense="true">
                                    <MudText Typo="Typo.body2">
                                        <MudIcon Icon="@Icons.Material.Filled.PriorityHigh" Size="Size.Small" Class="mr-1" />
                                        有 @repairOrderStats.UrgentCount 个紧急报修单待处理
                                    </MudText>
                                </MudAlert>
                            }

                            @if (repairOrderStats.OverdueCount > 0)
                            {
                                <MudAlert Severity="Severity.Error" Dense="true">
                                    <MudText Typo="Typo.body2">
                                        <MudIcon Icon="@Icons.Material.Filled.AccessTime" Size="Size.Small" Class="mr-1" />
                                        有 @repairOrderStats.OverdueCount 个报修单已超时
                                    </MudText>
                                </MudAlert>
                            }

                            @if (equipmentStats.WarrantyExpiredCount == 0 &&
                                 equipmentStats.MaintenanceDueCount == 0 &&
                                 repairOrderStats.UrgentCount == 0 &&
                                 repairOrderStats.OverdueCount == 0)
                            {
                                <MudAlert Severity="Severity.Success" Dense="true">
                                    <MudText Typo="Typo.body2">
                                        <MudIcon Icon="@Icons.Material.Filled.CheckCircle" Size="Size.Small" Class="mr-1" />
                                        系统运行正常，暂无紧急事项
                                    </MudText>
                                </MudAlert>
                            }

                            <!-- 天气信息或其他信息 -->
                            <MudDivider />
                            <MudStack Row AlignItems="AlignItems.Center" Justify="Justify.SpaceBetween">
                                <MudText Typo="Typo.body2" Color="Color.Secondary">
                                    <MudIcon Icon="@Icons.Material.Filled.AccessTime" Size="Size.Small" Class="mr-1" />
                                    最后更新: @lastUpdateTime.ToString("HH:mm:ss")
                                </MudText>
                                <MudIconButton Icon="@Icons.Material.Filled.Refresh"
                                               Size="Size.Small"
                                               OnClick="RefreshData" />
                            </MudStack>
                        </MudStack>
                    </MudCardContent>
                </MudCard>
            </MudItem>
        </MudGrid>
    }
</MudContainer>

@code {
    private bool loading = true;
    private string currentUserName = "用户";
    private DateTime lastUpdateTime = DateTime.Now;

    // 数据模型
    private MaintenanceDashboardData? dashboardData;
    private EquipmentStatisticsDto equipmentStats = new();
    private RepairOrderStatisticsDto repairOrderStats = new();

    // 计算属性
    private double systemHealthPercentage => equipmentStats.TotalCount > 0
        ? Math.Round((double)equipmentStats.NormalCount / equipmentStats.TotalCount * 100, 1)
        : 0;

    private double completionRate => repairOrderStats.TotalCount > 0
        ? Math.Round((double)repairOrderStats.CompletedCount / repairOrderStats.TotalCount * 100, 1)
        : 0;

    private double timelyRate => repairOrderStats.TotalCount > 0
        ? Math.Round((double)(repairOrderStats.TotalCount - repairOrderStats.OverdueCount) / repairOrderStats.TotalCount * 100, 1)
        : 0;

    private double responseRate => repairOrderStats.TotalCount > 0
        ? Math.Round((double)(repairOrderStats.InProgressCount + repairOrderStats.CompletedCount) / repairOrderStats.TotalCount * 100, 1)
        : 0;

    // ECharts 初始化标志
    private bool chartsInitialized = false;

    protected override async Task OnInitializedAsync()
    {
        await LoadCurrentUser();
        await LoadDashboardData();
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender && !loading && !chartsInitialized)
        {
            await InitializeCharts();
            chartsInitialized = true;
        }
        else if (!loading && chartsInitialized)
        {
            // 数据更新后重新渲染图表
            await UpdateCharts();
        }
    }

    private async Task InitializeCharts()
    {
        try
        {
            // 初始化设备状态图表
            await JSRuntime.InvokeVoidAsync("echartsHelper.initEquipmentChart", "equipmentChart", new
            {
                total = equipmentStats.TotalCount,
                normal = equipmentStats.NormalCount,
                maintenance = equipmentStats.MaintenanceCount,
                disabled = equipmentStats.DisabledCount,
                scrap = equipmentStats.ScrapCount
            });

            // 初始化报修单状态图表
            await JSRuntime.InvokeVoidAsync("echartsHelper.initRepairOrderChart", "repairOrderChart", new
            {
                total = repairOrderStats.TotalCount,
                pending = repairOrderStats.PendingCount,
                inProgress = repairOrderStats.InProgressCount,
                completed = repairOrderStats.CompletedCount,
                cancelled = repairOrderStats.CancelledCount,
                closed = repairOrderStats.ClosedCount,
                pendingConfirmation = repairOrderStats.PendingConfirmationCount
            });
        }
        catch (Exception ex)
        {
            Console.WriteLine($"初始化图表失败: {ex.Message}");
        }
    }

    private async Task UpdateCharts()
    {
        try
        {
            // 更新设备状态图表
            await JSRuntime.InvokeVoidAsync("echartsHelper.initEquipmentChart", "equipmentChart", new
            {
                total = equipmentStats.TotalCount,
                normal = equipmentStats.NormalCount,
                maintenance = equipmentStats.MaintenanceCount,
                disabled = equipmentStats.DisabledCount,
                scrap = equipmentStats.ScrapCount
            });

            // 更新报修单状态图表
            await JSRuntime.InvokeVoidAsync("echartsHelper.initRepairOrderChart", "repairOrderChart", new
            {
                total = repairOrderStats.TotalCount,
                pending = repairOrderStats.PendingCount,
                inProgress = repairOrderStats.InProgressCount,
                completed = repairOrderStats.CompletedCount,
                cancelled = repairOrderStats.CancelledCount,
                closed = repairOrderStats.ClosedCount,
                pendingConfirmation = repairOrderStats.PendingConfirmationCount
            });
        }
        catch (Exception ex)
        {
            Console.WriteLine($"更新图表失败: {ex.Message}");
        }
    }

    private async Task LoadCurrentUser()
    {
        try
        {
            var authState = await AuthStateProvider.GetAuthenticationStateAsync();
            if (authState.User.Identity?.IsAuthenticated == true)
            {
                currentUserName = authState.User.FindFirst("DisplayName")?.Value ??
                                 authState.User.FindFirst(ClaimTypes.Name)?.Value ??
                                 "用户";
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"获取用户信息失败: {ex.Message}");
        }
    }

    private async Task LoadDashboardData()
    {
        loading = true;
        try
        {
            var authState = await AuthStateProvider.GetAuthenticationStateAsync();
            if (authState.User.Identity?.IsAuthenticated == true)
            {
                var userIdClaim = authState.User.FindFirst("UserId")?.Value;
                if (int.TryParse(userIdClaim, out var userId))
                {
                    // 并行加载数据
                    var dashboardTask = MaintenanceDashboardService.GetDashboardDataAsync(userId);
                    var equipmentStatsTask = EquipmentService.GetEquipmentStatisticsAsync();
                    var repairOrderStatsTask = RepairOrderService.GetRepairOrderStatisticsAsync();

                    await Task.WhenAll(dashboardTask, equipmentStatsTask, repairOrderStatsTask);

                    dashboardData = await dashboardTask;
                    equipmentStats = await equipmentStatsTask;
                    repairOrderStats = await repairOrderStatsTask;

                }
            }

            // 如果用户未登录，加载基础统计数据
            if (dashboardData == null)
            {
                equipmentStats = await EquipmentService.GetEquipmentStatisticsAsync();
                repairOrderStats = await RepairOrderService.GetRepairOrderStatisticsAsync();
            }

            lastUpdateTime = DateTime.Now;
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载仪表板数据失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            loading = false;
        }
    }

    // 百分比计算方法
    private double GetEquipmentPercentage(int count)
    {
        return equipmentStats.TotalCount > 0 ? (double)count / equipmentStats.TotalCount * 100 : 0;
    }

    private double GetRepairOrderPercentage(int count)
    {
        return repairOrderStats.TotalCount > 0 ? (double)count / repairOrderStats.TotalCount * 100 : 0;
    }



    private async Task RefreshData()
    {
        await LoadDashboardData();
        Snackbar.Add("数据已刷新", Severity.Success);
    }

    // 辅助方法
    private Color GetHealthColor()
    {
        return systemHealthPercentage switch
        {
            >= 90 => Color.Success,
            >= 70 => Color.Warning,
            _ => Color.Error
        };
    }

    private string GetHealthDescription()
    {
        return systemHealthPercentage switch
        {
            >= 90 => "系统运行良好",
            >= 70 => "系统运行正常",
            >= 50 => "需要关注",
            _ => "需要紧急处理"
        };
    }

    private Color GetStatusColor(int status)
    {
        return RepairOrderStatusHelper.GetStatusColor(status);
    }

    private string GetStatusName(int status)
    {
        return RepairOrderStatusHelper.GetStatusName(status);
    }

    private Color GetUrgencyColor(int urgencyLevel)
    {
        return urgencyLevel switch
        {
            1 => Color.Error,     // 紧急
            2 => Color.Warning,   // 高
            3 => Color.Info,      // 中
            4 => Color.Default,   // 低
            _ => Color.Default
        };
    }

    private string GetUrgencyName(int urgencyLevel)
    {
        return urgencyLevel switch
        {
            1 => "紧急",
            2 => "高",
            3 => "中",
            4 => "低",
            _ => "未知"
        };
    }

    // 导航方法
    private void NavigateToRepairOrderManagement()
    {
        Navigation.NavigateTo("/repair-order-management");
    }
}

