<?xml version="1.0" encoding="utf-8" ?>
<views:BaseOperationPage 
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:views="clr-namespace:MauiScanManager.Views"
    xmlns:viewmodels="clr-namespace:MauiScanManager.ViewModels"
    x:Class="MauiScanManager.Views.ChemicalCheckPage"
    Title="{Binding Operation.Description}"
    BackgroundColor="{AppThemeBinding Light={StaticResource White}, Dark={StaticResource Gray950}}">

    <VerticalStackLayout 
        Spacing="25" 
        Padding="20,40" 
        VerticalOptions="Start">

        <!-- 扫描提示 -->
        <Label 
            Text="{Binding ScanPrompt}"
            HorizontalOptions="Center"
            FontSize="32"
            FontAttributes="Bold"
            TextColor="{StaticResource Primary}"
            Margin="0,0,0,20"/>

        <!-- 染料代号 -->
        <VerticalStackLayout Spacing="8">
            <Label 
                Text="染料代号"
                FontSize="20"
                TextColor="{AppThemeBinding Light={StaticResource Gray600}, Dark={StaticResource Gray400}}"/>
            <Frame 
                Padding="16,12" 
                BorderColor="{AppThemeBinding Light={StaticResource Gray300}, Dark={StaticResource Gray600}}"
                BackgroundColor="{AppThemeBinding Light={StaticResource Gray100}, Dark={StaticResource Gray800}}"
                CornerRadius="8"
                HasShadow="False">
                <Label 
                    Text="{Binding ChemicalCode}"
                    FontSize="28"
                    TextColor="{AppThemeBinding Light={StaticResource Black}, Dark={StaticResource White}}"/>
            </Frame>
        </VerticalStackLayout>

        <!-- 染料箱号 -->
        <VerticalStackLayout Spacing="8">
            <Label 
                Text="染料箱号"
                FontSize="20"
                TextColor="{AppThemeBinding Light={StaticResource Gray600}, Dark={StaticResource Gray400}}"/>
            <Frame 
                Padding="16,12" 
                BorderColor="{AppThemeBinding Light={StaticResource Gray300}, Dark={StaticResource Gray600}}"
                BackgroundColor="{AppThemeBinding Light={StaticResource Gray100}, Dark={StaticResource Gray800}}"
                CornerRadius="8"
                HasShadow="False">
                <Label 
                    Text="{Binding ChemicalBoxNo}"
                    FontSize="28"
                    TextColor="{AppThemeBinding Light={StaticResource Black}, Dark={StaticResource White}}"/>
            </Frame>
        </VerticalStackLayout>

    </VerticalStackLayout>

</views:BaseOperationPage> 