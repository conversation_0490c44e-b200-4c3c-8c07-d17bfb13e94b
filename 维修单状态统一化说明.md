# 维修单状态统一化说明

## 🔍 问题发现

在检查项目代码时发现维修单状态定义存在不统一的问题：

### ❌ 原有问题

1. **状态数量不一致**
   - `RepairOrder.cs` 模型：只定义了 5 个状态
   - `RepairWorkflowService.cs`：定义了 7 个状态
   - 前端组件：部分支持 5 个状态，部分支持 7 个状态

2. **状态名称重复定义**
   - 多个文件中都有状态名称的 switch 语句
   - 容易出现不一致的情况

3. **颜色映射分散**
   - 每个组件都有自己的 `GetStatusColor` 方法
   - 相同状态在不同组件中可能显示不同颜色

## ✅ 解决方案

### 1. 创建统一的状态帮助类

创建了 `RepairOrderStatusHelper` 类来统一管理所有状态相关的定义：

```csharp
// 文件位置：CoreHub.Shared/Utils/RepairOrderStatusHelper.cs
public static class RepairOrderStatusHelper
{
    // 状态常量
    public const int Pending = 1;          // 待处理
    public const int InProgress = 2;       // 处理中
    public const int Completed = 3;        // 已完成
    public const int Cancelled = 4;        // 已作废
    public const int Closed = 5;           // 已关闭
    public const int Paused = 6;           // 已暂停
    public const int PendingApproval = 7;  // 待审批
    
    // 统一的方法
    public static string GetStatusName(int status)
    public static Color GetStatusColor(int status)
    public static bool IsValidStatus(int status)
    // ... 更多实用方法
}
```

### 2. 统一所有状态定义

#### 数据库层面
- ✅ 更新了 `RepairOrder.cs` 模型注释
- ✅ 更新了数据库脚本注释
- ✅ 确保数据库视图支持所有 7 个状态

#### 前端组件层面
- ✅ 更新了 `RepairOrderDetailDialog.razor`
- ✅ 更新了 `RepairOrderManagement.razor`
- ✅ 确保所有组件都支持 7 个状态

### 3. 状态颜色统一

所有状态现在使用统一的颜色方案：

| 状态 | 颜色 | 说明 |
|------|------|------|
| 待处理 | Warning (橙色) | 需要关注 |
| 处理中 | Info (蓝色) | 正在进行 |
| 已完成 | Success (绿色) | 成功完成 |
| 已作废 | Default (灰色) | 已取消 |
| 已关闭 | Secondary (深灰色) | 已归档 |
| 已暂停 | Dark (黑色) | 暂时停止 |
| 待审批 | Primary (紫色) | 等待审批 |
| 待确认 | Tertiary (青色) | 等待报修人确认 |

## 📋 完整的状态定义

### 状态值和含义

```
1 = 待处理     - 新创建的报修单，等待分配或处理
2 = 处理中     - 已分配技术员，正在维修
3 = 已完成     - 维修工作已完成，流程结束
4 = 已作废     - 报修单被取消或作废
5 = 已关闭     - 报修单已完成并关闭，流程结束
6 = 已暂停     - 维修工作暂停（如等待配件）
7 = 待审批     - 需要审批才能继续的状态
8 = 待确认     - 技术员完成维修，等待报修人确认
```

### 状态流转规则

```
待处理 → 处理中 → 待确认 → 已完成 → 已关闭
   ↓        ↓        ↓        ↓
 已作废   已暂停   处理中   已作废
           ↓
        处理中
```

### 新增功能：报修人确认机制

#### 工作流程
1. **技术员完成维修**：状态从"处理中"变为"待确认"
2. **报修人确认**：
   - 确认完成 → 状态变为"已完成"
   - 要求重修 → 状态变为"处理中"（需要填写重修原因）

#### 权限控制
- **技术员**：可以将"处理中"的报修单标记为"待确认"
- **报修人**：可以确认或要求重修"待确认"状态的报修单
- **管理员**：可以管理所有状态的报修单

## 🔧 使用建议

### 1. 新组件开发

在新的组件中，请使用统一的帮助类：

```csharp
// ❌ 不要这样做
private string GetStatusName(int status) => status switch { ... };

// ✅ 应该这样做
using CoreHub.Shared.Utils;
private string GetStatusName(int status) => RepairOrderStatusHelper.GetStatusName(status);
private Color GetStatusColor(int status) => RepairOrderStatusHelper.GetStatusColor(status);
```

### 2. 状态验证

使用提供的验证方法：

```csharp
// 检查状态是否有效
if (RepairOrderStatusHelper.IsValidStatus(status))

// 检查是否可以分配技术员
if (RepairOrderStatusHelper.CanAssignTechnician(status))

// 检查是否为活跃状态
if (RepairOrderStatusHelper.IsActiveStatus(status))
```

### 3. 状态列表

获取状态选项：

```csharp
// 获取所有状态
var allStatuses = RepairOrderStatusHelper.GetAllStatuses();

// 获取活跃状态
var activeStatuses = RepairOrderStatusHelper.GetActiveStatuses();
```

## 📝 迁移说明

### 已更新的文件

1. **模型层**
   - `CoreHub.Shared/Models/Database/RepairOrder.cs`

2. **服务层**
   - `CoreHub.Shared/Services/IRepairWorkflowService.cs` (标记为过时)

3. **前端组件**
   - `CoreHub.Shared/Components/RepairOrderDetailDialog.razor`
   - `CoreHub.Shared/Pages/RepairOrderManagement.razor`

4. **数据库脚本**
   - `数据库脚本_完整版.sql`

5. **新增文件**
   - `CoreHub.Shared/Utils/RepairOrderStatusHelper.cs`

### 向后兼容性

- 旧的 `RepairOrderStatus` 类仍然可用，但标记为过时
- 建议逐步迁移到新的 `RepairOrderStatusHelper`

## 🎯 预期效果

1. **一致性**：所有地方的状态显示完全一致
2. **可维护性**：状态相关的修改只需要在一个地方进行
3. **可扩展性**：新增状态时只需要修改帮助类
4. **类型安全**：提供了丰富的验证方法，减少错误

## 🔍 验证方法

1. 启动应用程序
2. 查看不同页面的报修单状态显示
3. 确认颜色和名称的一致性
4. 测试工作流状态转换功能

所有状态现在应该在整个系统中保持完全一致！
