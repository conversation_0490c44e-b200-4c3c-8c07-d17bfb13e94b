# CoreHub.Web SSL 证书配置和管理指南

本文档详细说明 CoreHub.Web 项目中 SSL 证书的配置、管理和最佳实践。

## 📋 当前 SSL 配置

### 1. 配置文件结构

**appsettings.json**:
```json
{
  "Kestrel": {
    "EndPoints": {
      "Http": {
        "Url": "http://0.0.0.0:8080"
      },
      "Https": {
        "Url": "https://0.0.0.0:8081",
        "Certificate": {
          "Path": "Certificates/api_saintyeartex_com.pfx",
          "Password": "${SSL_CERT_PASSWORD:HO4LokI5cBvEuKYT}"
        },
        "Protocols": "Http1AndHttp2",
        "SslProtocols": ["Tls12", "Tls13"]
      }
    }
  }
}
```

### 2. 证书文件位置
- **证书文件**: `CoreHub.Web/Certificates/api_saintyeartex_com.pfx`
- **格式**: PKCS#12 (.pfx/.p12)
- **包含**: 私钥 + 公钥证书 + 证书链

## 🔧 SSL 配置详解

### 1. **端口配置**
- **HTTP**: 8080 端口（用于重定向到 HTTPS）
- **HTTPS**: 8081 端口（主要服务端口）
- **绑定地址**: `0.0.0.0`（允许所有网络接口访问）

### 2. **协议支持**
- **HTTP 协议**: HTTP/1.1 和 HTTP/2
- **TLS 版本**: TLS 1.2 和 TLS 1.3（推荐）

### 3. **安全特性**
- **HTTPS 重定向**: 自动将 HTTP 请求重定向到 HTTPS
- **HSTS**: 生产环境启用 HTTP 严格传输安全
- **现代 TLS**: 支持最新的 TLS 1.3 协议

## 🛠️ SSL 管理功能

### 1. **自动证书验证**
应用程序启动时自动验证证书：
- 检查证书文件是否存在
- 验证证书是否有效
- 检查证书是否过期
- 警告即将过期的证书（30天内）

### 2. **SSL 管理 API**

#### 获取证书信息
```http
GET /api/SslManagement/certificate-info
Authorization: Bearer {token}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "subject": "CN=api.saintyeartex.com",
    "issuer": "CN=Let's Encrypt Authority X3",
    "serialNumber": "03F7...",
    "thumbprint": "A1B2C3...",
    "notBefore": "2024-01-01T00:00:00Z",
    "notAfter": "2024-04-01T00:00:00Z",
    "daysUntilExpiry": 45,
    "isExpired": false,
    "isExpiringWithin30Days": false,
    "hasPrivateKey": true,
    "keyAlgorithm": "RSA",
    "signatureAlgorithm": "sha256RSA",
    "version": 3
  }
}
```

#### 验证证书
```http
POST /api/SslManagement/validate-certificate
Authorization: Bearer {token}
```

#### 检查过期状态
```http
GET /api/SslManagement/expiry-check?warningDays=30
Authorization: Bearer {token}
```

#### 获取 SSL 配置
```http
GET /api/SslManagement/ssl-config
Authorization: Bearer {token}
```

## 🔐 安全最佳实践

### 1. **密码管理**
```bash
# 使用环境变量存储证书密码
export SSL_CERT_PASSWORD="your_certificate_password"

# 或在生产环境中使用 Azure Key Vault / AWS Secrets Manager
```

### 2. **证书文件权限**
```bash
# Linux/macOS
chmod 600 Data/api_saintyeartex_com.pfx
chown app:app Data/api_saintyeartex_com.pfx

# Windows
# 确保只有应用程序用户有读取权限
```

### 3. **证书备份**
- 定期备份证书文件
- 备份私钥（安全存储）
- 记录证书过期时间

## 📅 证书生命周期管理

### 1. **证书监控**
- 自动检查证书过期时间
- 提前 30 天发出警告
- 记录证书状态变化

### 2. **证书更新流程**
1. **获取新证书**
   ```bash
   # 使用 Let's Encrypt (certbot)
   certbot certonly --webroot -w /var/www/html -d api.saintyeartex.com
   
   # 转换为 PFX 格式
   openssl pkcs12 -export -out api_saintyeartex_com.pfx \
     -inkey privkey.pem -in cert.pem -certfile chain.pem
   ```

2. **部署新证书**
   ```bash
   # 备份旧证书
   cp Certificates/api_saintyeartex_com.pfx Certificates/api_saintyeartex_com.pfx.backup

   # 部署新证书
   cp new_certificate.pfx Certificates/api_saintyeartex_com.pfx
   
   # 重启应用程序
   systemctl restart corehub-web
   ```

3. **验证部署**
   ```bash
   # 检查证书信息
   curl -k https://172.16.9.111:8081/api/SslManagement/certificate-info
   
   # 验证 HTTPS 连接
   openssl s_client -connect 172.16.9.111:8081 -servername api.saintyeartex.com
   ```

## 🚨 故障排查

### 1. **常见问题**

#### 证书文件不存在
```
错误: SSL证书文件不存在: Certificates/api_saintyeartex_com.pfx
解决: 检查文件路径和权限
```

#### 证书密码错误
```
错误: 证书验证失败: The specified network password is not correct
解决: 检查证书密码配置
```

#### 证书已过期
```
错误: 证书已过期，过期时间: 2024-01-01
解决: 更新证书文件
```

### 2. **调试命令**

#### 检查证书信息
```bash
# 查看 PFX 证书信息
openssl pkcs12 -info -in Certificates/api_saintyeartex_com.pfx -noout

# 测试 HTTPS 连接
curl -v https://172.16.9.111:8081/

# 检查证书链
openssl s_client -connect 172.16.9.111:8081 -showcerts
```

#### 验证证书有效性
```bash
# 检查证书过期时间
openssl x509 -in certificate.crt -noout -dates

# 验证证书和私钥匹配
openssl x509 -noout -modulus -in certificate.crt | openssl md5
openssl rsa -noout -modulus -in private.key | openssl md5
```

## 🔄 自动化建议

### 1. **证书自动更新**
```bash
#!/bin/bash
# 自动更新脚本示例
certbot renew --quiet
if [ $? -eq 0 ]; then
    # 转换证书格式
    openssl pkcs12 -export -out /app/Data/api_saintyeartex_com.pfx \
      -inkey /etc/letsencrypt/live/api.saintyeartex.com/privkey.pem \
      -in /etc/letsencrypt/live/api.saintyeartex.com/cert.pem \
      -certfile /etc/letsencrypt/live/api.saintyeartex.com/chain.pem \
      -password pass:$SSL_CERT_PASSWORD
    
    # 重启应用
    systemctl restart corehub-web
fi
```

### 2. **监控告警**
```bash
# 添加到 crontab，每日检查证书状态
0 9 * * * curl -s https://172.16.9.111:8081/api/SslManagement/expiry-check | \
  jq '.data.isExpiring' | grep -q true && \
  echo "SSL证书即将过期" | mail -s "SSL证书告警" <EMAIL>
```

## 📊 监控指标

### 1. **关键指标**
- 证书有效期剩余天数
- 证书验证状态
- HTTPS 连接成功率
- TLS 握手时间

### 2. **日志监控**
- SSL 证书验证日志
- HTTPS 请求日志
- 证书更新操作日志
- 安全事件日志

通过这套完整的 SSL 管理系统，你可以确保 CoreHub.Web 应用的 HTTPS 安全性和可靠性。
