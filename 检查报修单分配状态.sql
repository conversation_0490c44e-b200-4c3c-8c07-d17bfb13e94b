-- 检查报修单分配状态
USE [EquipmentManagement]
GO

PRINT '=== 检查报修单分配状态 ==='

-- 1. 检查当前显示的报修单详情
PRINT '1. 检查报修单 RO20250624003 的详情:'
SELECT 
    ro.Id as '报修单ID',
    ro.OrderNumber as '报修单号',
    ro.EquipmentId as '设备ID',
    e.Name as '设备名称',
    ro.Status as '状态',
    ro.AssignedTo as '分配给用户ID',
    au.Name as '分配给用户姓名',
    ro.ReporterId as '报修人ID',
    ru.Name as '报修人姓名',
    ro.MaintenanceDepartmentId as '维修部门ID',
    md.Name as '维修部门名称',
    ro.ReportedAt as '报修时间',
    ro.AssignedAt as '分配时间'
FROM RepairOrders ro
LEFT JOIN Equipment e ON ro.EquipmentId = e.Id
LEFT JOIN Users au ON ro.AssignedTo = au.Id
LEFT JOIN Users ru ON ro.ReporterId = ru.Id
LEFT JOIN Departments md ON ro.MaintenanceDepartmentId = md.Id
WHERE ro.OrderNumber = 'RO20250624003'

-- 2. 检查当前登录用户信息
PRINT ''
PRINT '2. 检查当前可能的登录用户:'
SELECT 
    u.Id as '用户ID',
    u.Username as '用户名',
    u.Name as '姓名',
    u.DepartmentId as '部门ID',
    d.Name as '部门名称',
    u.IsEnabled as '是否启用'
FROM Users u
LEFT JOIN Departments d ON u.DepartmentId = d.Id
WHERE u.IsEnabled = 1
ORDER BY u.Id

-- 3. 检查可以分配的维修人员
PRINT ''
PRINT '3. 检查可以分配的维修人员:'
SELECT DISTINCT
    u.Id as '用户ID',
    u.Username as '用户名',
    u.Name as '姓名',
    u.DepartmentId as '用户部门ID',
    ud.Name as '用户部门名称',
    rda.DepartmentId as '角色分配部门ID',
    rd.Name as '角色分配部门名称'
FROM Users u
INNER JOIN UserRoles ur ON u.Id = ur.UserId
INNER JOIN RoleDepartmentAssignments rda ON ur.RoleId = rda.RoleId
INNER JOIN Departments rd ON rda.DepartmentId = rd.Id
LEFT JOIN Departments ud ON u.DepartmentId = ud.Id
WHERE u.IsEnabled = 1 
  AND ur.IsEnabled = 1 
  AND rda.IsEnabled = 1
  AND rd.DepartmentTypeId IN (
      SELECT Id FROM DepartmentTypes WHERE IsMaintenance = 1
  )
ORDER BY u.Name

-- 4. 检查报修单应该分配给哪个维修部门
PRINT ''
PRINT '4. 检查报修单应该分配的维修部门:'
SELECT 
    ro.OrderNumber as '报修单号',
    e.Name as '设备名称',
    ed.Name as '设备部门',
    ed.DepartmentTypeId as '设备部门类型ID',
    edt.Name as '设备部门类型',
    ro.MaintenanceDepartmentId as '当前维修部门ID',
    md.Name as '当前维修部门名称'
FROM RepairOrders ro
LEFT JOIN Equipment e ON ro.EquipmentId = e.Id
LEFT JOIN Departments ed ON e.DepartmentId = ed.Id
LEFT JOIN DepartmentTypes edt ON ed.DepartmentTypeId = edt.Id
LEFT JOIN Departments md ON ro.MaintenanceDepartmentId = md.Id
WHERE ro.OrderNumber = 'RO20250624003'

PRINT ''
PRINT '=== 检查完成 ==='
