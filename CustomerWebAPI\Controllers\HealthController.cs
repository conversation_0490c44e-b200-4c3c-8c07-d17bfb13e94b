using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Threading;
using System.Threading.Tasks;
using CustomerWebAPI.Services.Health;
using CustomerWebAPI.Models;

namespace CustomerWebAPI.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class HealthController : ControllerBase
    {
        private readonly IHealthCheckService _healthService;
        private readonly ILogger<HealthController> _logger;

        public HealthController(
            IHealthCheckService healthService,
            ILogger<HealthController> logger)
        {
            _healthService = healthService;
            _logger = logger;
        }

        [HttpGet]
        public async Task<IActionResult> Get(CancellationToken cancellationToken)
        {
            try
            {
                var report = await _healthService.CheckHealthAsync(cancellationToken);
                return report.Status == "Healthy"
                    ? Ok(report)
                    : StatusCode(503, report);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Health check failed");
                return StatusCode(500, new HealthReport
                {
                    Status = "Unhealthy",
                    Timestamp = DateTime.UtcNow,
                    Error = "Health check failed"
                });
            }
        }
    }
} 