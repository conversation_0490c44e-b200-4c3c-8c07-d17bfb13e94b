using CustomerWebAPI.Constants;
using CustomerWebAPI.Middleware;
using CustomerWebAPI.Services.Health;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Serilog;

var builder = WebApplication.CreateBuilder(args);

// 配置Serilog
Log.Logger = new LoggerConfiguration()
    .ReadFrom.Configuration(builder.Configuration)
    .CreateLogger();

builder.Host.UseSerilog();

// 添加服务到容器
builder.Services
    .AddBasicAuthentication()
    .AddApplicationServices()
    .AddConfiguredHttpClients()
    .AddDirectoryServices(builder.Configuration)
    .AddDatabaseServices(builder.Configuration)
    .AddScoped<ILoggerService, LoggerService>()
    .AddFluentValidationServices()
    .AddApiControllerConfiguration()
    .AddCorsPolicy();

// 注册健康检查服务
builder.Services.AddScoped<IHealthCheckService, HealthCheckService>();

var app = builder.Build();

app.InitializeDirectories();

// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment())
{
    app.UseDeveloperExceptionPage();
}

app.UseCors(PolicyNames.AllowAllOrigin);
app.UseRouting();
// 添加全局异常处理中间件，用于捕获和处理应用程序中的所有未处理异常
app.UseMiddleware<ExceptionHandlingMiddleware>();
app.UseAuthentication();
app.UseAuthorization();

app.MapControllers();

app.Run();
