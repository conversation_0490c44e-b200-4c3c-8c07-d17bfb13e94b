using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace CustomerWebAPI.Models
{
    public class Department
    {
        public string Code { get; set; }
        public string Description { get; set; }
        public List<Operation> Operations { get; set; } = new List<Operation>();
    }

    public class Operation
    {
        public string Code { get; set; }
        public string Description { get; set; }
        public string DepartmentCode { get; set; }

        public List<string> AllowedDeviceIds { get; set; } = new List<string>();
        public bool IsAutoNavigate { get; set; }
    }

    // 添加存储过程结果集映射类
    public class DepartmentOperationView
    {
        public string Department { get; set; }
        public string Department_Description { get; set; }
        public string OpType { get; set; }
        public string OpDescription { get; set; }
        public string AllowedDeviceIds { get; set; }
        public bool IsAutoNavigate { get; set; }
    }
}
