<template>
	<div>
		<view class="flexContainer">
			<button class="flexItem" type="primary" @click="showManualInputDialog">手动输入打印</button>
			<button class="flexItem" type="primary" @click="startScan">开始扫描打印</button>
		</view>
	</div>
</template>



<script>
	//打印	
	const printer = uni.requireNativePlugin('LcPrinter');
	const modal = uni.requireNativePlugin('modal');
	var globalEvent = uni.requireNativePlugin('globalEvent');

	//页面级变量，用于访问Method及脚本变量
	var _self;

	//扫描	
	function utf8ByteToUnicodeStr(utf8Bytes) {
		var unicodeStr = "";
		for (var pos = 0; pos < utf8Bytes.length;) {
			var flag = utf8Bytes[pos];
			var unicode = 0;
			if ((flag >>> 7) === 0) {
				unicodeStr += String.fromCharCode(utf8Bytes[pos]);
				pos += 1;

			} else if ((flag & 0xFC) === 0xFC) {
				unicode = (utf8Bytes[pos] & 0x3) << 30;
				unicode |= (utf8Bytes[pos + 1] & 0x3F) << 24;
				unicode |= (utf8Bytes[pos + 2] & 0x3F) << 18;
				unicode |= (utf8Bytes[pos + 3] & 0x3F) << 12;
				unicode |= (utf8Bytes[pos + 4] & 0x3F) << 6;
				unicode |= (utf8Bytes[pos + 5] & 0x3F);
				unicodeStr += String.fromCodePoint(unicode);
				pos += 6;

			} else if ((flag & 0xF8) === 0xF8) {
				unicode = (utf8Bytes[pos] & 0x7) << 24;
				unicode |= (utf8Bytes[pos + 1] & 0x3F) << 18;
				unicode |= (utf8Bytes[pos + 2] & 0x3F) << 12;
				unicode |= (utf8Bytes[pos + 3] & 0x3F) << 6;
				unicode |= (utf8Bytes[pos + 4] & 0x3F);
				unicodeStr += String.fromCodePoint(unicode);
				pos += 5;

			} else if ((flag & 0xF0) === 0xF0) {
				unicode = (utf8Bytes[pos] & 0xF) << 18;
				unicode |= (utf8Bytes[pos + 1] & 0x3F) << 12;
				unicode |= (utf8Bytes[pos + 2] & 0x3F) << 6;
				unicode |= (utf8Bytes[pos + 3] & 0x3F);
				unicodeStr += String.fromCodePoint(unicode);
				pos += 4;

			} else if ((flag & 0xE0) === 0xE0) {
				unicode = (utf8Bytes[pos] & 0x1F) << 12;;
				unicode |= (utf8Bytes[pos + 1] & 0x3F) << 6;
				unicode |= (utf8Bytes[pos + 2] & 0x3F);
				unicodeStr += String.fromCharCode(unicode);
				pos += 3;

			} else if ((flag & 0xC0) === 0xC0) { //110
				unicode = (utf8Bytes[pos] & 0x3F) << 6;
				unicode |= (utf8Bytes[pos + 1] & 0x3F);
				unicodeStr += String.fromCharCode(unicode);
				pos += 2;

			} else {
				unicodeStr += String.fromCharCode(utf8Bytes[pos]);
				pos += 1;
			}
		}
		return unicodeStr;
	}

	var main, receiver, filter;
	var ScanDeviceClass = plus.android.importClass("android.device.ScanDevice");
	var scanDevice;
	import { ChemicalModel } from '@/models/ChemicalModel';
	export default {
		data() {
			return {
				manualInput: ''
			}
		},
		created: function(option) {
			_self = this;
			
			scanDevice = new ScanDeviceClass();
			scanDevice.openScan(); // 打开扫描
			scanDevice.setOutScanMode(0); // 扫描模式

			this.initScan();
			this.registerScan();
		},

		onUnload: function(cw) {
			this.unregisterScan();
		},
		onLoad() {
			// 添加打印状态监听
			globalEvent.addEventListener('onPrintCallback', function(e) {
				uni.showToast({
					title: 'state: ' + JSON.stringify(e),
					duration: 2000
				});
				console.log(JSON.stringify(e));
				if (e.key == 0) {
					uni.showToast({
						title: '打印成功',
						duration: 2000
					});
				} else if (e.key == 3) {
					uni.showToast({
						title: '缺纸',
						duration: 2000
					});
				} else if (e.key == 16) {
					uni.showToast({
						title: '设备未打开',
						duration: 2000
					});
				}

			});
			//打印机版本获取回调
			globalEvent.addEventListener('onVersion', function(e) {
				uni.showToast({
					title: 'version: ' + JSON.stringify(e),
					duration: 2000
				});
			});
			globalEvent.addEventListener('getsupportprint', function(e) {
				console.log('key: ' + JSON.stringify(e));
				uni.showToast({
					title: 'key: ' + JSON.stringify(e),
					duration: 2000
				});
			});
		},
		methods: {
			initPrinter() {
				// 调用异步方法
				console.log('初始化');
				var ret = printer.initPrinter({});
				modal.toast({
					message: ret,
					duration: 1.5
				});
				//设置黑标检测
				printer.printEnableMark({
					enable: true
				});
				printer.setConcentration({ //设置打印浓度。font_level 取值范围：1~39。
					level: 39
				});
				printer.setLineSpacing({
					spacing: 1
				});
				printer.getsupportprint();
			},
			initScan() {
				let _this = this;
				main = plus.android.runtimeMainActivity(); //获取activity  
				var IntentFilter = plus.android.importClass('android.content.IntentFilter');
				filter = new IntentFilter();
				filter.addAction("scan.rcv.message"); // 换你的广播动作  
				receiver = plus.android.implements('io.dcloud.feature.internal.reflect.BroadcastReceiver', {
					onReceive: function(context, intent) {
						plus.android.importClass(intent);
						let code = intent.getByteArrayExtra('barocode');
						//	let codeStr = String.fromCharCode(...code);

						let codeStr = utf8ByteToUnicodeStr(code);
						console.log('codeStr:', codeStr);
						scanDevice.stopScan(); // 停止扫描		

						_self.PrintLabel(codeStr); //创建并打印染料箱号信息
					}
				});
			},
			registerScan() {
				main.registerReceiver(receiver, filter);
			},
			unregisterScan() {
				main.unregisterReceiver(receiver);
			},
			startScan() {
				scanDevice.stopScan(); // 停止扫描
				scanDevice.startScan(); // 开始扫描
			},

			showManualInputDialog() {
				uni.showModal({
					title: '手动输入',
					editable: true,
					placeholderText: '请输入染料代码',
					success: (res) => {
						if (res.confirm && res.content.trim() !== '') {
							this.PrintLabel(res.content.trim(),true);
						} else if (res.confirm) {
							uni.showToast({
								title: '请输入染料代码',
								icon: 'none',
								duration: 2000
							});
						}
					}
				});
			},

			PrintLabel(input, isManualInput = false) {
				uni.request({
					url: 'http://172.16.1.12:8898/api/chemical/NewChemicalBoxNo',
					method: 'POST',
					data: { 'chemicalCode': input },
					header: {
						'Content-Type': 'application/json',
						'Authorization': 'Basic VEFMOlNhaW50eWVhcnRleA=='
					},
					success: (res) => {
						if (res.data && res.data.length > 0) {
							const chemical = new ChemicalModel(res.data[0]);
							
							// 根据输入类型选择条码文本
							let barcodeText = isManualInput ? chemical.chemicalCode : chemical.chemicalBoxNo;
							console.log(barcodeText);
							this.PrintChemicalLabel(chemical.chemicalCode, chemical.chemicalName, barcodeText);
						} else {
							uni.showToast({
								title: "未找到染料信息",
								icon: 'none',
								duration: 2000
							});
						}
					},
					fail: (res) => {
						console.log(res.errMsg);
						uni.showToast({
							title: "请求失败！" + res.errMsg,
							icon: 'none',
							duration: 2000
						});
					}
				});
			},
			PrintChemicalLabel(chemicalCode, chemicalName, chemicalBoxNo) {
				// 调用异步方法,初始化打印机
				console.log('初始化');
				var ret = printer.initPrinter({});
				modal.toast({
					message: ret,
					duration: 1.5
				});
				//设置黑标检测
				printer.printEnableMark({
					enable: true
				});
				printer.setConcentration({ //设置打印浓度。font_level 取值范围：1~39。
					level: 39
				});
				printer.setLineSpacing({
					spacing: 1
				});
				printer.getsupportprint();
				// printer.s

				//开始打印条码
				printer.printBarcode({
					text: chemicalBoxNo,
					height: 80,
					barcodeType: 73
				});
				printer.printText({
					content: '\n'
				});
				printer.printText2({
					offset: 2,
					fontSize: 6,
					isBold: false,
					isUnderLine: false,
					content: chemicalName
				});
				printer.printText({
					content: '\n'
				});
				printer.printText2({
					offset: 2,
					fontSize: 6,
					isBold: false,
					isUnderLine: false,
					content: chemicalCode
				});

				//打完换页
				printer.printGoToNextMark();
			}



		}
	}
</script>

<style>
	.flexContainer {
		display: flex;
		flex-direction: column-reverse;
		/* align-items: center; 
		justify-content: center; */
		height: 95vh; 
		flex-wrap: wrap;
		margin: 8rpx 20rpx 8rpx 8rpx;
		gap: 8rpx;
	}

	.flexItem {
		margin-left: 10rpx;
		margin-right: 10rpx;
		padding: 40rpx;
		font-size: 1.5rem;
	}

	input.flexItem {
		border: 1px solid #ccc;
		border-radius: 4px;
	}
</style>