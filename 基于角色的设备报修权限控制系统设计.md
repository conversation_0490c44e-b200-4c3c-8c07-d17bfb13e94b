# 基于角色的设备报修权限控制系统设计

## 系统概述

本系统实现了一个完整的基于角色的权限控制（RBAC）机制，用于管理设备报修流程中的权限分配和维修人员管理。系统支持细粒度的部门级权限控制，智能维修人员分配，以及基于技能的维修任务匹配。

## 核心功能特性

### 1. 基于角色的部门权限控制
- **权限类型**：
  - 可报修设备权限：用户可以报修指定部门的设备
  - 可接收报修权限：用户可以接收来自指定部门的报修请求
  - 可维修设备权限：用户可以维修指定部门的设备

- **权限分配**：
  - 管理员：拥有所有部门的全部权限
  - 操作员：只能报修本部门设备，可以将报修发送给维修部门
  - 访客：只有查看权限，无操作权限

### 2. 智能维修人员管理
- **维修人员属性**：
  - 技能等级：初级、中级、高级、专家
  - 专业技能：电气、机械、液压、控制系统等
  - 工作负载：最大并发处理报修单数量
  - 可用状态：是否可以接受新的报修任务

- **智能分配算法**：
  - 根据设备型号的技能要求匹配合适的维修人员
  - 考虑紧急程度、技能等级和当前工作负载
  - 支持手动指定和自动分配两种模式

### 3. 设备型号技能要求
- **技能定义**：每个设备型号可以定义所需的维修技能
- **技能等级**：不同技能有不同的等级要求
- **必需/可选**：区分必需技能和可选技能

## 数据库设计

### 新增表结构

#### 1. RoleDepartmentPermissions（角色部门权限表）
```sql
- Id: 主键
- RoleId: 角色ID
- DepartmentId: 部门ID  
- PermissionType: 权限类型（1=可报修设备, 2=可接收报修, 3=可维修设备）
- IsEnabled: 是否启用
- 创建和更新时间戳
```

#### 2. MaintenancePersonnel（维修人员表）
```sql
- Id: 主键
- UserId: 用户ID
- DepartmentId: 所属部门ID
- Specialties: 专业技能（逗号分隔）
- Level: 技能等级（1-4）
- MaxConcurrentOrders: 最大并发处理数量
- IsAvailable: 是否可接单
- 创建和更新时间戳
```

#### 3. EquipmentModelSkills（设备型号技能要求表）
```sql
- Id: 主键
- ModelId: 设备型号ID
- SkillName: 技能名称
- RequiredLevel: 所需技能等级
- IsRequired: 是否必需技能
```

### 权限数据示例
```sql
-- 管理员：所有部门的全部权限
-- 操作员：只能报修整理部设备，可以发送给维修部
-- 访客：无操作权限
```

## 服务层架构

### 1. IRoleDepartmentPermissionService
- **权限查询**：
  - GetUserReportableDepartmentsAsync：获取用户可报修的部门
  - GetUserReceivableDepartmentsAsync：获取用户可接收报修的部门
  - GetUserMaintainableDepartmentsAsync：获取用户可维修的部门
  - CanUserReportEquipmentAsync：检查用户是否可以报修指定部门设备

- **权限管理**：
  - CreateRoleDepartmentPermissionAsync：创建角色部门权限
  - SetRoleDepartmentPermissionsAsync：批量设置角色权限

### 2. IMaintenancePersonnelService
- **人员查询**：
  - GetAvailableMaintenancePersonnelAsync：获取可用维修人员
  - GetSuitableMaintenancePersonnelAsync：根据设备型号获取合适人员
  - GetMaintenancePersonnelWorkloadAsync：获取人员工作负载

- **智能分配**：
  - AssignOptimalMaintenancePersonnelAsync：智能分配最优维修人员
  - HasRequiredSkillsAsync：检查人员是否具备所需技能

## 前端实现

### 1. CreateRepairOrder.razor 改进
- **权限控制**：
  - 只显示用户有权限报修的部门设备
  - 部门选择框根据用户权限动态过滤
  - 提交按钮在无权限时禁用

- **维修人员选择**：
  - 根据选择的维修部门和设备型号动态加载合适的维修人员
  - 显示维修人员的技能等级、专业技能和当前工作负载
  - 支持手动指定或自动分配

- **智能提示**：
  - 清晰的权限提示信息
  - 维修人员可用状态显示
  - 自动分配结果通知

### 2. TestUserDepartment.razor 测试页面
- **权限展示**：
  - 显示用户在各个部门的权限类型
  - 展示用户可报修的设备列表
  - 维修人员信息展示

- **功能验证**：
  - 权限控制效果验证
  - 设备过滤效果测试
  - 维修人员信息查看

## 业务流程

### 1. 报修流程
```
1. 用户登录 → 获取用户角色和权限
2. 选择部门 → 根据权限过滤可选部门
3. 选择设备 → 只显示有权限报修的部门设备
4. 填写故障信息 → 选择紧急程度和维修部门
5. 选择维修人员 → 根据设备型号和技能要求推荐
6. 提交报修 → 权限验证 + 智能分配维修人员
```

### 2. 权限验证流程
```
1. 检查用户是否有报修权限
2. 验证选择的设备是否在用户权限范围内
3. 确认维修部门是否在用户可选范围内
4. 验证指定的维修人员是否具备所需技能
```

### 3. 智能分配算法
```
1. 获取设备型号的技能要求
2. 筛选具备必需技能的维修人员
3. 根据紧急程度选择分配策略：
   - 紧急/高：优先选择技能等级最高的
   - 中等：平衡技能等级和工作负载
   - 低：优先选择工作负载最轻的
```

## 配置和部署

### 1. 服务注册
在 MauiProgram.cs 和 Program.cs 中添加：
```csharp
builder.Services.AddScoped<IRoleDepartmentPermissionService, RoleDepartmentPermissionService>();
builder.Services.AddScoped<IMaintenancePersonnelService, MaintenancePersonnelService>();
```

### 2. 数据库初始化
执行更新后的 `数据库脚本_完整版.sql`，包含：
- 新表结构创建
- 索引和约束添加
- 示例权限数据
- 维修人员和技能要求数据

### 3. 测试验证
1. 使用不同角色的用户登录测试
2. 访问 `/create-repair-order` 验证权限控制
3. 访问 `/test-user-department` 查看详细权限信息
4. 测试维修人员智能分配功能

## 扩展性设计

### 1. 权限扩展
- 支持更细粒度的权限控制（如设备类型级别）
- 支持临时权限授权
- 支持权限继承和委托

### 2. 技能管理扩展
- 支持技能认证和过期管理
- 支持技能培训记录
- 支持技能评级和升级

### 3. 智能分配扩展
- 支持基于历史维修记录的推荐
- 支持维修人员偏好设置
- 支持工作时间和排班管理

## 安全考虑

1. **多层权限验证**：前端过滤 + 后端验证
2. **数据完整性**：外键约束和事务处理
3. **审计日志**：记录权限变更和关键操作
4. **错误处理**：优雅的错误处理和用户反馈
