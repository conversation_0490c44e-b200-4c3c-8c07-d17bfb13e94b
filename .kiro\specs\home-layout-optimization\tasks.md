# 实施计划

- [ ] 1. 创建基础组件结构和接口
  - 创建共享组件目录结构
  - 定义组件基础接口和数据模型
  - 实现错误边界组件
  - _需求: 1.1, 1.4, 7.1_

- [ ] 1.1 创建仪表板组件目录结构
  - 在 CoreHub.Shared/Components 下创建 Dashboard 文件夹
  - 创建子文件夹：Cards、Charts、Loading、Common
  - 创建组件基础文件和命名空间
  - _需求: 7.1, 7.2_

- [ ] 1.2 实现错误边界和加载状态组件
  - 创建 ErrorBoundary 组件处理组件级错误
  - 实现 LoadingState 组件替换简单的进度圆圈
  - 创建 DataLoadingState 类管理加载状态
  - 编写组件单元测试
  - _需求: 1.3, 6.2_

- [ ] 1.3 定义扩展数据模型和接口
  - 扩展 EquipmentStatisticsDto 添加趋势数据
  - 创建 RepairOrderTrendData 和 EquipmentTrendData 类
  - 定义 DashboardPreferences 用户偏好模型
  - 创建 QuickAction 快速操作模型
  - _需求: 2.2, 2.3, 5.1_

- [ ] 2. 实现可重用的指标卡片组件
  - 创建 MetricCard 组件替换现有的硬编码卡片
  - 实现卡片点击交互和导航功能
  - 添加趋势指示器和动画效果
  - _需求: 1.4, 5.3_

- [ ] 2.1 创建 MetricCard 基础组件
  - 实现 MetricCard.razor 组件支持标题、数值、图标、颜色
  - 添加可选的趋势文本和颜色显示
  - 实现点击事件和导航功能
  - 使用 MudBlazor 的 Card、Typography、Icon 组件
  - _需求: 1.4, 5.3, 7.2, 7.3_

- [ ] 2.2 实现 MetricCardSkeleton 骨架屏组件
  - 创建加载时的骨架屏效果
  - 使用 MudBlazor 的 Skeleton 组件或自定义动画
  - 确保骨架屏尺寸与实际卡片一致
  - _需求: 1.3, 7.1_

- [ ] 2.3 重构现有指标卡片使用新组件
  - 将 Home.razor 中的四个硬编码卡片替换为 MetricCard 组件
  - 配置每个卡片的属性：图标、颜色、点击行为
  - 实现卡片数据绑定和状态管理
  - 测试卡片响应式布局和交互
  - _需求: 1.4, 4.1, 5.3_

- [ ] 3. 增强数据可视化组件
  - 集成图表库（推荐 ApexCharts.Blazor）
  - 创建设备状态分布图表组件
  - 实现报修单趋势图表组件
  - _需求: 3.1, 3.2, 3.4_

- [ ] 3.1 集成 ApexCharts.Blazor 图表库
  - 安装 ApexCharts.Blazor NuGet 包
  - 在 Program.cs 中注册 ApexCharts 服务
  - 在 _Imports.razor 中添加 ApexCharts 命名空间
  - 创建图表基础配置和主题设置
  - _需求: 3.1, 7.1_

- [ ] 3.2 实现 EquipmentStatusChart 组件
  - 创建设备状态分布的环形图或饼图
  - 支持悬停显示详细信息的工具提示
  - 实现图表点击事件导航到设备管理页面
  - 使用 MudBlazor 颜色主题保持一致性
  - _需求: 3.1, 3.3, 3.4, 5.3_

- [ ] 3.3 创建 RepairOrderTrendChart 组件
  - 实现报修单趋势的时间序列图表
  - 显示新建、完成、待处理报修单的趋势线
  - 添加时间范围选择器（7天、30天、90天）
  - 实现图表数据的增量更新
  - _需求: 3.2, 3.4, 6.3_

- [ ] 3.4 替换现有的进度条图表
  - 将设备状态分布的进度条替换为 EquipmentStatusChart
  - 将报修单统计的进度条替换为 RepairOrderTrendChart
  - 保持数据绑定和更新逻辑
  - 测试图表在不同屏幕尺寸下的显示效果
  - _需求: 3.1, 4.2_

- [ ] 4. 实现系统健康度和性能指标组件
  - 创建 SystemHealthIndicator 组件
  - 实现 MaintenanceEfficiencyCard 组件
  - 添加快速操作面板
  - _需求: 5.1, 5.2_

- [ ] 4.1 创建 SystemHealthIndicator 组件
  - 实现带动画的环形进度指示器
  - 添加健康度评分算法和颜色映射
  - 显示关键设备状态统计信息
  - 实现健康度变化的动画效果
  - _需求: 3.3, 5.2_

- [ ] 4.2 重构维修效率卡片组件
  - 创建 MaintenanceEfficiencyCard 独立组件
  - 改进效率指标的计算和显示
  - 添加效率趋势指示器
  - 实现效率数据的实时更新
  - _需求: 5.2, 6.3_

- [ ] 4.3 实现 QuickActionPanel 组件
  - 创建快速操作按钮面板
  - 实现基于权限的操作显示
  - 添加常用操作：新建报修单、设备扫描、数据导出
  - 配置操作按钮的图标、颜色和导航
  - _需求: 5.1, 2.1_

- [ ] 5. 优化数据表格和通知组件
  - 改进最近报修单表格的显示和交互
  - 增强系统通知组件的功能
  - 实现表格的虚拟滚动和分页
  - _需求: 5.3, 5.4_

- [ ] 5.1 优化 RecentRepairOrdersTable 组件
  - 提取为独立的可重用组件
  - 实现表格的排序、筛选和搜索功能
  - 添加表格行的悬停效果和点击导航
  - 优化移动端的表格显示（卡片模式）
  - _需求: 4.1, 4.3, 5.3_

- [ ] 5.2 增强 SystemNotifications 组件
  - 创建独立的通知组件支持不同类型的警告
  - 实现通知的优先级排序和分类显示
  - 添加通知的点击操作和快速处理
  - 实现通知的自动刷新和实时更新
  - _需求: 3.3, 5.2, 6.4_

- [ ] 5.3 实现表格虚拟滚动优化
  - 为大数据量表格实现虚拟滚动
  - 优化表格的渲染性能
  - 实现表格数据的懒加载
  - 测试表格在不同数据量下的性能
  - _需求: 6.1, 6.3_

- [ ] 6. 实现用户偏好设置和状态管理
  - 创建用户偏好设置服务
  - 实现仪表板布局的个性化配置
  - 添加自动刷新和手动刷新功能
  - _需求: 2.1, 2.2, 2.3_

- [ ] 6.1 创建 DashboardPreferencesService
  - 实现用户偏好的保存和加载
  - 支持卡片显示/隐藏的配置
  - 实现卡片顺序的拖拽调整
  - 添加偏好设置的本地存储
  - _需求: 2.1, 2.2, 2.3_

- [ ] 6.2 实现 DashboardStateService 状态管理
  - 创建集中的仪表板状态管理
  - 实现数据的缓存和增量更新
  - 添加状态变化的事件通知
  - 优化数据加载的性能
  - _需求: 6.1, 6.3_

- [ ] 6.3 添加自动刷新和手动刷新功能
  - 实现可配置的自动刷新间隔
  - 添加手动刷新按钮和快捷键
  - 实现网络状态检测和离线处理
  - 优化刷新时的用户体验
  - _需求: 5.2, 6.3, 6.4_

- [ ] 7. 实现响应式布局和移动端优化
  - 优化移动端的布局和交互
  - 实现触摸友好的界面元素
  - 测试不同设备和屏幕尺寸的兼容性
  - _需求: 4.1, 4.2, 4.3, 4.4_

- [ ] 7.1 优化移动端布局和交互
  - 调整移动端的卡片布局和间距
  - 实现触摸友好的按钮尺寸
  - 优化移动端的图表显示和交互
  - 实现移动端的侧滑和手势操作
  - _需求: 4.1, 4.3_

- [ ] 7.2 实现横竖屏自适应布局
  - 处理设备方向变化的布局调整
  - 优化横屏模式下的内容显示
  - 实现布局的平滑过渡动画
  - 测试不同设备的横竖屏切换
  - _需求: 4.4_

- [ ] 7.3 测试多设备兼容性
  - 在不同尺寸的设备上测试布局
  - 验证触摸交互的准确性和响应性
  - 测试图表在移动端的性能
  - 优化移动端的加载速度
  - _需求: 4.1, 4.2, 4.3_

- [ ] 8. 性能优化和错误处理完善
  - 实现数据加载的性能优化
  - 完善错误处理和用户反馈
  - 添加加载状态和错误重试机制
  - _需求: 6.1, 6.2, 6.3, 6.4_

- [ ] 8.1 实现数据加载性能优化
  - 实现并行数据加载和缓存机制
  - 优化首次加载的关键路径
  - 实现数据的预加载和懒加载
  - 添加加载性能的监控和分析
  - _需求: 6.1, 6.3_

- [ ] 8.2 完善错误处理和用户反馈
  - 实现友好的错误提示和重试机制
  - 添加网络错误的处理和恢复
  - 实现数据加载失败的降级显示
  - 优化错误信息的用户体验
  - _需求: 6.2, 6.4_

- [ ] 8.3 添加自动重试和恢复机制
  - 实现数据加载失败的自动重试
  - 添加网络恢复后的自动刷新
  - 实现长时间未操作的自动数据更新
  - 优化重试策略和用户提示
  - _需求: 6.3, 6.4_

- [ ] 9. 集成测试和文档完善
  - 编写组件的单元测试和集成测试
  - 创建用户使用文档和开发文档
  - 进行性能测试和可访问性测试
  - _需求: 所有需求的验证_

- [ ] 9.1 编写组件单元测试
  - 使用 bUnit 为所有新组件编写单元测试
  - 测试组件的渲染、交互和数据绑定
  - 实现测试的自动化运行和持续集成
  - 确保测试覆盖率达到 80% 以上
  - _需求: 所有组件相关需求_

- [ ] 9.2 进行集成测试和端到端测试
  - 测试完整的数据流从服务到组件
  - 验证不同用户权限下的界面差异
  - 测试响应式布局在实际设备上的表现
  - 进行性能基准测试和优化
  - _需求: 1.1, 2.1, 4.1, 6.1_

- [ ] 9.3 创建文档和使用指南
  - 编写组件的开发文档和 API 说明
  - 创建用户的功能使用指南
  - 记录性能优化的最佳实践
  - 提供故障排除和维护指南
  - _需求: 所有需求的文档化_