using MauiScanManager.Constants;
using MauiScanManager.Models;
using Microsoft.Extensions.Logging;

namespace MauiScanManager.Services
{
    public class ColorCardService : IColorCardService
    {
        private readonly IApiService _apiService;
        private readonly IPlatformLoadingService _loadingService;
        private readonly ILogger<ColorCardService> _logger;

        public ColorCardService(
            IApiService apiService,
            IPlatformLoadingService loadingService,
            ILogger<ColorCardService> logger)
        {
            _apiService = apiService;
            _loadingService = loadingService;
            _logger = logger;
        }

        public async Task<ServiceResult<bool>> UpdateLocationAsync(ColorCardLocation model)
        {
            _loadingService.ShowNativeLoading("正在更新色卡位置...");
            try
            {
                var response = await _apiService.PostAsync<bool>(
                    ApiEndpoints.ColorCard.UpdateLocation, 
                    model);

                if (response.Success)
                {
                    _logger.LogInformation("色卡位置更新成功");
                }
                else
                {
                    _logger.LogWarning("色卡位置更新失败: {Message}", response.Message);
                }

                return response.Success 
                    ? ServiceResult<bool>.Success(response.Data)
                    : ServiceResult<bool>.Failure(response.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新色卡位置失败");
                return ServiceResult<bool>.Failure(ex.Message);
            }
            finally
            {
                _loadingService.HideNativeLoading();
            }
        }
    }
} 