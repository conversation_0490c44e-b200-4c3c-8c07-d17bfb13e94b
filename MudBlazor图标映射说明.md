# MudBlazor 图标映射说明

## 概述

本文档说明了系统菜单从 Bootstrap Icons 迁移到 MudBlazor Material Icons 的图标映射关系。

## 图标映射表

### 主要功能菜单

| 功能模块 | 原Bootstrap图标 | 新MudBlazor图标 | Material Icon |
|---------|----------------|----------------|---------------|
| 首页 | `bi bi-house-door-fill-nav-menu` | `home` | `Icons.Material.Filled.Home` |
| 计数器 | `bi bi-plus-square-fill-nav-menu` | `add` | `Icons.Material.Filled.Add` |
| 设备扫描 | `bi bi-camera-fill-nav-menu` | `qr_code_scanner` | `Icons.Material.Filled.QrCodeScanner` |
| 摄像头测试 | `bi bi-camera2-nav-menu` | `camera_alt` | `Icons.Material.Filled.Camera` |
| 天气预报 | `bi bi-list-nested-nav-menu` | `wb_sunny` | `Icons.Material.Filled.WbSunny` |
| 设备报修 | `bi bi-tools-nav-menu` | `build` | `Icons.Material.Filled.Build` |

### 系统管理菜单

| 功能模块 | 原Bootstrap图标 | 新MudBlazor图标 | Material Icon |
|---------|----------------|----------------|---------------|
| 系统管理 | `bi bi-gear-nav-menu` | `settings` | `Icons.Material.Filled.Settings` |
| 用户管理 | `bi bi-people-nav-menu` | `people` | `Icons.Material.Filled.People` |
| 角色管理 | `bi bi-person-badge-nav-menu` | `security` | `Icons.Material.Filled.Security` |
| 权限管理 | `bi bi-key-nav-menu` | `key` | `Icons.Material.Filled.Key` |
| 菜单管理 | `bi bi-list-ul` | `menu` | `Icons.Material.Filled.Menu` |
| 数据库设置 | `bi bi-database-gear` | `storage` | `Icons.Material.Filled.Storage` |
| 权限分配 | `bi bi-person-gear-nav-menu` | `person_add` | `Icons.Material.Filled.PersonAdd` |

### 其他功能

| 功能模块 | 原Bootstrap图标 | 新MudBlazor图标 | Material Icon |
|---------|----------------|----------------|---------------|
| 权限控制示例 | `bi bi-shield-check-nav-menu` | `shield` | `Icons.Material.Filled.Shield` |
| 认证调试 | `bi bi-bug-nav-menu` | `bug_report` | `Icons.Material.Filled.BugReport` |
| 登录 | `bi bi-box-arrow-in-right-nav-menu` | `login` | `Icons.Material.Filled.Login` |
| 代码示例 | `bi bi-code-slash` | `code` | `Icons.Material.Filled.Code` |
| 样式演示 | `bi bi-palette` | `palette` | `Icons.Material.Filled.Palette` |
| 树形结构 | `bi bi-diagram-2` | `account_tree` | `Icons.Material.Filled.AccountTree` |

## 使用说明

### 1. 数据库更新

运行以下脚本更新现有数据库：

```sql
-- 执行图标更新脚本
-- 文件: 图标更新脚本.sql
```

### 2. 新建菜单

在创建新菜单时，直接使用简化的图标名称：

```sql
INSERT INTO MenuItems (Code, Name, Icon, ...)
VALUES ('NewMenu', '新菜单', 'home', ...)
```

### 3. 代码实现

NavMenu.razor 中的 `GetMudIcon` 函数会自动处理图标映射：

```csharp
private string GetMudIcon(string? iconName)
{
    return iconName switch
    {
        "home" => Icons.Material.Filled.Home,
        "add" => Icons.Material.Filled.Add,
        "qr_code_scanner" => Icons.Material.Filled.QrCodeScanner,
        // ... 更多映射
        _ => Icons.Material.Filled.Circle
    };
}
```

## 兼容性说明

### 向后兼容

系统保持对旧的 Bootstrap 图标格式的兼容：

- 现有的 `bi bi-*` 格式仍然可以正常工作
- 建议逐步迁移到新的简化格式
- 新功能推荐使用简化图标名称

### 迁移建议

1. **立即迁移**：所有新增菜单使用新格式
2. **渐进式更新**：现有菜单可以逐步更新
3. **测试验证**：更新后检查菜单显示是否正常

## 扩展图标

如需添加新图标，请按以下步骤：

1. 在 `GetMudIcon` 函数中添加映射
2. 更新数据库脚本中的图标名称
3. 更新本文档的映射表

### 常用Material Icons参考

```csharp
// 常用图标示例
Icons.Material.Filled.Dashboard    // 仪表板
Icons.Material.Filled.Assignment   // 任务
Icons.Material.Filled.Assessment   // 评估
Icons.Material.Filled.Analytics    // 分析
Icons.Material.Filled.Category     // 分类
Icons.Material.Filled.Folder       // 文件夹
Icons.Material.Filled.Description  // 描述/文档
Icons.Material.Filled.Extension    // 扩展
Icons.Material.Filled.Help         // 帮助
Icons.Material.Filled.Info         // 信息
Icons.Material.Filled.Notifications // 通知
```

## 注意事项

1. **图标一致性**：确保同类功能使用相似的图标
2. **显示效果**：新图标在不同设备上的显示效果
3. **用户体验**：图标应该直观易懂
4. **维护成本**：简化的命名降低了维护复杂度

## 问题排查

### 图标不显示

1. 检查图标名称是否正确
2. 确认 `GetMudIcon` 函数包含该映射
3. 验证数据库中的图标字段值

### 显示错误图标

1. 清除浏览器缓存
2. 检查图标映射是否正确
3. 确认 MudBlazor 版本兼容性

## 版本历史

- **v1.0** (2024-01-xx): 初始版本，支持Bootstrap到MudBlazor的图标迁移
- **v1.1** (2024-01-xx): 添加向后兼容性支持
- **v1.2** (2024-01-xx): 优化图标映射，增加常用图标 