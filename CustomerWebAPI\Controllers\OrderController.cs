using Microsoft.AspNetCore.Mvc;
using CustomerWebAPI.Models;
using CustomerWebAPI.Services;
using System.Threading.Tasks;
using System.Linq;

namespace CustomerWebAPI.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class OrderController : ControllerBase
    {
        private readonly IOrderService _orderService;

        public OrderController(IOrderService orderService)
        {
            _orderService = orderService;
        }

        [HttpPost("SavePPOItemTrace")]
        public async Task<ApiResponse<bool>> SaveOrders([FromBody] ScanOrder model)
        {
            return await _orderService.SaveOrdersAsync(model);
        }
    }
} 