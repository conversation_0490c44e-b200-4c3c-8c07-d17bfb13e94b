@page "/create-repair-order"
@page "/create-repair-order/{equipmentCode}"
@using CoreHub.Shared.Models.Database
@using CoreHub.Shared.Models.Dto
@using CoreHub.Shared.Services
@using CoreHub.Shared.Components
@using FluentValidation
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.Extensions.Logging
@using System.Security.Claims
@inject IRepairOrderService RepairOrderService
@inject IEquipmentService EquipmentService
@inject IEquipmentModelService EquipmentModelService
@inject IDepartmentService DepartmentService
@inject IUserManagementService UserManagementService
@inject IRoleDepartmentAssignmentServiceV2 RoleDepartmentAssignmentService
@inject IMaintenanceDepartmentPermissionService MaintenanceDepartmentPermissionService

@inject IDepartmentTypeService DepartmentTypeService
@inject IJobTypeService JobTypeService
@inject ISnackbar Snackbar
@inject NavigationManager Navigation
@inject AuthenticationStateProvider AuthStateProvider
@inject ILogger<CreateRepairOrder> Lo<PERSON>
@inject IQrCodeScannerService QrCodeScannerService

<PageTitle>设备报修</PageTitle>

<AuthorizeView>
    <Authorized>
        <MudContainer MaxWidth="MaxWidth.Large" Fixed="true" Class="mt-4">
            <MudPaper Class="pa-6">
                <MudGrid>
                    <MudItem xs="12">
                        <MudText Typo="Typo.h4" Class="mb-6">
                            <MudIcon Icon="@Icons.Material.Filled.Build" Class="mr-2" />
                            设备报修
                            @if (isFromQrCode)
                            {
                                <MudChip T="string" Color="Color.Info" Size="Size.Small" Class="ml-2">
                                    <MudIcon Icon="@Icons.Material.Filled.QrCode" Size="Size.Small" Class="mr-1" />
                                    二维码扫描
                                </MudChip>
                            }
                        </MudText>
                    </MudItem>

                    <MudItem xs="12">
                        <MudForm @ref="form" Model="@repairOrder" Validation="@(new RepairOrderValidator())">
                            <MudGrid>
                                <!-- 设备选择 -->
                                <MudItem xs="12">
                                    <MudCard>
                                        <MudCardHeader>
                                            <CardHeaderContent>
                                                <MudText Typo="Typo.h6">
                                                    <MudIcon Icon="@Icons.Material.Filled.Devices" Class="mr-2" />
                                                    选择设备
                                                </MudText>
                                            </CardHeaderContent>
                                        </MudCardHeader>
                                        <MudCardContent>
                                            <MudGrid>
                                                <MudItem xs="12" md="6">
                                                    <MudSelect T="int?" Value="selectedDepartmentId"
                                                        Label="设备所属部门" ValueChanged="OnDepartmentChanged"
                                                        Clearable="true" Disabled="@(!hasReportPermission)">
                                                        @foreach (var dept in reportableDepartments)
                                                        {
                                                            <MudSelectItem T="int?" Value="@(dept.Id)">@dept.Name
                                                            </MudSelectItem>
                                                        }
                                                    </MudSelect>
                                                    @if (!hasReportPermission)
                                                    {
                                                        <MudText Typo="Typo.caption" Color="Color.Error" Class="mt-1">
                                                            您没有设备报修权限
                                                        </MudText>
                                                    }
                                                    else if (!reportableDepartments.Any())
                                                    {
                                                        <MudText Typo="Typo.caption" Color="Color.Warning" Class="mt-1">
                                                            没有可报修的部门
                                                        </MudText>
                                                    }
                                                </MudItem>
                                                <MudItem xs="12" md="6">
                                                    <MudSelect T="int?" Value="selectedModelId"
                                                        Label="设备型号" ValueChanged="OnModelChanged"
                                                        Clearable="true" Disabled="@(!hasReportPermission || !selectedDepartmentId.HasValue)">
                                                        @foreach (var model in filteredModels)
                                                        {
                                                            <MudSelectItem T="int?" Value="@(model.Id)">
                                                                <div class="d-flex align-center">
                                                                    <div class="flex-grow-1">
                                                                        <MudText Typo="Typo.body1">@model.Name</MudText>
                                                                        <MudText Typo="Typo.caption" Color="Color.Secondary">
                                                                            @model.Category @(!string.IsNullOrEmpty(model.Brand) ? $" | {model.Brand}" : "")
                                                                        </MudText>
                                                                    </div>
                                                                </div>
                                                            </MudSelectItem>
                                                        }
                                                    </MudSelect>
                                                    @if (!selectedDepartmentId.HasValue)
                                                    {
                                                        <MudText Typo="Typo.caption" Color="Color.Secondary" Class="mt-1">
                                                            请先选择部门
                                                        </MudText>
                                                    }
                                                </MudItem>
                                                <MudItem xs="12">
                                                    <MudGrid>
                                                        <MudItem xs="12" md="10">
                                                            <MudSelect T="int" Value="repairOrder.EquipmentId"
                                                                For="@(() => repairOrder.EquipmentId)" Label="选择设备"
                                                                Required="true" ValueChanged="OnEquipmentChanged"
                                                                ToStringFunc="@(id => GetEquipmentDisplayName(id))">
                                                                @foreach (var equipment in filteredEquipment)
                                                                {
                                                                    <MudSelectItem T="int" Value="@equipment.Id">
                                                                        <div class="d-flex align-center">
                                                                            <div class="flex-grow-1">
                                                                                <MudText Typo="Typo.body1">@equipment.Name</MudText>
                                                                                <MudText Typo="Typo.caption"
                                                                                    Color="Color.Secondary">
                                                                                    @equipment.Code | @equipment.DepartmentName |
                                                                                    @equipment.LocationName
                                                                                </MudText>
                                                                            </div>
                                                                            <MudChip T="string"
                                                                                Color="@GetEquipmentStatusColor(equipment.Status)"
                                                                                Size="Size.Small">
                                                                                @equipment.StatusName
                                                                            </MudChip>
                                                                        </div>
                                                                    </MudSelectItem>
                                                                }
                                                            </MudSelect>
                                                        </MudItem>
                                                        <MudItem xs="12" md="2" Class="d-flex align-center">
                                                            <MudButton Variant="Variant.Outlined"
                                                                Color="Color.Primary"
                                                                StartIcon="@Icons.Material.Filled.QrCodeScanner"
                                                                OnClick="ScanQrCodeAsync"
                                                                Disabled="@isScanning"
                                                                FullWidth="true"
                                                                Class="mt-2">
                                                                @if (isScanning)
                                                                {
                                                                    <MudProgressCircular Size="Size.Small" Indeterminate="true" Class="mr-2" />
                                                                    <span>扫描中...</span>
                                                                }
                                                                else
                                                                {
                                                                    <span>扫码选择</span>
                                                                }
                                                            </MudButton>
                                                        </MudItem>
                                                    </MudGrid>
                                                </MudItem>
                                                @if (selectedEquipment != null)
                                                {
                                                    <MudItem xs="12">
                                                        <MudAlert Severity="Severity.Info" Class="mt-2">
                                                            <MudText><strong>设备信息：</strong></MudText>
                                                            <MudText>名称：@selectedEquipment.Name</MudText>
                                                            <MudText>编码：@selectedEquipment.Code</MudText>
                                                            <MudText>位置：@selectedEquipment.DepartmentName -
                                                                @selectedEquipment.LocationName</MudText>
                                                            <MudText>状态：@selectedEquipment.StatusName</MudText>
                                                        </MudAlert>
                                                    </MudItem>
                                                }
                                            </MudGrid>
                                        </MudCardContent>
                                    </MudCard>
                                </MudItem>

                                <!-- 故障信息 -->
                                <MudItem xs="12">
                                    <MudCard>
                                        <MudCardHeader>
                                            <CardHeaderContent>
                                                <MudText Typo="Typo.h6">
                                                    <MudIcon Icon="@Icons.Material.Filled.ReportProblem" Class="mr-2" />
                                                    故障信息
                                                </MudText>
                                            </CardHeaderContent>
                                        </MudCardHeader>
                                        <MudCardContent>
                                            <MudGrid>
                                                <MudItem xs="12" md="6">
                                                    <MudSelect T="int" @bind-Value="repairOrder.UrgencyLevel"
                                                        For="@(() => repairOrder.UrgencyLevel)" Label="紧急程度"
                                                        Required="true">
                                                        <MudSelectItem T="int" Value="1">
                                                            <div class="d-flex align-center">
                                                                <MudIcon Icon="@Icons.Material.Filled.PriorityHigh"
                                                                    Color="Color.Error" Class="mr-2" />
                                                                <span>紧急 - 设备完全停机，严重影响生产</span>
                                                            </div>
                                                        </MudSelectItem>
                                                        <MudSelectItem T="int" Value="2">
                                                            <div class="d-flex align-center">
                                                                <MudIcon Icon="@Icons.Material.Filled.Warning"
                                                                    Color="Color.Warning" Class="mr-2" />
                                                                <span>高 - 设备功能受限，影响正常使用</span>
                                                            </div>
                                                        </MudSelectItem>
                                                        <MudSelectItem T="int" Value="3">
                                                            <div class="d-flex align-center">
                                                                <MudIcon Icon="@Icons.Material.Filled.Info"
                                                                    Color="Color.Info" Class="mr-2" />
                                                                <span>中 - 设备有异常，但不影响基本功能</span>
                                                            </div>
                                                        </MudSelectItem>
                                                        <MudSelectItem T="int" Value="4">
                                                            <div class="d-flex align-center">
                                                                <MudIcon Icon="@Icons.Material.Filled.Schedule"
                                                                    Color="Color.Default" Class="mr-2" />
                                                                <span>低 - 预防性维护或小问题</span>
                                                            </div>
                                                        </MudSelectItem>
                                                    </MudSelect>
                                                </MudItem>
                                                <MudItem xs="12" md="6">
                                                    <MudSelect T="int" Value="repairOrder.MaintenanceDepartmentId"
                                                        For="@(() => repairOrder.MaintenanceDepartmentId)" Label="维修部门"
                                                        Required="true" ValueChanged="OnMaintenanceDepartmentChanged"
                                                        ToStringFunc="@(id => GetMaintenanceDepartmentDisplayName(id))"
                                                        HelperText="@GetMaintenanceDepartmentHelperText()"
                                                        Disabled="@(selectedEquipment == null)">
                                                        @if (selectedEquipment != null)
                                                        {
                                                            @foreach (var dept in maintenanceDepartments)
                                                            {
                                                                <MudSelectItem T="int" Value="@dept.Id">
                                                                    <div class="d-flex align-center">
                                                                        <MudIcon Icon="@Icons.Material.Filled.Engineering" Size="Size.Small" Class="mr-2" />
                                                                        <span>@dept.Name</span>
                                                                        @if (dept.DepartmentType != null)
                                                                        {
                                                                            <MudChip T="string" Color="Color.Info" Size="Size.Small" Class="ml-2">
                                                                                @dept.DepartmentType.Name
                                                                            </MudChip>
                                                                        }
                                                                    </div>
                                                                </MudSelectItem>
                                                            }
                                                        }
                                                    </MudSelect>
                                                    @if (selectedEquipment != null && !maintenanceDepartments.Any())
                                                    {
                                                        <MudText Typo="Typo.caption" Color="Color.Warning" Class="mt-1">
                                                            <span>没有维修部门可以维修@selectedEquipment.DepartmentName的设备</span>
                                                        </MudText>
                                                    }
                                                </MudItem>
                                                <MudItem xs="12" md="6">
                                                    <MudSelect T="int?" @bind-Value="selectedMaintenancePersonnelId"
                                                        Label="指定维修人员" Clearable="true"
                                                        Disabled="@(repairOrder.MaintenanceDepartmentId == 0 || !availablePersonnel.Any())">
                                                        @foreach (var personnel in availablePersonnel)
                                                        {
                                                            <MudSelectItem T="int?" Value="@(personnel.Id)">
                                                                <div class="d-flex align-center">
                                                                    <div class="flex-grow-1">
                                                                        <MudText Typo="Typo.body2">@personnel.DisplayName</MudText>
                                                                        <MudStack Row Spacing="1" Class="mt-1">
                                                                            <MudChip T="string" Color="Color.Primary" Size="Size.Small">
                                                                                维修工种
                                                                            </MudChip>
                                                                            <MudChip T="string" Color="Color.Success" Size="Size.Small">
                                                                                可接单
                                                                            </MudChip>
                                                                        </MudStack>
                                                                        <MudText Typo="Typo.caption" Color="Color.Secondary">
                                                                            部门: @personnel.Department?.Name
                                                                        </MudText>
                                                                    </div>
                                                                </div>
                                                            </MudSelectItem>
                                                        }
                                                    </MudSelect>
                                                    @if (repairOrder.MaintenanceDepartmentId > 0 && !availablePersonnel.Any())
                                                    {
                                                        <MudText Typo="Typo.caption" Color="Color.Warning" Class="mt-1">
                                                            该部门暂无可用维修人员，系统将自动分配
                                                        </MudText>
                                                    }
                                                </MudItem>
                                                <MudItem xs="12">
                                                    <MudTextField @bind-Value="repairOrder.FaultDescription"
                                                        For="@(() => repairOrder.FaultDescription)" Label="故障描述"
                                                        Required="true" Lines="5"
                                                        Placeholder="请详细描述设备故障现象、发生时间、可能原因等..."
                                                        HelperText="详细的故障描述有助于维修人员快速定位问题" Immediate="true" />
                                                </MudItem>
                                                <MudItem xs="12">
                                                    <MudTextField @bind-Value="repairOrder.Remark"
                                                        For="@(() => repairOrder.Remark)" Label="补充说明" Lines="2"
                                                        Placeholder="其他需要说明的情况..." Immediate="true" />
                                                </MudItem>
                                            </MudGrid>
                                        </MudCardContent>
                                    </MudCard>
                                </MudItem>

                                <!-- 零件更换记录 - 创建维修单时不显示，由维修人员在维修过程中录入 -->
                                @* <MudItem xs="12">
                                    <PartReplacementRecordInput PartRecords="partReplacementRecords"
                                                                PartRecordsChanged="OnPartRecordsChanged"
                                                                ReadOnly="true"
                                                                RepairOrderId="0"
                                                                CurrentUserId="@currentUserId" />
                                </MudItem> *@

                                <!-- 操作按钮 -->
                                <MudItem xs="12">
                                    <MudStack Row Justify="Justify.Center" Spacing="4" Class="mt-6">
                                        <MudButton Variant="Variant.Outlined" StartIcon="@Icons.Material.Filled.Cancel"
                                            OnClick="Cancel">
                                            取消
                                        </MudButton>
                                        <MudButton Variant="Variant.Filled" Color="Color.Primary"
                                            StartIcon="@Icons.Material.Filled.Send" OnClick="SubmitRepairOrder"
                                            Disabled="@(submitting || !hasReportPermission)">
                                            @if (submitting)
                                            {
                                                <MudProgressCircular Class="ms-n1" Size="Size.Small" Indeterminate="true" />
                                                <MudText Class="ms-2">提交中...</MudText>
                                            }
                                            else
                                            {
                                                <MudText>提交报修</MudText>
                                            }
                                        </MudButton>
                                    </MudStack>
                                </MudItem>
                            </MudGrid>
                        </MudForm>
                    </MudItem>
                </MudGrid>
            </MudPaper>
        </MudContainer>
    </Authorized>
    <NotAuthorized>
        <MudContainer MaxWidth="MaxWidth.Large" Fixed="true" Class="mt-4">
            <MudPaper Class="pa-6">
                <MudStack AlignItems="AlignItems.Center" Spacing="4">
                    <MudIcon Icon="@Icons.Material.Filled.Lock" Size="Size.Large" Color="Color.Warning" />
                    <MudText Typo="Typo.h5">需要登录</MudText>
                    <MudText Typo="Typo.body1" Color="Color.Secondary">请先登录后再访问此页面</MudText>
                    <MudButton Variant="Variant.Filled" Color="Color.Primary" Href="/login"
                        StartIcon="@Icons.Material.Filled.Login">
                        前往登录
                    </MudButton>
                </MudStack>
            </MudPaper>
        </MudContainer>
    </NotAuthorized>
</AuthorizeView>

@code {
    [Parameter] public string? EquipmentCode { get; set; }

    private MudForm form = null!;
    private RepairOrder repairOrder = new();
    private PartReplacementRequestCollectionDto partReplacementRecords = new(); // 零件更换记录
    private List<Department> departments = new();
    private List<Department> reportableDepartments = new(); // 用户可以报修的部门
    private List<Department> maintenanceDepartments = new(); // 用户可以选择的维修部门
    private List<User> availablePersonnel = new(); // 可用的维修人员
    private List<EquipmentDetailDto> equipment = new();
    private List<EquipmentDetailDto> filteredEquipment = new();
    private EquipmentDetailDto? selectedEquipment = null;
    private List<EquipmentModel> allModels = new();
    private List<EquipmentModel> filteredModels = new();
    private int? selectedDepartmentId = null;
    private int? selectedModelId = null;
    private int? selectedMaintenancePersonnelId = null;
    private bool submitting = false;
    private bool isScanning = false; // 标记是否正在扫描二维码
    private int currentUserId = 0;
    private bool hasReportPermission = false;
    private bool isFromQrCode = false; // 标记是否来自二维码扫描

    protected override async Task OnInitializedAsync()
    {
        await LoadCurrentUser();
        await LoadBasicData();
        await LoadModels();
        await LoadEquipment();

        // 处理二维码扫描参数
        await HandleQrCodeParameter();

        // 如果默认选择了部门且不是来自二维码，需要触发过滤
        if (selectedDepartmentId.HasValue && !isFromQrCode)
        {
            FilterModels();
            FilterEquipment();
        }

        // 设置默认值
        repairOrder.UrgencyLevel = 3; // 默认中等紧急程度
        repairOrder.ReporterId = currentUserId;
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        await base.OnAfterRenderAsync(firstRender);
    }

    // 获取设备显示名称
    private string GetEquipmentDisplayName(int equipmentId)
    {
        if (equipmentId == 0) return string.Empty;

        // 首先尝试从过滤列表中查找
        var equipment = filteredEquipment.FirstOrDefault(e => e.Id == equipmentId);
        if (equipment != null)
        {
            return equipment.Name;
        }

        // 如果过滤列表中没有，尝试从完整设备列表中查找（用于二维码场景）
        equipment = this.equipment.FirstOrDefault(e => e.Id == equipmentId);
        if (equipment != null)
        {
            return equipment.Name;
        }

        // 如果都找不到，返回ID
        return equipmentId.ToString();
    }

    // 获取维修部门显示名称
    private string GetMaintenanceDepartmentDisplayName(int departmentId)
    {
        if (departmentId == 0) return string.Empty;

        // 从维修部门列表中查找
        var department = maintenanceDepartments.FirstOrDefault(d => d.Id == departmentId);
        if (department != null)
        {
            return department.Name;
        }

        // 如果找不到，返回ID
        return departmentId.ToString();
    }

    private async Task LoadCurrentUser()
    {
        try
        {
            var authState = await AuthStateProvider.GetAuthenticationStateAsync();
            if (authState.User.Identity?.IsAuthenticated == true)
            {
                var userIdClaim = authState.User.FindFirst("UserId") ?? authState.User.FindFirst(ClaimTypes.NameIdentifier);
                if (userIdClaim != null && int.TryParse(userIdClaim.Value, out int userId))
                {
                    currentUserId = userId;
                }
                else
                {
                    // 如果无法获取用户ID，使用默认值1（这种情况应该很少发生）
                    currentUserId = 1;
                    Snackbar.Add("无法获取用户ID，使用默认值", Severity.Warning);
                }
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"获取当前用户信息失败: {ex.Message}", Severity.Error);
            // 使用默认值以防止页面崩溃
            currentUserId = 1;
        }
    }

    private async Task LoadBasicData()
    {
        try
        {
            // 获取所有部门
            departments = await DepartmentService.GetEnabledDepartmentsAsync();

            // 获取用户可以报修设备的部门（基于角色部门分配）
            reportableDepartments = await RoleDepartmentAssignmentService.GetUserAccessibleDepartmentsAsync(currentUserId);

            // 检查用户是否有报修权限
            hasReportPermission = reportableDepartments.Any();

            if (!hasReportPermission)
            {
                Snackbar.Add("您没有设备报修权限，请联系管理员", Severity.Warning);
            }
            else
            {
                // 如果有权限且有可报修的部门，默认选择第一个部门
                if (reportableDepartments.Any())
                {
                    selectedDepartmentId = reportableDepartments.First().Id;
                }
            }

            // 不在初始化时加载维修部门，等用户选择设备后再根据设备部门进行过滤
            // await LoadAllMaintenanceDepartments();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载基础数据失败: {ex.Message}", Severity.Error);
        }
    }

    private async Task HandleQrCodeParameter()
    {
        if (!string.IsNullOrWhiteSpace(EquipmentCode))
        {
            try
            {
                isFromQrCode = true;
                Logger.LogInformation("[CreateRepairOrder] 处理二维码参数，设备编码: {EquipmentCode}", EquipmentCode);

                // 直接从数据库查找设备，不依赖已过滤的列表
                var allEquipmentList = await EquipmentService.GetEquipmentDetailsAsync();
                var targetEquipment = allEquipmentList.FirstOrDefault(e =>
                    e.Code.Equals(EquipmentCode, StringComparison.OrdinalIgnoreCase));

                if (targetEquipment != null)
                {
                    Logger.LogInformation("[CreateRepairOrder] 找到设备: {EquipmentName} (ID: {EquipmentId})",
                        targetEquipment.Name, targetEquipment.Id);

                    // 检查用户是否有权限报修该设备
                    var canReport = reportableDepartments.Any(d => d.Id == targetEquipment.DepartmentId);
                    if (!canReport)
                    {
                        Snackbar.Add($"您没有权限报修设备 {targetEquipment.Name}，请联系管理员", Severity.Warning);
                        return;
                    }

                    // 自动选择设备所属的部门和机型
                    selectedDepartmentId = targetEquipment.DepartmentId;
                    selectedModelId = targetEquipment.ModelId;

                    // 先执行过滤逻辑，确保设备在过滤列表中
                    FilterModels();
                    FilterEquipment();

                    // 直接设置设备选择
                    repairOrder.EquipmentId = targetEquipment.Id;
                    selectedEquipment = targetEquipment;

                    // 触发设备选择事件，加载维修部门
                    await OnEquipmentChanged(targetEquipment.Id);

                    Snackbar.Add($"已自动选择设备: {targetEquipment.Name}", Severity.Success);
                }
                else
                {
                    Logger.LogWarning("[CreateRepairOrder] 未找到设备编码: {EquipmentCode}", EquipmentCode);
                    Snackbar.Add($"未找到设备编码: {EquipmentCode}", Severity.Warning);
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "[CreateRepairOrder] 处理二维码参数失败: {EquipmentCode}", EquipmentCode);
                Snackbar.Add($"处理二维码参数失败: {ex.Message}", Severity.Error);
            }
        }
    }

    private async Task LoadModels()
    {
        try
        {
            allModels = await EquipmentModelService.GetAllEquipmentModelsAsync();
            FilterModels();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载设备型号数据失败: {ex.Message}", Severity.Error);
        }
    }

    private async Task LoadEquipment()
    {
        try
        {
            equipment = await EquipmentService.GetEquipmentDetailsAsync();
            FilterEquipment();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载设备数据失败: {ex.Message}", Severity.Error);
        }
    }

    private void FilterModels()
    {
        var filtered = allModels.AsEnumerable();

        // 根据选择的部门过滤机型 - 只显示该部门下有设备的机型
        if (selectedDepartmentId.HasValue)
        {
            var departmentEquipmentModelIds = equipment
                .Where(e => e.DepartmentId == selectedDepartmentId.Value)
                .Select(e => e.ModelId)
                .Distinct()
                .ToList();

            filtered = filtered.Where(m => departmentEquipmentModelIds.Contains(m.Id));
        }
        else
        {
            // 如果没有选择部门，不显示任何机型
            filtered = Enumerable.Empty<EquipmentModel>();
        }

        // 只显示启用的机型
        filtered = filtered.Where(m => m.IsEnabled);

        filteredModels = filtered.OrderBy(m => m.Category).ThenBy(m => m.Name).ToList();
        StateHasChanged();
    }

    private void FilterEquipment()
    {
        var filtered = equipment.AsEnumerable();

        // 首先按用户权限过滤 - 只显示用户有权限报修的部门设备
        var reportableDepartmentIds = reportableDepartments.Select(d => d.Id).ToList();
        if (reportableDepartmentIds.Any())
        {
            filtered = filtered.Where(e => reportableDepartmentIds.Contains(e.DepartmentId));
        }
        else
        {
            // 如果用户没有任何报修权限，则不显示任何设备
            filtered = Enumerable.Empty<EquipmentDetailDto>();
        }

        // 按选择的部门过滤
        if (selectedDepartmentId.HasValue)
        {
            filtered = filtered.Where(e => e.DepartmentId == selectedDepartmentId.Value);
        }

        // 按选择的机型过滤
        if (selectedModelId.HasValue)
        {
            filtered = filtered.Where(e => e.ModelId == selectedModelId.Value);
        }

        // 只显示启用的设备
        filtered = filtered.Where(e => e.IsEnabled);

        filteredEquipment = filtered.OrderBy(e => e.DepartmentName).ThenBy(e => e.Name).ToList();
        StateHasChanged();
    }

    private void OnDepartmentChanged(int? departmentId)
    {
        selectedDepartmentId = departmentId;
        selectedModelId = null; // 重置机型选择
        repairOrder.EquipmentId = 0; // 重置设备选择
        selectedEquipment = null;
        FilterModels(); // 先过滤机型
        FilterEquipment(); // 再过滤设备
    }

    private void OnModelChanged(int? modelId)
    {
        selectedModelId = modelId;
        repairOrder.EquipmentId = 0; // 重置设备选择
        selectedEquipment = null;
        FilterEquipment(); // 过滤设备
    }



    private async Task OnEquipmentChanged(int equipmentId)
    {
        repairOrder.EquipmentId = equipmentId;
        selectedEquipment = filteredEquipment.FirstOrDefault(e => e.Id == equipmentId);

        // 当设备改变时，根据设备所属部门过滤可用的维修部门
        if (selectedEquipment != null)
        {
            // 重置维修人员选择
            selectedMaintenancePersonnelId = null;
            availablePersonnel.Clear();

            await FilterMaintenanceDepartmentsByEquipment();
        }
        else
        {
            // 如果没有选择设备，显示所有维修部门
            await LoadAllMaintenanceDepartments();
        }
    }

    private async Task OnMaintenanceDepartmentChanged(int departmentId)
    {
        repairOrder.MaintenanceDepartmentId = departmentId;
        selectedMaintenancePersonnelId = null;
        await LoadMaintenancePersonnel();
    }

    // 根据设备所属部门过滤可用的维修部门
    private async Task FilterMaintenanceDepartmentsByEquipment()
    {
        try
        {
            if (selectedEquipment != null)
            {
                // 根据设备所属部门获取可以维修该部门设备的维修部门
                var availableMaintenanceDepts = await MaintenanceDepartmentPermissionService.GetAvailableMaintenanceDepartmentsAsync(selectedEquipment.DepartmentId);

                // 如果没有配置权限关系，则显示所有维修部门
                if (!availableMaintenanceDepts.Any())
                {
                    availableMaintenanceDepts = await DepartmentTypeService.GetMaintenanceDepartmentsAsync();
                    Snackbar.Add($"未配置维修权限关系，显示所有维修部门。建议在维修部门管理中配置权限关系。", Severity.Warning);
                }
                else
                {
                    Snackbar.Add($"已根据设备所属部门筛选出{availableMaintenanceDepts.Count}个可用维修部门", Severity.Info);
                }

                // 加载部门类型信息
                foreach (var dept in availableMaintenanceDepts)
                {
                    if (dept.DepartmentTypeId.HasValue)
                    {
                        dept.DepartmentType = await DepartmentTypeService.GetDepartmentTypeByIdAsync(dept.DepartmentTypeId.Value);
                    }
                }

                maintenanceDepartments = availableMaintenanceDepts;

                // 如果有可用的维修部门，默认选择第一个
                if (maintenanceDepartments.Any())
                {
                    repairOrder.MaintenanceDepartmentId = maintenanceDepartments.First().Id;
                    await LoadMaintenancePersonnel(); // 加载对应的维修人员
                }
            }
            StateHasChanged();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"筛选维修部门失败: {ex.Message}", Severity.Error);
        }
    }

    // 加载所有可用的维修部门
    private async Task LoadAllMaintenanceDepartments()
    {
        try
        {
            // 获取所有维修类型的部门作为可选择的维修部门
            // 注意：这里不应该基于当前用户的权限过滤，而应该显示所有维修部门
            // 维修部门的选择应该基于设备所属部门和维修部门的维修权限关系
            maintenanceDepartments = await DepartmentTypeService.GetMaintenanceDepartmentsAsync();

            // 加载部门类型信息
            foreach (var dept in maintenanceDepartments)
            {
                if (dept.DepartmentTypeId.HasValue)
                {
                    dept.DepartmentType = await DepartmentTypeService.GetDepartmentTypeByIdAsync(dept.DepartmentTypeId.Value);
                }
            }

            // 如果有可用的维修部门且当前没有选择维修部门，默认选择第一个
            if (maintenanceDepartments.Any() && repairOrder.MaintenanceDepartmentId == 0)
            {
                repairOrder.MaintenanceDepartmentId = maintenanceDepartments.First().Id;
                await LoadMaintenancePersonnel(); // 加载对应的维修人员
            }

            StateHasChanged();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载维修部门失败: {ex.Message}", Severity.Error);
        }
    }

    // 加载维修人员
    private async Task LoadMaintenancePersonnel()
    {
        try
        {
            Logger.LogInformation("[CreateRepairOrder] 开始加载维修人员，维修部门ID: {MaintenanceDepartmentId}", repairOrder.MaintenanceDepartmentId);

            if (repairOrder.MaintenanceDepartmentId > 0)
            {
                // 直接从选择的维修部门下面具有维修工种的用户中筛选
                availablePersonnel = await JobTypeService.GetMaintenanceUsersByDepartmentAsync(repairOrder.MaintenanceDepartmentId);

                Logger.LogInformation("[CreateRepairOrder] 获取到 {Count} 个维修人员", availablePersonnel.Count);
            }
            else
            {
                Logger.LogInformation("[CreateRepairOrder] 维修部门ID为0，清空维修人员列表");
                availablePersonnel.Clear();
            }
            StateHasChanged();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "[CreateRepairOrder] 加载维修人员失败");
            Snackbar.Add($"加载维修人员失败: {ex.Message}", Severity.Error);
        }
    }

    // 获取设备状态颜色
    private Color GetEquipmentStatusColor(int status)
    {
        return status switch
        {
            1 => Color.Success, // 正常
            2 => Color.Warning, // 维修中
            3 => Color.Default, // 停用
            4 => Color.Error, // 报废
            _ => Color.Default
        };
    }

    // 获取维修部门帮助文本
    private string GetMaintenanceDepartmentHelperText()
    {
        if (selectedEquipment != null)
        {
            return $"已根据设备所属部门({selectedEquipment.DepartmentName})自动筛选可用维修部门";
        }
        return "请先选择设备，系统将自动筛选可用维修部门";
    }

    /// <summary>
    /// 零件记录变更回调
    /// </summary>
    private void OnPartRecordsChanged(PartReplacementRequestCollectionDto updatedRecords)
    {
        partReplacementRecords = updatedRecords;
        StateHasChanged();
    }

    private void Cancel()
    {
        Navigation.NavigateTo("/repair-order-management");
    }

    private async Task SubmitRepairOrder()
    {
        // 验证用户权限
        if (!hasReportPermission)
        {
            Snackbar.Add("您没有设备报修权限", Severity.Error);
            return;
        }

        // 验证选择的设备是否在用户有权限的部门内
        if (selectedEquipment != null)
        {
            var canReport = await RoleDepartmentAssignmentService.CanUserAccessDepartmentAsync(currentUserId, selectedEquipment.DepartmentId);
            if (!canReport)
            {
                Snackbar.Add("您没有权限报修该部门的设备", Severity.Error);
                return;
            }
        }

        // 验证选择的维修部门是否有权限维修该设备所属部门
        if (selectedEquipment != null && repairOrder.MaintenanceDepartmentId > 0)
        {
            var canMaintain = await MaintenanceDepartmentPermissionService.CanMaintenanceDepartmentRepairAsync(repairOrder.MaintenanceDepartmentId, selectedEquipment.DepartmentId);
            if (!canMaintain)
            {
                Snackbar.Add("选择的维修部门没有权限维修该设备所属部门的设备", Severity.Error);
                return;
            }
        }

        await form.Validate();
        if (!form.IsValid) return;

        submitting = true;
        try
        {
            // 如果用户指定了维修人员，设置到报修单
            if (selectedMaintenancePersonnelId.HasValue)
            {
                repairOrder.AssignedTo = selectedMaintenancePersonnelId.Value;
            }
            else if (selectedEquipment != null && availablePersonnel.Any())
            {
                // 简单分配：选择第一个可用的维修人员
                var firstAvailablePersonnel = availablePersonnel.First();
                repairOrder.AssignedTo = firstAvailablePersonnel.Id;
                Snackbar.Add($"已自动分配维修人员：{firstAvailablePersonnel.DisplayName}", Severity.Info);
            }

            // 设置零件申请记录的申请人
            if (partReplacementRecords.HasAnyParts)
            {
                foreach (var partRequest in partReplacementRecords.PartRequests)
                {
                    partRequest.RequestedBy = currentUserId;
                    partRequest.RepairOrderId = 0; // 创建时为0，保存后会更新
                }
                Snackbar.Add($"已添加 {partReplacementRecords.TotalCount} 项零件更换记录", Severity.Info);
            }

            // 调用新的方法，同时保存维修单和零件申请
            var result = await RepairOrderService.CreateRepairOrderWithPartRequestsAsync(repairOrder, partReplacementRecords.PartRequests);
            if (result.IsSuccess)
            {
                Snackbar.Add($"报修单提交成功！报修单号：{result.OrderNumber}", Severity.Success);
                Navigation.NavigateTo("/repair-order-management");
            }
            else
            {
                Snackbar.Add($"提交失败: {result.ErrorMessage}", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"提交失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            submitting = false;
        }
    }

    public class RepairOrderValidator : AbstractValidator<RepairOrder>
    {
        public RepairOrderValidator()
        {
            RuleFor(x => x.EquipmentId)
            .GreaterThan(0).WithMessage("请选择设备");

            RuleFor(x => x.FaultDescription)
            .NotEmpty().WithMessage("请描述故障现象")
            .MinimumLength(10).WithMessage("故障描述至少需要10个字符")
            .MaximumLength(1000).WithMessage("故障描述不能超过1000个字符");

            RuleFor(x => x.UrgencyLevel)
            .InclusiveBetween(1, 4).WithMessage("请选择紧急程度");

            RuleFor(x => x.MaintenanceDepartmentId)
            .GreaterThan(0).WithMessage("请选择维修部门");

            RuleFor(x => x.Remark)
            .MaximumLength(1000).WithMessage("补充说明不能超过1000个字符");
        }

        public Func<object, string, Task<IEnumerable<string>>> ValidateValue => async (model, propertyName) =>
        {
            var result = await ValidateAsync(ValidationContext<RepairOrder>.CreateWithOptions((RepairOrder)model, x =>
    x.IncludeProperties(propertyName)));
            if (result.IsValid)
                return Array.Empty<string>();
            return result.Errors.Select(e => e.ErrorMessage);
        };
    }

    /// <summary>
    /// 扫描二维码选择设备
    /// </summary>
    private async Task ScanQrCodeAsync()
    {
        try
        {
            isScanning = true;
            StateHasChanged();

            Logger.LogInformation("[CreateRepairOrder] 开始扫描二维码...");

            // 调用二维码扫描服务
            var scannedCode = await QrCodeScannerService.ScanQrCodeAsync();

            // 首先检查特殊返回值（错误状态）
            if (scannedCode == "PERMISSION_DENIED")
            {
                Logger.LogWarning("[CreateRepairOrder] 摄像头权限被拒绝");
                Snackbar.Add("需要摄像头权限才能扫描二维码，请在设置中允许应用访问摄像头", Severity.Warning);
                return;
            }

            if (scannedCode == "CAMERA_UNAVAILABLE")
            {
                Logger.LogWarning("[CreateRepairOrder] 摄像头不可用");
                Snackbar.Add("摄像头不可用，请检查设备是否支持摄像头功能或摄像头是否被其他应用占用", Severity.Error);
                return;
            }

            // 检查是否成功扫描到内容
            if (!string.IsNullOrWhiteSpace(scannedCode))
            {
                Logger.LogInformation("[CreateRepairOrder] 扫描到二维码: {ScannedCode}", scannedCode);

                // 尝试从扫描结果中提取设备编码
                var equipmentCode = ExtractEquipmentCodeFromQrCode(scannedCode);

                if (!string.IsNullOrWhiteSpace(equipmentCode))
                {
                    Logger.LogInformation("[CreateRepairOrder] 提取到设备编码: {EquipmentCode}", equipmentCode);

                    // 查找设备
                    await SelectEquipmentByCode(equipmentCode);
                }
                else
                {
                    Logger.LogWarning("[CreateRepairOrder] 无法从二维码中提取设备编码: {ScannedCode}", scannedCode);
                    Snackbar.Add("二维码格式不正确，无法识别设备编码", Severity.Warning);
                }
            }
            else
            {
                // 用户取消扫描或扫描失败
                Logger.LogInformation("[CreateRepairOrder] 用户取消了二维码扫描");
                Snackbar.Add("已取消扫描", Severity.Info);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "[CreateRepairOrder] 扫描二维码失败");
            Snackbar.Add($"扫描二维码失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            isScanning = false;
            StateHasChanged();
        }
    }

    /// <summary>
    /// 从二维码内容中提取设备编码
    /// </summary>
    private string? ExtractEquipmentCodeFromQrCode(string qrCodeContent)
    {
        try
        {
            // 如果二维码内容是URL格式，尝试提取设备编码
            if (qrCodeContent.Contains("/create-repair-order/"))
            {
                var parts = qrCodeContent.Split('/');
                var equipmentCode = parts.LastOrDefault();
                Logger.LogInformation("[CreateRepairOrder] 从URL中提取设备编码: {EquipmentCode}", equipmentCode);
                return equipmentCode;
            }

            // 如果二维码内容直接是设备编码格式（例如：FN-DX-001）
            if (qrCodeContent.Contains("-") && qrCodeContent.Length <= 20)
            {
                Logger.LogInformation("[CreateRepairOrder] 直接使用二维码内容作为设备编码: {EquipmentCode}", qrCodeContent);
                return qrCodeContent.Trim();
            }

            // 尝试JSON格式解析
            if (qrCodeContent.StartsWith("{") && qrCodeContent.EndsWith("}"))
            {
                // 这里可以添加JSON解析逻辑，如果二维码包含JSON格式的设备信息
                Logger.LogInformation("[CreateRepairOrder] 检测到JSON格式二维码，暂不支持解析");
            }

            Logger.LogWarning("[CreateRepairOrder] 无法识别的二维码格式: {QrCodeContent}", qrCodeContent);
            return null;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "[CreateRepairOrder] 解析二维码内容失败: {QrCodeContent}", qrCodeContent);
            return null;
        }
    }

    /// <summary>
    /// 根据设备编码选择设备
    /// </summary>
    private async Task SelectEquipmentByCode(string equipmentCode)
    {
        try
        {
            Logger.LogInformation("[CreateRepairOrder] 根据设备编码选择设备: {EquipmentCode}", equipmentCode);

            // 直接从数据库查找设备，不依赖已过滤的列表
            var allEquipmentList = await EquipmentService.GetEquipmentDetailsAsync();
            var targetEquipment = allEquipmentList.FirstOrDefault(e =>
                e.Code.Equals(equipmentCode, StringComparison.OrdinalIgnoreCase));

            if (targetEquipment != null)
            {
                Logger.LogInformation("[CreateRepairOrder] 找到设备: {EquipmentName} (ID: {EquipmentId})",
                    targetEquipment.Name, targetEquipment.Id);

                // 检查用户是否有权限报修该设备
                var canReport = reportableDepartments.Any(d => d.Id == targetEquipment.DepartmentId);
                if (!canReport)
                {
                    Snackbar.Add($"您没有权限报修设备 {targetEquipment.Name}，请联系管理员", Severity.Warning);
                    return;
                }

                // 标记为来自二维码扫描
                isFromQrCode = true;

                // 自动选择设备所属的部门和机型
                selectedDepartmentId = targetEquipment.DepartmentId;
                selectedModelId = targetEquipment.ModelId;

                // 先执行过滤逻辑，确保设备在过滤列表中
                FilterModels();
                FilterEquipment();

                // 直接设置设备选择
                repairOrder.EquipmentId = targetEquipment.Id;
                selectedEquipment = targetEquipment;

                // 触发设备选择事件，加载维修部门
                await OnEquipmentChanged(targetEquipment.Id);

                Snackbar.Add($"已通过扫码自动选择设备: {targetEquipment.Name}", Severity.Success);
                StateHasChanged();
            }
            else
            {
                Logger.LogWarning("[CreateRepairOrder] 未找到设备编码: {EquipmentCode}", equipmentCode);
                Snackbar.Add($"未找到设备编码: {equipmentCode}", Severity.Warning);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "[CreateRepairOrder] 根据设备编码选择设备失败: {EquipmentCode}", equipmentCode);
            Snackbar.Add($"选择设备失败: {ex.Message}", Severity.Error);
        }
    }
}
