@page "/DeviceScanner"
@inject IQrCodeScannerService QrCodeScanner
@inject NavigationManager Navigation
@using Microsoft.AspNetCore.Components.Authorization
@using CoreHub.Shared.Components

<PageTitle>设备扫描</PageTitle>

<PermissionView RequiredPermission="DeviceScanner.View">
    <ChildContent>
        <MudContainer MaxWidth="MaxWidth.Large">
            <MudText Typo="Typo.h3" Class="mb-4">设备二维码扫描</MudText>

            <!-- 快速操作按钮 -->
            <MudPaper Class="pa-4 mb-4">
                <MudText Typo="Typo.h5" GutterBottom="true">快速操作</MudText>
                <div class="d-flex gap-2 mb-3">
                    <MudButton Variant="Variant.Filled" 
                              Color="Color.Primary" 
                              OnClick="QuickScan"
                              StartIcon="@Icons.Material.Filled.QrCodeScanner">
                        快速扫描
                    </MudButton>
                    
                    <MudButton Variant="Variant.Outlined" 
                              OnClick="TestScan"
                              StartIcon="@Icons.Material.Filled.Science">
                        测试扫描
                    </MudButton>
                </div>

                @if (quickScanSuccess)
                {
                    <MudAlert Severity="Severity.Success" Class="mb-3">
                        <div class="d-flex align-center">
                            <MudIcon Icon="@Icons.Material.Filled.CheckCircle" Class="mr-2" />
                            <span>快速扫描模式已启用，可以连续扫描多个设备</span>
                        </div>
                    </MudAlert>
                }
            </MudPaper>

            <!-- 扫描功能选择 -->
            <MudPaper Class="pa-4 mb-4">
                <MudText Typo="Typo.h5" GutterBottom="true">选择扫描方式</MudText>
                
                <MudGrid>
                    <MudItem xs="12" md="6">
                        <MudPaper Class="pa-3 text-center" Style="height: 200px; display: flex; flex-direction: column; justify-content: center;">
                            <MudIcon Icon="@Icons.Material.Filled.CameraAlt" 
                                    Size="Size.Large" 
                                    Color="Color.Primary" 
                                    Class="mb-2" />
                            <MudText Typo="Typo.h6" Class="mb-2">摄像头扫描</MudText>
                            <MudText Typo="Typo.body2" Class="mb-3">使用设备摄像头扫描二维码</MudText>
                            
                            <div class="d-flex gap-1 justify-center">
                                <MudButton Variant="Variant.Filled" 
                                          Color="Color.Primary" 
                                          Size="Size.Small"
                                          OnClick="StartCameraScan">
                                    开始扫描
                                </MudButton>
                                <MudButton Variant="Variant.Outlined" 
                                          Size="Size.Small"
                                          OnClick="NavigateToCameraTest">
                                    测试模式
                                </MudButton>
                                <MudButton Variant="Variant.Text" 
                                          Size="Size.Small"
                                          OnClick="ShowCameraHelp">
                                    帮助
                                </MudButton>
                            </div>
                        </MudPaper>
                    </MudItem>
                    
                    <MudItem xs="12" md="6">
                        <MudPaper Class="pa-3 text-center" Style="height: 200px; display: flex; flex-direction: column; justify-content: center;">
                            <MudIcon Icon="@Icons.Material.Filled.Keyboard" 
                                    Size="Size.Large" 
                                    Color="Color.Secondary" 
                                    Class="mb-2" />
                            <MudText Typo="Typo.h6" Class="mb-2">手动输入</MudText>
                            <MudText Typo="Typo.body2" Class="mb-3">手动输入设备编号</MudText>
                            
                            <div class="d-flex gap-1 justify-center">
                                <MudTextField @bind-Value="manualDeviceCode"
                                             Label="设备编号"
                                             Variant="Variant.Outlined"
                                             Margin="Margin.Dense"
                                             Style="max-width: 150px;" />
                                <MudButton Variant="Variant.Filled" 
                                          Color="Color.Primary" 
                                          Size="Size.Small"
                                          OnClick="ProcessManualCode">
                                    确认
                                </MudButton>
                            </div>
                        </MudPaper>
                    </MudItem>
                </MudGrid>
            </MudPaper>

            <!-- 扫描结果显示 -->
            @if (!string.IsNullOrEmpty(scanResult))
            {
                <MudPaper Class="pa-4 mb-4">
                    <MudText Typo="Typo.h5" GutterBottom="true">扫描结果</MudText>
                    
                    @if (isScannedSuccessfully)
                    {
                        <MudAlert Severity="Severity.Success" Class="mb-3">
                            <div class="d-flex align-center">
                                <MudIcon Icon="@Icons.Material.Filled.CheckCircle" Class="mr-2" />
                                <span>扫描成功！设备编号: @scanResult</span>
                            </div>
                        </MudAlert>
                        
                        <div class="d-flex gap-2">
                            <PermissionView RequiredPermission="Device.Repair">
                                <ChildContent>
                                    <MudButton Variant="Variant.Filled" 
                                              Color="Color.Primary" 
                                              OnClick="ProcessScannedDevice"
                                              StartIcon="@Icons.Material.Filled.Build">
                                        报修此设备
                                    </MudButton>
                                </ChildContent>
                            </PermissionView>
                            <MudButton Variant="Variant.Outlined" 
                                      OnClick="ViewDeviceInfo"
                                      StartIcon="@Icons.Material.Filled.Info">
                                查看设备信息
                            </MudButton>
                            <MudButton Variant="Variant.Text" 
                                      OnClick="ClearResult"
                                      StartIcon="@Icons.Material.Filled.Clear">
                                清除结果
                            </MudButton>
                        </div>
                    }
                    else
                    {
                        <MudAlert Severity="Severity.Error">
                            扫描失败或取消: @scanResult
                        </MudAlert>
                    }
                </MudPaper>
            }

            <!-- 操作记录 -->
            @if (scanHistory.Any())
            {
                <MudPaper Class="pa-4 mb-4">
                    <MudText Typo="Typo.h5" GutterBottom="true">扫描记录</MudText>
                    
                    <MudList T="string">
                        @foreach (var record in scanHistory.TakeLast(5))
                        {
                            <MudListItem T="string" Icon="@Icons.Material.Filled.History">
                                <MudText>@record</MudText>
                            </MudListItem>
                        }
                    </MudList>
                    
                    <MudButton Variant="Variant.Text" 
                              Size="Size.Small" 
                              OnClick="ClearHistory"
                              StartIcon="@Icons.Material.Filled.ClearAll">
                        清空记录
                    </MudButton>
                </MudPaper>
            }

            <!-- 状态消息 -->
            @if (!string.IsNullOrEmpty(statusMessage))
            {
                <MudAlert Severity="@(isSuccess ? Severity.Success : Severity.Error)" 
                         CloseIcon="true"
                         CloseIconClicked="ClearStatus">
                    @statusMessage
                </MudAlert>
            }
        </MudContainer>
    </ChildContent>
    
    <NotAuthorized>
        <MudPaper Class="pa-6 text-center" Style="min-height: 400px; display: flex; flex-direction: column; justify-content: center;">
            <MudIcon Icon="@Icons.Material.Filled.Lock" 
                    Size="Size.Large" 
                    Color="Color.Warning" 
                    Class="mb-3" />
            <MudText Typo="Typo.h4" Class="mb-2">权限不足</MudText>
            <MudText Typo="Typo.body1" Class="mb-4">您没有设备扫描权限，请联系管理员</MudText>
            <MudButton Variant="Variant.Filled" 
                      Color="Color.Primary" 
                      OnClick="GoToLogin"
                      StartIcon="@Icons.Material.Filled.Login">
                重新登录
            </MudButton>
        </MudPaper>
    </NotAuthorized>
</PermissionView>

@code {
    private string scanResult = "";
    private string manualDeviceCode = "";
    private string statusMessage = "";
    private bool isSuccess = false;
    private bool isScannedSuccessfully = false;
    private bool quickScanSuccess = false;
    private List<string> scanHistory = new();

    private async Task QuickScan()
    {
        try
        {
            SetStatus("启动快速扫描模式...", true);
            await Task.Delay(500); // 模拟启动时间
            quickScanSuccess = true;
            SetStatus("快速扫描模式已启用", true);
        }
        catch (Exception ex)
        {
            SetStatus($"启动快速扫描失败: {ex.Message}", false);
        }
    }

    private async Task TestScan()
    {
        try
        {
            SetStatus("执行测试扫描...", true);
            await Task.Delay(1000); // 模拟扫描过程
            
            // 模拟扫描结果
            var testCode = $"TEST{DateTime.Now:HHmmss}";
            scanResult = testCode;
            isScannedSuccessfully = true;
            AddToHistory($"测试扫描: {testCode}");
            SetStatus("测试扫描完成", true);
        }
        catch (Exception ex)
        {
            SetStatus($"测试扫描失败: {ex.Message}", false);
        }
    }

    private async Task StartCameraScan()
    {
        try
        {
            SetStatus("正在启动摄像头...", true);
            
            var result = await QrCodeScanner.ScanQrCodeAsync();
            
            if (!string.IsNullOrEmpty(result))
            {
                scanResult = result;
                isScannedSuccessfully = true;
                AddToHistory($"摄像头扫描: {result}");
                SetStatus("扫描成功", true);
            }
            else
            {
                scanResult = "扫描被取消或未检测到二维码";
                isScannedSuccessfully = false;
                SetStatus("扫描未完成", false);
            }
        }
        catch (Exception ex)
        {
            scanResult = $"扫描异常: {ex.Message}";
            isScannedSuccessfully = false;
            SetStatus($"扫描失败: {ex.Message}", false);
        }
    }

    private void ProcessManualCode()
    {
        if (string.IsNullOrWhiteSpace(manualDeviceCode))
        {
            SetStatus("请输入设备编号", false);
            return;
        }

        scanResult = manualDeviceCode.Trim();
        isScannedSuccessfully = true;
        AddToHistory($"手动输入: {scanResult}");
        SetStatus("设备编号已确认", true);
        manualDeviceCode = "";
    }

    private void ProcessScannedDevice()
    {
        if (!string.IsNullOrEmpty(scanResult))
        {
            Navigation.NavigateTo($"/devicerepair/{scanResult}");
        }
    }

    private void ViewDeviceInfo()
    {
        if (!string.IsNullOrEmpty(scanResult))
        {
            Navigation.NavigateTo($"/test-repair/{scanResult}");
        }
    }

    private void NavigateToCameraTest()
    {
        Navigation.NavigateTo("/camerascan");
    }

    private void ShowCameraHelp()
    {
        SetStatus("提示：确保设备有摄像头权限，并保持二维码清晰可见", true);
    }

    private void ClearResult()
    {
        scanResult = "";
        isScannedSuccessfully = false;
        quickScanSuccess = false;
        ClearStatus();
    }

    private void ClearHistory()
    {
        scanHistory.Clear();
        SetStatus("扫描记录已清空", true);
    }

    private void AddToHistory(string record)
    {
        scanHistory.Add($"{DateTime.Now:HH:mm:ss} - {record}");
        StateHasChanged();
    }

    private void SetStatus(string message, bool success)
    {
        statusMessage = message;
        isSuccess = success;
        StateHasChanged();

        // 3秒后自动清除状态消息
        _ = Task.Delay(3000).ContinueWith(_ => InvokeAsync(ClearStatus));
    }

    private void ClearStatus()
    {
        statusMessage = "";
        StateHasChanged();
    }

    private void GoToLogin()
    {
        Navigation.NavigateTo("/login");
    }
} 