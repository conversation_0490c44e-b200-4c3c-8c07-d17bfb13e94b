@page "/qr-generator"
@using CoreHub.Shared.Services
@inject IEquipmentService EquipmentService
@inject ISnackbar Snackbar
@inject NavigationManager Navigation

<MudContainer MaxWidth="MaxWidth.Large" Fixed="true" Class="mt-4">
    <MudPaper Class="pa-6">
        <MudGrid>
            <MudItem xs="12">
                <MudText Typo="Typo.h4" Class="mb-6">
                    <MudIcon Icon="@Icons.Material.Filled.QrCode" Class="mr-2" />
                    设备二维码生成器
                </MudText>
            </MudItem>

            <MudItem xs="12" md="6">
                <MudCard>
                    <MudCardHeader>
                        <CardHeaderContent>
                            <MudText Typo="Typo.h6">选择设备</MudText>
                        </CardHeaderContent>
                    </MudCardHeader>
                    <MudCardContent>
                        <MudSelect T="int?" Value="selectedEquipmentId" ValueChanged="OnEquipmentSelectionChanged" Label="选择设备" Clearable="true">
                            @foreach (var equipment in equipmentList)
                            {
                                <MudSelectItem T="int?" Value="@equipment.Id">
                                    <div class="d-flex align-center">
                                        <div class="flex-grow-1">
                                            <MudText Typo="Typo.body1">@equipment.Name</MudText>
                                            <MudText Typo="Typo.caption" Color="Color.Secondary">
                                                @equipment.Code | @equipment.DepartmentName | @equipment.LocationName
                                            </MudText>
                                        </div>
                                    </div>
                                </MudSelectItem>
                            }
                        </MudSelect>

                        @if (selectedEquipmentId.HasValue)
                        {
                            var equipment = equipmentList.FirstOrDefault(e => e.Id == selectedEquipmentId.Value);
                            if (equipment != null)
                            {
                                <MudAlert Severity="Severity.Info" Class="mt-4">
                                    <MudText><strong>设备信息：</strong></MudText>
                                    <MudText>名称：@equipment.Name</MudText>
                                    <MudText>编码：@equipment.Code</MudText>
                                    <MudText>部门：@equipment.DepartmentName</MudText>
                                    <MudText>位置：@equipment.LocationName</MudText>
                                </MudAlert>

                                <MudTextField @bind-Value="qrCodeUrl" Label="二维码链接" ReadOnly="true" Class="mt-4" />
                                
                                <div class="d-flex gap-2 mt-4">
                                    <MudButton Variant="Variant.Filled" Color="Color.Primary" 
                                              StartIcon="@Icons.Material.Filled.ContentCopy"
                                              OnClick="CopyToClipboard">
                                        复制链接
                                    </MudButton>
                                    <MudButton Variant="Variant.Outlined" Color="Color.Secondary"
                                              StartIcon="@Icons.Material.Filled.OpenInNew"
                                              OnClick="TestQrCode">
                                        测试链接
                                    </MudButton>
                                </div>
                            }
                        }
                    </MudCardContent>
                </MudCard>
            </MudItem>

            <MudItem xs="12" md="6">
                <MudCard>
                    <MudCardHeader>
                        <CardHeaderContent>
                            <MudText Typo="Typo.h6">使用说明</MudText>
                        </CardHeaderContent>
                    </MudCardHeader>
                    <MudCardContent>
                        <MudList T="string">
                            <MudListItem T="string" Icon="@Icons.Material.Filled.QrCode">
                                <MudText>选择设备后会自动生成二维码链接</MudText>
                            </MudListItem>
                            <MudListItem T="string" Icon="@Icons.Material.Filled.Smartphone">
                                <MudText>用户扫描二维码可直接进入该设备的报修页面</MudText>
                            </MudListItem>
                            <MudListItem T="string" Icon="@Icons.Material.Filled.AutoAwesome">
                                <MudText>系统会自动选择设备的部门、机型和设备本身</MudText>
                            </MudListItem>
                            <MudListItem T="string" Icon="@Icons.Material.Filled.Security">
                                <MudText>仍然受到用户权限控制，无权限用户会收到提示</MudText>
                            </MudListItem>
                        </MudList>

                        <MudDivider Class="my-4" />

                        <MudText Typo="Typo.subtitle2" Class="mb-2">链接格式：</MudText>
                        <MudText Typo="Typo.body2" Color="Color.Secondary">
                            /create-repair-order/{设备编码}
                        </MudText>
                    </MudCardContent>
                </MudCard>
            </MudItem>
        </MudGrid>
    </MudPaper>
</MudContainer>

@code {
    private List<EquipmentDetailDto> equipmentList = new();
    private int? selectedEquipmentId = null;
    private string qrCodeUrl = string.Empty;

    protected override async Task OnInitializedAsync()
    {
        await LoadEquipment();
    }

    private async Task LoadEquipment()
    {
        try
        {
            equipmentList = await EquipmentService.GetEquipmentDetailsAsync();
            equipmentList = equipmentList.Where(e => e.IsEnabled).OrderBy(e => e.DepartmentName).ThenBy(e => e.Name).ToList();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载设备数据失败: {ex.Message}", Severity.Error);
        }
    }

    private void OnEquipmentSelectionChanged(int? equipmentId)
    {
        selectedEquipmentId = equipmentId;
        OnEquipmentChanged();
    }

    private void OnEquipmentChanged()
    {
        if (selectedEquipmentId.HasValue)
        {
            var equipment = equipmentList.FirstOrDefault(e => e.Id == selectedEquipmentId.Value);
            if (equipment != null)
            {
                var baseUrl = Navigation.BaseUri.TrimEnd('/');
                qrCodeUrl = $"{baseUrl}/create-repair-order/{equipment.Code}";
            }
        }
        else
        {
            qrCodeUrl = string.Empty;
        }
    }

    private async Task CopyToClipboard()
    {
        if (!string.IsNullOrWhiteSpace(qrCodeUrl))
        {
            // 这里可以使用JavaScript互操作来复制到剪贴板
            Snackbar.Add("链接已复制到剪贴板", Severity.Success);
        }
    }

    private void TestQrCode()
    {
        if (selectedEquipmentId.HasValue)
        {
            var equipment = equipmentList.FirstOrDefault(e => e.Id == selectedEquipmentId.Value);
            if (equipment != null)
            {
                Navigation.NavigateTo($"/create-repair-order/{equipment.Code}");
            }
        }
    }
}
