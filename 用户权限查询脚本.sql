-- =============================================
-- 用户权限诊断查询脚本
-- =============================================

USE [PermissionSystem]
GO

PRINT '=== 1. 基础数据统计 ==='
-- 查看所有表的记录数
SELECT 
    'Users' AS TableName, 
    COUNT(*) AS RecordCount,
    SUM(CASE WHEN IsEnabled = 1 THEN 1 ELSE 0 END) AS EnabledCount
FROM Users

UNION ALL

SELECT 
    'Roles' AS TableName, 
    COUNT(*) AS RecordCount,
    SUM(CASE WHEN IsEnabled = 1 THEN 1 ELSE 0 END) AS EnabledCount
FROM Roles

UNION ALL

SELECT 
    'Permissions' AS TableName, 
    COUNT(*) AS RecordCount,
    SUM(CASE WHEN IsEnabled = 1 THEN 1 ELSE 0 END) AS EnabledCount
FROM Permissions

UNION ALL

SELECT 
    'UserRoles' AS TableName, 
    COUNT(*) AS RecordCount,
    SUM(CASE WHEN IsEnabled = 1 THEN 1 ELSE 0 END) AS EnabledCount
FROM UserRoles

UNION ALL

SELECT 
    'RolePermissions' AS TableName, 
    COUNT(*) AS RecordCount,
    SUM(CASE WHEN IsEnabled = 1 THEN 1 ELSE 0 END) AS EnabledCount
FROM RolePermissions

UNION ALL

SELECT 
    'MenuItems' AS TableName, 
    COUNT(*) AS RecordCount,
    SUM(CASE WHEN IsEnabled = 1 THEN 1 ELSE 0 END) AS EnabledCount
FROM MenuItems;

PRINT ''
PRINT '=== 2. 用户详细信息 ==='
-- 查看所有用户的详细信息
SELECT 
    Id,
    Username,
    DisplayName,
    Email,
    IsEnabled,
    IsLocked,
    LoginFailureCount,
    LastLoginTime,
    CreatedAt
FROM Users
ORDER BY Id;

PRINT ''
PRINT '=== 3. 角色详细信息 ==='
-- 查看所有角色
SELECT 
    Id,
    Code,
    Name,
    Description,
    SortOrder,
    IsEnabled,
    IsSystem,
    CreatedAt
FROM Roles
ORDER BY SortOrder;

PRINT ''
PRINT '=== 4. 用户角色分配情况 ==='
-- 查看用户角色分配
SELECT 
    u.Id AS UserId,
    u.Username,
    u.DisplayName,
    r.Id AS RoleId,
    r.Code AS RoleCode,
    r.Name AS RoleName,
    ur.AssignedAt,
    ur.ExpiresAt,
    ur.IsEnabled AS UserRoleEnabled,
    ur.Remark
FROM Users u
LEFT JOIN UserRoles ur ON u.Id = ur.UserId AND ur.IsEnabled = 1
LEFT JOIN Roles r ON ur.RoleId = r.Id
ORDER BY u.Username, r.SortOrder;

PRINT ''
PRINT '=== 5. admin用户的权限详情 ==='
-- 专门查看admin用户的权限
DECLARE @AdminUserId INT;
SELECT @AdminUserId = Id FROM Users WHERE Username = 'admin';

IF @AdminUserId IS NOT NULL
BEGIN
    PRINT 'Admin用户ID: ' + CAST(@AdminUserId AS NVARCHAR(10));
    
    -- admin用户的角色
    SELECT 
        '通过角色获得的权限' AS PermissionSource,
        r.Code AS RoleCode,
        r.Name AS RoleName,
        p.Code AS PermissionCode,
        p.Name AS PermissionName,
        p.Module,
        p.Action,
        p.RouteUrl,
        p.Level,
        rp.IsEnabled AS RolePermissionEnabled
    FROM Users u
    INNER JOIN UserRoles ur ON u.Id = ur.UserId
    INNER JOIN Roles r ON ur.RoleId = r.Id
    INNER JOIN RolePermissions rp ON r.Id = rp.RoleId
    INNER JOIN Permissions p ON rp.PermissionId = p.Id
    WHERE u.Id = @AdminUserId 
    AND ur.IsEnabled = 1 
    AND r.IsEnabled = 1 
    AND rp.IsEnabled = 1 
    AND p.IsEnabled = 1
    ORDER BY p.Module, p.SortOrder;
    
    -- admin用户的直接权限
    SELECT 
        '直接分配的权限' AS PermissionSource,
        p.Code AS PermissionCode,
        p.Name AS PermissionName,
        p.Module,
        p.Action,
        p.RouteUrl,
        up.IsGranted,
        up.AssignedAt,
        up.ExpiresAt,
        up.IsEnabled
    FROM Users u
    INNER JOIN UserPermissions up ON u.Id = up.UserId
    INNER JOIN Permissions p ON up.PermissionId = p.Id
    WHERE u.Id = @AdminUserId 
    AND up.IsEnabled = 1 
    AND p.IsEnabled = 1
    ORDER BY p.Module, p.SortOrder;
END
ELSE
BEGIN
    PRINT 'Admin用户不存在！';
END

PRINT ''
PRINT '=== 6. 菜单权限映射 ==='
-- 查看菜单项和对应的权限要求
SELECT 
    m.Id,
    m.Code AS MenuCode,
    m.Name AS MenuName,
    m.RouteUrl,
    m.PermissionCode,
    m.IsEnabled AS MenuEnabled,
    m.IsPublic,
    m.MenuType,
    m.Level,
    m.SortOrder,
    p.Name AS RequiredPermissionName,
    p.Module AS RequiredModule,
    p.Action AS RequiredAction
FROM MenuItems m
LEFT JOIN Permissions p ON m.PermissionCode = p.Code
WHERE m.IsEnabled = 1
ORDER BY m.Level, m.SortOrder;

PRINT ''
PRINT '=== 7. 角色权限分配统计 ==='
-- 查看每个角色有多少权限
SELECT 
    r.Code AS RoleCode,
    r.Name AS RoleName,
    COUNT(DISTINCT rp.PermissionId) AS PermissionCount,
    STRING_AGG(p.Code, ', ') AS PermissionCodes
FROM Roles r
LEFT JOIN RolePermissions rp ON r.Id = rp.RoleId AND rp.IsEnabled = 1
LEFT JOIN Permissions p ON rp.PermissionId = p.Id AND p.IsEnabled = 1
WHERE r.IsEnabled = 1
GROUP BY r.Id, r.Code, r.Name
ORDER BY r.Code;

PRINT ''
PRINT '=== 8. 权限使用情况分析 ==='
-- 分析哪些权限被使用，哪些没有
SELECT 
    p.Code AS PermissionCode,
    p.Name AS PermissionName,
    p.Module,
    p.Action,
    p.RouteUrl,
    CASE 
        WHEN EXISTS (SELECT 1 FROM RolePermissions rp WHERE rp.PermissionId = p.Id AND rp.IsEnabled = 1) 
        THEN '已分配给角色' 
        ELSE '未分配' 
    END AS RoleAssignStatus,
    CASE 
        WHEN EXISTS (SELECT 1 FROM UserPermissions up WHERE up.PermissionId = p.Id AND up.IsEnabled = 1) 
        THEN '有直接分配' 
        ELSE '无直接分配' 
    END AS DirectAssignStatus,
    CASE 
        WHEN EXISTS (SELECT 1 FROM MenuItems m WHERE m.PermissionCode = p.Code) 
        THEN '被菜单使用' 
        ELSE '未被菜单使用' 
    END AS MenuUsageStatus
FROM Permissions p
WHERE p.IsEnabled = 1
ORDER BY p.Module, p.SortOrder;

PRINT ''
PRINT '=== 9. 问题诊断 ==='

-- 检查admin用户是否存在
IF NOT EXISTS (SELECT 1 FROM Users WHERE Username = 'admin')
    PRINT '⚠️ 问题: admin用户不存在！'
ELSE
    PRINT '✓ admin用户存在'

-- 检查admin用户是否启用
IF EXISTS (SELECT 1 FROM Users WHERE Username = 'admin' AND IsEnabled = 0)
    PRINT '⚠️ 问题: admin用户被禁用！'
ELSE
    PRINT '✓ admin用户已启用'

-- 检查admin用户是否被锁定
IF EXISTS (SELECT 1 FROM Users WHERE Username = 'admin' AND IsLocked = 1)
    PRINT '⚠️ 问题: admin用户被锁定！'
ELSE
    PRINT '✓ admin用户未被锁定'

-- 检查admin用户是否有角色分配
DECLARE @AdminRoleCount INT;
SELECT @AdminRoleCount = COUNT(*) 
FROM Users u
INNER JOIN UserRoles ur ON u.Id = ur.UserId
WHERE u.Username = 'admin' AND ur.IsEnabled = 1;

IF @AdminRoleCount = 0
    PRINT '⚠️ 问题: admin用户没有分配任何角色！'
ELSE
    PRINT '✓ admin用户已分配 ' + CAST(@AdminRoleCount AS NVARCHAR(10)) + ' 个角色'

-- 检查Administrator角色是否存在
IF NOT EXISTS (SELECT 1 FROM Roles WHERE Code = 'Administrator')
    PRINT '⚠️ 问题: Administrator角色不存在！'
ELSE
    PRINT '✓ Administrator角色存在'

-- 检查Administrator角色是否有权限
DECLARE @AdminRolePermissionCount INT;
SELECT @AdminRolePermissionCount = COUNT(*) 
FROM Roles r
INNER JOIN RolePermissions rp ON r.Id = rp.RoleId
WHERE r.Code = 'Administrator' AND rp.IsEnabled = 1;

IF @AdminRolePermissionCount = 0
    PRINT '⚠️ 问题: Administrator角色没有分配任何权限！'
ELSE
    PRINT '✓ Administrator角色已分配 ' + CAST(@AdminRolePermissionCount AS NVARCHAR(10)) + ' 个权限'

-- 检查MenuItems表是否有数据
DECLARE @MenuItemCount INT;
SELECT @MenuItemCount = COUNT(*) FROM MenuItems WHERE IsEnabled = 1;

IF @MenuItemCount = 0
    PRINT '⚠️ 问题: MenuItems表中没有启用的菜单项！'
ELSE
    PRINT '✓ MenuItems表中有 ' + CAST(@MenuItemCount AS NVARCHAR(10)) + ' 个启用的菜单项'

-- 检查公开菜单
DECLARE @PublicMenuCount INT;
SELECT @PublicMenuCount = COUNT(*) FROM MenuItems WHERE IsEnabled = 1 AND IsPublic = 1;

PRINT '✓ 公开菜单数量: ' + CAST(@PublicMenuCount AS NVARCHAR(10))

-- 检查需要权限的菜单
DECLARE @PermissionMenuCount INT;
SELECT @PermissionMenuCount = COUNT(*) FROM MenuItems WHERE IsEnabled = 1 AND PermissionCode IS NOT NULL;

PRINT '✓ 需要权限的菜单数量: ' + CAST(@PermissionMenuCount AS NVARCHAR(10))

PRINT ''
PRINT '=== 10. 修复建议 ==='
PRINT '如果admin用户看不到菜单，请检查以下几点：'
PRINT '1. 确保admin用户存在且已启用'
PRINT '2. 确保admin用户已分配Administrator角色'
PRINT '3. 确保Administrator角色有相关权限'
PRINT '4. 确保MenuItems表中有相应的菜单数据'
PRINT '5. 检查菜单的PermissionCode是否与权限表中的Code匹配'

PRINT ''
PRINT '查询完成！' 