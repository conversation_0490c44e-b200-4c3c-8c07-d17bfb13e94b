SET QUOTED_IDENTIFIER ON
SET ANSI_NULLS ON
GO
--工艺单称完后的整箱料校验
ALTER PROCEDURE usp_lbCheckDyestuffConsitentWithBill
(
    @Batch_NO VARCHAR(50),
    @ChemicalBoxNo VARCHAR(50)
)
AS
BEGIN
    --DECLARE @Batch_NO VARCHAR(50),@ChemicalBoxNo VARCHAR(50)
    --SELECT @Batch_NO ='B24074687',@ChemicalBoxNo = 'M2407300002'


	--检查是否录入单箱重量
    IF EXISTS
    (
        SELECT 1
        FROM PUBDB..uvw_pbChemicalPerBoxWeight a
            JOIN dbo.uvw_lbChemicalBoxNo b
                ON b.Chemical_Code = a.Chemical_Code
        WHERE b.ChemicalBoxNo = @ChemicalBoxNo
              AND a.PerBoxWeightKG = 0
    )
    BEGIN
        RAISERROR('染料没有单箱重', 16, 1);
        RETURN;
    END;


    DECLARE @IsConsitent BIT = 0;
    DECLARE @BoxNum INT;
    SELECT @BoxNum = dbo.udf_lbGetDyestuffBoxNumByBill(@Batch_NO, @ChemicalBoxNo);


    IF @BoxNum >= 1
    BEGIN
        SET @IsConsitent = 1;
    END;


    DECLARE @LastWeightBillNOByBatch VARCHAR(50) = '';
    SELECT @LastWeightBillNOByBatch = dbo.udf_lbGetLastWeightBillNoByBatchNo(@Batch_NO);

    SET XACT_ABORT ON;

    BEGIN TRAN;

    --校验记录
    INSERT INTO dbo.lbCheckDyeingBatchChemicalBoxNo
    (
        ChemicalBoxNo,
        Batch_NO,
        Bill_NO,
        Operate_Time,
        Check_Result
    )
    VALUES
    (   @ChemicalBoxNo,           -- ChemicalBoxNo - varchar(50)
        @Batch_NO,                -- Batch_NO - varchar(50)
        @LastWeightBillNOByBatch, -- Bill_NO - varchar(50)
        GETDATE(),                -- Operate_Time - datetime
        CASE
            WHEN @IsConsitent = 1 THEN
                '相同'
            ELSE
                '不同'
        END                       -- Check_Result - varchar(50)
        );

    COMMIT;

    SELECT CASE
               WHEN @IsConsitent = 1 THEN
                   '相同'
               ELSE
                   '不同'
           END AS IsConsitent,
           @BoxNum AS BoxNum;
END;




GO

