using Microsoft.Extensions.Options;
using CoreHub.Web.Services;
using CoreHub.Shared.Services;

namespace CoreHub.Web.Services
{
    /// <summary>
    /// 证书监控配置
    /// </summary>
    public class CertificateMonitoringOptions
    {
        public const string SectionName = "CertificateMonitoring";
        
        /// <summary>
        /// 是否启用监控
        /// </summary>
        public bool Enabled { get; set; } = true;
        
        /// <summary>
        /// 检查间隔（小时）
        /// </summary>
        public int CheckIntervalHours { get; set; } = 24;
        
        /// <summary>
        /// 提前警告天数
        /// </summary>
        public int WarningDays { get; set; } = 30;
        
        /// <summary>
        /// 紧急警告天数
        /// </summary>
        public int CriticalDays { get; set; } = 7;
        
        /// <summary>
        /// 通知邮箱
        /// </summary>
        public List<string> NotificationEmails { get; set; } = new();
        
        /// <summary>
        /// 是否发送邮件通知
        /// </summary>
        public bool EnableEmailNotification { get; set; } = false;
    }

    /// <summary>
    /// 证书监控服务接口
    /// </summary>
    public interface ICertificateMonitoringService
    {
        /// <summary>
        /// 开始监控
        /// </summary>
        Task StartMonitoringAsync();
        
        /// <summary>
        /// 停止监控
        /// </summary>
        Task StopMonitoringAsync();
        
        /// <summary>
        /// 立即检查证书状态
        /// </summary>
        Task<CertificateMonitoringResult> CheckCertificateNowAsync();
    }

    /// <summary>
    /// 证书监控服务实现
    /// </summary>
    public class CertificateMonitoringService : ICertificateMonitoringService, IDisposable
    {
        private readonly ISslCertificateService _sslService;
        private readonly IApplicationLogger _logger;
        private readonly IConfiguration _configuration;
        private readonly CertificateMonitoringOptions _options;
        private Timer? _monitoringTimer;
        private bool _disposed = false;

        public CertificateMonitoringService(
            ISslCertificateService sslService,
            IApplicationLogger logger,
            IConfiguration configuration,
            IOptions<CertificateMonitoringOptions> options)
        {
            _sslService = sslService;
            _logger = logger;
            _configuration = configuration;
            _options = options.Value;
        }

        /// <summary>
        /// 开始监控
        /// </summary>
        public async Task StartMonitoringAsync()
        {
            if (!_options.Enabled)
            {
                _logger.LogInformation("证书监控已禁用");
                return;
            }

            _logger.LogInformation("启动SSL证书监控服务，检查间隔: {Hours}小时", _options.CheckIntervalHours);

            // 立即执行一次检查
            await CheckCertificateNowAsync();

            // 设置定时器
            var interval = TimeSpan.FromHours(_options.CheckIntervalHours);
            _monitoringTimer = new Timer(async _ => await CheckCertificateNowAsync(), 
                null, interval, interval);
        }

        /// <summary>
        /// 停止监控
        /// </summary>
        public async Task StopMonitoringAsync()
        {
            _monitoringTimer?.Dispose();
            _monitoringTimer = null;
            _logger.LogInformation("SSL证书监控服务已停止");
            await Task.CompletedTask;
        }

        /// <summary>
        /// 立即检查证书状态
        /// </summary>
        public async Task<CertificateMonitoringResult> CheckCertificateNowAsync()
        {
            try
            {
                var certPath = _configuration["Kestrel:EndPoints:Https:Certificate:Path"];
                var certPassword = _configuration["Kestrel:EndPoints:Https:Certificate:Password"];

                if (string.IsNullOrEmpty(certPath) || string.IsNullOrEmpty(certPassword))
                {
                    var result = new CertificateMonitoringResult
                    {
                        IsSuccess = false,
                        Message = "SSL证书配置不完整",
                        CheckTime = DateTime.Now
                    };
                    
                    _logger.LogWarning("SSL证书配置不完整，跳过监控检查");
                    return result;
                }

                var certInfo = await _sslService.GetCertificateInfoAsync(certPath, certPassword);
                if (certInfo == null)
                {
                    var result = new CertificateMonitoringResult
                    {
                        IsSuccess = false,
                        Message = "无法获取证书信息",
                        CheckTime = DateTime.Now
                    };
                    
                    _logger.LogError(new InvalidOperationException("证书监控检查失败"), "证书监控检查失败：无法获取证书信息");
                    return result;
                }

                var monitoringResult = new CertificateMonitoringResult
                {
                    IsSuccess = true,
                    CertificateInfo = certInfo,
                    CheckTime = DateTime.Now,
                    DaysUntilExpiry = certInfo.DaysUntilExpiry,
                    IsExpired = certInfo.IsExpired,
                    IsExpiringSoon = certInfo.DaysUntilExpiry <= _options.WarningDays,
                    IsCritical = certInfo.DaysUntilExpiry <= _options.CriticalDays
                };

                // 根据证书状态记录日志和发送通知
                await ProcessMonitoringResultAsync(monitoringResult);

                return monitoringResult;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "证书监控检查过程中发生错误: {Error}", ex.Message);
                return new CertificateMonitoringResult
                {
                    IsSuccess = false,
                    Message = $"监控检查失败: {ex.Message}",
                    CheckTime = DateTime.Now
                };
            }
        }

        /// <summary>
        /// 处理监控结果
        /// </summary>
        private async Task ProcessMonitoringResultAsync(CertificateMonitoringResult result)
        {
            if (result.CertificateInfo == null) return;

            var certInfo = result.CertificateInfo;

            if (certInfo.IsExpired)
            {
                _logger.LogError(new InvalidOperationException("SSL证书已过期"), "🚨 SSL证书已过期！过期时间: {ExpiryDate}", certInfo.NotAfter);
                await SendNotificationAsync("SSL证书已过期", 
                    $"证书已于 {certInfo.NotAfter:yyyy-MM-dd HH:mm:ss} 过期，请立即更换证书！", 
                    NotificationLevel.Critical);
            }
            else if (result.IsCritical)
            {
                _logger.LogWarning("⚠️ SSL证书即将过期！剩余天数: {Days}，过期时间: {ExpiryDate}", 
                    certInfo.DaysUntilExpiry, certInfo.NotAfter);
                await SendNotificationAsync("SSL证书紧急警告", 
                    $"证书将在 {certInfo.DaysUntilExpiry} 天后过期（{certInfo.NotAfter:yyyy-MM-dd}），请尽快更换证书！", 
                    NotificationLevel.Critical);
            }
            else if (result.IsExpiringSoon)
            {
                _logger.LogWarning("📅 SSL证书即将过期，剩余天数: {Days}，过期时间: {ExpiryDate}", 
                    certInfo.DaysUntilExpiry, certInfo.NotAfter);
                await SendNotificationAsync("SSL证书过期提醒", 
                    $"证书将在 {certInfo.DaysUntilExpiry} 天后过期（{certInfo.NotAfter:yyyy-MM-dd}），建议准备更换证书。", 
                    NotificationLevel.Warning);
            }
            else
            {
                _logger.LogInformation("✅ SSL证书状态正常，剩余天数: {Days}，过期时间: {ExpiryDate}", 
                    certInfo.DaysUntilExpiry, certInfo.NotAfter);
            }
        }

        /// <summary>
        /// 发送通知
        /// </summary>
        private async Task SendNotificationAsync(string subject, string message, NotificationLevel level)
        {
            try
            {
                // 记录通知日志
                _logger.LogInformation("发送证书通知: {Subject} - {Message}", subject, message);

                // 这里可以集成邮件服务、短信服务、企业微信等
                if (_options.EnableEmailNotification && _options.NotificationEmails.Any())
                {
                    // TODO: 实现邮件发送逻辑
                    _logger.LogInformation("邮件通知功能待实现，收件人: {Recipients}", 
                        string.Join(", ", _options.NotificationEmails));
                }

                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "发送证书通知失败: {Error}", ex.Message);
            }
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                _monitoringTimer?.Dispose();
                _disposed = true;
            }
        }
    }

    /// <summary>
    /// 证书监控结果
    /// </summary>
    public class CertificateMonitoringResult
    {
        public bool IsSuccess { get; set; }
        public string? Message { get; set; }
        public DateTime CheckTime { get; set; }
        public CertificateInfo? CertificateInfo { get; set; }
        public int DaysUntilExpiry { get; set; }
        public bool IsExpired { get; set; }
        public bool IsExpiringSoon { get; set; }
        public bool IsCritical { get; set; }
    }

    /// <summary>
    /// 通知级别
    /// </summary>
    public enum NotificationLevel
    {
        Info,
        Warning,
        Critical
    }
}
